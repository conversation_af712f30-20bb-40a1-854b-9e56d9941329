/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: gme_doc_obj.h
 * Description: Provide gme doc api
 * Author: wangxiangdong
 * Create: 2024-03-07
 */

#ifndef GME_DOC_OBJ_H
#define GME_DOC_OBJ_H

#include <stdbool.h>
#include "gme_api.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum GmeDocType {
    GME_MAP_TYPE = 0,
    GME_ARRAY_TYPE = 1,
    GME_TEXT_TYPE = 2,
    GME_XML_FRAGMENT_TYPE = 3,
    GME_XML_ELEMENT_TYPE = 4,
    GME_XML_TEXT_TYPE = 5,
} GmeDocTypeE;

typedef struct GmeElementId {
    char *equipId;
    uint64_t incrClock;
} GmeElementIdT;

typedef struct GmeXmlInfo {
    uint32_t type;
    GmeElementIdT elementId;
} GmeXmlInfoT;

typedef struct GmeXmlOpPosition {
    const char *tableName;
    const GmeElementIdT *elementId;
} GmeXmlOpPositionT;

typedef struct GmeXmlNodeDiff {
    GmeDocTypeE type;
    const char *content;
} GmeXmlNodeInfoT;
/**
 * @ingroup gme_doc_obj
 * @brief register event to doc.
 * @param[in] conn Connection which handles this operation.
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeEventRegister(GmeConnT *conn);

/**
 * @ingroup gme_doc_obj
 * @brief get sharedobj by type of json string.
 * @param[in] conn Connection which handles this operation.
 * @param[in] name Name of type struct.
 * @param[in] type Type of doc obj.
 * @param[out] value String shard object.
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeGetShardObjByType(GmeConnT *conn, const char *name, GmeDocTypeE type, char **value);

/**
 * @ingroup gme_doc_obj
 * @brief set doc event status.
 * @param[in] conn Connection which handles this operation.
 * @param[in] eventName Name of event.
 * @param[in] int32_t int32_t to be set.
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeSetDocEventStatus(GmeConnT *conn, const char *eventName, bool status);

typedef int32_t (*GmeDocEventFuncT)(uint32_t count, ...);  // Register event func to notify
/**
 * @ingroup gme_doc_obj
 * @brief get doc type json string.
 * @param[in] conn Connection which handles this operation.
 * @param[in] eventNotify Callback function to notify.
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeSetDocCallback(GmeConnT *conn, GmeDocEventFuncT eventNotify);

/**
 * @brief Set attribute to an XmlElement with a specific elementId
 * @param[in] conn Connection which handles this operation.
 * @param[in] tableName Name of the top-level node to which the current object is attached.
 * @param[in] elementId elementId of the XmlElement
 * @param[in] attributeName key name of attribute to be set
 * @param[in] attributeValue value of attribute
 * @return GRD_OK if success.
 */
GME_EXPORT int32_t GmeXmlElementSetAttribute(GmeConnT *conn, const char *tableName, const GmeElementIdT *elementId,
    const char *attributeName, const char *attributeValue);

/**
 * @brief Remove an attribute of an XmlElement with a specific elementId
 * @param[in] conn Connection which handles this operation.
 * @param[in] tableName Name of the top-level node to which the current object is attached.
 * @param[in] elementId elementId of the XmlElement
 * @param[in] attributeName key name of attribute to be removed
 * @return GRD_OK if success.
 */
GME_EXPORT int32_t GmeXmlElementRemoveAttribute(
    GmeConnT *conn, const char *tableName, const GmeElementIdT *elementId, const char *attributeName);

/**
 * @brief Get all attributes of an XmlElement with a specific elementId
 * @brief After allAttributes is used, we needs call GmeFreeSeqValue to free the memory
 * @param[in] conn Connection which handles this operation.
 * @param[in] tableName Name of the top-level node to which the current object is attached.
 * @param[in] elementId elementId of the XmlElement
 * @param[out] allAttributes all the attributes of the XmlElement
 * @return GRD_OK if success.
 */
GME_EXPORT int32_t GmeXmlElementGetAttributes(
    GmeConnT *conn, const char *tableName, const GmeElementIdT *elementId, char **allAttributes);

/**
 * @ingroup gme_doc_obj
 * @brief set map key value.
 * @param[in] conn Connection which handles this operation.
 * @param[in] mapName The current map name
 * @param[in] parent The ElementId of parent
 * @param[in] key The operation key
 * @param[in] value The value to set
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeMapSet(
    GmeConnT *conn, const char *mapName, const GmeElementIdT *parent, const char *key, const char *value);

/**
 * @ingroup gme_doc_obj
 * @brief set map delete key.
 * @param[in] conn Connection which handles this operation.
 * @param[in] mapName The current map name
 * @param[in] parent The ElementId of parent
 * @param[in] key The operation key
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeMapDelete(GmeConnT *conn, const char *mapName, const GmeElementIdT *parent, const char *key);

/**
 * @ingroup gme_doc_obj
 * @brief get map key-value json string.
 * @param[in] conn Connection which handles this operation.
 * @param[in] mapName The current map name
 * @param[in] parent The ElementId of parent
 * @param[in] key The operation key
 * @param[out] value The value of the key
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeMapRead(
    GmeConnT *conn, const char *mapName, const GmeElementIdT *parent, const char *key, char **value);

/**
 * @ingroup gme_doc_obj
 * @brief insert object or value to xml fragment.
 * @param[in] conn Connection which handles this operation.
 * @param[in] fragmentName Name of fragment.
 * @param[in] elementId elementId of the Xml Fragment
 * @param[in] index Insert location.
 * @param[in] nodeInfo nodeInfo of new node.
 * @param[out] elementId of new node.
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeXmlFragmentInsert(
    GmeConnT *conn, GmeXmlOpPositionT *parent, uint32_t index, GmeXmlNodeInfoT *nodeInfo, GmeElementIdT **outId);

/**
 * @ingroup gme_doc_obj
 * @brief delete object or value from xml fragment.
 * @param[in] conn Connection which handles this operation.
 * @param[in] fragmentName Name of fragment.
 * @param[in] elementId elementId of the Xml Fragment
 * @param[in] index Delete location.
 * @param[in] length The number of elements to remove.
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeXmlFragmentDelete(
    GmeConnT *conn, const char *fragmentName, const GmeElementIdT *elementId, uint32_t index, uint32_t length);

/**
 * @ingroup gme_doc_obj
 * @brief fragment get element by index.
 * @param[in] conn Connection which handles this operation.
 * @param[in] fragmentName Name of fragment.
 * @param[in] elementId elementId of the Xml Fragment.
 * @param[in] index Index of element in fragment.
 * @param[out] respXml Response type and xmlElementId info.
 * @return GMERR_OK if success.
 */
int32_t GmeXmlFragmentGet(
    GmeConnT *conn, const char *fragmentName, const GmeElementIdT *elementId, uint32_t index, GmeXmlInfoT **respXml);

/**
 * @ingroup gme_doc_obj
 * @brief converting  xml fragment to string.
 * @param[in] conn Connection which handles this operation.
 * @param[in] fragmentName Name of fragment.
 * @param[in] elementId elementId of the Xml Fragment
 * @param[out] replyJson result string.
 * @return GMERR_OK if success.
 */
int32_t GmeXmlFragmentToString(
    GmeConnT *conn, const char *fragmentName, const GmeElementIdT *elementId, char **replyJson);

/**
 * @ingroup gme_doc_obj
 * @brief read xml text
 * @param[in] conn Connection which handles this operation.
 * @param[in] xmlTextInfo containing fragmentName and fragment node
 * @param[out] value read text
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeXmlTextRead(GmeConnT *conn, GmeXmlOpPositionT *xmlTextInfo, char **value);

/**
 * @ingroup gme_doc_obj
 * @brief insert xml text
 * @param[in] conn Connection which handles this operation.
 * @param[in] xmlTextInfo containing fragmentName and fragment node
 * @param[in] index node-inner index
 * @param[in] content insert content
 * @param[in] attrStr insert content's attributes
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeXmlTextInsert(
    GmeConnT *conn, GmeXmlOpPositionT *xmlTextInfo, uint32_t index, const char *content, const char *attrStr);

/**
 * @ingroup gme_doc_obj
 * @brief delete xml text
 * @param[in] conn Connection which handles this operation.
 * @param[in] xmlTextInfo containing fragmentName and fragment node
 * @param[in] index node-inner index
 * @param[in] length op length
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeXmlTextDelete(GmeConnT *conn, GmeXmlOpPositionT *xmlTextInfo, uint32_t index, uint32_t length);

/**
 * @ingroup gme_doc_obj
 * @brief assign attribute for xml text
 * @param[in] conn Connection which handles this operation.
 * @param[in] xmlTextInfo containing fragmentName and fragment node
 * @param[in] index node-inner index
 * @param[in] length op length
 * @param[in] attrStr assigning content's attributes
 * @return GMERR_OK if success.
 */
GME_EXPORT int32_t GmeXmlTextAssignAttributes(
    GmeConnT *conn, GmeXmlOpPositionT *xmlTextInfo, uint32_t index, uint32_t length, const char *attrStr);

/**
 * @ingroup gme_doc_obj
 * @brief free GmeElementIdT
 * @param[in] outId, the addr of GmeElementIdT
 */
GME_EXPORT void GmeXmlFreeElementId(GmeElementIdT *outId);

#ifdef __cplusplus
}
#endif

#endif /* GME_DOC_OBJ_H */
