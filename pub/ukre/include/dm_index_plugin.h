/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: header file for data model EdgeT
 * Author: xuwenjie
 * Create: 2025-05-28
 */

#ifndef DM_INDEX_PLUGIN_H
#define DM_INDEX_PLUGIN_H

#include "adpt_index_plugin.h"
#include "db_common_index_plugin.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define DM_VLIVF_MAX_LEVEL 1
#define DM_MAX_NAME_LENGTH 128
#define ART_NODE_TYPE_NUM 4u

typedef struct AnnFloatVector {
    uint16_t dim;
    void *items;
} AnnFloatVecT;

typedef enum {
    NODE_MEMBER_INDEX = 0,
    HASH_INDEX,
    HASH_LINKLIST_INDEX,
    ART_INDEX_LOCAL,        // 用ART索引支持原LOCAL排序索引
    ART_INDEX_HASHCLUSTER,  // 用ART索引支持原HASHCLUSTER索引
    LPM4_INDEX,
    LPM6_INDEX,
    HASHCLUSTER_INDEX,
    CHAINED_HASH_INDEX,
    LIST_LOCALHASH_INDEX,  // 仅用于YANG场景list唯一性校验
    BTREE_INDEX,
    TTREE_INDEX,
    DISKANN_INDEX,
    LPASMEM_INDEX,
#ifdef FEATURE_HAC
    HAC_HASH_INDEX,
    MULTI_HASH_INDEX,
#endif
#ifdef FEATURE_VLIVF
    VLIVF_INDEX,
#endif
    INDEX_TYPE_MAX
} DmIndexTypeE;

// 索引的约束条件枚举
typedef enum {
    UNIQUE = 0,  // 主键肯定是唯一的，此处主要是针对非主键。
    PRIMARY,     // 是否是主键，主键肯定是唯一的。
    NON_UNIQUE,  // 非主键，也非唯一
} DmIndexConstraintE;

typedef enum DmSysPropeType {
    CHECK_VERSION,             // 全表/分区对账中的check_version
    STATUS_MERGE_NODE_ADDR,    // 用于记录订阅链表中数据节点的address信息
    STATUS_MERGE_DELETE_MARK,  // 标记pubsub信息被删除但是未被获取
    DATA_SYNC_VERSION,         // 同步表新增的系统列，用于数据同步
    SYSTEM_PROPE_BOTTOM,
} DmSysPropeTypeE;

typedef enum {
    DM_DEFAULT_TYPE_VALUE = 0,          // 常量类型默认值
    DM_DEFAULT_TYPE_EXPR,               // 常量表达式类型默认值
    DM_DEFAULT_TYPE_CURRENT_TIME,       // 当前时间，格式为HH:MM:SS
    DM_DEFAULT_TYPE_CURRENT_DATE,       // 当前日期，格式为YYYY-MM-DD
    DM_DEFAULT_TYPE_CURRENT_TIMESTAMP,  // 当前时间戳，格式为YYYY-MM-DD HH:MM:SS
    DM_DEFAULT_TYPE_BUTT
} DmDefaultTypeE;

typedef struct DmYangUnionTypes {
    uint64_t typesInfo;  // 用于yang场景，union内置类型, bit存储 padding(4)|unionTypeBits(12)|order bits(4*12)
} DmYangUnionTypesT;

typedef struct {
    DbDataTypeE type;
    uint32_t padding1;  // 8字节对齐下，隐式padding
    union {
        char charValue;
        uint8_t ucharValue;
        int8_t byteValue;
        uint8_t ubyteValue;
        int16_t shortValue;
        uint16_t ushortValue;
        int32_t intValue;
        uint32_t uintValue;
        bool boolValue;
        int64_t longValue;
        uint64_t ulongValue;
        float floatValue;
        double doubleValue;
        uint64_t resValue;
        int64_t timeValue;
        uint8_t bitfield8;
        uint16_t bitfield16;
        uint32_t bitfield32;
        uint64_t bitfield64;
        uint8_t partitionValue;
        struct {
            uint32_t strAddrOffset;
            uint32_t padding2;  // 8字节对齐下，隐式padding
            union {
                uint64_t padding;  // 确保指针跨平台大小一致
                void *strAddr;
                const void *constStrAddr;
            };
            uint32_t length;
            uint16_t beginPos;
            uint16_t endPos;  // BitMap数据类型的长度有约束小于32K bits
        };
    } value;
} DbValueT;

typedef DbValueT DmValueT;

typedef struct DmRangeConsT {
    DmValueT begin;  // 范围约束的起始值
    DmValueT end;    // 范围约束的结束值
} DmRangeConsT;

typedef struct DmShmText {
    char *str;
    uint32_t len;
    uint32_t strOffset;
} DmShmTextT;

typedef struct DmPropertyConstraint {
    uint8_t valueRangeNum;           // 值范围校验的个数
    uint8_t lengthRangeNum;          // 长度范围校验的个数
    uint8_t patternNum;              // 正向匹配pattern校验的个数
    uint8_t invertNum;               // 反正向匹配invert pattern校验的个数
    DmRangeConsT *valueRangeArray;   // 值范围校验数组，由若干begin&end组成
    DmRangeConsT *lengthRangeArray;  // 长度范围校验的数组，由若干begin&end组成
    uint32_t valueRangeArrayOffset;
    uint32_t lengthRangeArrayOffset;
    uint32_t patternArrayOffset;
    uint32_t invertArrayOffset;
    DmShmTextT *patternArray;  // pattern数组，由若干段字符串模式组成
    DmShmTextT *invertArray;   // invert pattern数组，由若干段字符串模式组成
} DmPropConsT;

typedef enum DmYangClauseType {
    DM_YANG_CLAUSE_WHEN = 0,  // when可以定义在节点和字段上，但节点和字段上的条件只能有一个
    DM_YANG_CLAUSE_LEAFREF,   // leafref只能定义在字段上
    DM_YANG_CLAUSE_MUST,      // must可以定义在节点和字段上，且节点和字段上的条件能有多个
    DM_YANG_CLAUSE_NO_REQUIRED_LEAFREF,  // require-instance 为false的leafref
    DM_YANG_CLAUSE_BOTTOM
} DmYangClauseTypeE;

typedef struct DmYangClause {
    DmYangClauseTypeE type;  // XPath表达式类型
    uint32_t strLen;         // XPath表达式长度
    char *str;               // XPath表达式
    uint8_t clauseStatus;    // XPath的状态，0 - 未校验， 1 - 校验有效， 2 - 校验无效
    uint32_t strOffset;
} DmYangClauseT;

typedef struct DmYangClauseInfo {
    DmYangClauseT *clauses;  // 校验信息数组
    uint16_t count;          // 数组个数
    uint32_t clausesOffset;
} DmYangClauseInfoT;

typedef struct DmDerivedPath {
    uint32_t pathLen;  // 单条derived-path的长度
    uint32_t pathNameOffset;
    char *pathName;  // derived-path名字
} DmDerivedPathT;

typedef struct DmAttributeRef {
    int32_t value;                 // enum或identity枚举值
    uint32_t nameLen;              // enum或identity名字长度
    char *name;                    // enum或identity枚举名
    DmDerivedPathT *derivedPaths;  // identity的derivedPath信息
    uint32_t pathCnt;              // derivedPaths个数,不能超过10
    uint32_t nameOffset;
    uint32_t derivedPathsOffset;
} DmAttributeRefT;

typedef struct DmYangAttributeInfo {
    uint32_t attributeRefNameLen;    // 用于yang场景，记录该字段的enum或identity名字长度
    uint32_t attributeRefCount;      // 用于yang场景，记录该字段下有多少个enum或identity的信息
    char *attributeRefName;          // 用于yang场景，记录该字段的enum或identity名字
    DmAttributeRefT *attributeRefs;  // 用于yang场景，存储字段上的enum或identity信息
    uint32_t attributeRefNameOffset;
    uint32_t attributeRefsOffset;
} DmYangAttributeInfoT;

// 注意：此结构体改动会影响datalog的so，发生改动需要知会datalog组wangyuzhong、wangsi，及时更新datalog so
typedef struct PropertySchema {
    uint32_t propeId;              // 标志当前propertySchema在vertexLabel中的下标
    DbDataTypeE dataType;          // 数据类型
    DmSysPropeTypeE sysPropeType;  // 系统字段的类型，isSysPrope = true时才生效
#if defined(FEATURE_PERSISTENCE) || defined(TS_MULTI_INST) || defined(FEATURE_TS)
    uint32_t stbPropId;        // 系统表的属性ID，仅在系统表代码中使用
    uint32_t stbPropParentId;  // 系统表的属性的父节点ID，仅在系统表代码中使用
#endif
    uint32_t size;     // 属性的长度，可能是定长长度，也可能是最大长度，由isFixed标识
                       // Note：位域数据类型和BitMap数据类型时，大小为bit，其他数据类型为Byte
    char *name;        // 属性名称
    char *comment;     // 属性说明 //
    uint16_t nameLen;  // 属性名称长度
    uint16_t commentLen;  // 属性说明的长度 //
    bool isValid;  // 标识当前PropertySchema是否有效，如果无效表明该属性是Node类型，此时下面成员无意义
    bool isNullable;  // 当前属性是否可为空
    bool isFixed;  // 标识当前属性是否为定长，如果为真，则成员size为定长长度，否则为最大长度
    bool isResource;     // 当前属性是否是资源字段
    bool isSysPrope;     // 当前属性是否是系统字段
    bool isAutoIncProp;  // 标识属性是否是自增列
    bool isSensitive;    // 如果该属性是敏感数据，则不打印
    bool isInPrimKey;    // 标识当前属性是否在根节点的主键字段中
    bool isInLpmKey;     // 目前仅在Datalog场景使用该字段，在Datalog离线编译填充，Parser解析判断
    bool hasConstraint;  // 用于yang场景下是否有校验约束
    bool isConfig;       // 用于yang场景下判断当前属性是否为配置属性
    uint8_t bitfieldOffset;  // 当前数据类型为位域时才有效, 记录在位域中的偏移量

    DmValueT *defaultValue;  // 属性的默认值，有default value情况下才可访问，否则为NULL
#ifdef FEATURE_SQL
    DmDefaultTypeE defaultType;  // 属性默认值的类型
    uint32_t dim;  // dataType为向量时有效,表示向量的维度,无论isNotCopy的值如何都会填充
#endif
    uint8_t defaultValueNum;  // 用于yang leaf-list 多默认值的场景，此时defaultValue为数组，普通场景则置为1
    uint8_t npaIndex;  // 用于yang场景含有when和默认值定义的字段。leaflist场景必须使用 vertex schema 上的 npaIndex。
                       // 普通字段在NPABitmap的下标值；leaf-list的字段的第一个默认值在NPABitmap的下标。
    uint16_t parentId;  // 记录属性所属的父节点Id
#ifdef FEATURE_SIMPLEREL
    uint16_t customTypeId;  // 记录自定义数据ID值, 非自定义数据类型该值为DM_INVALID_CUSTOM_ID
    uint16_t propeDefLen;  // 记录V1原始字段定义长度，部分数据类型与DmPropertySchemaT中的size大小不同
    uint16_t offset;       // 记录V1一条数据buf中本字段偏移
#endif
    union {
        DmYangUnionTypesT unionTypes;  // 用于yang场景, 记录当前属性的union类型的内置类型,该类型不会有值约束
        DmPropConsT *constraint;  // 用于yang场景下的值范围和长度范围校验约束
    };
    DmYangClauseInfoT *clauseInfo;        // 存字段上语义校验的解析
    DmYangAttributeInfoT *attributeInfo;  // 存字段上enum和identityref的解析
    uint32_t nameOffset;
    uint32_t commentOffset;
    uint32_t defaultValueOffset;
    uint32_t constraintOffset;
    uint32_t clauseInfoOffset;
    uint32_t attributeInfoOffset;
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
    uint16_t codecInfo;
    bool isInet;  // 是否为ip类型字段
#endif
#ifdef FEATURE_VLIVF
    bool isInVlIvfKey;
#endif
} DmPropertySchemaT;

typedef enum DmLogicalOperator {
    DM_LOGICAL_OPERATOR_AND,  // 对condition数组中的元素进行与运算
    DM_LOGICAL_OPERATOR_OR,   // 对condition数组中的元素进行或运算
    DM_LOGICAL_OPERATOR_MAX
} DmLogicalOperatorE;

typedef enum { DM_COND_EQUAL, DM_COND_UNEQUAL, DM_COND_MAX } DmCondCompareTypeE;

typedef struct DmFilterCondition {
    DmValueT value;
    DmCondCompareTypeE compareType;
    DmPropertySchemaT *property;
    uint32_t propertyOffset;
} DmFilterConditionT;

typedef struct DmIndexFilter {
    uint32_t conditionNum;            // 索引过滤条件的数目
    DmLogicalOperatorE operatorType;  // 索引过滤条件之间的逻辑关系
    DmFilterConditionT *conditions;   // 索引过滤条件数组
    uint32_t conditionsOffset;
} DmIndexFilterT;

/* resolve type when conflict */
typedef enum DmConflictType {
    DM_RESOLVE_NONE,     /* There is no constraint to check */
    DM_RESOLVE_ABORT,    /* Back out changes but do no rollback transaction */
    DM_RESOLVE_ROLLBACK, /* Fail the operation and rollback the transaction */
    DM_RESOLVE_FAIL,     /* Stop the operation but leave all prior changes */
    DM_RESOLVE_IGNORE,   /* Ignore the error. Do not do the INSERT or UPDATE */
    DM_RESOLVE_REPLACE,  /* Delete existing record, then do INSERT or UPDATE */
    DM_RESOLVE_BUTT,
} DmConflictTypeE;

typedef enum DmSortType { DM_SORT_UNDEFINED, DM_SORT_ASC, DM_SORT_DESC, DM_SORT_END } DmSortTypeE;

typedef struct DmVlIvfIndexInfo {
    uint32_t dim;
    uint32_t propId;
    float oodThreshold;
    float scanRatio;
    float candiRatio;
    uint8_t metric;
    uint8_t quantType;
    uint8_t nCodeBits;
    uint8_t nlevel;
    uint8_t oodMetric;
    uint32_t ncentroids[DM_VLIVF_MAX_LEVEL];
} DmVlIvfIndexInfoT;

typedef struct DmIndexLabelCfg {
    uint32_t initHashCapacity;  // hash初始表的大小
    DmIndexTypeE hashIdxType;
} DmIndexLabelCfgT;

typedef struct DmIndexLabelBase {
    uint32_t dbId;                       // 该索引所属的DB
    uint32_t indexId;                    // 索引的id
    uint32_t srcLabelId;                 // 索引所属标签的id
    uint32_t srcLabelNameLen;            // 索引所属标签名称长度
    char *srcLabelName;                  // 索引所属标签名称
    ShmemPtrT shmAddr;                   // 索引空间的共享内存入口addr
    DmIndexLabelCfgT indexLabelCfg;      // 索引的可选配置项
    DmIndexConstraintE indexConstraint;  // 索引的约束
    DmIndexTypeE indexType;              // 索引的类型
    bool isLabelLatchMode;               // 该索引是否处于大表锁模式下
    bool isKvLabel;                      // 标识 kv label 或者 vertex label
    uint8_t reverse;                     // 字节对齐
    uint32_t srcLabelNameOffset;
} DmIndexLabelBaseT;

typedef struct DmExprIndexInfo {
    void *exprs;                // 索引的表达式ExprT数组 ExprFuncT
    DbListT irExprs;            // 索引的表达式<IRExprT *> (a+b, c-d)
    uint32_t num;               // 索引的表达式ExprT数组个数
    DbDataTypeE *resDataTypes;  // 索引表达式结果类型数组
} DmExprIndexInfoT;

typedef struct DmVlIndexLabel {
    DmIndexLabelBaseT idxLabelBase;   // DmIndexLabelBaseT
    uint16_t propeNum;                // 索引的属性个数
    uint32_t indexNameLen;            // 索引的名称长度
    char *indexName;                  // 索引的名称
    bool isNullable;                  // 该索引上属性是否可允许为空
    bool isRealHashTypeSetByKey;      // 是否已经索引级别配置过 hash_type
    bool isInitHashCapacitySetByKey;  // 是否已经索引级别配置过 capacity
    uint8_t globalIndexId;  // 索引的全局id,兼容V3,按建表json中keys定义索引的顺序设置globalIndexId,从0开始计数
    uint16_t hcIndexId;      // hashcluster索引ID
    uint16_t fixedPropeNum;  // 定长索引属性的个数
    uint32_t maxKeyLen;      // 按照每个索引属性取最大空间而计算的keyBuf长度，包含nullInfoBytes
    uint32_t stbIndexId;     // 索引系统表id，普通索引与indexId，只有memberKey会赋予单独的id
    char *comment;           // 该索引的comment信息
    uint32_t *propIds;       // 在哪些属性id上建立了该索引
    uint16_t *parentIds;  // 这些属性所属的父节点id，与DmPropertySchemaT中的parentId含义相同，仅供yang使用
    DmPropertySchemaT *properties;  // 索引所属的schema上的属性数组
    DmIndexFilterT indexFilter;     // 索引创建的过滤条件
    uint32_t commentLen;            // 该索引的comment长度
    uint16_t indexNameOffset;
    uint16_t commentOffset;
    uint16_t propIdsOffset;
    uint16_t parentIdsOffset;
    uint16_t propertiesOffset;
    uint8_t nullInfoBytes;  // keybuf中表示索引属性是否设值的位图所需字节数
    bool isCheckNullProp;   // 是否检查null值，仅用于YANG场景list唯一性校验
#ifdef FEATURE_SQL
    DmConflictTypeE conflictStrategy;  // 唯一性索引的冲突策略
    DmSortTypeE *sortType;             // 索引每列的升降序标记
    bool isExprIdx;                    // 是否存在索引列为表达式
    bool isLpasPq;                     // 是否为LpasPQ索引
    DmExprIndexInfoT *exprIdxInfo;     // 表达式索引的信息 DmExprIndexInfoT
    char *exprIndexStr;                // 原SQL串
    uint32_t sortTypeOffset;
    uint32_t posOfPqAddr;  // 保存的PQ信息在隐藏自增索引中的key后方的位置
#endif
#if defined(FEATURE_SQL) || defined(FEATURE_DISKANN) || defined(FEATURE_LPASMEM)
    DmVecDistTypeE indexDistType;  // 向量距离度量类型
#endif
#ifdef FEATURE_VLIVF
    DmVlIvfIndexInfoT vlivfInfo;
#endif
} DmVlIndexLabelT;

typedef struct DmKvIndexLabel {
    DmIndexLabelBaseT idxLabelBase;
} DmKvIndexLabelT;

bool DmDataTypeIsVector(DbDataTypeE type);

static inline DbDataTypeE DmIndexLabelGetVectorDataType(DmVlIndexLabelT *indexLabel)
{
    uint32_t propsNum = indexLabel->propeNum;
    DmPropertySchemaT *props = indexLabel->properties;
    if (propsNum == 1) {
        return props[indexLabel->propIds[0]].dataType;
    }
    uint32_t i;
    for (i = 0; i < propsNum; i++) {
        uint32_t propId = indexLabel->propIds[i];
        if (DmDataTypeIsVector(props[propId].dataType)) {
            // 取第一个向量类型，后续可能存在混合向量索引
            return props[propId].dataType;
        }
    }
    DB_ASSERT(false);
    return props[indexLabel->propIds[0]].dataType;
}

typedef struct DmPropertyInfo {  // property information extracted from schema
    uint32_t propeId;
    DbDataTypeE dataType;
    uint32_t propeMaxLen;  // string/bytes/bitmap max size (Byte), bitfield max bitSize, 定长字段即为定长长度
    uint32_t offset;  // offset of each property in runningBuf (位域属性比较特殊，是指位域类型的起始位置)
    char *propeName;   // property name
    uint16_t nameLen;  // property name len
    uint8_t bitfieldOffset;  // 当前数据类型为DB_DATATYPE_BITFIELD8\16\32\64时才有效, 记录在位域中的偏移量
    bool isValid;         // False: Node类型, True: 普通属性, 只有在普通属性时上面的字段才有意义
    bool isNullable;      // 当前属性是否可为空
    bool isAutoIncPrope;  // 标识属性是否是自增列
    bool isSysPrope;      // 当前属性是否是系统字段
    bool isSensitive;     // 标识该属性是否是敏感数据
    bool isResCol;
    bool isFixed;
    uint8_t defaultValueNum;  // 用于yang leaf-list 多默认值的场景，此时defaultValue为数组，普通场景则置为1
    uint8_t npaIndex;  // 用于yang场景含有when和默认值定义的字段。leaflist场景必须使用 vertex schema 上的 npaIndex。
                       // 普通字段在NPABitmap的下标值；leaf-list的字段的第一个默认值在NPABitmap的下标。
    DmValueT *defaultValue;  // 属性的默认值，有default value情况下才可访问，否则为NULL；yang
                             // leaf-list多默认值的场景时defaultValue为数组
} DmPropertyInfoT;

typedef struct DmSuperFieldInfo {  // superField information
    uint32_t superFieldId;
    uint32_t nameLen;
    char *superFieldName;
    uint32_t beginPropeId;
    uint32_t propeNum;   // property number in superField
    uint32_t offset;     // offset of superField in runningBuf
    uint32_t length;     // the length of value in superField
    uint32_t hasBitMap;  // if superfield has bitmap property
} DmSuperFieldInfoT;

typedef enum RecordType {
    DM_RECORD_IN_VERTEX,       // record in vertex, memory malloc by record
    DM_RECORD_IN_NODE,         // record in node, memory malloc by node
    DM_RECORD_IN_YANG_VERTEX,  // record in yang vertex, to support yang condition
    DM_RECORD_IN_YANG_NODE,    // record in yang node, to support yang condition
} DmRecordTypeE;

typedef struct RecordDesc {
    DbMemCtxT *memCtx;
    enum RecordType recordType;
    uint32_t propeNum;  // 包括node，不包括系统字段, 第一层会空余1，与schema->propeNum语义不完全相同
    DmPropertyInfoT *propeInfos;  // the order is the same as the order of schema property

    struct DmSuperFieldInfo *superFieldInfos;
    uint32_t superFieldNum;  // superField number
    uint32_t runningBufLen;

    uint32_t fixedPropertiesLen;  // the totalLength of fixed-length properties in runningBuf

    uint8_t isFixed : 1;         // 所有属性都是定长的
    uint8_t hasBitMapPrope : 1;  // 是否含有bitmap属性
    uint8_t hasLongDefault : 1;  // 表示默认值是否有大长度变长字段
    uint8_t reserved : 5;
    bool hasDefaultValue;  // 优化默认值的处理
    uint16_t notNullPropeNum;

    uint8_t *defaultValueBuf;       // 对应于runningBuf
    uint8_t *defaultValueNullInfo;  // 对应于propeIsSetValue
    int8_t *propeIsSetValueAllSet;  // 序列化性能优化，创建时全置1，序列化时与propeIsSetValue比较判断是否有空属性

    uint16_t *notNullPropeIds;  // 序列化性能优化，为了可以更快的找到非空字段的ID

    uint32_t memSizePreMalloc;
    uint32_t bitmapUpdateInfoLen;
    uint16_t totalBitMapNum;

    uint32_t
        reservedNum;  // 为了兼容orm结构化，预留属性个数，第一层为RESERVED_SIZE，其他为0，目前预留长度也可以用该值标识
    // yang相关
    uint32_t propOpTypesLen;  // 字段级五原语使用，长度大小等于属性数目
} RecordDescT;

typedef struct DmRecord {
    uint16_t magicCode;
    uint32_t recordIndex;  // 当前record在数组/动态数组中的下标，如果是普通记录，则为0
    DbMemCtxT *memCtx;
    RecordDescT *recordDesc;
    int8_t *propeIsSetValue;  // whether property is set value or not
    uint8_t *runningBuf;      // fixed-length property in front, Variable-length property in later,
                              // Variable-length property: 4B (varBufLen) + 4B (real length)
                              // + property value length(如果小于varBufLen，大于则需要额外申请空间)
    uint8_t *recordSeriBuf;   // 指向vertexBuf上的对应位置
    uint32_t recordSeriBufLen;
    uint8_t hasLongVarPrope : 1;  // 表示是否有大长度变长字段，是运行态的flag，不是一个表schema中有超大长度的
    // 变长字段就为1，而是实际该记录中确实存在一个超大长度的变长字段
    bool hasBitMapPartialSet : 1;  // 是否存在bitmap局部更新
    uint8_t reserved : 6;
    uint8_t *updateBitMapBuf;  // bitMap属性id（2B） + beginPos（4B） + endPos（4B）

    // yang相关
    uint8_t *propeOpTypes;  // 字段级五原语使用，表示各个属性所赋予的DML操作类型
    /* yang的subtree查询使用 */
    int8_t *propeIsEmpty;  // 仅用于 subtree过滤
                           // 在查询树上，表示属性值是否设置了叶子过滤；在结果树上，表示属性值是否来自于默认值
} DmRecordT;

typedef enum DmNodeOpType {
    DM_NODE_CLEAR = 0,
    DM_VECTOR_APPEND,       // 在当前节点Append元素
    DM_VECTOR_REMOVE,       // 在当前节点Remove元素
    DM_VECTOR_UPDATE,       // 在当前节点Update元素
    DM_NODE_MERGE,          // yang场景对当前节点Merge操作
    DM_NODE_REPLACE_GRAPH,  // yang场景对当前节点Replace操作
    DM_NODE_INSERT,         // yang场景对当前节点Create操作
    DM_NODE_DELETE_GRAPH,   // yang场景对当前节点Delete操作
    DM_NODE_REMOVE_GRAPH,   // yang场景对当前节点Delete操作
    DM_NODE_NONE,           // yang场景对当前节点None操作
    DM_NODE_OP_BOTTOM,      // 用于判断操作是否合法
} DmNodeOpTypeE;

typedef enum {
    DM_NODE_RECORD,  // 当前Node仅为一条记录，一条记录一般含多个属性
    DM_NODE_ARRAY,   // 当前Node为数组，数组的每个元素为一条记录
    DM_NODE_VECTOR,  // 当前Node为动态数组，动态数组的每个元素为一条记录
} DmNodeTypeE;

typedef struct DmIndexKeyBufInfo {
    uint32_t indexId;
    DmIndexConstraintE indexConstraint;  // 索引的约束

    uint16_t keyPropeNum;  // 记录索引属性的基本信息，用以优化索引属性是否全部已设置值
    bool isNullable;       // 是否有索引属性支持非空
    // 记录keybuf中表示索引属性是否设值的位图所需字节数
    uint8_t nullInfoBytes;
    uint16_t varPropeNum;    // 索引中变长字段的个数
    uint16_t fixedPropeNum;  // 定长属性个数

    uint16_t *keyPropeIds;
    uint16_t *keyPropeNodeIds;  // key属性对应所属的节点
    uint8_t indexKeyType;       // 索引类型，小型化考虑只占用1字节，索引类型不会超过8bit
    uint8_t seriType;           // 序列化类型
    // 以下参数与序列化类型相关, 后续通过序列化类型申请内存

    // 对于VertexT中的runningBuf以及序列化后的vertexSeriBuf，下面offset信息是一样的
    // 索引中定长属性非连续的段数
    // 假设索引建立在(F1, F2, F3, F6, F7)这5个定长属性上，那么非连续段数为2，分别为：F1, F2, F3 和 F6, F7
    uint16_t segmentNum;
    // 每个key buf段针对定长存储区的偏移量
    uint16_t *segmentOffsets;
    // 每个索引段的字节长度
    uint16_t *segmentLengths;
    // 每个索引段的对应的nodeId
    uint16_t *segmentNodeIds;
    // 每个定长属性的size
    uint16_t *fixedPropeSize;

    // 服务于VertexT的runningBuf中变长key属性的获取
    uint16_t *varPropeOffsets;
    // 索引中变长key属性在所有变长字段中的序号，服务于vertexSeriBuf中变长key属性的获取
    // 因为vertexSeriBuf中的变长属性实际长度是不定的，所以必须挨个遍历以获取指定变长字段的value
    uint16_t *varPropeSequences;
    // seriType为SPARSE时使用
    uint32_t totalPropeNum;  // 索引所在层对应的总的属性数
    uint16_t *propeSize;  // 当前层所有属性的长度，数组长度为totalPropeNum，定长为属性长度，变长| node 为0
    uint8_t *isValide;  // 数组长度为totalPropeNum, 属性为1，node为0
} DmIndexKeyBufInfoT;

typedef enum DmYangVertexType {
    DM_YANG_CONTAINER,  // yang的container类型
    DM_YANG_LIST,       // yang的list类型
    DM_YANG_CHOICE,     // yang的choice类型
    DM_YANG_CASE,       // yang的case类型
    DM_YANG_BOTTOM
} DmYangVertexTypeE;

typedef enum DmYangModelType {
    DM_YANG_MODEL_GRAPH,  // 全打散方案
    DM_YANG_MODEL_TREE,   // 部分打散方案
    DM_YANG_MODEL_BOTTOM
} DmYangModelTypeE;

typedef struct DmYangInfoDesc {
    DmYangVertexTypeE type;
    DmYangModelTypeE modelType;
    uint16_t isDefault : 1;   // 标识是否为默认case
    uint16_t isNullable : 1;  // 标识choice/case的nullable属性，默认true
    uint16_t isRoot : 1;      // 标识vertex是否为根节点，默认为true
    uint16_t isPresence : 1;  // 标识vertex是否为Yang定义中的Presence Container
    uint16_t isConfig : 1;    // 标识vertex/node是否默认是配置属性
    uint16_t isLeaflist : 1;  // 区别list和leaflist
    uint16_t isNpAccess : 1;  // 区别于用户业务定义的表，该默认值数据不可见表用于语义校验
    uint16_t isDefaultChild : 1;  // 标识default case的子树节点
    uint16_t hasYangDefault : 1;  // 标识子树默认值数据的存在性
    uint16_t reserved1 : 7;
    uint16_t uniqueNodeId;  // 提供node表级唯一标识，由于node数目约束，此处用uint16_t类型
    uint8_t npaIndex;       // 所属节点在表NPABitmap的下标
    uint8_t reserved2;
    uint16_t reserved3;
} DmYangInfoDescT;

typedef struct DmNodeDesc {
    DbMemCtxT *memCtx;
    char *name;
    uint32_t nameLen;
    uint32_t nodeId;
    DmNodeTypeE nodeType;  // 普通记录，数组，动态数组

    uint32_t indexNum;  // memberkey number
    DmIndexKeyBufInfoT *indexKeyBufInfos;
    uint32_t perKeyBufInfoSize;  // 每个indexKeyBufInfo的大小 = sizeof(DmIndexKeyBufInfoT) + 其中所有数组的大小

    uint32_t maxKeyLen;             // 静态信息中预计算，避免create时进行计算，提升create性能
    uint32_t maxKeyLenWithoutBits;  // 除去位图后的maxKeyLen
    uint32_t maxElementNum;         // 动态数组最大元素数
    uint16_t initElementNum;        // 动态数组初始的元素数
    uint16_t vectorExtendSize;      // 动态数组每次扩充的容量，当nodeType为vector时有效
    uint32_t nodeNumPerElement;     // 每个数组（array/vector）元素中的节点个数
    struct RecordDesc *recordDesc;
    struct DmNodeDesc **childNodeDescs;  // node指针数组，包含子节点的静态信息
    DmYangInfoDescT *yangInfoDesc;
} DmNodeDescT;

struct DmIndexKey {
    uint32_t dbId;                       // database id
    uint32_t labelId;                    // vertextLabel id
    uint32_t indexId;                    // indexLabel id
    DmIndexConstraintE indexConstraint;  // whether primary key or unique
    DmIndexTypeE indexType;              // indexType: hash, btree...
    uint32_t keyBufLen;                  // 按实际索引属性空间计算的keyBuf长度，包含nullInfoBytes
    uint8_t *keyBuf;                     // pointer which points to data buf
    uint32_t maxKeyLen;     // 按照每个索引属性取最大空间而计算的keyBuf长度，包含nullInfoBytes
    uint32_t propertyNum;   // the number of property in IndexKey
    bool isNullable;        // 索引属性是否支持非空
    uint8_t nullInfoBytes;  // keybuf中表示索引属性是否设值的位图所需字节数
    uint32_t seriBufLen;  // 记录该key的序列化长度，在基于memberKey的增量更新时使用，对DmIndexKey序列化不用关注
    DbMemCtxT *memCtx;  // if memCtx is null, invoker ensure memory alloc and free in the same MemContex
                        // if memCtx is not null, memory alloc and free all in given memCtx
};

struct DmNode {
    uint16_t headMagicCode;
    bool isClear;  // 用以优化DmResetNode的性能，只有之前执行过clear操作，reset时才需要将totalElementNum
                   // 的元素进行reset，否则只需要reset realElementNum的元素即可
    bool isCreated;  // 标识是否创建node，兼容v3，如果没有创建node则该node下的约束不用校验
    DbMemCtxT *memCtx;
    DmNodeDescT *nodeDesc;

    uint8_t *nodeKeyBuf;  // 将record中提取出来keyBuf放在nodeKeyBuf中

    uint32_t totalElementNum;  // 数组、动态数组总元素数
    uint32_t realElementNum;   // 动态数组真实使用的元素数

    struct DmRecord *currRecord;
    int8_t *propeIsSetValue;  // 标识属性是否设置了值，array/vector中所有record的propeIsSetValue
    int8_t *propeIsEmpty;  // yang子树过滤使用，标识属性是否为empty，array/vector中所有record的propeIsEmpty
    uint8_t *propeOpTypes;  // yang属性编辑使用，标识属性的操作类型，array/vector中所有record的propeOpTypes

    DmNodeOpTypeE deltaOpType;     // 当前节点如果是delta节点，记录插入该节点的操作类型
    uint32_t maxOpNum;             // delta节点中opArray的最大物理长度
    uint32_t maxOpNumByMemberKey;  // delta节点中delta member key的最大物理长度
    uint32_t opNum;  // delta节点中操作的数量，其中包括基于memberKey的操作，基于index的操作，append & clear操作
    uint32_t opNumByIdx;                  // 在增量更新的过程中，基于index更新操作的数量
    uint32_t opNumByMemberKey;            // 在增量更新的过程中，基于memberKey更新操作的数量
    uint16_t *opArray;                    // 储存操作的数组
    struct DmIndexKey **deltaMemberKeys;  // 基于memberKey更新时，储存memberKey的数组

    uint8_t *runningBuf;         // array/vector中所有record的数据buf
    uint32_t totalBufSize;       // total buf (runningBuf) size
    uint32_t usedBufSize;        // real buf (runningBuf) size of vector
    uint32_t *recordSeriLenArr;  // 记录每个record序列化buf的长度

    uint8_t **nodeSeriBuf;     // 记录DmParesVertex解析的recordbuf指针
    uint32_t *nodeSeriBufLen;  // 记录DmParesVertex解析的recordbuf指针

    uint8_t *updateBitmapBuf;  // array/vector中所有record的bitmap buf

    struct DmNode **nodes;      // node指针数组，包含所有Element中node节点的指针
    struct DmNode **currNodes;  // 指向当前数组元素的node起始位置
                                // yang句柄为按需创建，初始时nodes指针均为NULL，访问时需要考虑NULL的场景
    uint32_t mergeElementNum;  // 在vector型的delta节点中，标识merge后element的数量
    uint16_t tailMagicCode;
};

typedef enum EnumConcurrencyControlType {
    CONCURRENCY_CONTROL_NORMAL = 0,
    CONCURRENCY_CONTROL_LABEL_LATCH,
    CONCURRENCY_CONTROL_READ_UNCOMMIT,
    CONCURRENCY_CONTROL_INVALID,
} ConcurrencyControlE;

typedef struct DmHeapInfo {
    ShmemPtrT heapShmAddr;  // 存储引擎Heap空间的共享内存入口addr
#if defined(FEATURE_PERSISTENCE) && !defined(IDS_HAOTIAN)
    void *heapPtr;  // 存储引擎Heap空间的持久化HeapT Ptr
#endif
    uint64_t maxVertexNum;       // 该点标签中最多可能存在的点数目
    uint64_t trxId;              // 记录最后一个操作过该vertexLabel的事务Id（成功提交的）
    uint64_t trxIdLastModify;    // 记录最后一个修改过该vertexLabel的事务Id（成功提交的）
    uint64_t trxCommitTime;      // 记录最后一个操作过该vertexLabel的事务提交时间（成功提交的）
    ConcurrencyControlE ccType;  // 该点标签对应的表的并发控制类型
    bool maxVertexNumCheck;      // 该点标签是否要检查最大记录数
    bool needDefragmentation;    // 该点标签对应的表是否需要内存碎片整理
    bool isSupportReservedMemory;    // 该点标签是否支持使用保留内存
    bool supportUndeterminedLength;  // 该点标签对应的表是否关闭ClusterHash, 为true时，使用VarHeap
#ifdef FEATURE_GQL
    bool skipRowLockPessimisticRR;  // cfg designed for FES, default: false
#endif
    IsolationLevelE isolationLevel;  // 该标签对应的表的隔离级别
    TrxTypeE trxType;                // 该标签对应的表的事务类型
} DmHeapInfoT;

#define DM_RES_COL_MAX_COUNT 4

typedef struct DmResColInfo {
    uint32_t resPropeId[DM_RES_COL_MAX_COUNT];  // 资源字段的property id数组
    ShmemPtrT resColPool;                       // 点标签所绑定的res col pool的addr，表级别绑定
    uint32_t labelLatchId;                      // 点标签所绑定的res col pool的latch id
    uint32_t labelLatchVersionId;               // 点标签所绑定的res col pool的latch version id
    ShmemPtrT resPoolLatchShmAddr;  // 点标签所绑定的res col pool的锁共享内存（直连写适配）
    uint32_t resColCount;           // 资源字段的数量，若没有则为0
} DmResColInfoT;

typedef struct DmAutoIncrPropInfo {
    DbDataTypeE type;  // 自增列的类型，只支持uint32与uint64
    uint32_t
        autoIncrPropId;  // 标识自增列的属性ID,用于在插入数据时快速访问和修改自增列。自增列只能定义在树模型的根节点上
    uint64_t autoIncrStartValue;  // 记录自增列的起始值
    uint64_t autoIncrValue;       // 自增列值，插入数据时更新，保持严格单调性
    uint64_t autoIncrMaxValue;    // 自增列的最大值，当自增列超过该值，则插入失败
} DmAutoIncrPropInfoT;

typedef enum DmDatalogLabelType {
    DM_DTL_NORMAL,               // 普通表
    DM_DTL_RESOURCE_SEQUENTIAL,  // 固定资源表
    DM_DTL_RESOURCE_PUBSUB,      // pubsub型资源表
    DM_DTL_UPDATE,               // 可更新表
    DM_DTL_TRANSIENT_FIELD,      // transient修饰字段
    DM_DTL_TRANSIENT_TUPLE,      // transient表
    DM_DTL_TRANSIENT_FINISH,     // transient finish表
    DM_DTL_TBM,                  // TBM 表
    DM_DTL_MSG_NOTIFY,           // 消息通知表
    DM_DTL_EXTERN,               // 外部表（虽然定义在.d文件里，但实际是DB中现有的表）
    DM_DTL_STATE,                // 状态表
    DM_DTL_TYPE_BOTTOM
} DmDtlLabelTypeE;

typedef enum {
    DM_DTL_INPUT_LABEL = 0,     // 输入表
    DM_DTL_INTERMEDIATE_LABEL,  // 中间表
    DM_DTL_OUTPUT_LABEL,        // 输出表
    DM_DTL_INOUT_BOTTOM
} DmDtlInOutE;

typedef enum {
    DM_DTL_DB_WRITE = 0,      // 写DB表
    DM_DTL_TBM_SHMEM,         // 写TBM表
    DM_DTL_MSG_NOTIFY_SHMEM,  // 写消息通知
    DM_DTL_SUBSCRIPTION,      // 推送到订阅
    DM_DTL_EXTERNAL_TABLE,    // 写外部表
    DM_DTL_POST_PROCESS_BOTTOM
} DmDtlPostProcE;

typedef struct DmDatalogUpgradeInfo {
    int32_t prevUpgradeVersion;  // 上一个版本的upgradeVersion , undo过程中access_delta/access_current用来读写旧记录 ,
                                 // redo过程中access_delta/access_current用来读写新记录
    int32_t upgradeVersion;  // tuple的升级版本信息。
    // 1. 若tuple中该字段值和vertexLabel中的不一致，则需要根据新旧规则重做;
    // 2. 若tuple中该字段值和vertexLabel中的一致，否则不用处理；
} DmDatalogUpgradeInfoT;

typedef struct DmResourceInfo {
    uint32_t inputCount;      // 若类型是RESOURCE，输入的个数
    uint32_t outputCount;     // 若类型是RESOURCE，输出的个数
    uint32_t *inputPropIds;   // 若类型是RESOURCE，输入的prop id的数组
    uint32_t *outputPropIds;  // 若类型是RESOURCE，输出的prop id的数组
    DbValueT *defaultResVals;  // 若类型是RESOURCE，资源申请失败时资源字段填充的默认值的数组，和outputPropIds一一对应
    uint32_t resColId;
    uint32_t inputPropIdsOffset;
    uint32_t outputPropIdsOffset;
    uint32_t defaultResValsOffset;
} DmDtlResourceInfoT;

typedef struct DmUpdateInfo {
    bool updatePartial;  // 若类型是UPDATE，需在此记录是否为部分字段可更新表
    bool updateByRank;   // 若类型是UPDATE，需在此记录是否by rank
    uint32_t cmpUdfNameOffset;
    char *cmpUdfName;  // 比较udf的函数名
} DmDtlUpdateInfoT;

typedef struct DmDatalogLabelInfo {
    DmDtlLabelTypeE labelType;    // datalog特殊表的类型
    DmDtlInOutE inoutType;        // 决定表是输入表，中间表，输出表
    DmDtlPostProcE postProc;      // 后置处理
    uint32_t soId;                // 该vertexLabel属于哪个so
    uint16_t topoSortId;          // topo排序中，表示表顺序的id， datalog表数量有为数，65536够用
    uint16_t preTopoSortId;       // 升级/降级前的topo排序ID
    uint32_t timeoutPropId;       // 超时字段的propId
    uint32_t timeoutIndexSeqNum;  // 超时字段上需要建立排序索引，该属性标识该索引的indexId
    bool withTimeout : 1;         // 表是否带timeout选项
    bool withIndex0Type : 1;  // 表是否指定主键索引类型，.d中没有定义则加载时按配置项DB_CFG_DEFAULT_HASH_TYPE指定类型
    bool isAllFieldsPk : 1;  // 若vertexLabel的主键包含除dtlReservedCount外的全字段，则为true；否则为false
    bool withVariant : 1;       // 表是否带variant选项
    bool withFastInsert : 1;    // 表是否带fastInsert选项，fastUpdateMerge算子性能优化
    bool withMsgNotifyCmp : 1;  // 消息通知表是否带有 ordered 选项
    bool isTrans : 1;           // 分布式场景下表是否为转换后的fake表
    bool reserve : 1;           // 预留字段用于字节对齐 后续新增字段可复用该字段
    uint8_t unionTableNum;      // 与该vertexLabel关联的表的数量(不超过 DTL_UNION_DELETE_TABLE_MAX_NUM 个)
    char *timeoutUdfName;       // 超时udf的函数名
    DmShmTextT *unionTableNames;  // 与该vertexLabel关联的表的表名数组
    uint64_t batchMsgSize;        // 消息通知表的udf批消息大小约束
    DmDatalogUpgradeInfoT upgradeInfo;
    union {
        DmDtlResourceInfoT *resourceInfo;  // 资源表
        DmDtlUpdateInfoT *updateInfo;      // 可更新表
        uint32_t transientPropId;          // transient表
        char *tbmUdfName;                  // tbm表
        struct {
            char *msgNotifyUdfName;     // 消息通知表的udf函数名
            char *msgNotifyCmpUdfName;  // 消息通知表的排序 udf 函数名
        };
    };
    uint32_t timeoutUdfNameOffset;
    uint32_t unionTableNamesOffset;
    union {
        uint32_t resourceInfoOffset;  // 资源表
        uint32_t updateInfoOffset;    // 可更新表
        uint32_t tbmUdfNameOffset;    // tbm表
        struct {
            uint32_t msgNotifyUdfNameOffset;     // 消息通知表
            uint32_t msgNotifyCmpUdfNameOffset;  // 带排序的消息通知表
        };
    };
} DmDatalogLabelInfoT;

typedef struct DmEdgeLabelInfo {
    uint32_t edgeLabelId;  // 边标签ID
    uint32_t edgeNameLen;  // 边标签名称长度
    char *edgeName;        // 边标签名称
    uint32_t edgeNameOffset;
} DmEdgeLabelInfoT;

typedef struct DmDLRInfo {
    bool isDataSyncLabel;  // 是否为数据同步表
    uint64_t incrId;       // 自增id，用于DLR记录插入和更新次数
} DmDLRInfoT;

typedef struct DmOperStat {
    uint64_t successPrepareCount;
    uint64_t failPrepareCount;
    uint64_t successExecuteCount;
    uint64_t failExecuteCount;
} DmDmlOperStatT;

typedef struct DmPerfStat {
    uint64_t totalPrepareTime;
    uint64_t maxPrepareTime;
    uint64_t totalExecuteTime;
    uint64_t maxExecuteTime;
} DmDmlPerfStatT;

typedef enum DmVertexTimeStatType {
    INSERT_TIME_STATIS = 0,
    DELETE_TIME_STATIS = 1,
    UPDATE_TIME_STATIS = 2,
    REPLACE_TIME_STATIS = 3,
    MERGE_TIME_STATIS = 4,
    TIME_STATIS_END,
} DmVertexTimeStatTypeE;

typedef struct DmDwOperStat {
    uint64_t successExecuteCount;
    uint64_t failExecuteCount;
} DmDwDmlOperStatT;

typedef enum DmSubsEvent {
    DM_SUBS_EVENT_INSERT,
    DM_SUBS_EVENT_DELETE,
    DM_SUBS_EVENT_UPDATE,
    DM_SUBS_EVENT_REPLACE,
    DM_SUBS_EVENT_REPLACE_INSERT,
    DM_SUBS_EVENT_REPLACE_UPDATE,
    DM_SUBS_EVENT_MERGE,
    DM_SUBS_EVENT_MERGE_INSERT,
    DM_SUBS_EVENT_MERGE_UPDATE,
    DM_SUBS_EVENT_KV_SET,
    DM_SUBS_EVENT_INIT,
    DM_SUBS_EVENT_INIT_EOF,
    DM_SUBS_EVENT_AGED,
    DM_SUBS_EVENT_TRIGGER_SCAN_BEGIN,
    DM_SUBS_EVENT_TRIGGER_SCAN,
    DM_SUBS_EVENT_TRIGGER_SCAN_END,
    DM_SUBS_EVENT_MODIFY,
    DM_SUBS_EVENT_DIFF,
    DM_SUBS_EVENT_DIFF_EXPLICIT,
    DM_SUBS_EVENT_INIT_SCAN,
#ifdef FEATURE_GQL
    DM_SUBS_EVENT_INIT_BEGIN,
    DM_SUBS_EVENT_INIT_END,
#endif
    DM_SUBS_EVENT_CEIL,
} DmSubsEventE;

typedef struct DmReusableMeta {
    uint32_t vertexLabelId;
    uint32_t schemaVersion;         // 记录vertexlabel当前有效的最小版本
    uint32_t schemaMaxVersion;      // 记录vertexlabel当前有效的最大版本
    uint32_t subscriptionNum : 16;  // 该版本上的订阅数
    uint32_t stmgSubNum : 16;       // 该版本上的合并订阅数
    ShmemPtrT shmPtr;
    DmObjPrivT objPrivilege;  // 对象权限
    uint32_t schemaVersions[SCHEMA_VERSION_NUM];  // 记录点标签当前存在的所有schema版本号，用于校验对应版本号是否被删除
    uint32_t uuids[SCHEMA_VERSION_NUM];  // 点标签相同版本号通过该uuid判断是否和客户端的缓存版本一致
    DmDmlOperStatT dmlOperStat[(uint32_t)TIME_STATIS_END];  // DML操作执行次数统计数组
    DmDmlPerfStatT dmlPerfStat[(uint32_t)TIME_STATIS_END];  // DML操作执行时间统计数组
#ifdef DIRECT_WRITE
    // 考虑小型化，此处隔离
    DmDwDmlOperStatT dwDmlOperStat[(uint32_t)TIME_STATIS_END];  // 直连写DML操作执行次数统计数组
#endif
    struct DmReusableMeta *next;
    uint16_t subEventNums[(int32_t)DM_SUBS_EVENT_CEIL];  // 事件类型的订阅数量，事件有insert/update/delete/replace/age
} DmReusableMetaT;

typedef enum DmCheckStatus {
    DM_CHECK_STATUS_NORMAL,
    DM_CHECK_STATUS_CHECKING,
    DM_CHECK_STATUS_ABNORMAL
} DmCheckStatusE;

typedef struct DmCheckVersion {
    DmCheckStatusE checkStatus;
    bool hasBeenChecked;  // 本分区是否曾经被执行过对账操作
    bool truncatedInChecking;  // 对账过程中，是否执行了truncate，表示所有低版本数据将被清空，仅最新版本可见
    uint8_t version;          // 最新版本
    uint8_t checkingVersion;  // 对账开启时版本，仅在对账中(非truncate)有效
    uint8_t minRecovery;  // [min, max)表示需要恢复的版本，仅在对账中(非truncate)和恢复时有效，其他场景重置为最新版本
    uint8_t maxRecovery;  // min在后台恢复任务完成时更新，max在终止对账时更新
} DmCheckVersionT;

typedef struct DmCheckInfo {
    DmCheckVersionT version;
    uint8_t checkCnt;  // 每次结束对账生成老化任务的时候+1,老化完成移出任务队列的时候-1
    volatile uint64_t originalVertexCnt;  // 开始对账的时候，表/分区的点记录数
    volatile uint64_t changeVertexCnt;  // 对账过程中，checkVersion发生变更的记录数, 即不用老化的记录数,包括删除的记录
    volatile uint64_t shouldAgedCnt;      // 正常结束对账时，通过originalVertexCnt - changeVertexCnt得到
    volatile uint64_t realAgedCnt;        // 老化任务计算得出
    volatile uint64_t shouldTruncateCnt;  // 正常开启后台删除任务时统计
    volatile uint64_t realTruncatedCnt;   // 后台删除任务计算得出
    volatile uint64_t shouldRecoveryCnt;  // 异常结束对账时，通过originalVertexCnt - changeVertexCnt得到
    volatile uint64_t realRecoveryCnt;    // 老化任务计算得出
    uint64_t startTime;                   // 开始对账的时间，每次开始成功都会刷新这个时间
    uint64_t recordCnt;  // 当前表或者当前分区的记录数,不包括新订阅的标记删除的记录
    uint64_t logicCnt;   // 当前表或者当前分区的逻辑记录数
} DmCheckInfoT;

typedef struct DmAccCheck {
    DbLatchT lock;  // 短周期锁，外部模块可使用，checkInfo字段中volatile类型字段并发控制, 暂不设置可靠性锁
    bool hasBeenChecked;            // 本表是否曾经被执行过对账操作
    bool isPartition;               // 是否是分区表
    ShmemPtrT accCheckShmPtr;       // DmAccCheckT 共享内存
    uint32_t partitionPropeId;      // 分区字段的id
    uint32_t partitionPropeOffset;  // 分区字段的偏移
    uint32_t pushAgeVertexBatch;    // 每次推送老化的记录数
    uint32_t checkInfoNum;          // 对账信息数组长度，全表对账情况下数组长度为1
                                    // 分区对账情况下数组长度为DM_MAX_PARTITION_ID
    struct DmAccCheck *accCheckNext;
    DmCheckInfoT checkInfo[];  // 对账信息数组
} DmAccCheckT;

typedef struct DmRsmInfo DmRsmInfoT;

typedef struct RsmLabelValue RsmLabelValueT;

typedef enum RsmLabelStatus {
    LABEL_INIT = 0,
    LABEL_LOAD,
    LABEL_IMPORT,
    LABEL_TRX_RECOVER,
    LABEL_DATA_RECOVER,
    LABEL_WRITE_CACHE_MERGE,
    LABEL_FINISH,
} RsmLabelStatusE;

typedef struct RsmLabelKey {
    uint32_t dbId;
    const char *nspName;
    const char *labelName;
} RsmLabelKeyT;

typedef struct RsmLabelInfo {
    // 用于恢复
    ShmemPtrT labelInfoPtr;
    // 用于匹配
    uint32_t dbId;
    uint32_t nspNameOffset;
    uint32_t labelNameOffset;
    bool isLabelValid;
    uint8_t reserved[3];
} RsmLabelInfoT;

typedef struct RsmLabelListItem {
    ShmemPtrT next;  // RsmLabelListItemT
    RsmLabelInfoT info;
} RsmLabelListItemT;

typedef struct RsmLabelValue {
    RsmLabelStatusE labelStatus;  // 表恢复的当前状态
    RsmLabelKeyT key;             // 用于map匹配
    ShmemPtrT labelItemShmPtr;    // 用于remove时匹配
    RsmLabelListItemT *labelItem;
} RsmLabelValueT;

typedef struct RsmTableDowngradeInfo {
    bool isDowngrading;      // 是否正在降级中，如果为true，在恢复时需要添加任务到后台队列
    uint32_t targetVersion;  // 降级目标版本号
    uint32_t originVersion;  // 首次降级的版本
    uint32_t vertexLabelId;  // 降级的表
    uint32_t namespaceId;    // 降级表namespaceId,降级进度视图使用
    uint32_t degradedTimes;  // 降级表已降级次数,降级进度视图使用
    uint32_t vertexLabelNameLen;  // 降级表名长度,降级视图使用
} RsmTableDowngradeInfoT;

typedef struct RsmSchemaVersion {  // 不会存在相同version，恢复时使用schemaVersion恢复相应的uuid
    uint32_t schemaVersion;        // 记录点标签当前schema版本号
    uint32_t uuid;                 // uuid判断是否和客户端的缓存版本一致
} RsmSchemaVersionT;

typedef enum RsmContainerType {
    RSM_HEAP_LABEL,
    RSM_CLUSTERED_HASH_LABEL,
    RSM_KV_LABEL,
    RSM_CONTAINER_TYPE_NUMS,
} RsmContainerTypeE;

typedef struct RsmTableInfo {
    uint8_t partitionIdMax;  // {0, 1, DM_MAX_PARTITION_ID}
    bool isLabelTruncate;
    bool isDuplicate;                      // 表多实例复制出的子表
    bool isAutoRecovery;                   // 重启恢复阶段自动建表
    uint32_t upgradeVersionCnt;            // 总的版本数量
    uint32_t recoveryUpgradeVersionCnt;    // 已经恢复的版本数量
    uint32_t labelId;                      // 恢复表id
    RsmContainerTypeE containerType;       // 表类型
    ShmemPtrT containerInfo;               // 保存存储容器重建需要用到的信息
    ShmemPtrT writeCachePtr;               // 写缓存的保留内存Addr
    ShmemPtrT duplicateInfoPtr;            // 表多实例相关信息
    ShmemPtrT undoLiteRecPtr;              // 当前保留内存仅支持轻量化事务，liteUndo做到表级别即可
    ShmemPtrT labelJsonPtr;                // 仅在isAutoRecovery=true时使用，用于记录表定义
    ShmemPtrT configJsonPtr;               // 仅在isAutoRecovery=true时使用，用于记录表配置
    RsmUndoRecordT rsmUndoRec;             // 当前保留内存仅支持轻量化事务，rsmUndo做到表级别即可
    RsmTableDowngradeInfoT downgradeInfo;  // 记录该表降级信息
    RsmSchemaVersionT schemaVersions[SCHEMA_VERSION_NUM];  // 写缓存需要恢复schemaVersion信息
    DmCheckVersionT rsmCheckVersion[];
} RsmTableInfoT;  // 该结构体用于存储恢复阶段无法获取的必要信息，新增结构体成员需要严格把控

struct DmRsmInfo {
    uint64_t loadCycle;           // 统计恢复索引、事务等所需时间
    ShmemPtrT rsmTableInfoPtr;    // 保留内存管理结构体内存，warmReboot恢复时使用
    RsmLabelValueT *labelValue;   // 动态内存，包括恢复阶段状态等信息
    RsmTableInfoT *rsmTableInfo;  // 保留内存中表结构信息
    bool isLoad;  // 判断是否是warmReboot恢复的表还是warmReboot之后新建的表, warmReboot为true生效
    uint8_t reserved[3];
};

typedef struct DmAutoQuantPair {
    uint16_t type;
    uint16_t metric;
    uint16_t source;
    uint16_t property;
    uint16_t nCodeBits;
    uint16_t dim;
    uint32_t sourceSize;
} DmAutoQuantPairT;

typedef struct DmAutoQuantInfo {
    uint32_t num;
    uint32_t pairsOffset;
    DmAutoQuantPairT *pairs;
} DmAutoQuantInfoT;

typedef struct VertexLabelCommonInfo {
    char *creator;                       // 创建该vertexlabel的user
    uint32_t vertexLabelLatchId;         // 点标签latchID
    uint32_t vertexLabelLatchVersionId;  // 点标签latchID的版本号
    DmHeapInfoT heapInfo;                // 点标签中中的存储相关信息
    ShmemPtrT labelLatchShmAddr;         // 点标签的表latch共享内存addr
    DmResColInfoT *resColInfo;           // 点标签的资源列信息
    DmAutoIncrPropInfoT *autoIncrPropInfo;  // 点标签的自增列信息，非 yang 表至多一个，yang 表至多 11 个
    DmDatalogLabelInfoT *datalogLabelInfo;  // datalog表相关信息，仅在datalog使用场景分配内存，其余时候为null
    DmEdgeLabelInfoT *relatedEdgeLabels;  // 关联边标签信息数组
    ShmemPtrT relatedEdgeLabelsShm;       // 关联边标签信息数组的共享内存addr
    uint32_t edgeLabelNum;                // 关联边标签的个数
    uint32_t refCount;  // 此commonInfo被引用的次数，vertexlabel具有多版本时所有版本引用同一个commonInfo,
                        // 每次引用时此refCount加1
    uint32_t pushAgeVertexBatch;  // 每次推送老化的记录数
    uint32_t objPrivVersion;
    uint32_t degradedTimes;  // 已经降级的次数
    uint32_t maxVersion;     // vertexlabel的最大版本号
    uint32_t dwEntryToken;  // 当前仅允许一个stmt进入直连写。prepare的时候设置dwEntryToken为pid，断链的时候清除。
    uint8_t autoIncrPropNum;  // 自增列的数量，只有 Yang 表可以有超过一个自增列
    uint8_t versionCnt;       // 记录vertexlabel的未被删除的版本数
#ifdef FEATURE_REPLICATION
    uint8_t replicationType;  // 主备的复制方式 DmReplicationTypeE
#endif
    bool hasUpd : 1;         // 标记schema是否升级过
    bool isGroupCreate : 1;  // true 表示由 group 创建，false 表示由 user 创建
    bool canSub : 1;         // 是否允许创建订阅
#ifdef FEATURE_HAC
    bool enableHac : 1;  // 表是否使用硬件卸载特性
#endif
    bool enableDirectWrite : 1;  // 表级直连写开关：true 表示开启，在满足直连写的约束时，DML 操作使用直连写；
                                 // false 表示关闭，此表的任何操作均不会使用直连写
    bool isRsmAutoRecovery : 1;  // 是否为保留内存重启自动建表恢复场景
#ifdef ART_CONTAINER
    bool enableArtContainer : 1;  // 表级Art聚簇容器开关
    bool isArtRealCluster : 1;
#endif
    uint32_t rsmInfoOffset;
    DmDLRInfoT dlrInfo;          // DLR相关信息
    ShmemPtrT statusMergeList;   // 状态合并订阅链表
    ShmemPtrT nspObjPrivShmPtr;  // label所属的nsp的对象权限，此处仅记录，create和drop label时不处理
    ShmemPtrT metaInfoShm;       // 服务端metaInfo存储的是共享内存addr
    DmReusableMetaT *metaInfoAddr;  // 客户端会把metaInfo共享内存addr转换为逻辑addr;
    ShmemPtrT accCheckShm;          // 点标签的对账、老化信息
    DmAccCheckT *accCheckAddr;
    ShmemPtrT writeCacheDesc;  // 共享内存 or 保留内存，指向该表对应的写缓存的WriteCacheDescT
    DmRsmInfoT *rsmInfo;
    uint32_t creatorOffset;
    uint32_t resColInfoOffset;
    uint32_t datalogLabelInfoOffset;
    uint32_t autoIncrPropInfoOffset;
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
    uint32_t persistentSpaceId;  // 持久化表空间id
#endif
#ifdef FEATURE_AUTOQUANT
    DmAutoQuantInfoT autoQuantInfo;
#endif
} VertexLabelCommonInfoT;

typedef struct DmVertexDesc DmVertexDescT;

typedef enum VertexType {
    DM_FIXED_VERTEX,  // 平的vertex,字段均为定长，不包含变长
    DM_FLAT_VERTEX,   // 平的vertex,包含有变长
    DM_TREE_VERTEX,   // tree vertex，有node节点（只要是带子树节点的都是树模型）
} DmVertexTypeE;

struct DmVertexDesc {
    DbMemCtxT *memCtx;
    uint32_t labelId;  // 标签ID
    uint32_t memSizePreMalloc;
    char *labelName;                     // 标签名称
    VertexLabelCommonInfoT *commonInfo;  // 元数据中的commonInfo指针，如为共享内存则访问指针时需使用MEMBER_PTR

    struct RecordDesc *recordDesc;
    uint32_t secIdxNum;
    uint32_t resColNum;
    uint32_t edgeLabelNum;  // 该值是vertexLabel中edgeNum的备份，对vertex句柄的操作，使用vertex->edgeLabelNum
    uint32_t keyBufLen;

    uint32_t indexNum;           // indexKey number
    uint32_t perKeyBufInfoSize;  // 每个indexKeyBufInfo的大小 = sizeof(DmIndexKeyBufInfoT) + 其中所有数组的大小
    DmIndexKeyBufInfoT *indexKeyBufInfos;

    uint8_t seriType;         // 序列化类型
    uint8_t vertexType;       // DmVertexTypeE
    uint8_t labelLevel;       // DmVertexLabelLevelE
    uint8_t vertexLabelType;  // DmVertexLabelTypeE
    bool hasBitMapProp : 1;
    bool hasMemberKey : 1;
    bool checkValidity : 1;
    bool hasPkIdx : 1;
    bool hasResCol : 1;
    uint8_t reserve : 3;

    uint8_t vertexOffset;  // 简单表头长度，包含系统字段，vertexHead和fixLen，指向第一个属性字段。
                           // 该字段仅简单表有效，其他表，该值均为0。

    uint8_t sysPropeNum;  // 系统字段的个数
    uint8_t npaCount;  // YANG场景，描述当前VertexLabel表NPA节点个数（含有when定义，NP节点或带默认值的字段、leaf-list）
    uint32_t sysPropeOffset[(uint8_t)
            SYSTEM_PROPE_BOTTOM];  // 系统字段在定长Buffer尾部中的偏移信息，和DmSysPropeTypeE的排布信息一致。
                                   // 如果没有这个系统字段，offset则填0，当前CHECK_VERSION字段是所有表都存在的系统字段用于对账，
                                   // 在新订阅场景新增了STAT_MERGE_NODE_ADDR字段用于记录订阅链表中数据节点的address信息。
                                   // 在DLR表中（DLR表是通过配置文件定义的，在元数据最后解析，所以排在最末尾的地方）
                                   // DATA_SYNC_VERSION字段是用于DLR版本号冲突检测
    uint32_t sysPropeTotalLen;  // 系统字段的总长度

    uint32_t nodeNum;
    struct DmNodeDesc **nodeDescs;  // node info, support TreeModel
    DmYangInfoDescT *yangInfoDesc;
#if defined(FEATURE_FASTPATH) || defined(TS_MULTI_INST)
    DbOamapT *propPatternRegexMap;  // YANG 字段值校验pattern正则表达式编译后的结果，key是 labelId，nodeId, propId
#endif
};

typedef enum {
    DM_LIST_FIRST,  /**< 在list的头部 */
    DM_LIST_LAST,   /**< 在list的尾部 */
    DM_LIST_STAY,   /**< 位置保持不变，不存在则等价于DM_ARRAY_LAST */
    DM_LIST_BEFORE, /**< 某个位置之前 */
    DM_LIST_AFTER   /**< 某个位置之后 */
} DmListPosE;

typedef struct DmEdgeAddrs {
    uint64_t first;  // vertex对应的首边addr
    uint64_t last;   // vertex对应的尾边addr，当前只有在vertex为list类型使用，可快速获取尾边addr
} DmEdgeAddrsT;

typedef enum VertexState {
    DM_VERTEX_RUNNING,
    DM_VERTEX_DESTROYED,
} DmVertexStateE;

typedef struct DmVertex {
    uint16_t headMagicCode;
    bool hasBitMapPropPartialSet;
    bool isDeltaVertex;   // 标识该vertex是否是用于增量更新的deltaVertex
    DmListPosE position;  // vertex为list类型时，移动时的位置
    DbMemCtxT *memCtx;
    DmVertexDescT *vertexDesc;
    struct DmRecord *record;

    uint8_t *compareKeyBuf;  // get keyBuf from vertexBuf efficiently, malloc memory in advance
                             // length is enough to support all index, avoid malloc mem in getting keybuf
                             // only for DmGetKeyBufFromVertexBuf interface
    uint8_t *vertexKeyBuf;   // only for DmGetKeyBufFromVertex interface
    uint8_t *sysPropeBuf;    // 系统字段对应的buf
    uint8_t *npaBitmap;      // 存储当前一个记录内NPA信息
    struct DmNode **nodes;   // Record node info, support TreeModel
                            // yang句柄为按需创建，初始时nodes指针均为NULL，访问时需要考虑NULL的场景

    uint8_t *vertexSeriBuf;        // malloc memory in advance
    uint32_t recordSeriBufLen;     // 在调用DmVertexGetSeriBufLen时进行记录
    uint32_t edgeLabelNum;         // 根据vertexDesc进行初始化，标识edgeAddrsEntry的实际size，
                                   // 对vertex句柄的操作应该用该值，而非vertexDesc
    DmEdgeAddrsT *edgeAddrsEntry;  // 这里的边addr包含first首边和last尾边

    DmVertexStateE state;
    uint16_t tailMagicCode;
} DmVertexT;

Status DmDeSerialize2ExistsVertex(uint8_t *buf, uint32_t length, DmVertexT *vertex, bool checkFlag);

Status DmVertexSetPropeByName(const char *propeName, DmValueT propeValue, const DmVertexT *vertex);

Status DmSerializeVertex(DmVertexT *vertex, uint8_t **buf, uint32_t *length);

#ifdef __cplusplus
}
#endif  // __cplusplus
#endif  // __DM_INDEX_PLUGIN_H__
