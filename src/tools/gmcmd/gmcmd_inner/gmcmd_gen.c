/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of generate test data
 * Author: maokuancheng
 * Create: 2022/3/10
 */

// "gen object <tablename> <schema version> <data_number> <output_file_path> <cfg_json_path>"

#include "sys/stat.h"     // S_ISDIR
#include "adpt_string.h"  //
#include "db_file.h"      // DbGetRealPath 等文件操作
#include "clt_stmt.h"
#include "dm_data_define.h"  // 用来获取最大的schema size(for string and byte)
#include "dm_data_print.h"   // DmVertexCreateJsonStr
#include "dm_meta_prop_strudefs.h"
#include "gmc_graph.h"
#include "gmc_internal.h"
#include "gmcmd_log.h"
#include "gmcmd_gen_utils.h"
#include "gmcmd_gen.h"

const char *g_Divide = " \n\t";

// ======================= parse cfg json =============================
Status GenLocalCheckJson(DbJsonT *cfgJson, const char *cfgJsonStr)
{
    if (cfgJson == NULL) {
        CMD_ERROR("Unsuccessful to load json for object \"%s\"", cfgJsonStr);
        return STATUS_INVALID_ARGUMENT;
    }

    if (!DbJsonIsObject(cfgJson)) {
        CMD_ERROR("cfg json is not object, str=%s", cfgJsonStr);  // LCOV_EXCL_LINE
        return STATUS_INVALID_ARGUMENT;
    }
    return GMERR_OK;
}

Status GenLocalParseShortInt(DbJsonT *cfgJson, const char *cfgJsonStr, GenDataCfgT *cfg)
{
    DB_POINTER(cfg);
    Status ret = GenParseInt8Cfg(cfgJson, &cfg->int8Properties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate int8 cfg json unsuccessfully, str=%s", cfgJsonStr);
        return ret;
    }
    ret = GenParseUint8Cfg(cfgJson, &cfg->uint8Properties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate uint8 cfg json unsuccessfully, str=%s", cfgJsonStr);
        return ret;
    }
    ret = GenParseInt16Cfg(cfgJson, &cfg->int16Properties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate int16 cfg json unsuccessfully, str=%s", cfgJsonStr);
        return ret;
    }
    ret = GenParseUint16Cfg(cfgJson, &cfg->uint16Properties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate uint16 cfg json unsuccessfully, str=%s", cfgJsonStr);
        return ret;
    }
    return GMERR_OK;
}

Status GenLocalParseLongInt(DbJsonT *cfgJson, const char *cfgJsonStr, GenDataCfgT *cfg)
{
    DB_POINTER(cfg);
    Status ret = GenParseInt32Cfg(cfgJson, &cfg->int32Properties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate int32 cfg json unsuccessfully, str=%s", cfgJsonStr);
        return -1;
    }
    ret = GenParseUint32Cfg(cfgJson, &cfg->uint32Properties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate uint32 cfg json unsuccessfully, str=%s", cfgJsonStr);
        return ret;
    }
    ret = GenParseInt64Cfg(cfgJson, &cfg->int64Properties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate int64 cfg json unsuccessfully, str=%s", cfgJsonStr);  // LCOV_EXCL_LINE
        return ret;
    }
    ret = GenParseUint64Cfg(cfgJson, &cfg->uint64Properties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate uint64 cfg json unsuccessfully, str=%s", cfgJsonStr);  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

Status GenLocalParseStringFloat(DbJsonT *cfgJson, const char *cfgJsonStr, GenDataCfgT *cfg)
{
    DB_POINTER(cfg);
    Status ret = GenParseStrCfg(cfgJson, &cfg->strProperties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate string cfg json unsuccessfully, str=%s", cfgJsonStr);
        return ret;
    }
    ret = GenParseFloatCfg(cfgJson, &cfg->floatProperties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate float cfg json unsuccessfully, str=%s", cfgJsonStr);  // LCOV_EXCL_LINE
        return ret;
    }
    ret = GenParseDoubleCfg(&cfg->floatProperties, &cfg->doubleProperties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate double cfg json unsuccessfully.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = GenParseTimeCfg(cfgJson, &cfg->timeProperties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate time cfg json unsuccessfully, str=%s", cfgJsonStr);  // LCOV_EXCL_LINE
        return ret;
    }

    return GMERR_OK;
}

Status GenlocalParseBits(DbJsonT *cfgJson, const char *cfgJsonStr, GenDataCfgT *cfg)
{
    DB_POINTER(cfg);
    Status ret = GenParseByteCfg(cfgJson, &cfg->byteProperties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate Byte cfg json unsuccessfully, str=%s", cfgJsonStr);
        return ret;
    }
    ret = GenParseBooleanCfg(cfgJson, &cfg->booleanProperties);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse generate Boolean cfg json unsuccessfully, str=%s", cfgJsonStr);  // LCOV_EXCL_LINE
        return ret;
    }
    return GenParseBitmapCfg(cfgJson, &cfg->bitmapProperties);
}

Status GenParseCfgJson(const char *cfgJsonStr, GenDataCfgT *cfg)
{
    DbJsonT *cfgJson;
    Status ret;
    DB_POINTER2(cfgJsonStr, cfg);

    if (DbStrCmp(cfgJsonStr, "{}", true) == 0) {
        CMD_INFO("use default configuration to generate data.");
        cfgJson = DbJsonLoads(cfgJsonStr, 0);
    } else if (cfgJsonStr == NULL) {
        CMD_ERROR("no default \"json\" and no json path.");  // LCOV_EXCL_LINE
        return STATUS_INVALID_ARGUMENT;
    } else {
        cfgJson = DbJsonLoadsFile(cfgJsonStr, 0);
        ret = GenLocalCheckJson(cfgJson, cfgJsonStr);
        if (ret != GMERR_OK) {
            CMD_ERROR("Check json unsuccessfully");
            DbJsonDelete(cfgJson);
            return ret;
        }
    }

    ret = GenLocalParseShortInt(cfgJson, cfgJsonStr, cfg);
    if (ret != GMERR_OK) {
        CMD_ERROR("ParseShortInt unsuccessfully");
        DbJsonDelete(cfgJson);
        return ret;
    }
    ret = GenLocalParseLongInt(cfgJson, cfgJsonStr, cfg);
    if (ret != GMERR_OK) {
        CMD_ERROR("ParseLongInt unsuccessful");
        DbJsonDelete(cfgJson);
        return ret;
    }
    ret = GenLocalParseStringFloat(cfgJson, cfgJsonStr, cfg);
    if (ret != GMERR_OK) {
        CMD_ERROR("ParseStringFloat unsuccessful");
        DbJsonDelete(cfgJson);
        return ret;
    }
    ret = GenlocalParseBits(cfgJson, cfgJsonStr, cfg);
    if (ret != GMERR_OK) {
        CMD_ERROR("ParseBits unsuccessful");
        DbJsonDelete(cfgJson);
        return ret;
    }
    DbJsonDelete(cfgJson);

    return GMERR_OK;
}

static Status ParseGenTableCmd(char *cmd, char **leftPtr, char *tableName, uint32_t tableNameLen)
{
    char *token = NULL;
    uint32_t tokenLen = 0;
    // object
    token = strtok_r(cmd, g_Divide, &(*leftPtr));
    if (token == NULL) {
        CMD_ERROR("Keyword \"object\" is expected.");  // LCOV_EXCL_LINE
        return STATUS_INVALID_ARGUMENT;
    }
    if (DbStrCmp(token, "object", true) != 0) {
        CMD_ERROR("Keyword \"object\" is expected, Unknown command:%s", token);  // LCOV_EXCL_LINE
        return STATUS_INVALID_ARGUMENT;
    }

    // table name
    token = strtok_r(NULL, g_Divide, &(*leftPtr));
    if (token == NULL) {
        CMD_ERROR("Keyword \"tablename\" is expected.");  // LCOV_EXCL_LINE
        return STATUS_INVALID_ARGUMENT;
    }
    // tokenLen允许最大值为MAX_TABLE_NAME_LEN,包含\0
    tokenLen = (uint32_t)strlen(token);
    if (tokenLen >= MAX_TABLE_NAME_LEN) {
        CMD_ERROR("the length:%" PRIu32 " of tableName:%s is threshold(%" PRId32 ") exceeded.", tokenLen + 1, token,
            MAX_TABLE_NAME_LEN);  // LCOV_EXCL_LINE
        return STATUS_INVALID_ARGUMENT;
    }
    Status ret = strncpy_s(tableName, tableNameLen, token, tokenLen);
    if (ret != GMERR_OK) {
        CMD_ERROR("strncpy_s tableName:%s unsuccessful.", token);  // LCOV_EXCL_LINE
        return STATUS_INVALID_ARGUMENT;
    }
    return GMERR_OK;
}

static Status ParseGenSchemaCmd(char **leftPtr, uint32_t *schemaVersion, uint32_t *dataNum)
{
    char *strEnd = NULL;
    errno = 0;
    char *token = NULL;
    // schema version 这里暂时保留，默认输入“newest”
    token = strtok_r(NULL, g_Divide, &(*leftPtr));
    if (token == NULL) {
        CMD_ERROR("Keyword \"schema version\" is expected.");  // LCOV_EXCL_LINE
        return STATUS_INVALID_ARGUMENT;
    }

    if (DbStrCmp(token, GEN_NEWEST_SCHEMA_DATA_STR, true) == 0) {
        *schemaVersion = NEWEST_SCHEMA_VERSION;
    } else {
        int64_t version = (int64_t)strtoll(token, &strEnd, 10);
        if (version < 0 || (version == 0 && DbStrCmp("0", token, false) != 0) || version > NEWEST_SCHEMA_VERSION) {
            CMD_ERROR("invalid schema version \"%s\", schema version value should be [0,%u]", token,
                NEWEST_SCHEMA_VERSION);  // LCOV_EXCL_LINE
            return STATUS_INVALID_ARGUMENT;
        }
        if (errno != 0 || strEnd == token || (*strEnd) != '\0') {
            return GMERR_DATA_EXCEPTION;
        }
        *schemaVersion = (uint32_t)version;
    }

    // data number
    token = strtok_r(NULL, g_Divide, &(*leftPtr));
    if (token == NULL) {
        CMD_ERROR("Keyword \"data num\" is expected.");  // LCOV_EXCL_LINE
        return STATUS_INVALID_ARGUMENT;
    }

    int32_t genDataNum = (int32_t)strtol(token, &strEnd, 10);
    if (genDataNum <= 0) {
        CMD_ERROR("gen data num is: %" PRId32 ", invalid data num \"%s\", data num value should be (1, %" PRId32 ")",
            genDataNum, token, DB_MAX_INT32);  // LCOV_EXCL_LINE
        return STATUS_INVALID_ARGUMENT;
    }
    if (errno != 0 || strEnd == token || (*strEnd) != '\0') {
        return GMERR_DATA_EXCEPTION;
    }
    *dataNum = (uint32_t)genDataNum;
    return GMERR_OK;
}

Status GenParseGenCmd(GmCmdParamT *cmdParamStruct, char *cmd, GenDataCfgT *cfg)
{
    DB_POINTER3(cmd, cmdParamStruct, cfg);
    char *leftPtr = NULL;
    char *token = NULL;

    Status ret = ParseGenTableCmd(cmd, &leftPtr, cmdParamStruct->tableName, cmdParamStruct->tableNameLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = ParseGenSchemaCmd(&leftPtr, &(cmdParamStruct->schemaVersion), &(cmdParamStruct->genDataNum));
    if (ret != GMERR_OK) {
        return ret;
    }
    // file path
    token = strtok_r(NULL, g_Divide, &leftPtr);
    if (token == NULL) {
        CMD_ERROR("Keyword \"file path\" is expected.");  // LCOV_EXCL_LINE
        return STATUS_INVALID_ARGUMENT;
    }
    ret = strcpy_s(cmdParamStruct->outputFilePath, cmdParamStruct->outputFilePathLen, token);
    if (ret != GMERR_OK) {
        CMD_ERROR("strcpy_s outputFilePath:%s unsuccessful.", token);  // LCOV_EXCL_LINE
        return STATUS_INVALID_ARGUMENT;
    }

    ret = GenParseCfgJson(leftPtr, cfg);
    if (ret != GMERR_OK) {
        CMD_ERROR("parse cfg json unsuccessfully, left_ptr = %s.", leftPtr);
        return STATUS_INVALID_ARGUMENT;
    }
    return GMERR_OK;
}

void GenInitialForCfg(GenDataCfgT *cfg)
{
    GenInitBitfield8Cfg(&cfg->bitfield8Properties);
    GenInitBitfield16Cfg(&cfg->bitfield16Properties);
    GenInitBitfield32Cfg(&cfg->bitfield32Properties);
    GenInitBitfield64Cfg(&cfg->bitfield64Properties);
    GenInitCharCfg(&cfg->charProperties);
    GenInitUcharCfg(&cfg->ucharProperties);

    cfg->schemaverify->minMaxBytesSize = DB_INVALID_ID32;
    cfg->schemaverify->minMaxStringSize = DB_INVALID_ID32;
}

// ====================== generate data ============================
void FreeDmValue(const DmValueT *value, DbMemCtxT *ctx)
{
    if (DM_TYPE_NEED_MALLOC(value->type)) {
        void *bytes = (void *)value->value.strAddr;
        DB_FREE(bytes);
    }
}

Status GenGenerateNodeObject(DmNodeT *node, DmSchemaT *nodeSchema, GenDataCfgT *cfg);

Status GenGenerateField(const DmPropertySchemaT *schema, DmValueT *value, GenDataCfgT *cfg)
{
    Status ret = GMERR_OK;
    uint32_t size;

    value->type = schema->dataType;
    switch (schema->dataType) {
        case DB_DATATYPE_CHAR:
            ret = GenGenerateInt8(&cfg->charProperties, (int8_t *)&value->value.charValue);
            break;
        case DB_DATATYPE_INT8:
            ret = GenGenerateInt8(&cfg->int8Properties, &value->value.byteValue);
            break;
        case DB_DATATYPE_UCHAR:
            ret = GenGenerateUint8(&cfg->ucharProperties, &value->value.ubyteValue);
            break;
        case DB_DATATYPE_UINT8:
            ret = GenGenerateUint8(&cfg->uint8Properties, &value->value.ubyteValue);
            break;
        case DB_DATATYPE_PARTITION:
            CMD_ERROR("Not support generate data for partition field.");  // LCOV_EXCL_LINE
            ret = STATUS_INVALID_FIELD;
            break;
        case DB_DATATYPE_INT16:
            ret = GenGenerateInt16(&cfg->int16Properties, &value->value.shortValue);
            break;
        case DB_DATATYPE_UINT16:
            ret = GenGenerateUint16(&cfg->uint16Properties, &value->value.ushortValue);
            break;
        case DB_DATATYPE_INT32:
            ret = GenGenerateInt32(&cfg->int32Properties, &value->value.intValue);
            break;
        case DB_DATATYPE_UINT32:
            ret = GenGenerateUint32(&cfg->uint32Properties, &value->value.uintValue);
            break;
        case DB_DATATYPE_INT64:
            ret = GenGenerateInt64(&cfg->int64Properties, &value->value.longValue);
            break;
        case DB_DATATYPE_UINT64:
            ret = GenGenerateUint64(&cfg->uint64Properties, &value->value.ulongValue);
            break;
        case DB_DATATYPE_RESOURCE:
            CMD_ERROR("Not support generate data for resource field.");  // LCOV_EXCL_LINE
            ret = STATUS_INVALID_FIELD;
            break;
        case DB_DATATYPE_TIME:
            ret = GenGenerateTime(&cfg->timeProperties, &value->value.longValue);
            break;
        case DB_DATATYPE_BOOL:
            ret = GenGenerateBoolean(&cfg->booleanProperties, &value->value.boolValue);
            break;
        case DB_DATATYPE_FLOAT:
            ret = GenGenerateFloat(&cfg->floatProperties, &value->value.floatValue);
            break;
        case DB_DATATYPE_DOUBLE:
            ret = GenGenerateDouble(&cfg->doubleProperties, &value->value.doubleValue);
            break;
        case DB_DATATYPE_STRING:
            // schema中str的size默认是带了\0的，而GenGenerateString函数接收的size默认不带\0
            ret = GenGenerateString(&cfg->strProperties, value, schema->size - 1);
            break;
        case DB_DATATYPE_BYTES:
            ret = GenGenerateBytes(&cfg->byteProperties, value, schema->size);
            break;
        case DB_DATATYPE_FIXED:
            ret = GenGenerateBytes(&cfg->byteProperties, value, schema->size);
            break;
        case DB_DATATYPE_BITMAP:
            ret = GenGenerateBitmap(&cfg->bitmapProperties, value, schema->size);
            break;
        case DB_DATATYPE_BITFIELD8:
            size = schema->size;
            cfg->bitfield8Properties.maxValue = (uint8_t)((1 << size) - 1);
            ret = GenGenerateUint8(&cfg->bitfield8Properties, &value->value.ubyteValue);
            break;
        case DB_DATATYPE_BITFIELD16:
            size = schema->size;
            cfg->bitfield16Properties.maxValue = (uint16_t)((1 << size) - 1);
            ret = GenGenerateUint16(&cfg->bitfield16Properties, &value->value.ushortValue);
            break;
        case DB_DATATYPE_BITFIELD32:
            size = schema->size;
            cfg->bitfield32Properties.maxValue = (uint32_t)((1 << size) - 1);
            ret = GenGenerateUint32(&cfg->bitfield32Properties, &value->value.uintValue);
            break;
        case DB_DATATYPE_BITFIELD64:
            size = schema->size;
            cfg->bitfield64Properties.maxValue = (uint64_t)((1 << size) - 1);
            ret = GenGenerateUint64(&cfg->bitfield64Properties, &value->value.ulongValue);
            break;
        default:
            CMD_ERROR("unknown value type %" PRId32 ".", schema->dataType);  // LCOV_EXCL_LINE
            ret = STATUS_INVALID_FIELD;
    }
    return ret;
}

Status GenGenerateArrayObject(DmNodeT *node, DmSchemaT *nodeSchema, GenDataCfgT *cfg)
{
    Status ret = GMERR_OK;
    DmNodeTypeE type = DmNodeGetNodeType(node);
    if (type != DM_NODE_ARRAY && type != DM_NODE_VECTOR) {
        return GMERR_DATATYPE_MISMATCH;
    }
    node->isCreated = true;

    uint32_t size = 1;
    uint32_t maxElementNum = DmNodeGetMaxElementNum(node);
    // 如果vector类型schema没有设置size(maxElementNum=1024), 默认设置1，否则使用schema中的size
    size = maxElementNum != 1024 ? maxElementNum : 1;
    for (uint32_t i = 0; i < size && ret == GMERR_OK; i++) {
        if (type == DM_NODE_ARRAY) {
            ret = DmNodeSetElementIndex(node, i);
        } else {
            ret = DmNodeVectorAppend(node);
        }
        // for循环判断了失败，所以只判断ok就行了
        if (ret == GMERR_OK) {
            ret = GenGenerateNodeObject(node, nodeSchema, cfg);
        }
    }
    return ret;
}

Status GenGenerateRecordObject(DmNodeT *node, DmSchemaT *nodeSchema, GenDataCfgT *cfg)
{
    DmNodeTypeE type = DmNodeGetNodeType(node);
    if (type != DM_NODE_RECORD) {
        return GMERR_DATATYPE_MISMATCH;
    }
    node->isCreated = true;
    return GenGenerateNodeObject(node, nodeSchema, cfg);
}

Status GenGenerateChildObject(DmSchemaT *nodeSchema, GenDataCfgT *cfg, DmNodeT *child)
{
    Status ret = GMERR_OK;
    DmNodeSchemaT *schema = NULL;
    ret = DmSchemaGetNodeById(nodeSchema, child->nodeDesc->nodeId, &schema);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmSchemaT *childSchema = MEMBER_PTR(schema, schema);
    DmNodeTypeE type = DmNodeGetNodeType(child);
    if (type == DM_NODE_RECORD) {
        if ((ret = GenGenerateRecordObject(child, childSchema, cfg)) != GMERR_OK) {
            CMD_ERROR("Unsuccessful to generate record object");  // LCOV_EXCL_LINE
            return ret;
        }
    } else if (type == DM_NODE_ARRAY || type == DM_NODE_VECTOR) {
        if ((ret = GenGenerateArrayObject(child, childSchema, cfg)) != GMERR_OK) {
            CMD_ERROR("Unsuccessful to generate array object");  // LCOV_EXCL_LINE
            return ret;
        }
    }
    return GMERR_OK;
}

void GenGenerateOneVertexRange(DmPropertySchemaT *propSchema, GenVerifyPropertiesT *schemaverify)
{
    switch (propSchema->dataType) {
        case DB_DATATYPE_BYTES:
        case DB_DATATYPE_FIXED:
            if (schemaverify->minMaxBytesSize > propSchema->size) {
                schemaverify->minMaxBytesSize = propSchema->size;
            }
            break;
        case DB_DATATYPE_STRING:
            if (schemaverify->minMaxStringSize > propSchema->size) {
                schemaverify->minMaxStringSize = propSchema->size;
            }
            break;
        default:
            break;
    }
}

Status GenGenerateOneNodeNum(DmPropertySchemaT *propSchema, GenDataCfgT *cfg, DmNodeT *node)
{
    DB_ASSERT(propSchema->isValid);
    Status ret = GMERR_OK;
    DmValueT propValue;
    if ((ret = GenGenerateField(propSchema, &propValue, cfg)) != GMERR_OK) {
        CMD_ERROR("Unsuccessful to generate field");  // LCOV_EXCL_LINE
        return ret;
    }

    if ((ret = DmNodeSetPropeById(propSchema->propeId, propValue, node)) != GMERR_OK) {
        // 这里是生成数据成功，但是设置不进去dm。
        CMD_ERROR("Set propValue node unsuccessfully. you should check your cfg. ret is %" PRId32 "\n",
            ret);  // LCOV_EXCL_LINE
        FreeDmValue(&propValue, node->memCtx);
        return ret;
    }
    FreeDmValue(&propValue, node->memCtx);
    return GMERR_OK;
}

Status GenGenerateNodeObject(DmNodeT *node, DmSchemaT *nodeSchema, GenDataCfgT *cfg)
{
    Status ret = GMERR_OK;
    uint32_t propeNum = nodeSchema->propeNum;

    for (uint32_t i = 0; i < propeNum; i++) {
        DmPropertySchemaT *propSchema = NULL;
        ret = DmSchemaGetPropeById(nodeSchema, i, &propSchema);
        if (ret == GMERR_DATA_EXCEPTION) {
            // node 有子节点
            DmNodeT *child = NULL;
            if ((ret = DmNodeGetChildNodeById(node, i, &child)) != GMERR_OK) {
                // 这里如果GetNodeById失败，说明有问题，需要分析
                CMD_ERROR("DmNodeGetChildNodeById unsuccessful. ret is %" PRId32 ".", ret);  // LCOV_EXCL_LINE
                return ret;
            }
            if ((ret = GenGenerateChildObject(nodeSchema, cfg, child)) != GMERR_OK) {
                return ret;
            }
            continue;
        } else if (ret != GMERR_OK) {
            return ret;
        }

        if (!cfg->schemaverify) {
            ret = GenGenerateOneNodeNum(propSchema, cfg, node);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else {
            GenGenerateOneVertexRange(propSchema, cfg->schemaverify);
        }
    }
    return ret;
}

Status GenGenerateOneVertexNum(DmPropertySchemaT *propSchema, GenDataCfgT *cfg, DmVertexT *vertex)
{
    DB_ASSERT(propSchema->isValid);
    Status ret = GMERR_OK;
    DmValueT propValue = {0};
    if ((ret = GenGenerateField(propSchema, &propValue, cfg)) != GMERR_OK) {
        CMD_ERROR("Unsuccessful to generate field");
        return ret;
    }
    if ((ret = DmVertexSetPropeById(propSchema->propeId, propValue, vertex)) != GMERR_OK) {
        // 这里是生成数据成功，但是设置不进去dm
        CMD_ERROR("Set propValue unsuccessfully. you should check your cfg. ret is %" PRId32 "\n", ret);
        // 这里free掉proValue中str类型malloc出来的内存
        FreeDmValue(&propValue, vertex->memCtx);
        return ret;
    }
    FreeDmValue(&propValue, vertex->memCtx);
    return GMERR_OK;
}

// 递归进入树模型，设置值
Status GenGenerateOneVertexNumOrRange(DmVertexT *vertex, DmVertexLabelT *vertexLabel, GenDataCfgT *cfg)
{
    Status ret = GMERR_OK;
    DmSchemaT *vertexSchema = MEMBER_PTR(vertexLabel->metaVertexLabel, schema);
    uint32_t propeNum = vertexSchema->propeNum;  // 一层的record数量

    // 这里第一层for, 遍历schema最外层的数量, 最后一个property是系统自带的，要去掉
    for (uint32_t i = 0; i < propeNum - 1; i++) {
        // 先拿出propSchema
        DmPropertySchemaT *propSchema = NULL;
        ret = DmSchemaGetPropeById(vertexSchema, i, &propSchema);
        // 该propSchema是node
        if (ret == GMERR_DATA_EXCEPTION) {
            DmNodeT *child = NULL;
            if ((ret = DmVertexGetNodeById(vertex, i, &child)) != GMERR_OK) {
                // 这里如果GetNodeById失败，说明有问题，需要分析
                CMD_ERROR("Unsuccessful to GetNodeById. ret is %" PRId32 ".", ret);  // LCOV_EXCL_LINE
                return ret;
            }
            if ((ret = GenGenerateChildObject(vertexSchema, cfg, child)) != GMERR_OK) {
                CMD_ERROR("Unable to generate child node");  // LCOV_EXCL_LINE
                return ret;
            }
            continue;
        } else if (ret != GMERR_OK) {
            return ret;
        }
        if (!cfg->schemaverify) {
            // 该propSchema是data
            ret = GenGenerateOneVertexNum(propSchema, cfg, vertex);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else {
            GenGenerateOneVertexRange(propSchema, cfg->schemaverify);
        }
    }
    return ret;
}

// 设置好vertex中各个字段的值并写入到文件fd
Status GenGenerateOneVertex(
    DmVertexT *vertex, DmVertexLabelT *vertexLabel, int32_t fd, const char *tableName, GenDataCfgT *cfg)
{
    DB_POINTER(vertex);
    cfg->schemaverify = NULL;
    Status ret = GenGenerateOneVertexNumOrRange(vertex, vertexLabel, cfg);
    if (ret != GMERR_OK) {
        CMD_ERROR("GenerateOneVertex unsuccessful");
        return ret;
    }

    char *objText = NULL;
    uint32_t flag = 0;
    ret = DmVertexCreateJsonStr(vertex->memCtx, vertex, flag, NULL, &objText, true);
    if (ret != GMERR_OK) {
        CMD_ERROR("Unsuccessful to dump vertex to json");  // LCOV_EXCL_LINE
        return ret;
    }

    ret = DbWriteFile(fd, objText, strlen(objText));
    if (ret != GMERR_OK) {
        CMD_ERROR("Unsuccessful to write one vertex to fd.");  // LCOV_EXCL_LINE
        return ret;
    }

    ret = DmResetVertex(vertex);
    if (ret != GMERR_OK) {
        CMD_ERROR("Reset vertex unsuccessfully.ret is %" PRId32 ".", ret);  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}

// 设置好vertex中各个字段的值,并循环dataNum次，然后写入文件
void GenGenerateDataFromFile(
    DmVertexT *vertex, DmVertexLabelT *vertexLabel, GmCmdParamT *cmdParamStruct, GenDataCfgT *cfg, const char *fileName)
{
    int32_t fd = DB_INVALID_FD;
    Status ret = DbOpenFile(fileName, CREATE_FILE | WRITE_ONLY, PERM_USER, &fd);
    if (ret != GMERR_OK) {
        CMD_ERROR("Open data file %s unsuccessfully, ret is %" PRId32 ", os no %" PRId32 ".", fileName, ret,
            (int32_t)errno);  // LCOV_EXCL_LINE
        return;
    }

    char *str = "[";
    ret = DbWriteFile(fd, str, strlen(str));
    if (ret != GMERR_OK) {
        CMD_ERROR("gen data in file %s unsuccessfully, ret is %" PRId32 ", os no %" PRId32 ".", fileName, ret,
            (int32_t)errno);  // LCOV_EXCL_LINE
        DbCloseFile(fd);
        return;
    }

    for (uint32_t i = 0; i < cmdParamStruct->genDataNum; i++) {
        if (i != 0) {
            char *tmp = ",\n";
            ret = DbWriteFile(fd, tmp, strlen(tmp));
        } else {
            char *tmp = "\n";
            ret = DbWriteFile(fd, tmp, strlen(tmp));
        }
        if (ret != GMERR_OK) {
            CMD_ERROR("gen data in file %s unsuccessfully, ret is %" PRId32 ", os no %" PRId32 ".", fileName, ret,
                (int32_t)errno);  // LCOV_EXCL_LINE
            DbCloseFile(fd);
            return;
        }
        ret = GenGenerateOneVertex(vertex, vertexLabel, fd, cmdParamStruct->tableName, cfg);
        if (ret != GMERR_OK) {
            // 如果GenGenerateOneVertex失败，就写上json的末尾，退出。
            CMD_ERROR("generate one object to data file %s unsuccessfully, succeeded %" PRIu32 "times.", fileName, i);
            char *tmp = "\n]\n";
            (void)DbWriteFile(fd, tmp, strlen(tmp));
            DbCloseFile(fd);
            return;
        }
    }
    char *tmp = "\n]\n";
    ret = DbWriteFile(fd, tmp, strlen(tmp));
    if (ret != GMERR_OK) {
        CMD_ERROR("gen data in file %s unsuccessfully, ret is %" PRId32 ", os no %" PRId32 ".", fileName, ret,
            (int32_t)errno);  // LCOV_EXCL_LINE
        DbCloseFile(fd);
        return;
    }
    DbCloseFile(fd);
    CMD_SUCCESS("write %" PRIu32 " data success.", cmdParamStruct->genDataNum);
}

void GenGenerateData(DmVertexT *vertex, DmVertexLabelT *vertexLabel, GmCmdParamT *cmdParamStruct, GenDataCfgT *cfg)
{
    struct stat pathInfo;
    char fileName[IMP_EXP_MAX_FILE_PATH_LEN] = {0};
    char tempFileName[PATH_MAX] = {0};
    // 判断文件状态
    if (stat(cmdParamStruct->outputFilePath, &pathInfo) != 0) {
        CMD_ERROR("Stat file_path \"%s\" unsuccessful.", cmdParamStruct->outputFilePath);  // LCOV_EXCL_LINE
        return;
    }
    // 判断是否是文件
    if (!S_ISDIR(pathInfo.st_mode)) {
        CMD_ERROR("file_path \"%s\" is not dir.", cmdParamStruct->outputFilePath);  // LCOV_EXCL_LINE
        return;
    }
    Status ret = snprintf_s(fileName, IMP_EXP_MAX_FILE_PATH_LEN, IMP_EXP_MAX_FILE_PATH_LEN - 1, "%s/%s%s",
        cmdParamStruct->outputFilePath, cmdParamStruct->tableName, EXPORT_DATA_FILE_SUFFIX);
    if (ret <= 0) {
        CMD_ERROR("snprintf_s unsuccessful");  // LCOV_EXCL_LINE
        return;
    }
    // 相对路径转绝对路径，如果已经是绝对路径，那tempFileName == fileName
    ret = DbGetRealPath(fileName, tempFileName, PATH_MAX);
    if (ret != GMERR_OK) {
        CMD_ERROR("get realpath unsuccessful");  // LCOV_EXCL_LINE
        return;
    }
    // 如果文件存在，删除
    if (DbFileExist((const char *)tempFileName)) {
        ret = DbRemoveFile((const char *)tempFileName);
        if (ret != GMERR_OK) {
            CMD_ERROR("remove file %s unsuccessful", tempFileName);  // LCOV_EXCL_LINE
            return;
        }
    }
    // 注意，传进去的是fileName,不是tempFileName
    GenGenerateDataFromFile(vertex, vertexLabel, cmdParamStruct, cfg, tempFileName);
}

Status GenGetVertex(
    GmcStmtT *stmt, char *tableName, DmVertexT **vertex, DmVertexLabelT **vertexLabel, uint32_t versionId)
{
    // 确认一下，这里理论上需要1.建连成功。2.创建表成功。
    Status ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tableName, versionId, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        CMD_ERROR("Unsuccessful to PrepareStmtByLabelName. ret = %" PRId32 ".", ret);  // LCOV_EXCL_LINE
        return ret;
    }
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        CMD_ERROR("Unsuccessful to Execute. ret = %" PRId32 ".", ret);  // LCOV_EXCL_LINE
        return ret;
    }
    bool eof;
    ret = GmcFetch(stmt, &eof);
    if (ret != GMERR_OK) {
        CMD_ERROR("Unsuccessful to Fetch. ret == %" PRId32 ".", ret);  // LCOV_EXCL_LINE
        return ret;
    }

    *vertex = CltGetVertexInStmt(stmt);
    if (*vertex == NULL) {
        CMD_ERROR("CltGetVertexInStmt unsuccessful.");  // LCOV_EXCL_LINE
        return STATUS_GET_VERTEX_ERROR;
    }
    *vertexLabel = CltGetCltCataLabelInStmt(stmt)->vertexLabel;
    if (*vertexLabel == NULL) {
        CMD_ERROR("CltGetCltCataLabelInStmt unsuccessful.");  // LCOV_EXCL_LINE
        return STATUS_GET_VERTEX_ERROR;
    }
    return GMERR_OK;
}

Status GenVerifyGenDataCfgInner(
    GenVerifyPropertiesT schemaverify, GenStrPropertiesT *strProperties, GenBytePropertiesT *byteProperties)
{
    if (schemaverify.minMaxBytesSize != DB_INVALID_ID32) {
        if (byteProperties->maxStrlen == GEN_STR_DEFAULT_MAX_LEN) {
            if (byteProperties->minStrlen > schemaverify.minMaxBytesSize) {
                CMD_ERROR("can not generate valid object, schema has a field with max size %u,"
                          " but generate configure min len is %u",
                    schemaverify.minMaxBytesSize, byteProperties->minStrlen);  // LCOV_EXCL_LINE
                return STATUS_INVALID_ARGUMENT;
            }

            byteProperties->maxStrlen = schemaverify.minMaxBytesSize;
        } else {
            if (byteProperties->maxStrlen > schemaverify.minMaxBytesSize) {
                CMD_ERROR("possible to generate invalid object, schema has a field with max size %u,"
                          " but generate configure max len is %u",
                    schemaverify.minMaxBytesSize, byteProperties->maxStrlen);  // LCOV_EXCL_LINE
                return STATUS_INVALID_ARGUMENT;
            }
        }
    }

    if (schemaverify.minMaxStringSize != DB_INVALID_ID32) {
        if (strProperties->maxStrlen == GEN_STR_DEFAULT_MAX_LEN) {
            if (strProperties->minStrlen > schemaverify.minMaxStringSize) {
                CMD_ERROR("can not generate valid object, schema has a field with max size %u,"
                          " but generate configure min len is %u",
                    schemaverify.minMaxStringSize, strProperties->minStrlen);
                return STATUS_INVALID_ARGUMENT;
            }

            strProperties->maxStrlen = schemaverify.minMaxStringSize;
        } else {
            if (strProperties->maxStrlen > schemaverify.minMaxStringSize) {
                CMD_ERROR("possible to generate invalid object, schema has a field with max size %u,"
                          " but generate configure max len is %u",
                    schemaverify.minMaxStringSize, strProperties->maxStrlen);
                return STATUS_INVALID_ARGUMENT;
            }
        }
    }
    return GMERR_OK;
}

Status GenVerifyGenDataCfg(GenDataCfgT *cfg, DmVertexT *vertex, DmVertexLabelT *vertexLabel)
{
    GenStrPropertiesT *strProperties = NULL;
    GenBytePropertiesT *byteProperties = NULL;

    Status ret = GenGenerateOneVertexNumOrRange(vertex, vertexLabel, cfg);
    if (ret != GMERR_OK) {
        return ret;
    }

    strProperties = &cfg->strProperties;
    byteProperties = &cfg->byteProperties;

    return GenVerifyGenDataCfgInner(*cfg->schemaverify, strProperties, byteProperties);
}

void GmCmdParamInit(GmCmdParamT *cmdParamStruct)
{
    cmdParamStruct->tableNameLen = MAX_TABLE_NAME_LEN;
    (void)memset_s(cmdParamStruct->tableName, cmdParamStruct->tableNameLen, 0, cmdParamStruct->tableNameLen);
    cmdParamStruct->outputFilePathLen = IMP_EXP_MAX_FILE_PATH_LEN;
    (void)memset_s(
        cmdParamStruct->outputFilePath, cmdParamStruct->outputFilePathLen, 0, cmdParamStruct->outputFilePathLen);
}

void GmCmdGenExec(GmCmdContextT *cmdContext, char *cmdParam)
{
    GmCmdParamT cmdParamStruct;
    GmCmdParamInit(&cmdParamStruct);

    GenDataCfgT cfg;
    cfg.strProperties.fixValue = NULL;
    GenVerifyPropertiesT schemaverify = {};
    cfg.schemaverify = &schemaverify;

    // 1.parseCmd 完成 tableName, schemaVersion genDataNum, outputFilePath, cfg的解析
    Status ret = GenParseGenCmd(&cmdParamStruct, (char *)cmdParam, &cfg);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    // 2. 初始化剩余的cfg，配置文件不支持的类型也需要给定默认配置
    GenInitialForCfg(&cfg);

    // 3. 初始化随机数种子
    ret = GenInitRandomFd();
    if (ret != GMERR_OK) {
        CMD_ERROR("init random fd unsuccessfully");  // LCOV_EXCL_LINE
        goto EXIT;
    }

    // 4. 获取DmVertex
    DmVertexT *vertex = NULL;
    DmVertexLabelT *vertexLabel = NULL;
    ret = GenGetVertex(cmdContext->stmt, cmdParamStruct.tableName, &vertex, &vertexLabel, cmdParamStruct.schemaVersion);
    if (ret != GMERR_OK) {
        CMD_ERROR("Get vertex from server unsuccessfully, ret is %" PRId32 ".", ret);  // LCOV_EXCL_LINE
        goto EXIT;
    }

    ret = GenVerifyGenDataCfg(&cfg, vertex, vertexLabel);
    if (ret != GMERR_OK) {
        CMD_ERROR("gen verify gen data unsuccessfully, ret is %" PRId32 ".", ret);
        goto EXIT;
    }
    cfg.schemaverify = NULL;
    ret = DmResetVertex(vertex);
    if (ret != GMERR_OK) {
        CMD_ERROR("Reset vertex unsuccessfully, ret is %" PRId32 ".", ret);  // LCOV_EXCL_LINE
        goto EXIT;
    }

    // 5. 主函数流程
    DB_POINTER(vertex);
    GenGenerateData(vertex, vertexLabel, &cmdParamStruct, &cfg);

EXIT:
    if (cfg.strProperties.fixValue != NULL) {
        DB_FREE(cfg.strProperties.fixValue);
        cfg.strProperties.fixValue = NULL;
    }
    // 6. 清理环境
    GenDestroyRandomFd();

    return;
}
