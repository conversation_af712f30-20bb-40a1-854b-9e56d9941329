/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: gmsysview alarm command
 * Author: gaohaiyang
 * Create: 2021-12-21
 */

#ifndef GMSYSVIEW_DISPALY_ALARM_H
#define GMSYSVIEW_DISPALY_ALARM_H

#include "sysview_tool.h"

#ifdef __cplusplus
extern "C" {
#endif

#define NONGLOBAL_ALARM_COUNT_LIMIT 1024
void SysviewInitAlarm(SysviewRulesMgrT *rulesMgr);
Status GetAlarmInfo(const SysviewContextT *ctx);

#ifdef __cplusplus
}
#endif

#endif
