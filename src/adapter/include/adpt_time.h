/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: adpt_time.h
 * Description: header file for gmdb time
 * Author:
 * Create: 2020-7-27
 */

#ifndef ADPT_TIME_H
#define ADPT_TIME_H

#include <sys/time.h>
#include <pthread.h>
#include "adpt_types.h"
#include "adpt_process_id.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define DATE_DELTA 584389

#define DB_DEFAULT_TIME_FORMAT "%Y-%m-%d %H:%M:%S"
#define DB_YEAR_RESET 1900
#define TIME_STR_MAX_SIZE 50
#define DB_CLOCKID_T clockid_t
#define DB_CLOCK_REALTIME CLOCK_REALTIME
#define DB_CLOCK_MONOTONIC CLOCK_MONOTONIC
#define DB_CLOCK_THERAD_CPUTIME_ID CLOCK_THREAD_CPUTIME_ID
#define DB_CLOCK_MONOTONIC_COARSE CLOCK_MONOTONIC_COARSE
// macro start with devil just for devil number
#define DEVIL_TIMEH_RESERVE_COUNT 5
#define DEVIL_TIMELINUX_BASE_YEAR 1900
#define DEVIL_TIMELINUX_MSEC_TIME 1000000
#define DEVIL_TIMELINUX_DAY_TIME 86400000
#define DEVIL_TIMELINUX_TOTAL_SEC_TIME 3600000
#define DEVIL_TIMELINUX_MSEC_HOUR 60000
#define DEVIL_TIMELINUX_SEC_TRANS_MSEC 1000
#define DEVIL_TIMELINUX_COUNT_ROW 2
#define DEVIL_TIMELINUX_COUNT_COL 12

typedef enum {
    DEFAULT_TIME_FORMAT,
    INVALID_TIME_FORMAT,
} TimeStrFormatIdE;

typedef struct CmTimeDesc {
    uint32_t msec;
    uint16_t year;
    uint8_t mon;
    uint8_t day;
    uint8_t hour;
    uint8_t min;
    uint8_t sec;
    char reserve[DEVIL_TIMEH_RESERVE_COUNT];
} CmTimeDescT;

typedef void (*TimerProc)(void *arg);

#define NSECONDS_IN_USECOND ((uint64_t)1000)
#define USECONDS_IN_MSECOND ((uint64_t)1000)
#define MSECONDS_IN_SECOND ((uint64_t)1000)
#define NSECONDS_IN_MSECOND (NSECONDS_IN_USECOND * USECONDS_IN_MSECOND)
#define NSECONDS_IN_SECOND (NSECONDS_IN_MSECOND * MSECONDS_IN_SECOND)
#define USECONDS_IN_SECOND (USECONDS_IN_MSECOND * MSECONDS_IN_SECOND)

#define SECOND_OF_ONE_MINUTE 60
#define USECOND_OF_ONE_MIN (SECOND_OF_ONE_MINUTE * USECONDS_IN_SECOND)
#define SECOND_OF_ONE_DAY 86400
#define SECOND_OF_ONE_HOUR ((uint64_t)3600)
#define MSECOND_OF_ONE_HOUR (SECOND_OF_ONE_HOUR * MSECONDS_IN_SECOND)
#define MSECOND_OF_ONE_DAY (SECOND_OF_ONE_DAY * MSECONDS_IN_SECOND)

ADPTR_EXPORT uint64_t DbGetNsec(void);

ADPTR_EXPORT uint64_t DbGetMsec(void);

inline static double DbNsecToSecs(uint64_t nsec)
{
    return ((double)nsec) / NSECONDS_IN_SECOND;
}

inline static double DbNsecToMsecs(uint64_t nsec)
{
    return ((double)nsec) / (double)NSECONDS_IN_MSECOND;
}

inline static uint64_t DbSecToNsec(uint32_t sec)
{
    return (uint64_t)sec * (uint64_t)NSECONDS_IN_SECOND;
}
/*****************************************************************************
Description  : similar of gettimeofday on Linux
Input        : clk_id:
Input        : tp:
Output       : None
Return Value : return DB_SUCCESS when success.
History:
1.Author       :
Modification : Create function
*****************************************************************************/
#define DB_GET_TIME_OF_DAY gettimeofday

/*****************************************************************************
Description  : similar of localtime_r on Linux
Input        : clk_id:
Input        : tp:
Output       : None
Return Value : return result when success, otherwise NULL.
History:
1.Author       :
Modification : Create function
*****************************************************************************/
#define DB_LOCAL_TIME_R localtime_r

ADPTR_EXPORT Status DbStrToTime(const char *time, const char *format, struct tm *timeptr);
ADPTR_EXPORT Status DbTimeToStr(const CmTimeDescT *desc, char *timeStr, uint16_t maxsize, TimeStrFormatIdE timeFormat);

ADPTR_EXPORT uint32_t DbNowSec(void);
ADPTR_EXPORT int64_t DbNowMilliSec(void);
ADPTR_EXPORT void DbDecodeTimeDesc(double dayTime, CmTimeDescT *desc);
ADPTR_EXPORT double DbEncodeTimeDesc(CmTimeDescT *desc);
ADPTR_EXPORT void DbConvertTimestampToDesc(int64_t time, CmTimeDescT *desc);
ADPTR_EXPORT double DbEncodeTimestamp(int64_t time);
ADPTR_EXPORT uint64_t DbGettimeMonotonicUsec(void);
ADPTR_EXPORT uint64_t DbGettimeMonotonicMsec(void);
ADPTR_EXPORT uint64_t DbGetCurrentTimestamp(void);
ADPTR_EXPORT Status DbStrToTimestamp(const char *timeStr, const char *format, int64_t *timestamp);

/**
 * @brief 把线程挂起一段时间
 * @param timeUs : 入参，时间大小为微秒（千分之一毫秒）
 * @return 返回void类型
 */
ADPTR_EXPORT void DbUsleep(uint32_t timeUs);

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
ADPTR_EXPORT uint32_t DbGetDaysByYearAndMonth(uint32_t year, uint32_t month);
#endif

// 以下为目前暂无调用链的函数。如需要使用以下函数，将其定义(.c文件)与声明移出NDEBUG宏隔离范围
#ifndef NDEBUG
ADPTR_EXPORT uint64_t DbGetSec(void);
ADPTR_EXPORT uint64_t DbNowUsec(void);
ADPTR_EXPORT void DbDecodeTime(double dayTime, CmTimeDescT *desc);
#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // ADPT_TIME_H
