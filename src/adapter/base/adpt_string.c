/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: adpt_string.c
 * Description: Implement of string-related functions
 * Author:
 * Create: 2020-7-27
 */

#include "adpt_string.h"
#include <stdlib.h>
#include <ctype.h>
#include "adpt_types.h"
#include "adpt_float16.h"

static int DbToLower(int c)
{
    if (c < 'A' || c > 'Z') {
        return c;
    }
    return c + ('a' - 'A');
}

int32_t DbStrCmp(const char *s1, const char *s2, bool caseIns)
{
    DB_POINTER2(s1, s2);
    int ret = caseIns ? strcasecmp(s1, s2) : strcmp(s1, s2);
    if (ret > 0) {
        return 1;
    } else if (ret < 0) {
        return -1;
    }
    return 0;
}

inline int32_t DbStrNCmp(const char *s1, const char *s2, uint32_t len, bool caseIns)
{
    DB_POINTER2(s1, s2);
    int ret = caseIns ? strncasecmp(s1, s2, len) : strncmp(s1, s2, len);
    if (ret > 0) {
        return 1;
    } else if (ret < 0) {
        return -1;
    }
    return 0;
}

Status DbStrToInt32(const char *str, int32_t *val)
{
    DB_POINTER2(str, val);

    int64_t int64Value = 0;
    Status ret = DbStrToInt64(str, &int64Value);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (int64Value > DB_MAX_INT32 || int64Value < DB_MIN_INT32) {
        return GMERR_DATATYPE_MISMATCH;
    }

    *val = (int32_t)int64Value;
    return GMERR_OK;
}

Status DbStrToUint32(const char *str, uint32_t *val)
{
    DB_POINTER2(str, val);

    int64_t int64Value = 0;
    Status ret = DbStrToInt64(str, &int64Value);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (int64Value > DB_MAX_UINT32 || int64Value < 0) {
        return GMERR_DATATYPE_MISMATCH;
    }

    *val = (uint32_t)int64Value;
    return GMERR_OK;
}

Status DbStrToInt64(const char *str, int64_t *val)
{
    DB_POINTER2(str, val);

    char *strEnd = NULL;
    errno = 0;  // clear errno before convert
    const uint32_t base = 10;
    int64_t value = strtoll(str, &strEnd, (int)base);

    if ((errno != 0) ||      // conversion error
        (strEnd == str) ||   // no valid character interpreted
        ((*strEnd) != '\0')  // not null terminated after last valid character
    ) {
        return GMERR_DATA_EXCEPTION;
    }

    *val = value;
    return GMERR_OK;
}

void DbStrToLower(char *str)
{
    DB_POINTER(str);
    for (char *p = str;; ++p) {
        int c = *p;
        if (c == '\0') {
            break;
        }
        // gcc is able to inline DbToLower and optimize this judge
        // so this is faster than *p = DbToLower(*p)
        int d = DbToLower(c);
        if (d != c) {
            *p = (char)d;
        }
    }
}

bool DbCheckDigit(const char *str)
{
    DB_POINTER(str);
    const char *p = str;

    // use int instead of char to avoid bit masking in generated ARM assembly
    int c = *p++;

    if (c == '-') {
        c = *p++;
    }

    do {
        // avoid using isdigit() because it can be affected by locale
        if (c < '0' || c > '9') {
            return false;
        }
        c = *p++;
    } while (c != '\0');

    return true;
}

/* v1 so 小型化 隔离未使用的函数 */
#ifndef FEATURE_SIMPLEREL
bool DbStrStartWith(const char *str, const char *prefix)
{
    // Obtains the length of a prefix string.
    size_t prefixLen = strlen(prefix);

    // Use strncmp to compare the beginning of str with the prefix to obtain the length of the prefix string.
    return strncmp(str, prefix, prefixLen) == 0;
}

bool DbStrEndsWith(const char *str, const char *suffix)
{
    // Obtains the length of the main string and the suffix string.
    size_t strLen = strlen(str);
    size_t suffixLen = strlen(suffix);
    // If the suffix length is greater than the main string length, it is not possible to end with that suffix.
    if (suffixLen > strLen) {
        return false;
    }

    // Use strncmp to compare the end part of str with suffix.
    return strncmp(str + strLen - suffixLen, suffix, suffixLen) == 0;
}

// 校验是否是一个有效的十进制数
bool IsValidNumber(const char *string)
{
    if (string == NULL) {
        return false;
    }
    uint32_t stringLen = (uint32_t)strlen(string);
    if (stringLen < 1) {
        return false;
    }
    const char *strAddr = string;
    if (strAddr[0] == '-') {
        strAddr++;
        stringLen--;
    }
    if (strAddr[0] == '0') {  // 不能是03，可以是0.3开头
        if (strAddr[1] != '\0' && strAddr[1] != '.') {
            return false;
        }
    }
    if (!isdigit(strAddr[0])) {
        return false;
    }
    uint32_t pointCnt = 0;
    for (uint32_t i = 0; i < stringLen; i++) {
        if (isdigit(strAddr[i])) {
            continue;
        }
        if (strAddr[i] == '.') {
            if ((pointCnt != 0) || (i == stringLen - 1)) {  // 防止小数点出现在最后一位
                return false;
            } else {
                pointCnt++;
                continue;
            }
        }
        return false;
    }
    return true;
}

static int DbToUpper(int c)
{
    if (c < 'a' || c > 'z') {
        return c;
    }
    return c + ('A' - 'a');
}

void DbStrToUpper(char *str)
{
    DB_POINTER(str);
    for (char *p = str;; ++p) {
        int c = *p;
        if (c == '\0') {
            break;
        }
        int d = DbToUpper(c);
        if (d != c) {
            *p = (char)d;
        }
    }
}

Status DbStrToUint64(const char *str, uint64_t *val)
{
    DB_POINTER2(str, val);
    if (!IsNumChar(str[0])) {
        return GMERR_DATA_EXCEPTION;
    }
    char *strEnd = NULL;
    errno = 0;  // clear errno before convert
    const int32_t base = 0;
    uint64_t value = strtoull(str, &strEnd, base);

    if ((errno != 0) ||      // conversion error
        (strEnd == str) ||   // no valid character interpreted
        ((*strEnd) != '\0')  // not null terminated after last valid character
    ) {
        return GMERR_DATA_EXCEPTION;
    }

    *val = value;
    return GMERR_OK;
}

Status DbStrToInt16(const char *str, int16_t *val)
{
    DB_POINTER2(str, val);

    int64_t int64Value = 0;
    Status ret = DbStrToInt64(str, &int64Value);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (int64Value > DB_MAX_INT16 || int64Value < DB_MIN_INT16) {
        return GMERR_DATATYPE_MISMATCH;
    }

    *val = (int16_t)int64Value;
    return GMERR_OK;
}

Status DbStrToUint16(const char *str, uint16_t *val)
{
    DB_POINTER2(str, val);

    int64_t int64Value = 0;
    Status ret = DbStrToInt64(str, &int64Value);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (int64Value > DB_MAX_UINT16 || int64Value < 0) {
        return GMERR_DATATYPE_MISMATCH;
    }

    *val = (uint16_t)int64Value;
    return GMERR_OK;
}

Status DbStrToInt8(const char *str, int8_t *val)
{
    DB_POINTER2(str, val);

    int64_t int64Value = 0;
    Status ret = DbStrToInt64(str, &int64Value);
    if (ret != GMERR_OK) {
        return ret;
    }

    if ((int64Value > DB_MAX_INT8) || (int64Value < DB_MIN_INT8)) {
        return GMERR_DATATYPE_MISMATCH;
    }

    *val = (int8_t)int64Value;
    return GMERR_OK;
}

Status DbStrToUint8(const char *str, uint8_t *val)
{
    DB_POINTER2(str, val);

    int64_t int64Value = 0;
    Status ret = DbStrToInt64(str, &int64Value);
    if (ret != GMERR_OK) {
        return ret;
    }

    if ((int64Value > DB_MAX_UINT8) || (int64Value < 0)) {
        return GMERR_DATATYPE_MISMATCH;
    }

    *val = (uint8_t)int64Value;
    return GMERR_OK;
}

Status DbStrToDouble(const char *str, double *val)
{
    DB_POINTER2(str, val);

    char *strEnd = NULL;
    errno = 0;  // clear errno before convert
    double value = strtod(str, &strEnd);

    if ((errno != 0) ||      // conversion error
        (strEnd == str) ||   // no valid character interpreted
        ((*strEnd) != '\0')  // not null terminated after last valid character
    ) {
        return GMERR_DATA_EXCEPTION;
    }

    *val = value;
    return GMERR_OK;
}

Status DbStrToFloat(const char *str, float *val)
{
    DB_POINTER2(str, val);

    char *strEnd = NULL;
    errno = 0;  // clear errno before convert
    float value = strtof(str, &strEnd);

    if ((errno != 0) ||      // conversion error
        (strEnd == str) ||   // no valid character interpreted
        ((*strEnd) != '\0')  // not null terminated after last valid character
    ) {
        return GMERR_DATA_EXCEPTION;
    }

    *val = value;
    return GMERR_OK;
}

Status DbStrToFloat16(const char *str, float16 *val)
{
    DB_POINTER2(str, val);

    char *strEnd = NULL;
    errno = 0;  // clear errno before convert
    float value = strtof(str, &strEnd);

    if ((errno != 0) ||      // conversion error
        (strEnd == str) ||   // no valid character interpreted
        ((*strEnd) != '\0')  // not null terminated after last valid character
    ) {
        return GMERR_DATA_EXCEPTION;
    }

    *val = DbFloatToFloat16(value);
    return GMERR_OK;
}

Status DbStrToBool(const char *str, bool *val)
{
    DB_POINTER2(str, val);

    if (DbStrCmp(str, "true", false) == 0) {
        *val = true;
        return GMERR_OK;
    }

    if (DbStrCmp(str, "false", false) == 0) {
        *val = false;
        return GMERR_OK;
    }

    return GMERR_DATA_EXCEPTION;
}

Status DbStrToEmpty(const char *str, uint8_t *val)
{
    DB_POINTER2(str, val);

    if (DbStrCmp(str, "", false) == 0) {
        *val = '\0';
        return GMERR_OK;
    }
    return GMERR_DATA_EXCEPTION;
}

Status DbStrToChar(const char *str, char *val)
{
    DB_POINTER2(str, val);

    if (((*val = *str) != '\0') &&  // not empty string
        (*(str + 1) == '\0')        // strlen == 1
    ) {
        return GMERR_OK;
    }

    return GMERR_DATA_EXCEPTION;
}

uint32_t DbStrSplit(char *input, const char *delim, uint32_t subStrNum, char *result[])
{
    DB_POINTER3(input, delim, result);
    uint32_t count = 0;
    char *context = NULL;

    for (char *token = strtok_s(input, delim, &context); token != NULL; token = strtok_s(NULL, delim, &context)) {
        // more than subStrNum exists
        if (count == subStrNum) {
            return DB_MAX_UINT32;
        }
        result[count++] = token;
    }

    return count;
}

inline static uint32_t DbHexToValue(char ch)
{
    // returns UINT32_MAX for invalid characters
    static const uint8_t table[256] = {
        ['0'] = 0x0 + 1,
        ['1'] = 0x1 + 1,
        ['2'] = 0x2 + 1,
        ['3'] = 0x3 + 1,
        ['4'] = 0x4 + 1,
        ['5'] = 0x5 + 1,
        ['6'] = 0x6 + 1,
        ['7'] = 0x7 + 1,
        ['8'] = 0x8 + 1,
        ['9'] = 0x9 + 1,
        ['a'] = 0xa + 1,
        ['b'] = 0xb + 1,
        ['c'] = 0xc + 1,
        ['d'] = 0xd + 1,
        ['e'] = 0xe + 1,
        ['f'] = 0xf + 1,
        ['A'] = 0xA + 1,
        ['B'] = 0xB + 1,
        ['C'] = 0xC + 1,
        ['D'] = 0xD + 1,
        ['E'] = 0xE + 1,
        ['F'] = 0xF + 1,
    };
    uint8_t index = (uint8_t)ch;
    uint32_t value = table[index];
    return value - 1;
}

void DbStrTransformHex2Uint64(const char *hex, uint64_t *iret, uint32_t len)
{
    DB_POINTER2(hex, iret);
    uint64_t value = 0;
    const char *str = hex;

    for (uint32_t count = len; count != 0; --count) {
        uint32_t digit = DbHexToValue(*str);
        ++str;
        const uint32_t base = 16;
        DB_ASSERT(digit < base);  // 调用者外面检查过合法性
        value = (value * base) + digit;
    }

    *iret = value;
}

Status DbStrHexToBytes(const char *hex, uint8_t byte[], uint32_t len)
{
    DB_POINTER(hex);
    const char *str = hex;
    uint8_t *bytes = byte;
    uint32_t count = len;

    if ((count & 1) != 0) {
        uint32_t value = DbHexToValue(*str);
        ++str;
        if (value > DB_MAX_UINT8) {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        *bytes = (uint8_t)value;
        ++bytes;
    }

    for (count >>= 1; count != 0; --count) {
        uint32_t high = DbHexToValue(*str);
        ++str;
        uint32_t low = DbHexToValue(*str);
        ++str;
        const uint32_t base = 16;
        uint32_t value = (high * base) | low;
        if (value > DB_MAX_UINT8) {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        *bytes = (uint8_t)value;
        ++bytes;
    }

    return GMERR_OK;
}

Status DbStrBinToBytes(const char *strBin, uint8_t byte[], uint32_t *len)
{
    DB_POINTER(len);
    uint8_t bitVals[] = {128, 1, 2, 4, 8, 16, 32, 64};
    const char *str = strBin;
    uint32_t strLen = *len;

    uint32_t spaceNums = 0;  // 导入时，支持01序列中，包含空格，统计一下空格数量
    uint8_t *bytes = byte;
    uint32_t count = 1;
    uint32_t bitsLenOfByte = 8;
    uint32_t byteNum = 0;  // 字符串序列转换成byte的值时多少
    uint32_t byteCnt = 0;  // 记录往byte数组中保存了多少个数据

    for (; count <= strLen; count++) {
        if (*str == ' ') {  //  略过字符串序列中的空格
            spaceNums++;
            str++;
            continue;
        }
        if (*str == '1') {
            byteNum = byteNum + bitVals[((count - spaceNums) % bitsLenOfByte)];  // 如果是1的话，需要增加当前位的值
        } else if (*str != '0' && *str != ' ') {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        if (((count - spaceNums) % bitsLenOfByte) == 0) {  // 累计满8位，需要往byte中存入
            *bytes = (uint8_t)byteNum;
            bytes++;
            byteNum = 0;
            byteCnt++;
        }
        str++;
    }
    if (((strLen - spaceNums) % bitsLenOfByte) != 0) {  // 支持01序列不满足8的倍数
        *bytes = (uint8_t)byteNum;
        byteCnt++;
    }
    *len = byteCnt * bitsLenOfByte;
    return GMERR_OK;
}
#endif
