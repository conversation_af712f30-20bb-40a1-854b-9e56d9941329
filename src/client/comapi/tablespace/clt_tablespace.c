/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: clt_tablespace.c
 * Description: internal realization of interface of gmdb tablespace
 * Author:
 * Create: 2023-06-06
 */

#include "clt_tablespace.h"

Status GmcCreateTablespaceCheck(GmcStmtT *stmt, GmcTspCfgT *tspCfg, GmcConnTypeE type)
{
    if (tspCfg == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Param tspCfg.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    Status ret = CltStmtBasicCheck(stmt, type);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(tspCfg->tablespaceName, MAX_TABLE_SPACE_LENGTH, "tablespaceName");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

Status GmcCreatePersistTablespaceCheck(GmcStmtT *stmt, GmcPersistentTspCfgT *tspCfg, GmcConnTypeE type)
{
    if (tspCfg == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Param tspCfg.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    if (tspCfg->isVolatile || DbStrCmp(tspCfg->customFilePath, "", false) != 0) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "Unable to set isVolatilen or customFilePath now.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    Status ret = CltStmtBasicCheck(stmt, type);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckStringValid(tspCfg->tablespaceName, MAX_TABLE_SPACE_LENGTH, "tablespaceName");
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

/* 后续会有新增配置 */
Status FillPersistConfig(FixBufferT *req, const void *in)
{
    const GmcPersistentTspCfgT *persistConf = in;
    Status ret = HelpFillString(req, true, persistConf->tablespaceName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufPutUint16(req, persistConf->initSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufPutUint16(req, persistConf->stepSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    return SecureFixBufPutUint16(req, persistConf->maxSize);
}

Status FillMemConfig(FixBufferT *req, const void *in)
{
    const GmcTspCfgT *config = in;
    Status ret = HelpFillString(req, true, config->tablespaceName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufPutUint16(req, config->initSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SecureFixBufPutUint16(req, config->stepSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    return SecureFixBufPutUint16(req, config->maxSize);
}

Status CreateTablespaceBuildReq(FixBufferT *req, const void *in, bool isUseRsm, bool isPersistent)
{
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_CREATE_TABLESPACE);
    Status ret = GMERR_OK;
    if (isPersistent) {
        ret = FillPersistConfig(req, in);
    } else {
        ret = FillMemConfig(req, in);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    uint16_t useRsmFlag = isUseRsm ? 1 : 0;
    ret = SecureFixBufPutUint16(req, useRsmFlag);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint16_t persistentFlag = isPersistent ? 1 : 0;
    return SecureFixBufPutUint16(req, persistentFlag);
}

Status CreateTablespaceBuildRequest(FixBufferT *req, const void *in)
{
    return CreateTablespaceBuildReq(req, in, false, false);
}

Status CreateRsmTablespaceBuildRequest(FixBufferT *req, const void *in)
{
    return CreateTablespaceBuildReq(req, in, true, false);
}

Status CreatePersistentTablespaceBuildRequest(FixBufferT *req, const void *in)
{
    return CreateTablespaceBuildReq(req, in, false, true);
}

Status DropTablespaceBuildRequest(FixBufferT *req, const void *in)
{
    FillSimpleStmtMsgHeader(req, MSG_OP_RPC_DROP_TABLESPACE);
    const char *tablespaceName = in;
    return HelpFillString(req, true, tablespaceName);
}
