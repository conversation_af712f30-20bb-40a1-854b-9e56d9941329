/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmc_transaction.c
 * Description: gmdb client transaction interface
 * Author: zhangyong
 * Create: 2021-06-11
 */
#include "gmc.h"
#include "gmc_internal.h"

#include "clt_check.h"
#include "clt_msg.h"

static Status GmcTransCheck(GmcConnT *conn, GmcConnTypeE expectedConnType)
{
    Status ret = CltPtrCheckWithErr(conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckConnType(conn, expectedConnType);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static Status GmcTransSavepointCheck(GmcConnT *conn, const char *savepointName, GmcConnTypeE expectedConnType)
{
    Status ret = GmcTransCheck(conn, expectedConnType);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (savepointName != NULL) {
        ret = CheckStringInvalid(savepointName, MAX_SAVEPOINT_LENGTH);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_VALUE, "Savepoint name exceeded limit!");
            return GMERR_INVALID_VALUE;
        }
    }
    return GMERR_OK;
}

static Status TransStartBuildReq(FixBufferT *req, const void *in)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_TX_START);
    const GmcTxConfigT *config = in;
    return SecureFixBufPut5Uint32(req, (uint32_t)config->type, (uint32_t)config->readOnly, (uint32_t)config->trxType,
        (uint32_t)config->forceCommitMode, (uint32_t) false);
}

#ifdef EXPERIMENTAL_GUANGQI
static Status GmcTransStartExtAsyncCheck(GmcConnT *conn, GmcStartExtOptionT *option, GmcConnTypeE expectedConnType)
{
    Status ret = CltPtr2CheckWithErr(conn, option);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CltCheckConnType(conn, expectedConnType);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

/* *
 * @ingroup gmc_types
 * 事务克隆、合并配置的结构体。
 */
struct GmcStartExtOptionT {
    GmcTxConfigT config;
    uint32_t cloneId;  // 为0表示开启父事务，否则根据cloneId去执行事务克隆操作
};

struct GmcStartExtResT {
    uint32_t cloneId;  // 开启父事务时，回传的cloneId
};

static Status TransCloneBuildReq(FixBufferT *req, const void *in)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_TX_CLONE);
    const GmcStartExtOptionT *config = in;
    Status ret = SecureFixBufPutUint32(req, config->cloneId);
    if (ret != GMERR_OK) {
        return ret;
    }
    return SecureFixBufPutUint16(req, 0);  // 自调度消息需要多传一个，为了防止解析错误，多传一个uint16
}

static Status TransStartWithCloneIdBuildReq(FixBufferT *req, const void *in)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_TX_START);
    const GmcTxConfigT *config = in;
    return SecureFixBufPut5Uint32(req, (uint32_t)config->type, (uint32_t)config->readOnly, (uint32_t)config->trxType,
        (uint32_t)config->forceCommitMode, (uint32_t) true);
}
#endif

static Status TransEndBuildReq(FixBufferT *req, const void *in)
{
    const MsgOpcodeRpcE *opCode = in;
    FillPublicStmtMsgHeader(req, *opCode);
    return GMERR_OK;
}

static Status TransStartSync(GmcConnT *conn, const GmcTxConfigT *config)
{
    Status ret = CltRequestSync(conn, TransStartBuildReq, config, DummyParseRsp, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    conn->transMode = GMC_TRANS_USED_IN_CS;
    return GMERR_OK;
}

static Status TransEndSync(GmcConnT *conn, const MsgOpcodeRpcE *opCode)
{
    Status ret = CltRequestSync(conn, TransEndBuildReq, opCode, DummyParseRsp, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    conn->transMode = GMC_TRANS_NONE;
    return GMERR_OK;
}

static void TransAsyncCallback(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out)
{
    DB_UNUSED(out);

    if (ret != GMERR_OK) {
        const TextT *error = DbGetLastErrorInfo();
        InvokeUserCallback(GmcTransDoneT, ctx->userCb, ctx->userData, ret, error->str);
        return;
    }

    conn->transMode = (uint8_t)ctx->cltDataU32;  // 由Async接口设置，保证有效
    InvokeUserCallback(GmcTransDoneT, ctx->userCb, ctx->userData, GMERR_OK, NULL);
}

static Status TransStartAsync(GmcConnT *conn, const GmcTxConfigT *config, GmcTransDoneT userCb, void *userData)
{
    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, TransAsyncCallback, userCb, userData, false);
    ctx.cltDataU32 = GMC_TRANS_USED_IN_CS;
    return CltRequestAsync(conn, TransStartBuildReq, config, &ctx);
}

#ifdef EXPERIMENTAL_GUANGQI
void CommonTransStartForChildTrxCallback(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out)
{
    DB_UNUSED(conn);

    if (ret != GMERR_OK) {
        const TextT *error = DbGetLastErrorInfo();
        GmcStartExtResT startExtRes = {0};
        InvokeUserCallback(GmcTransStartExtExecuteDoneT, ctx->userCb, ctx->userData, &startExtRes, ret, error->str);
        return;
    }

    conn->transMode = (uint8_t)ctx->cltDataU32;    // 由Async接口设置，保证有效
    GmcStartExtResT startExtRes = {.cloneId = 0};  // 子事务不支持再克隆，cloneId传0
    InvokeUserCallback(GmcTransStartExtExecuteDoneT, ctx->userCb, ctx->userData, &startExtRes, GMERR_OK, NULL);
}

static Status TransCloneAsync(
    GmcConnT *conn, const GmcStartExtOptionT *config, GmcTransStartExtExecuteDoneT userCb, void *userData)
{
    AsyncMsgContextT ctx =
        MakeAsyncMsgContext(DummyParseRsp, CommonTransStartForChildTrxCallback, userCb, userData, false);
    ctx.cltDataU32 = GMC_TRANS_USED_IN_CS;
    return CltRequestAsync(conn, TransCloneBuildReq, config, &ctx);
}

void CommonTransStartForParentTrxCallback(GmcConnT *conn, const AsyncMsgContextT *ctx, Status ret, const void *out)
{
    DB_UNUSED(conn);

    if (ret != GMERR_OK) {
        const TextT *error = DbGetLastErrorInfo();
        GmcStartExtResT startExtRes = {0};
        InvokeUserCallback(GmcTransStartExtExecuteDoneT, ctx->userCb, ctx->userData, &startExtRes, ret, error->str);
        return;
    }

    conn->transMode = (uint8_t)ctx->cltDataU32;  // 由Async接口设置，保证有效
    const uint32_t *cloneId = out;
    GmcStartExtResT startExtRes = {.cloneId = *cloneId};  // 设置父事务的cloneId
    InvokeUserCallback(GmcTransStartExtExecuteDoneT, ctx->userCb, ctx->userData, &startExtRes, GMERR_OK, NULL);
}

static Status TransStartWithCloneIdAsync(
    GmcConnT *conn, const GmcTxConfigT *config, GmcTransStartExtExecuteDoneT userCb, void *userData)
{
    AsyncMsgContextT ctx =
        MakeAsyncMsgContext(U32_RESULT_PARSE_RSP, CommonTransStartForParentTrxCallback, userCb, userData, false);
    ctx.cltDataU32 = GMC_TRANS_USED_IN_CS;
    return CltRequestAsync(conn, TransStartWithCloneIdBuildReq, config, &ctx);
}
#endif

static Status TransEndAsync(GmcConnT *conn, const MsgOpcodeRpcE *opCode, GmcTransDoneT userCb, void *userData)
{
    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, TransAsyncCallback, userCb, userData, false);
    ctx.cltDataU32 = GMC_TRANS_NONE;
    return CltRequestAsync(conn, TransEndBuildReq, opCode, &ctx);
}

static GmcTxConfigT CltTranslateTxConfig(const GmcTxConfigT *config)
{
    if (config != NULL) {
        return *config;
    }
    GmcTxConfigT defaultCfg = {
        .readOnly = false,
        .transMode = GMC_TRANS_USED_IN_CS,
        .type = GMC_TX_ISOLATION_DEFAULT,
        .trxType = GMC_DEFAULT_TRX,
    };
    return defaultCfg;
}

Status GmcTransStart(GmcConnT *conn, const GmcTxConfigT *config)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FlowControlWhenStartTx(&conn->flowControl);
    if (ret != GMERR_OK) {
        return ret;
    }

    const GmcTxConfigT cfg = CltTranslateTxConfig(config);
    if (cfg.transMode == GMC_TRANS_USED_IN_CS) {
        return TransStartSync(conn, &cfg);
    }

    DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "config.");
    return GMERR_INVALID_PARAMETER_VALUE;
}

Status GmcTransStartAsync(GmcConnT *conn, const GmcTxConfigT *config, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FlowControlWhenStartTx(&conn->flowControl);
    if (ret != GMERR_OK) {
        return ret;
    }

    const GmcTxConfigT cfg = CltTranslateTxConfig(config);
    if (cfg.transMode == GMC_TRANS_USED_IN_CS) {
        return TransStartAsync(conn, &cfg, userCb, userData);
    }
    DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "config.");
    return GMERR_INVALID_PARAMETER_VALUE;
}

#ifdef EXPERIMENTAL_GUANGQI
Status GmcTransCreateStartExtOption(GmcStartExtOptionT **option)
{
    Status ret = CltPtrCheckWithErr(option);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (*option != NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "option is not Null.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // 用户调用 GmcTransStartExtOptionDestroy 接口释放该内存
    *option = DbDynMemCtxAlloc(g_gmdbCltInstance.memCtx, sizeof(GmcStartExtOptionT));
    if (*option == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc option.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*option, sizeof(GmcStartExtOptionT), 0, sizeof(GmcStartExtOptionT));
    return GMERR_OK;
}

Status GmcTransStartExtOptionSetTrxCfg(GmcStartExtOptionT *option, GmcTxConfigT *config)
{
    Status ret = CltPtr2CheckWithErr(option, config);
    if (ret != GMERR_OK) {
        return ret;
    }

    option->config = *config;
    return GMERR_OK;
}

Status GmcTransStartExtOptionSetCloneId(GmcStartExtOptionT *option, uint32_t cloneId)
{
    Status ret = CltPtrCheckWithErr(option);
    if (ret != GMERR_OK) {
        return ret;
    }

    option->cloneId = cloneId;
    option->config.transMode = GMC_TRANS_USED_IN_CS;  // 只支持CS模式
    return GMERR_OK;
}

Status GmcTransStartExtResGetCloneId(GmcStartExtResT *res, uint32_t *cloneId)
{
    Status ret = CltPtr2CheckWithErr(res, cloneId);
    if (ret != GMERR_OK) {
        return ret;
    }

    *cloneId = res->cloneId;
    return GMERR_OK;
}

void GmcTransStartExtOptionDestroy(GmcStartExtOptionT *option)
{
    DbDynMemCtxFree(g_gmdbCltInstance.memCtx, option);
    option = NULL;
}

Status GmcTransStartExtAsync(
    GmcConnT *conn, GmcStartExtOptionT *option, GmcTransStartExtExecuteDoneT userCb, void *userData)
{
    Status ret = GmcTransStartExtAsyncCheck(conn, option, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = FlowControlWhenStartTx(&conn->flowControl);
    if (ret != GMERR_OK) {
        return ret;
    }

    const GmcTxConfigT cfg = CltTranslateTxConfig(&option->config);
    if (cfg.transMode == GMC_TRANS_USED_IN_CS) {
        if (option->cloneId == 0) {
            return TransStartWithCloneIdAsync(conn, &cfg, userCb, userData);  // 父事务
        } else {
            return TransCloneAsync(conn, option, userCb, userData);  // 子事务
        }
    }
    DB_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE, "config.");
    return GMERR_INVALID_PARAMETER_VALUE;
}
#endif

Status GmcTransCommit(GmcConnT *conn)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    const MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_COMMIT;
    return TransEndSync(conn, &opCode);
}

Status GmcTransCommitAsync(GmcConnT *conn, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    const MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_COMMIT;
    return TransEndAsync(conn, &opCode, userCb, userData);
}

Status GmcTransRollBack(GmcConnT *conn)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    const MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_ROLLBACK;
    return TransEndSync(conn, &opCode);
}

Status GmcTransRollBackAsync(GmcConnT *conn, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    const MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_ROLLBACK;
    return TransEndAsync(conn, &opCode, userCb, userData);
}

static Status TransAbortBuildReq(FixBufferT *req, const void *in)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_TX_ABORT);
    const uint64_t *transId = in;
    return SecureFixBufPutUint64(req, *transId);
}

Status GmcTransAbort(GmcConnT *conn, uint64_t transId)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_SYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    return CltRequestSync(conn, TransAbortBuildReq, &transId, DummyParseRsp, NULL);
}

void GmcCloseVertexLabel(void *label)
{
    CltCataCloseVertexLabel((CltCataLabelT *)label);
}

static Status CreateSavePointBuildReq(FixBufferT *req, const void *in)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_TX_CREATE_SAVEPOINT);
    return SecureFixBufPutStringNullable(req, in);
}

static Status ReleaseSavePointBuildReq(FixBufferT *req, const void *in)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_TX_RELEASE_SAVEPOINT);
    return SecureFixBufPutStringNullable(req, in);
}

static Status RollbackSavePointBuildReq(FixBufferT *req, const void *in)
{
    FillPublicStmtMsgHeader(req, MSG_OP_RPC_TX_ROLLBACK_SAVEPOINT);
    return SecureFixBufPutStringNullable(req, in);
}

Status GmcTransCreateSavepointAsync(GmcConnT *conn, const char *savepointName, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransSavepointCheck(conn, savepointName, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, CommonDDLCallback, userCb, userData, false);
    return CltRequestAsync(conn, CreateSavePointBuildReq, savepointName, &ctx);
}

Status GmcTransReleaseSavepointAsync(GmcConnT *conn, const char *savepointName, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransSavepointCheck(conn, savepointName, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, CommonDDLCallback, userCb, userData, false);
    return CltRequestAsync(conn, ReleaseSavePointBuildReq, savepointName, &ctx);
}

Status GmcTransRollBackSavepointAsync(GmcConnT *conn, const char *savepointName, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransSavepointCheck(conn, savepointName, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }

    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, CommonDDLCallback, userCb, userData, false);
    return CltRequestAsync(conn, RollbackSavePointBuildReq, savepointName, &ctx);
}

Status GmcTransCheckOptimisticTrxConflictAsync(GmcConnT *conn, GmcTransDoneT userCb, void *userData)
{
    Status ret = GmcTransCheck(conn, GMC_CONN_TYPE_ASYNC);
    if (ret != GMERR_OK) {
        return ret;
    }
    AsyncMsgContextT ctx = MakeAsyncMsgContext(DummyParseRsp, TransAsyncCallback, userCb, userData, false);
    ctx.cltDataU32 = GMC_TRANS_NONE;
    const MsgOpcodeRpcE opCode = MSG_OP_RPC_TX_CHECK_OPTIMISTIC_CONFLICT;
    return CltRequestAsync(conn, TransEndBuildReq, &opCode, &ctx);
}
