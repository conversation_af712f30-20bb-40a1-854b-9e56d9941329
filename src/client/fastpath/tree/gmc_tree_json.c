/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: gmc_tree_json.c
 * Description: Implement of GMDB client tree json API
 * Author: zhangyong
 * Create: 2021-10-23
 */

#include "gmc_tree.h"

#include "db_json.h"

#include "gmc_internal.h"
#include "dm_data_prop_json.h"
#include "gmc_tree_check.h"

Status GmcSetVertexByJson(GmcStmtT *stmt, uint32_t flag, const char *json)
{
    Status ret = GmcSetVertexByJsonCheck(stmt, json);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    DB_POINTER(vertex);
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(stmt);

    // 导入前先设置默认值
    ret = DmResetVertex(vertex);
    if (ret != GMERR_OK) {
        return ret;
    }

    const uint32_t mask = 0x11;
    DbJsonT *object = DbLoadJsonClt(json, flag & mask);
    if (object == NULL) {
        ret = GMERR_INVALID_JSON_CONTENT;
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "json file load, code is: %d!", (int32_t)ret);
        return ret;
    }
    ret = DmSetVertexByJson(vertex, cltCataLabel->vertexLabel, object);
    DbJsonDelete(object);

    if (ret == GMERR_OK) {
        return GMERR_OK;
    }

    // 如果失败，清空已经设置的值
    DmClearVertex(vertex);
    return ret;
}

Status GmcDumpVertexToJson(GmcStmtT *stmt, uint32_t flag, char **json)
{
    Status ret = GmcDumpVertexToJsonCheck(stmt, json);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    DB_POINTER(vertex);
    ret = CltCheckAndDeseriData(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    const uint32_t mask = 0x0100003F;
    ret = DmVertexCreateJsonStr(stmt->memCtx, vertex, flag & mask, NULL, json, true);
    return ret;
}

void GmcFreeJsonStr(GmcStmtT *stmt, char *json)
{
    Status ret = CltBasicCheckWithStmt(stmt);
    if (ret != GMERR_OK) {
        return;
    }

    // json为空时内部有处理，这里不再考虑
    DmVertexDestroyJson(stmt->memCtx, json);
}
