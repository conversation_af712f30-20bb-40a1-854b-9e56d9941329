/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: drt_flow_control_inner.h
 * Description: header file for drt flow control in model
 * Author:
 * Create: 2023-6-12
 */

#ifndef DRT_FLOW_CONTROL_INNER_H
#define DRT_FLOW_CONTROL_INNER_H

#include "adpt_types.h"
#include "adpt_define.h"
#include "db_mem_context.h"
#include "drt_abnormity.h"
#include "db_config.h"
#include "db_flow_control.h"

#ifdef __cplusplus
extern "C" {
#endif

#define RESOURCE_CHECK_TIME 1000  // 1s

typedef enum DrtThersholdAarryIndex {
    THERSHOLD_TYPE = 0,
    RECOVER_THERSHOLD_1,
    OVER_THERSHOLD_1,
    RECOVER_THERSHOLD_2,
    OVER_THERSHOLD_2,
    RECOVER_THERSHOLD_3,
    OVER_THERSHOLD_3,
} DrtThersholdAarryIndexE;

Status DrtInitFlowCtrlInfo(DrtFlowCtrlInfoT *flowCtrlInfo, DrtAbnormityMgrT *abnMgr);

#ifdef __cplusplus
}
#endif

#endif /* DRT_FLOW_CONTROL_INNER_H */
