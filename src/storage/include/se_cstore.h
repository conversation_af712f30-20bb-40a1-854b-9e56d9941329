/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Provide CStore management interfaces for external usage
 * Author: zhangtao
 * Create: 2024-01-02
 */

#ifndef SE_CSTORE_H
#define SE_CSTORE_H

#include "db_hashmap.h"
#include "db_internal_error.h"
#include "se_instance.h"

#ifdef __cplusplus
extern "C" {
#endif

#define INVALID_TBL_ID (0)

typedef struct CuStorageHdl CuStorageHdlT;

typedef struct CuStorageKey {
    uint32_t tblSpcId;
    uint32_t tblId;
} CuStorageKeyT;

// Cache slot type
typedef struct SeCuLookupEntry {
    uint32_t tblSpcId;
    uint32_t tblId;
    int64_t cuPointer;
    uint32_t cuSize;
    uint8_t *cuBuffAddr;
} SeCuLookupEntryT;

typedef struct SeSaveCuDataEntry {
    uint32_t *cuSizeArray;     // CuSize array of a patch of Cu
    int64_t *cuPtrArray;       // CuPointer array of a patch of Cu
    uint8_t **cuBufAddrArray;  // Cu buffer address array of a patch of Cu
    uint32_t colCount;         // Column count of a patch of Cu
} SeSaveCuDataEntryT;

typedef struct SeCuStorageHdlCache {
    DbMemCtxT *memCtx;
    DbOamapT *tblMap;
    DbSpinLockT tblMapLock;  // Lock for hashmap tblMap
} SeCuStorageHdlCacheT;

/*
 * @brief CStore initialization interface which will register exposed CStore functions
 */
SO_EXPORT void SeCStoreInit(void);

/*
 * @brief Initialize CuStorageHdlCache
 * @param[in] memCtx : Memory context to allocate resource
 * @return : GMERR_OK, or other errors
 */
SO_EXPORT_FOR_TS Status SeInitCuStorageHdlCache(DbMemCtxT *memCtx);

/*
 * @brief UnInitialize CuStorageHdlCache
 */
SO_EXPORT_FOR_TS void SeUnInitCuStorageHdlCache(void);

/*
 * @brief Initialize CuFileSizeCache
 * @param[in] memCtx : Memory context to allocate resource
 * @return : GMERR_OK, or other errors
 */
SO_EXPORT_FOR_TS Status SeInitCuFileSizeCache(DbMemCtxT *memCtx);

SO_EXPORT_FOR_TS uint64_t SeGetCuFileSize(uint32_t tblSpcId);
/*
 * @brief UnInitialize CuFileSizeCache
 */
SO_EXPORT_FOR_TS void SeUnInitCuFileSizeCache(void);
/*
 * @brief Reset cu save status.
 */
SO_EXPORT void SeResetCuSaveStatus(void);

/*
 * @brief Allocate CuId for a patch of Cu
 * @param[in] cuStorageHdl : Cu operation handle
 * @param[out] nextCuId : Next available CuId of cuStorageHdl
 * @return : GMERR_OK, or other errors
 */
Status SeAllocCuId(CuStorageHdlT *cuStorageHdl, uint32_t *nextCuId);

/*
 * @brief Allocate CuPointer for a patch of Cu
 * @param[in] cuStorageHdl : Cu operation handle
 * @param[in] cuSizeArray : CuSize array of a patch of Cu
 * @param[out] cuPtrArray : CuPointer array of a patch of Cu
 * @param[in] colCount : Column count of a patch of Cu
 * @return : GMERR_OK, or other errors
 */
Status SeAllocCuPointer(CuStorageHdlT *cuStorageHdl, uint32_t *cuSizeArray, int64_t *cuPtrArray, uint32_t colCount);

Status SeBeginMergeCuData(CuStorageHdlT *cuStorageHdl, int64_t cuPointer, uint32_t cuId);
void SeEndMergeCuData(CuStorageHdlT *cuStorageHdl);
/*
 * @brief Save Cu meta info into CuMetaFile
 * @param[in] cuStorageHdl : Cu operation handle
 * @return : GMERR_OK, or other errors
 */
Status SeSaveCuMetaData(CuStorageHdlT *cuStorageHdl);

/*
 * @brief Save Cu block into CuFile
 * @param[in] cuStorageHdl : Cu operation handle
 * @param[in] seSaveCuDataEntry : Parameters entry for saving Cu Data
 * @return : GMERR_OK, or other errors
 */
Status SeSaveCuData(CuStorageHdlT *cuStorageHdl, SeSaveCuDataEntryT seSaveCuDataEntry);

/*
 * @brief Open an available CuStorageHdl from CuStorageHdlCache, Create a new handle if not exist
 * @param[in] cuStorageKey : CuStorageHdl lookup key
 * @param[out] cuStorageHdl : CuStorageHdl Entry
 * @param[out] maxCuId : the max cu id this table obtains now
 * @note if maxCuId is NULL, it doesn't give back the id.
 * @note if isInsert is true (and tsHighConcurrency is on), it will prevent concurrent insertion into the same
 * partition; plus it will write undo log. Only called once in single transaction.
 * @return : GMERR_OK, or other errors
 */
Status SeOpenCuStorageHdl(
    SeRunCtxHdT seRunCtx, CuStorageKeyT *cuStorageKey, bool isInsert, CuStorageHdlT **cuStorageHdl, uint32_t *maxCuId);

/*
 * @brief Close an available CuStorageHdl from CuStorageHdlCache
 * @param[out/in] cuStorageHdl : CuStorageHdl Entry
 */
void SeCloseCuStorageHdl(CuStorageHdlT *cuStorageHdl, bool isInsert);

/*
 * @brief Remove Cu directory, such as logical or physical table directory
 * @param[in] cuStorageKey : CuStorageHdl lookup key, which includes logical and physical table Ids, if tblId is
 * invalid, it means removing empty logical table directory. if both Ids are valid, it means removing physical table
 * directory only
 */
SO_EXPORT_FOR_TS Status SeRemoveCuDir(SeRunCtxHdT seRunCtx, CuStorageKeyT *cuStorageKey);

/*
 * @brief Remove Cu directory during transaction commit, such as logical or physical table directory
 * @param[in] physicalId : physical label id, if NULL, this removes the whole logical directory.
 * @param[in] logicalId : logical label id, not NULL.
 * directory only
 */
SO_EXPORT_FOR_TS void SeRemoveCuDirCommit(uint32_t physicalId, uint32_t logicalId);

SO_EXPORT_FOR_TS Status SeTrimCuDirForRollback(
    uint32_t physicalId, uint32_t logicalId, uint64_t cuPointer, uint32_t cuId);

/*
 * @brief set memCtx for CuBuffer
 * @param[in] memCtx: MemCtx to allocate memory for CuBuffer
 */
SO_EXPORT_FOR_TS Status SeSetCuOperateMemCtx(DbMemCtxT *memCtx);

SO_EXPORT_FOR_TS DbMemCtxT *SeGetCuOperateMemCtx(void);

/*
 * @brief Fetch Cu from CuCache if CuCache enabled or load Cu from Cu file directly.
 * @param[in] seCuLookupEntry: Lookup entry includes necessary arguments
 */
Status SeFetchCuData(SeCuLookupEntryT *seCuLookupEntry);

/*
 * @brief If CuCache enabled, slotId is VALID and unpin will decrease slot's reference count only,
 * otherwise, slotId is INVALID and unpin will release CuBuffer only.
 * @param[in] seCuLookupEntry: Lookup entry includes necessary arguments
 */
void SeUnpinCuData(SeCuLookupEntryT *seCuLookupEntry);

/*
 * @brief Pre-operation before SaveCu or LoadCu. such as reset THREAD_LOCAL CuFd
 * @return : GMERR_OK, or other errors
 */
Status SeBeginCuFileAccess(void);

/*
 * @brief Post-operation after SaveCu or LoadCu. such as reset THREAD_LOCAL CuFd
 * @return : GMERR_OK, or other errors
 */
Status SeEndCuFileAccess(void);

Status SeGetMaxCuIdByHdl(CuStorageHdlT *cuStorageHdl);

SO_EXPORT Status WriteCuInsertUndo(SeRunCtxHdT seRunCtx, CuStorageKeyT *key, CuStorageHdlT *handle);

// 打印cu数据损坏的日志
void SePrintLogForFatalCu(CuStorageHdlT *cuStorageHdl, uint32_t cuId, int64_t cuPointer, uint64_t bufSize);

void SeBeginFatalCuProcess(CuStorageHdlT *cuStorageHdl, bool *isDeleted);

void SeEndFatalCuProcess(CuStorageHdlT *cuStorageHdl, uint64_t decreasedSize, bool *isToDelete);

void SeRecordFatalCuId(CuStorageHdlT *cuStorageHdl, uint32_t cuId, bool *isFull);

Status SeGetFatalCuId(CuStorageHdlT *cuStorageHdl, uint32_t index, uint32_t *cuId);

SO_EXPORT_FOR_TS Status SeRenameCuDir(
    SeRunCtxHdT seRunCtx, CuStorageKeyT *oldPathKey, CuStorageKeyT *newPathKey, uint32_t spaceId);

StatusInter SeCommitRenameCuDir(
    SeRunCtxHdT seRunCtx, CuStorageKeyT *oldPathKey, CuStorageKeyT *newPathKey, uint32_t spaceId);

#ifdef __cplusplus
}
#endif

#endif  // SE_CSTORE_H
