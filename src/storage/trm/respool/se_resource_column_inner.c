/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: resource模块负责资源池的管理和资源id的分配
 * Author: chendechen
 * Create: 2021-03-15
 */
#include "se_resource_column_inner.h"
#include "dm_meta_prop_label.h"
#include "se_trx_inner.h"
#include "se_undo.h"
#include "se_log.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SE_RES_SHM_CTX_BASE_SIZE (1024 * 64)        // 64K is enough for RES COL during server initialization
#define SE_RES_SHM_CTX_STEP_SIZE (1024 * 1024 * 1)  // 1M is enough for creating single resource pool

#define SE_RES_POOL_ZERO_POS 0

#define RES_COL_POOL_VALID_MAGIC_NUM 0xcdcace  // 内存有效的魔术字
#define RES_COL_POOL_INVALID_MAGIC_NUM 0x0     // 内存无效的魔术字(释放时标记)

#define RES_COL_POOL_OPEN_CNT_MAX_NUM 0xfffffff0

_Static_assert((uint32_t)SE_RES_SHM_CTX_STEP_SIZE > (uint32_t)sizeof(ResColPoolT),
    "The step size of the resource pool memCtx must be greater than the memory size consumed by creating a single "
    "resource pool");

typedef struct ResColPoolRunCtx {
    SeRunCtxT *seRunCtx;
    ResColPoolT *resPool;
} ResColPoolRunCtxT;

typedef struct ResColExtendPoolLinkPoint {
    ResColPoolT *linkPointPrev;
    ResColPoolT *linkPointNext;
} ResColExtPoolLinkPointT;

// 涉及两个资源池加锁的地方，必须按照资源池扩展方向来加锁, 解锁通过调用ResPoolUnlock配对完成
inline static void ResPoolLock(DbSpinLockT *lockA, DbSpinLockT *lockB)
{
    DbSpinLock(lockA);
    DbSpinLock(lockB);
}

inline static void ResPoolUnlock(DbSpinLockT *lockA, DbSpinLockT *lockB)
{
    DbSpinUnlock(lockB);
    DbSpinUnlock(lockA);
}

inline static uint32_t ResColEvenModeAlign(const ResColPoolT *resPool, uint32_t num)
{
    DB_POINTER(resPool);

    if (resPool->allocType == RES_COL_POOL_ALLOC_TYPE_EVEN) {
        // 偶数不变， 奇数加1
        return num + (num & 1);
    }
    return num;
}

inline static void ResColPaserResColId(uint64_t resId, ResColIdT *resColId)
{
    resColId->poolId = DmResColGetPoolIdFromResId(resId);
    resColId->count = DmResColGetCountFromResId(resId);
    resColId->startIndex = DmResColGetStartIndexFromResId(resId);
}

StatusInter ResColMgrCreateImpl(SeInstanceT *seIns, DbMemCtxT *seTopShmMemCtx)
{
    DB_POINTER2(seIns, seTopShmMemCtx);

    // 创建并初始化resColMgr，addr挂在HugeTLB memory context上
    void *tlbMemctx = DbGetShmemCtxById(DB_HUGETLB_SHMCTX_ID, seIns->instanceId);
    if (tlbMemctx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "get tlb memctx unsucc. instanceId:%" PRIu32 ", ctxId:%" PRId32,
            seIns->instanceId, (int32_t)DB_HUGETLB_SHMCTX_ID);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    // 从HugeTLB memory context申请共享内存承载资源列管理器, 在依赖该memCtx在server退出时销毁
    ResColMgrT *resColMgr = (ResColMgrT *)SeShmAlloc(tlbMemctx, sizeof(ResColMgrT), &seIns->resColMgrShm);
    if (resColMgr == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "shmMalloc(%" PRIu32 ") unsucc.", (uint32_t)sizeof(ResColMgrT));
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s((void *)resColMgr, sizeof(ResColMgrT), 0x0, sizeof(ResColMgrT));

    DbSpinInit(&resColMgr->lock);
    resColMgr->headResPool = DB_INVALID_SHMPTR;

    // 申请分配res pool需要的共享内存上下文resColShmMemCtxId
    DbMemCtxArgsT resColShmMemCtxArgs = {0};
    resColShmMemCtxArgs.instanceId = seIns->seConfig.instanceId;
    resColShmMemCtxArgs.ctxId = DB_SE_RES_COL_SHM_CTX_ID;

    // Use private memory context setting
    char param[sizeof(DbBlockMemParamT)] = {0};
    DbBlockMemParamT *blockParam = (DbBlockMemParamT *)param;
    blockParam->isHugePage = false;
    blockParam->baseSize = SE_RES_SHM_CTX_BASE_SIZE;
    blockParam->stepSize = SE_RES_SHM_CTX_STEP_SIZE;
    blockParam->isReused = false;
    blockParam->allowBigChunk = true;
    blockParam->maxSize = (uint64_t)DbGetResPoolShmemMaxSize();
    blockParam->blkPoolType = BLK_NORMAL;
    AlgoParamT algoParam;
    algoParam.blockParam = blockParam;
    resColShmMemCtxArgs.algoParam = &algoParam;
    /* SeResColShmMemCtx 说明
        用    途: 用于创建资源列容器所需共享内存的申请和释放
        生命周期: 长进程级别
        释放策略: 就近释放
        兜底清空措施: 依赖上层seTopShmMemCtx, server退出时销毁
    */
    void *resColShmMemCtx = DbCreateBlockPoolShmemCtx(seTopShmMemCtx, "SeResColShmMemCtx", &resColShmMemCtxArgs);
    if (resColShmMemCtx == NULL) {
        DbShmemCtxFree(tlbMemctx, seIns->resColMgrShm);
        seIns->resColMgrShm = DB_INVALID_SHMPTR;
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER,
            "resColMgr alloc ShmemCtx unsucc. instanceId:%" PRIu32 ", ctxId:%" PRIu32, resColShmMemCtxArgs.instanceId,
            resColShmMemCtxArgs.ctxId);
        return MEMORY_OPERATE_FAILED_INTER;
    }

    return STATUS_OK_INTER;
}

inline static uint32_t ResColGetBitmapSizeAligned(uint32_t capacity)
{
    uint64_t bitmapWordSize = ((uint64_t)capacity + BITS_PER_DWORD - 1) / BITS_PER_DWORD;
    return (uint32_t)(bitmapWordSize * BITS_PER_DWORD / BITS_PER_BYTE);
}

static StatusInter ResColCheckResPoolParameter(const ResColPoolConfigT *resPoolConfig)
{
    DB_POINTER(resPoolConfig);

    if (resPoolConfig->poolId >= SE_RES_POOL_MAX_POOL_ID) {
        SE_LAST_ERROR(
            RES_COL_ERR_POOL_INVALID_PARA, "novalid resource pool id, (id: %" PRIu32 ")", resPoolConfig->poolId);
        return RES_COL_ERR_POOL_INVALID_PARA;
    }

    if (resPoolConfig->capacity == 0) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_INVALID_PARA, "novalid resource pool capacity, (capacity: %" PRIu32 ")",
            resPoolConfig->capacity);
        return RES_COL_ERR_POOL_INVALID_PARA;
    }

    if (resPoolConfig->allocOrder >= RES_COL_POOL_ALLOC_ORDER_INVALID) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_INVALID_PARA, "novalid resource pool allocOrder, (allocOrder: %" PRId32 ")",
            (int32_t)resPoolConfig->allocOrder);
        return RES_COL_ERR_POOL_INVALID_PARA;
    }

    uint64_t maxIndex = (uint64_t)resPoolConfig->startId + (uint64_t)resPoolConfig->capacity - 1;
    if (maxIndex >= SE_RES_POOL_MAX_START_INDEX) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_INVALID_PARA,
            "novalid resource pool parameters, (startId: %" PRIu32 ", capacity: %" PRIu32 ")", resPoolConfig->startId,
            resPoolConfig->capacity);
        return RES_COL_ERR_POOL_INVALID_PARA;
    }

    if (resPoolConfig->allocType >= RES_COL_POOL_ALLOC_TYPE_INVALID) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_INVALID_PARA, "novalid alloc type, alloc_type = %" PRId32,
            (int32_t)resPoolConfig->allocType);
        return RES_COL_ERR_POOL_INVALID_PARA;
    }

    if (resPoolConfig->allocType == RES_COL_POOL_ALLOC_TYPE_EVEN && ((resPoolConfig->startId & 1) == 1)) {
        SE_LAST_ERROR(
            RES_COL_ERR_POOL_INVALID_PARA, "start id novalid, start id = %" PRIu32 "", resPoolConfig->startId);
        return RES_COL_ERR_POOL_INVALID_PARA;
    }
    return STATUS_OK_INTER;
}

// 头插法
static StatusInter ResColPoolListAdd(ResColMgrT *resColMgr, ResColPoolT *resPool, ShmemPtrT resPoolShm)
{
    DB_POINTER2(resColMgr, resPool);
    if (!DbIsShmPtrValid(resColMgr->headResPool)) {
        resPool->nextPool = DB_INVALID_SHMPTR;
    } else {
        ShmemPtrT nextPoolShm = resColMgr->headResPool;
        ResColPoolT *nextPool = DbShmPtrToAddr(nextPoolShm);
        if (nextPool == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
                "get next resPool virAddr by shmPtr(segId: %" PRIu32 ", ofset: %" PRIu32 ")", nextPoolShm.segId,
                nextPoolShm.offset);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        nextPool->prevPool = resPoolShm;
        resPool->nextPool = resColMgr->headResPool;
    }
    resPool->prevPool = DB_INVALID_SHMPTR;
    resColMgr->headResPool = resPoolShm;
    resColMgr->resPoolCount++;
    return STATUS_OK_INTER;
}

static void ResColPoolListRemove(ResColMgrT *resColMgr, const ResColPoolT *resPool)
{
    DB_POINTER2(resColMgr, resPool);
    if (DbIsShmPtrValid(resPool->nextPool)) {
        ResColPoolT *nextPool = DbShmPtrToAddr(resPool->nextPool);
        if (nextPool == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
                "get next resPool virAddr by shmPtr(segId: %" PRIu32 ", ofset: %" PRIu32 ")", resPool->nextPool.segId,
                resPool->nextPool.offset);
            return;
        }
        nextPool->prevPool = resPool->prevPool;
    }
    if (!DbIsShmPtrValid(resPool->prevPool)) {
        // 该节点是头结点
        resColMgr->headResPool = resPool->nextPool;
    } else {
        ResColPoolT *prevPool = DbShmPtrToAddr(resPool->prevPool);
        if (prevPool == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER,
                "get prev resPool virAddr by shmPtr(segId: %" PRIu32 ", ofset: %" PRIu32 ")", resPool->prevPool.segId,
                resPool->prevPool.offset);
            return;
        }
        prevPool->nextPool = resPool->nextPool;
    }
    DB_ASSERT(resColMgr->resPoolCount > 0);  // 确保Create和Destroy成对执行
    resColMgr->resPoolCount--;
}

static StatusInter ResColInitResPool(ResColPoolT *resPool, ShmemPtrT resPoolShmPtr,
    const ResColPoolConfigT *resPoolConfig, uint32_t resPoolSize, DbMemCtxT *resPoolShmMemCtx)
{
    DB_POINTER2(resPool, resPoolConfig);

    // bitmap中，0代表当前资源是free状态，可被分配
    errno_t ret = memset_s(resPool, resPoolSize, 0, resPoolSize);
    if (ret != EOK) {
        SE_LAST_ERROR(
            MEMORY_OPERATE_FAILED_INTER, "set res pool bitmap unsucc, pool id: %" PRIu32 "", resPoolConfig->poolId);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    ret = memcpy_s(resPool->name, DM_RES_POOL_MAX_NAME_LEN, resPoolConfig->name, sizeof(resPoolConfig->name));
    if (ret != EOK) {
        SE_LAST_ERROR(
            MEMORY_OPERATE_FAILED_INTER, "copy res pool name unsucc, pool id: %" PRIu32 "", resPoolConfig->poolId);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    DbSpinInit(&resPool->lock);
    resPool->poolId = resPoolConfig->poolId;
    resPool->poolLabelId = resPoolConfig->poolLabelId;
    resPool->labelRefCnt = 0;
    resPool->poolRefCnt = 0;
    resPool->startId = resPoolConfig->startId;
    resPool->capacity = resPoolConfig->capacity;
    resPool->curPos = 0;
    resPool->allocOrder = resPoolConfig->allocOrder;
    resPool->allocType = resPoolConfig->allocType;
    resPool->magicNum = RES_COL_POOL_VALID_MAGIC_NUM;
    // res pool初始化时都不包含扩展pool
    resPool->extendedPool = DB_INVALID_SHMPTR;
    resPool->bitmapSizeAligned = ResColGetBitmapSizeAligned(resPoolConfig->capacity);
    resPool->bitmapShmPtr = DbAdptShmOffsetShift(resPoolShmPtr, sizeof(ResColPoolT));
    uint32_t *bitmap = DbShmPtrToAddr(resPool->bitmapShmPtr);
    if (SECUREC_UNLIKELY(bitmap == NULL)) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER,
            "Create bitmap unsucc, poolid: %" PRIu32 ", segId:%" PRIu32 ", offset:%" PRIu32, resPoolConfig->poolId,
            resPool->bitmapShmPtr.segId, resPool->bitmapShmPtr.offset);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    (void)memset_s(bitmap, resPool->bitmapSizeAligned, 0, resPool->bitmapSizeAligned);

    // 如果capacity不是sizeof(uint32)的倍数，将最后一个uint32里剩余的bit置为1
    uint32_t excessBit =
        (uint32_t)((uint64_t)resPool->bitmapSizeAligned * BITS_PER_BYTE - (uint64_t)resPoolConfig->capacity);
    DB_ASSERT(excessBit < BITS_PER_DWORD);  // 因为对齐超出capacity的位数，必然不会超过32位
    if (excessBit > 0) {
        bitmap[resPool->bitmapSizeAligned / SE_DWORD_SIZE - 1] = DB_INVALID_UINT32 << (BITS_PER_DWORD - excessBit);
    }
    return STATUS_OK_INTER;
}

ResColMgrT *ResColGetMgr(void)
{
    SeInstanceT *seInstance = (SeInstanceT *)SeGetInstance(DbGetProcGlobalId());
    if (SECUREC_UNLIKELY(seInstance == NULL)) {
        SE_LAST_ERROR(
            UNEXPECTED_NULL_VALUE_INTER, "resCol unsucc to get storage instance, Id: %" PRIu16 "", DbGetProcGlobalId());
        return NULL;
    }

    ResColMgrT *resColMgr = DbShmPtrToAddr(seInstance->resColMgrShm);
    if (SECUREC_UNLIKELY(resColMgr == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "unsucc to get resColMgr, shmAddr: (%" PRIu32 ", %" PRIu32 ")",
            seInstance->resColMgrShm.segId, seInstance->resColMgrShm.offset);
    }
    return resColMgr;
}

Status ResColCreateResPoolImpl(ResColPoolConfigT *resPoolConfig, ShmemPtrT *resPool)
{
    DB_POINTER2(resPoolConfig, resPool);

    StatusInter ret = ResColCheckResPoolParameter(resPoolConfig);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);
    }

    DbMemCtxT *resPoolMemCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_SE_RES_COL_SHM_CTX_ID, DbGetProcGlobalId());
    if (resPoolMemCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get resPool memCtx unsucc. instanceId:%" PRIu32 ", ctxId:%" PRId32,
            DbGetProcGlobalId(), (int32_t)DB_SE_RES_COL_SHM_CTX_ID);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    // 申请内存的时候计算bitmap的大小
    uint32_t bitmapSizeAligned = ResColGetBitmapSizeAligned(resPoolConfig->capacity);
    uint32_t resPoolSize = (uint32_t)sizeof(ResColPoolT) + bitmapSizeAligned;
    // 申请共享内存承载资源列容器, 在drop 操作中释放
    ResColPoolT *resPoolPtr = SeShmAlloc(resPoolMemCtx, resPoolSize, resPool);
    if (resPoolPtr == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "shmMalloc res pool unsucc, resPoolSize:%" PRIu32 ".", resPoolSize);
        return GMERR_OUT_OF_MEMORY;
    }
    ret = ResColInitResPool(resPoolPtr, *resPool, resPoolConfig, resPoolSize, resPoolMemCtx);
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(resPoolMemCtx, *resPool);
        SE_ERROR(ret, "init res pool(%s) unsucc", resPoolConfig->name);
        return DbGetExternalErrno(ret);
    }

    // 维护全局的res pool链表，便于全量扫描
    ResColMgrT *resColMgr = ResColGetMgr();
    if (SECUREC_UNLIKELY(resColMgr == NULL)) {
        DbShmemCtxFree(resPoolMemCtx, *resPool);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbSpinLock(&resColMgr->lock);
    ret = ResColPoolListAdd(resColMgr, resPoolPtr, *resPool);
    DbSpinUnlock(&resColMgr->lock);
    if (ret != STATUS_OK_INTER) {
        DbShmemCtxFree(resPoolMemCtx, *resPool);
    } else {
        DB_LOG_INFO("(SE-Respool) Create new res pool. (pool Id:%" PRIu32 ")", resPoolConfig->poolId);
    }

    return DbGetExternalErrno(ret);
}

static Status ResColSetResPoolLabelIsBind(ResColPoolT *resPool, uint32_t metaId)
{
    // 先循环一次，看是否有重复绑定的表
    for (int32_t i = 0; i < DM_RES_POOL_MAX_TABLE_NUM; i++) {
        if (resPool->labelUsed[i].metaId == metaId) {
            SE_LAST_ERROR(
                RES_POOL_ALREADY_BOUND_INTER, "bind table unsucc, table already bound (metaId: %" PRIu32 ")", metaId);
            return GMERR_RESOURCE_POOL_ERROR;
        }
    }

    for (int32_t i = 0; i < DM_RES_POOL_MAX_TABLE_NUM; i++) {
        if (resPool->labelUsed[i].metaId == 0) {
            resPool->labelUsed[i].metaId = metaId;
            return GMERR_OK;
        }
    }
    SE_LAST_ERROR(RES_POOL_ALREADY_BOUND_INTER, "table bound exceed limit(metaId: %" PRIu32 ")", metaId);
    return GMERR_RESOURCE_POOL_ERROR;
}

static Status ResColSetResPoolLabelUnBind(ResColPoolT *resPool, uint32_t metaId)
{
    for (int32_t i = 0; i < DM_RES_POOL_MAX_TABLE_NUM; i++) {
        if (resPool->labelUsed[i].metaId == metaId) {
            if (resPool->labelUsed[i].usedNum > 0) {
                SE_LAST_ERROR(RES_COL_ERR_POOL_UNBIND_FAILED, "unbind table unsucc, (used res: %" PRIu32 ")",
                    resPool->labelUsed[i].usedNum);
                return GMERR_RESOURCE_POOL_ERROR;
            }
            resPool->labelUsed[i].metaId = 0;
            resPool->labelUsed[i].usedNum = 0;
            return GMERR_OK;
        }
    }
    SE_LAST_ERROR(RES_COL_ERR_POOL_UNBIND_FAILED, "table not bound respool. (metaId: %" PRIu32 ")", metaId);
    return GMERR_RESOURCE_POOL_ERROR;
}

static Status ResColGetResPoolLabelUsed(ResColPoolT *resPool, uint32_t metaId, uint32_t *usedNum)
{
    for (int32_t i = 0; i < DM_RES_POOL_MAX_TABLE_NUM; i++) {
        if (resPool->labelUsed[i].metaId == metaId) {
            *usedNum = resPool->labelUsed[i].usedNum;
            return GMERR_OK;
        }
    }

    return GMERR_RESOURCE_POOL_ERROR;
}

uint32_t ResColGetResPoolTotalLabelUsed(ResColPoolT *resPool)
{
    uint32_t totalLabelUsed = 0;
    for (int32_t i = 0; i < DM_RES_POOL_MAX_TABLE_NUM; i++) {
        totalLabelUsed += resPool->labelUsed[i].usedNum;
    }

    return totalLabelUsed;
}

static StatusInter ResColAllocResPoolSingleLabelUsed(ResColPoolT *resPool, uint32_t metaId, uint32_t used)
{
    for (int32_t i = 0; i < DM_RES_POOL_MAX_TABLE_NUM; i++) {
        if (resPool->labelUsed[i].metaId == metaId) {
            resPool->labelUsed[i].usedNum += used;
            return STATUS_OK_INTER;
        }
    }
    SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_BOUND, "alloc unsucc, not bound respool . (metaId: %" PRIu32 ")", metaId);
    return RES_COL_ERR_POOL_NOT_BOUND;
}

static StatusInter ResColFreeResPoolSingleLabelUsed(ResColPoolT *resPool, uint32_t metaId, uint32_t count)
{
    for (int32_t i = 0; i < DM_RES_POOL_MAX_TABLE_NUM; i++) {
        if (resPool->labelUsed[i].metaId == metaId) {
            DB_ASSERT(resPool->labelUsed[i].usedNum >= count);
            resPool->labelUsed[i].usedNum -= count;
            return STATUS_OK_INTER;
        }
    }
    SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_BOUND, "free unsucc, not bound respool. (metaId: %" PRIu32 ")", metaId);
    return RES_COL_ERR_POOL_NOT_BOUND;
}

static StatusInter ResColSetExtendResPoolBindLabelUsed(ResColPoolLabelUsedT labelUsed, ResColPoolT *extendPool)
{
    // 先检查一遍有没有绑定过
    for (int32_t i = 0; i < DM_RES_POOL_MAX_TABLE_NUM; i++) {
        if (extendPool->labelUsed[i].metaId == labelUsed.metaId) {
            return STATUS_OK_INTER;
        }
    }

    for (int32_t i = 0; i < DM_RES_POOL_MAX_TABLE_NUM; i++) {
        if (extendPool->labelUsed[i].metaId == 0) {
            extendPool->labelUsed[i].metaId = labelUsed.metaId;
            extendPool->labelUsed[i].usedNum = 0;
            return STATUS_OK_INTER;
        }
    }

    SE_LAST_ERROR(RES_COL_ERR_INVALID_COUNT, "extendpool has bound max.");
    return RES_COL_ERR_INVALID_COUNT;
}

static StatusInter ResColSetResPoolExtendResPoolBind(ResColPoolT *resPool, ResColPoolT *extendPool)
{
    for (int32_t i = 0; i < DM_RES_POOL_MAX_TABLE_NUM; i++) {
        if (resPool->labelUsed[i].metaId != 0) {
            StatusInter ret = ResColSetExtendResPoolBindLabelUsed(resPool->labelUsed[i], extendPool);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
        }
    }

    return STATUS_OK_INTER;
}

static uint32_t ResColGetExtendResPoolTotalLabelUsed(ResColPoolT *resPool, ResColPoolT *extendedPool)
{
    uint32_t totalUsed = 0;
    for (int32_t i = 0; i < DM_RES_POOL_MAX_TABLE_NUM; i++) {
        for (int32_t j = 0; j < DM_RES_POOL_MAX_TABLE_NUM; j++) {
            if (resPool->labelUsed[i].metaId == extendedPool->labelUsed[j].metaId) {
                totalUsed += extendedPool->labelUsed[j].usedNum;
            }
        }
    }
    return totalUsed;
}

// 上层需要在加上表latch后调用本函数来控制DDL与DML的并发
Status ResColDestroyResPoolCheckImpl(ShmemPtrT resPoolShm)
{
    ResColPoolT *resPool = DbShmPtrToAddr(resPoolShm);
    if (resPool == NULL) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST, "novalid res pool ptr (segid: %" PRIu32 " offset: %" PRIu32 ")",
            resPoolShm.segId, resPoolShm.offset);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    if (resPool->openCnt > 0) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_DROP_FAILED, "res pool openCnt: %" PRIu32 "not zero, poolName: %s",
            resPool->openCnt, resPool->name);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    if (resPool->poolRefCnt > 0 || resPool->labelRefCnt > 0) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_DROP_FAILED,
            "res pool refCnt not zero, poolName: %s, poolRefCnt: %" PRIu32 ", labelRefCnt: %" PRIu32, resPool->name,
            resPool->poolRefCnt, resPool->labelRefCnt);
        return GMERR_RESOURCE_POOL_ERROR;
    }

    uint32_t totalLabelUsed = ResColGetResPoolTotalLabelUsed(resPool);
    if (totalLabelUsed > 0) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_DROP_FAILED, "still has used res (pool id: %" PRIu32 " used res: %" PRIu32 ")",
            resPool->poolId, totalLabelUsed);
        return GMERR_RESOURCE_POOL_ERROR;
    }

    if (DbIsShmPtrValid(resPool->extendedPool)) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_DROP_FAILED, "res drop unsucc, have extended pool (%s)", resPool->name);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    return GMERR_OK;
}

// 需要处理DDL操作和DML操作之间的冲突
// 将各种约束校验提前到ResColDestroyResPoolCheck中，由上层控制并发
Status ResColDestroyResPoolImpl(ShmemPtrT resPoolShm)
{
    ResColPoolT *resPool = DbShmPtrToAddr(resPoolShm);
    if (resPool == NULL) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST, "novalid res pool shm ptr (segid: %" PRIu32 " offset: %" PRIu32 ")",
            resPoolShm.segId, resPoolShm.offset);
        return GMERR_RESOURCE_POOL_ERROR;
    }

    DbMemCtxT *resPoolMemCtx = (DbMemCtxT *)DbGetShmemCtxById(DB_SE_RES_COL_SHM_CTX_ID, DbGetProcGlobalId());
    if (resPoolMemCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get resPool memCtx unsucc. instanceId:%" PRIu32 ", ctxId:%" PRId32,
            DbGetProcGlobalId(), (int32_t)DB_SE_RES_COL_SHM_CTX_ID);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    ResColMgrT *resColMgr = ResColGetMgr();
    if (SECUREC_UNLIKELY(resColMgr == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get resColMgr unsucc.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbSpinLock(&resColMgr->lock);
    ResColPoolListRemove(resColMgr, resPool);
    DbSpinUnlock(&resColMgr->lock);
    resPool->magicNum = RES_COL_POOL_INVALID_MAGIC_NUM;
    DbShmemCtxFree(resPoolMemCtx, resPoolShm);
    return GMERR_OK;
}

static void ResPoolSetBitmapUnused(ResColPoolT *resPool, uint32_t startPos, uint32_t count)
{
    DB_POINTER(resPool);
    // 对bitmap进行操作前要确保执行过校验
    DB_ASSERT(((uint64_t)startPos + count) <= ((uint64_t)resPool->bitmapSizeAligned * BITS_PER_BYTE));
    // 直连写适配：访问bitmap前要确保先完成转换
    uint32_t *bitmap = DbShmPtrToAddr(resPool->bitmapShmPtr);
    if (SECUREC_UNLIKELY(bitmap == NULL)) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER,
            "Convert shmPtr unsucc, poolid: %" PRIu32 ", segId:%" PRIu32 ", offset:%" PRIu32, resPool->poolId,
            resPool->bitmapShmPtr.segId, resPool->bitmapShmPtr.offset);
        return;
    }
    // startPos：要置为0的第一个bit，endPos：要置为0的最后一个bit
    uint32_t endPos = startPos + count - 1;
    // 计算要置位的第一个uint32和相应掩码
    uint32_t startWord = startPos / BITS_PER_DWORD;
    uint32_t startMask = (uint32_t)(((uint64_t)DB_INVALID_UINT32) >> (BITS_PER_DWORD - (startPos % BITS_PER_DWORD)));
    // 计算要置位的最后一个uint32和相应掩码
    uint32_t endWord = endPos / BITS_PER_DWORD;
    uint32_t endMask = (uint32_t)(((uint64_t)DB_INVALID_UINT32) << (endPos % BITS_PER_DWORD + 1));
    if (startWord == endWord) {
        DB_ASSERT(~(startMask | endMask) == (bitmap[startWord] & ~(startMask | endMask)));
        bitmap[startWord] &= (startMask | endMask);
    } else {
        bitmap[startWord] &= startMask;
        bitmap[endWord] &= endMask;
        for (uint32_t i = startWord + 1; i < endWord; i++) {
            DB_ASSERT(bitmap[i] == DB_INVALID_UINT32);
            bitmap[i] = 0;
        }
    }
}

static StatusInter ResPoolReleaseSingleResIdInner(ResColPoolT *resPool, const ResColIdT *resColId, uint32_t metaId)
{
    DB_POINTER2(resPool, resColId);
    DB_ASSERT(resPool->poolId == resColId->poolId);  // 确保调用者完成校验
    uint32_t labelUsed = 0;
    if (ResColGetResPoolLabelUsed(resPool, metaId, &labelUsed) != GMERR_OK) {
        SE_LAST_ERROR(
            RES_COL_ERR_POOL_NOT_EXIST, "ResPool release label unsucc. metaId (%" PRIu32 ") did not found.", metaId);
        return RES_COL_ERR_POOL_NOT_EXIST;
    }

    if (labelUsed < resColId->count) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
            "ResPool release resid unsucc. count(%" PRIu16 ") greater than used(%" PRIu32 ")", resColId->count,
            labelUsed);
        return RES_COL_ERR_INVALID_COUNT;
    }
    if (resColId->startIndex + resColId->count - resPool->startId > resPool->capacity) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
            "ResPool release resid unsucc. startIndex(%" PRIu32 ") + count(%" PRIu16 ") exceed capacity(%" PRIu32 ")",
            resColId->startIndex, resColId->count, resPool->capacity);
        return RES_COL_ERR_ID_INVALID_PARA;
    }
    ResPoolSetBitmapUnused(resPool, (resColId->startIndex - resPool->startId), resColId->count);
    return ResColFreeResPoolSingleLabelUsed(resPool, metaId, resColId->count);
}

static StatusInter ResPoolReleaseSingleResId(ResColPoolT *resPool, ResId resId, uint32_t metaId)
{
    DB_POINTER(resPool);
    ResColIdT resColId = {0};
    ResColPaserResColId(resId, &resColId);
    if (resColId.count == 0) {
        return STATUS_OK_INTER;
    }
    ResColPoolT *tmpResPool = resPool;
    while (tmpResPool) {
        DbSpinLock(&tmpResPool->lock);
        if (tmpResPool->poolId == resColId.poolId) {
            StatusInter ret = ResPoolReleaseSingleResIdInner(tmpResPool, &resColId, metaId);
            DbSpinUnlock(&tmpResPool->lock);
            return ret;
        }
        ResColPoolT *extendedPool = DbShmPtrToAddr(tmpResPool->extendedPool);
        DbSpinUnlock(&tmpResPool->lock);
        tmpResPool = extendedPool;
    }
    // 没有指定的归还的res pool，释放失败
    SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
        "expected release pool not exist in pool list (pool: %" PRIu16 " count: %" PRIu16 " start: %" PRIu32 ")",
        resColId.poolId, resColId.count, resColId.startIndex);
    return RES_COL_ERR_POOL_NOT_EXIST;
}

uint32_t *ResPoolGetBitmap(ResColPoolT *resPool, uint32_t startPos, uint32_t count, uint32_t *findPos)
{
    // 校验count是否非法
    if ((uint64_t)startPos + count > resPool->capacity || count == 0) {
        SE_LAST_ERROR(RES_COL_ERR_INVALID_COUNT, "count: %" PRIu32 " is novalid.", count);
        if (findPos != NULL) {
            *findPos = DB_INVALID_UINT32;
        }
        return NULL;
    }
    // 直连写适配：访问bitmap前要确保先完成转换
    uint32_t *bitmap = DbShmPtrToAddr(resPool->bitmapShmPtr);
    if (SECUREC_UNLIKELY(bitmap == NULL)) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER,
            "Convert shmPtr unsucc, poolid:%" PRIu32 ", segId:%" PRIu32 ", offset:%" PRIu32, resPool->poolId,
            resPool->bitmapShmPtr.segId, resPool->bitmapShmPtr.offset);
        if (findPos != NULL) {
            *findPos = DB_INVALID_UINT32;
        }
        return NULL;
    }
    return bitmap;
}

// 以startPos为起点，从低位向高位查找是否存在count个连续的未使用的资源。如果存在返回true；否则返回false并返回第一个1的下标
bool ResPoolBitMapHasFreeResWithUsedPos(ResColPoolT *resPool, uint32_t startPos, uint32_t count, uint32_t *findPos)
{
    DB_POINTER(resPool);

    uint32_t *bitmap = ResPoolGetBitmap(resPool, startPos, count, findPos);
    if (SECUREC_UNLIKELY(bitmap == NULL)) {
        return false;
    }
    uint32_t startWordIdx = startPos / BITS_PER_DWORD;
    uint32_t startBitOffset = startPos % BITS_PER_DWORD;
    // 保存startPos对应的uint32
    uint32_t startWord = bitmap[startWordIdx];
    if (startBitOffset > 0) {
        // 如果startPos不是恰好在对应的uint32的起始位置，将该uint32中startPos之前的bit置为0
        bitmap[startWordIdx] &= DB_INVALID_UINT32 << startBitOffset;
    }

    // 最多需要遍历的uint32的个数
    uint32_t travelNum = (count + BITS_PER_DWORD - 1) / BITS_PER_DWORD;  // 向上取整
    if (startBitOffset + count % BITS_PER_DWORD > BITS_PER_DWORD) {
        ++travelNum;
    }

    bool findBitOne = false;
    uint32_t firstBitOneOffset = DB_INVALID_UINT32;
    uint32_t bitMapSize = resPool->bitmapSizeAligned / (uint32_t)sizeof(uint32_t);
    for (uint32_t i = 0; i < travelNum && startWordIdx + i < bitMapSize; ++i) {
        uint32_t idx = startWordIdx + i;
        if (bitmap[idx] == 0) {
            continue;
        }
        int32_t pos = (int32_t)ffs((int32_t)bitmap[idx]);
        if (pos > 0) {
            findBitOne = true;
            firstBitOneOffset = BITS_PER_DWORD * idx + (uint32_t)pos - 1;
            break;
        }
    }

    // 复原startPos对应的uint32
    bitmap[startWordIdx] = startWord;

    if (findBitOne && (firstBitOneOffset - startPos < count)) {  // 没找到连续的count个空位
        if (findPos != NULL) {
            *findPos = (uint32_t)firstBitOneOffset;
        }
        return false;
    }
    return true;
}

// 由上层保证uint32足够使用
// 以startPos为起点，从低位向高位查找是否存在count个连续的未使用的资源。如果存在返回true；否则返回false
bool ResPoolBitMapHasFreeRes(ResColPoolT *resPool, uint32_t startPos, uint32_t count)
{
    DB_POINTER(resPool);
    return ResPoolBitMapHasFreeResWithUsedPos(resPool, startPos, count, NULL);
}

static void ResPoolSetBitmapUsed(ResColPoolT *resPool, uint32_t startPos, uint32_t count)
{
    DB_POINTER(resPool);
    // 搜索到的资源必然都在合理范围内，不会超过respool所持有bitmap
    DB_ASSERT(((uint64_t)startPos + count) <= ((uint64_t)resPool->bitmapSizeAligned * BITS_PER_BYTE));
    // 直连写适配：访问bitmap前要确保先完成转换
    uint32_t *bitmap = DbShmPtrToAddr(resPool->bitmapShmPtr);
    if (SECUREC_UNLIKELY(bitmap == NULL)) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER,
            "Convert shmPtr unsucc, poolid: %" PRIu32 ", segId:%" PRIu32 ", offset:%" PRIu32, resPool->poolId,
            resPool->bitmapShmPtr.segId, resPool->bitmapShmPtr.offset);
        return;
    }
    // startPos：要置为1的第一个bit，endPos：要置为1的最后一个bit
    uint32_t endPos = startPos + count - 1;
    // 计算要置位的第一个uint32和相应掩码
    uint32_t startWord = startPos / BITS_PER_DWORD;
    uint32_t startMask = DB_INVALID_UINT32 << (startPos % BITS_PER_DWORD);
    // 计算要置位的最后一个uint32和相应掩码
    uint32_t endWord = endPos / BITS_PER_DWORD;
    uint32_t endMask = (uint32_t)(((uint64_t)DB_INVALID_UINT32) >> ((BITS_PER_DWORD - 1) - (endPos % BITS_PER_DWORD)));
    if (startWord == endWord) {
        DB_ASSERT((bitmap[startWord] & (startMask & endMask)) == 0);
        bitmap[startWord] |= (startMask & endMask);
    } else {
        bitmap[startWord] |= startMask;
        bitmap[endWord] |= endMask;
        for (uint32_t i = startWord + 1; i < endWord; i++) {
            DB_ASSERT(bitmap[i] == 0);
            bitmap[i] = DB_INVALID_UINT32;
        }
    }
}

/*
 * position指物理上在bitmap的第几个bit，从0开始
 * index指bitmap中的某个bit代表的映射值
 * 注意二者区别
 */
static StatusInter ResPoolFindFreeResId(ResColPoolT *resPool, uint32_t startPos, uint32_t count, uint32_t *findPos)
{
    DB_POINTER2(resPool, findPos);
    uint32_t nextUsedPos;
    // EVEN模式下查询起始点对齐为偶数
    uint32_t pos = ResColEvenModeAlign(resPool, startPos);
    // ANY模式下循环步长为1, EVEN模式下循环步长为2
    uint32_t step = ResColEvenModeAlign(resPool, 1);
    while ((uint64_t)pos + count <= resPool->capacity) {
        bool hasFound = ResPoolBitMapHasFreeResWithUsedPos(resPool, pos, count, &nextUsedPos);
        if (hasFound) {
            *findPos = pos;
            return STATUS_OK_INTER;
        }
        pos = nextUsedPos + step;
    }
    return RES_COL_ERR_POOL_NOT_ENOUGH_RES;
}

// 从当前资源池中申请指定资源，上层负责加锁
static StatusInter ResPoolAllocSpecResIdInner(ResColPoolT *resPool, const ResColIdT *resColId, uint32_t metaId)
{
    DB_POINTER2(resPool, resColId);
    DB_ASSERT(resPool->poolId == resColId->poolId);  // 确保调用者已经进行校验
    // EVEN模式下指定的startIndex必须为偶数否则报错
    if (resPool->allocType == RES_COL_POOL_ALLOC_TYPE_EVEN) {
        if ((resColId->startIndex & 1) == 1) {
            SE_LAST_ERROR(RES_COL_ERR_ID_INVALID_PARA,
                "start index should be even in even mod, (pool: %" PRIu32 ", satrtIndex: %" PRIu32 ")",
                resColId->poolId, resColId->startIndex);
            return RES_COL_ERR_ID_INVALID_PARA;
        }
    }

    // 搜索bitmap中从指定位置开始，有多少连续的可用资源（多少连续的0）
    if (resColId->startIndex < resPool->startId || (((int64_t)resColId->startIndex - (int64_t)resPool->startId) +
                                                       (int64_t)resColId->count) > (int64_t)resPool->capacity) {
        SE_LAST_ERROR(RES_COL_ERR_SPEC_RES_NOT_EXIST,
            "resId not exist (pool: %" PRIu32 " count: %" PRIu32 " start: %" PRIu32 ") (start id: %" PRIu32 ")",
            resColId->poolId, resColId->count, resColId->startIndex, resPool->startId);
        return RES_COL_ERR_SPEC_RES_NOT_EXIST;
    }

    uint32_t startPos = resColId->startIndex - resPool->startId;
    bool found = ResPoolBitMapHasFreeRes(resPool, startPos, resColId->count);
    if (found == false) {  // 从指定位置开始没有足够的连续的可用资源，在这种情况下，是申请的指定资源已被申请
        SE_LAST_ERROR(RES_COL_ERR_SPEC_RES_ALREADY_USED,
            "resId has been used (pool: %" PRIu32 " count: %" PRIu32 " start: %" PRIu32 ")", resColId->poolId,
            resColId->count, resColId->startIndex);
        return RES_COL_ERR_SPEC_RES_ALREADY_USED;
    }

    ResPoolSetBitmapUsed(resPool, startPos, resColId->count);
    StatusInter ret = ResColAllocResPoolSingleLabelUsed(resPool, metaId, resColId->count);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    uint32_t totalLabelUsed = ResColGetResPoolTotalLabelUsed(resPool);
    DB_ASSERT(totalLabelUsed <= resPool->capacity);  // 前面的操作确保申请使用的资源不会超过容量
    // 不用设置resId，直接返回
    if (resPool->curPos < startPos + resColId->count) {
        resPool->curPos = startPos + resColId->count;
    }
    return STATUS_OK_INTER;
}

StatusInter ResColSetResIdInner(ResId *resId, uint32_t poolId, uint32_t count, uint32_t start)
{
    DB_POINTER(resId);

    if (poolId > SE_RES_POOL_MAX_POOL_ID || count > SE_RES_POOL_MAX_RES_COUNT) {
        SE_LAST_ERROR(RES_COL_ERR_ID_INVALID_PARA,
            "novalid res id (poolId: %" PRIu32 " count: %" PRIu32 " statrIndex:%" PRIu32 ")", poolId, count, start);
        return RES_COL_ERR_ID_INVALID_PARA;
    }
    DmResColSetResId(resId, (uint16_t)poolId, (uint16_t)count, start);
    return STATUS_OK_INTER;
}

// 从当前资源池中申请空闲资源
static StatusInter ResPoolAllocFreeResIdInner(ResColPoolT *resPool, ResId *resId, uint32_t metaId)
{
    DB_POINTER2(resPool, resId);
    StatusInter ret;
    uint32_t count = DmResColGetCountFromResId(*resId);
    uint32_t findPos;
    // 搜索当前资源池中是否有满足条件的可用res
    if (resPool->allocOrder == RES_COL_POOL_ALLOC_ORDER_CYCLE) {
        // 循环申请模式下，先从当前index开始搜索，再尝试从最小的index再搜索一次
        ret = ResPoolFindFreeResId(resPool, resPool->curPos, count, &findPos);
        if (ret == RES_COL_ERR_POOL_NOT_ENOUGH_RES) {
            ret = ResPoolFindFreeResId(resPool, SE_RES_POOL_ZERO_POS, count, &findPos);
        }
    } else {
        // sequence申请模式下，直接从最小的index开始搜索
        ret = ResPoolFindFreeResId(resPool, SE_RES_POOL_ZERO_POS, count, &findPos);
    }
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    uint32_t totalLabelUsed = ResColGetResPoolTotalLabelUsed(resPool);
    DB_ASSERT(totalLabelUsed + count <= resPool->capacity);  // 前面的操作确保申请使用的资源不会超过容量
    ret = ResColAllocResPoolSingleLabelUsed(resPool, metaId, count);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 已搜索到可用资源，设置bitmap
    ResPoolSetBitmapUsed(resPool, findPos, count);
    resPool->curPos = findPos + count;
    DB_ASSERT(resPool->curPos <= resPool->capacity);  // 搜索到资源确保不会超过容量
    DB_ASSERT((uint64_t)findPos + resPool->startId < SE_RES_POOL_MAX_START_INDEX);  // 到此处时已保证findPos肯定是合法的
    ret = ResColSetResIdInner(resId, resPool->poolId, count, findPos + resPool->startId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "set resId unsucc.");
        return ret;
    }
    return STATUS_OK_INTER;
}
// 申请任意符合条件的空闲resid
static StatusInter ResPoolAllocFreeResId(ResColPoolT *resPool, ResId *resId, const ResColIdT *resColId, uint32_t metaId)
{
    DB_POINTER3(resPool, resId, resColId);
    StatusInter ret;
    ResColPoolT *tmpResPool = resPool;
    while (tmpResPool) {
        DbSpinLock(&tmpResPool->lock);
        ret = ResPoolAllocFreeResIdInner(tmpResPool, resId, metaId);
        // 申请成功或发生错误，退出函数
        if (ret != RES_COL_ERR_POOL_NOT_ENOUGH_RES) {
            DbSpinUnlock(&tmpResPool->lock);
            return ret;
        }
        ResColPoolT *extendedPool = DbShmPtrToAddr(tmpResPool->extendedPool);
        DbSpinUnlock(&tmpResPool->lock);
        tmpResPool = extendedPool;
    }
    uint32_t totalLabelUsed = ResColGetResPoolTotalLabelUsed(resPool);
    // 没有足够的资源，申请失败
    SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_ENOUGH_RES,
        "not enough res to alloc (pool: %" PRIu16 " count: %" PRIu16 " start: %" PRIu32 "), resname %s poolid %" PRIu32
        " startId %" PRIu32 " cap %" PRIu32 " used %" PRIu32,
        resColId->poolId, resColId->count, resColId->startIndex, resPool->name, resPool->poolId, resPool->startId,
        resPool->capacity, totalLabelUsed);
    return RES_COL_ERR_POOL_NOT_ENOUGH_RES;
}

// 申请指定的resid
static StatusInter ResPoolAllocSpecResId(ResColPoolT *resPool, const ResColIdT *resColId, uint32_t metaId)
{
    DB_POINTER2(resPool, resColId);
    ResColPoolT *tmpResPool = resPool;
    while (tmpResPool) {
        DbSpinLock(&tmpResPool->lock);
        if (tmpResPool->poolId == resColId->poolId) {
            StatusInter ret = ResPoolAllocSpecResIdInner(tmpResPool, resColId, metaId);
            DbSpinUnlock(&tmpResPool->lock);
            return ret;
        }
        ResColPoolT *extendedPool = DbShmPtrToAddr(tmpResPool->extendedPool);
        DbSpinUnlock(&tmpResPool->lock);
        tmpResPool = extendedPool;
    }
    // 没有指定的申请的res pool，申请失败
    SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
        "expected alloc pool not exist in pool list (pool: %" PRIu16 " count: %" PRIu16 " start: %" PRIu32 ")",
        resColId->poolId, resColId->count, resColId->startIndex);
    return RES_COL_ERR_POOL_NOT_EXIST;
}

static StatusInter ResPoolCheckAllocResId(const ResColIdT *resColId)
{
    // 申请指定resid时需要同时指定pool id和startIndex
    // 当startIndex为有效值时，pool id需为有效值
    // 当startIndex为无效值时，按任意分配处理,pool id可为任意值
    if (resColId->startIndex != SE_RES_POOL_MAX_START_INDEX && resColId->poolId == SE_RES_POOL_MAX_POOL_ID) {
        SE_LAST_ERROR(RES_COL_ERR_ID_INVALID_PARA,
            "novalid res pool id (pool: %" PRIu16 " count: %" PRIu16 " start: %" PRIu32 ")", resColId->poolId,
            resColId->count, resColId->startIndex);
        return RES_COL_ERR_ID_INVALID_PARA;
    }

    if (resColId->count > SE_RES_ID_MAX_COUNT) {
        SE_LAST_ERROR(RES_COL_ERR_ID_INVALID_PARA,
            "out of max res count (pool: %" PRIu16 " count: %" PRIu16 " start: %" PRIu32 ")", resColId->poolId,
            resColId->count, resColId->startIndex);
        return RES_COL_ERR_ID_INVALID_PARA;
    }

    return STATUS_OK_INTER;
}

static StatusInter ResPoolAllocSingleResId(ResColPoolT *resPool, ResId *resId, uint32_t metaId)
{
    DB_POINTER2(resPool, resId);
    ResColIdT resColId = {0};
    ResColPaserResColId(*resId, &resColId);
    if (resColId.count == 0) {
        *resId = 0;
        return STATUS_OK_INTER;
    }
    StatusInter ret = ResPoolCheckAllocResId(&resColId);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    // 从指定pool中还是从任意空闲的pool中申请资源
    if (resColId.startIndex == SE_RES_POOL_MAX_START_INDEX) {
        return ResPoolAllocFreeResId(resPool, resId, &resColId, metaId);
    } else {
        return ResPoolAllocSpecResId(resPool, &resColId, metaId);
    }
}

static StatusInter ResColLiteSetTrxInfoForAlloc(
    const ResColPoolRunCtxT *poolCtx, const ResId *resId, uint32_t resCountOfGroup, TrxT *trx, uint32_t metaId)
{
    DB_POINTER3(poolCtx, resId, trx);

    StatusInter ret = TrxLiteUndoLogInit(trx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    UndoLiteRecordT *undoLiteRec = trx->liteTrx.undoLiteRec;
    undoLiteRec->isChLabel = false;
    UndoLiteOpRecord *opRecord = TrxLiteGetNextFreeRecord(undoLiteRec, undoLiteRec->usedUndoRecNum);
    if (!undoLiteRec->hasResColUndoLog) {
        opRecord->record.resColRec.resCount = 0;
    }
    uint32_t resCount = opRecord->record.resColRec.resCount;
    if (resCount >= DM_RES_COL_MAX_COUNT) {
        SE_LAST_ERROR(
            RES_COL_ERR_POOL_TOO_MANY_RES_COUNT, "too many resource pool, resPool count: %" PRIu32 ".", resCount);
        return RES_COL_ERR_POOL_TOO_MANY_RES_COUNT;
    }
    opRecord->recordType = TRX_OP_RES_COL_ALLOC;
    opRecord->record.resColRec.poolLabelId[resCount] = poolCtx->resPool->poolLabelId;
    opRecord->record.resColRec.resIdArr[resCount] = *resId;
    opRecord->record.resColRec.operLabelId[resCount] = metaId;
    opRecord->record.resColRec.resCount++;
    if (!undoLiteRec->hasResColUndoLog) {
        undoLiteRec->hasResColUndoLog = true;
    }
    if (resCountOfGroup == opRecord->record.resColRec.resCount) {
        TrxLiteMoveNextFreeRecord(undoLiteRec);
        undoLiteRec->hasResColUndoLog = false;
    }
    return STATUS_OK_INTER;
}

static StatusInter ResColSetTrxInfoForAlloc(
    ResColPoolRunCtxT *poolCtx, uint32_t resCountOfGroup, ResId *resId, uint32_t metaId)
{
    DB_POINTER2(poolCtx, resId);
    TrxT *trx = (TrxT *)poolCtx->seRunCtx->trx;
    if (trx->base.state != TRX_STATE_ACTIVE) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "trx not active, trx state: %" PRId32 ".", (int32_t)trx->base.state);
        return INTERNAL_ERROR_INTER;
    }
    if (TrxIsLiteTrx(trx)) {
        return ResColLiteSetTrxInfoForAlloc(poolCtx, resId, resCountOfGroup, trx, metaId);
    }
    // 记录undo
    UndoRowOpInfoT rowOpInfo = {0};
    rowOpInfo.isRetained = false;
    rowOpInfo.isPersistent = false;
    rowOpInfo.resType = TRX_RES_RES_COL;
    rowOpInfo.labelType = RESOURCE_POOL;
    rowOpInfo.opType = TRX_OP_RES_COL_ALLOC;
    rowOpInfo.labelId = poolCtx->resPool->poolLabelId;
    rowOpInfo.rowBuf = (uint8_t *)resId;
    rowOpInfo.rowSize = (uint32_t)sizeof(ResId);
    rowOpInfo.operLabelId = metaId;
    StatusInter ret = TrxUndoReportRowOperation(poolCtx->seRunCtx->undoCtx, trx, &rowOpInfo, NULL);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "set alloc trx info unsucc, trxId:%" PRIu64, trx->base.trxId);
    }
    return ret;
}

Status ResColPoolAllocResIdImpl(ResColPoolRunHdl poolHdl, uint32_t resCountOfGroup, ResId *resId, uint32_t metaId)
{
    DB_POINTER2(poolHdl, resId);
    ResColPoolRunCtxT *poolCtx = (ResColPoolRunCtxT *)poolHdl;
    DB_POINTER2(poolCtx->seRunCtx, poolCtx->resPool);

    StatusInter ret = ResPoolAllocSingleResId(poolCtx->resPool, resId, metaId);
    if (ret != STATUS_OK_INTER) {
        return DbGetExternalErrno(ret);
    }

    // 记录申请res id的undo日志
    ret = ResColSetTrxInfoForAlloc(poolCtx, resCountOfGroup, resId, metaId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "set trx info for alloc unsucc");
        Status retTmp = ResColPoolReleaseResIdImpl(poolHdl, resId, false, metaId);
        if (retTmp != GMERR_OK) {
            SE_ERROR(DbGetStatusInterErrno(retTmp), "release res id after set trx info unsucc");
        }
    }
    return DbGetExternalErrno(ret);
}

static StatusInter ResColSetTrxInfoForRelease(ResColPoolRunCtxT *poolCtx, ResId *resId, uint32_t metaId)
{
    DB_POINTER(poolCtx);
    TrxT *trx = (TrxT *)poolCtx->seRunCtx->trx;
    if (trx->base.state != TRX_STATE_ACTIVE) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "trx not active, trx state: %" PRId32 ".", (int32_t)trx->base.state);
        return INTERNAL_ERROR_INTER;
    }
    if (TrxIsLiteTrx(trx)) {  // 轻量化事务下删除操作不记undo，都是真正删除
        return STATUS_OK_INTER;
    }
    // 记录undo
    UndoRowOpInfoT rowOpInfo = {0};
    rowOpInfo.isRetained = false;
    rowOpInfo.isPersistent = false;
    rowOpInfo.resType = TRX_RES_RES_COL;
    rowOpInfo.labelType = RESOURCE_POOL;
    rowOpInfo.opType = TRX_OP_RES_COL_RELEASE;
    rowOpInfo.labelId = poolCtx->resPool->poolLabelId;
    rowOpInfo.rowBuf = (uint8_t *)resId;
    rowOpInfo.rowSize = (uint32_t)sizeof(ResId);
    rowOpInfo.operLabelId = metaId;
    StatusInter ret = TrxUndoReportRowOperation(poolCtx->seRunCtx->undoCtx, trx, &rowOpInfo, NULL);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "set release trx info unsucc, trxId:%" PRIu64, trx->base.trxId);
    }
    return ret;
}

Status ResColPoolReleaseResIdImpl(ResColPoolRunHdl poolHdl, ResId *resId, bool isMarkRelease, uint32_t metaId)
{
    DB_POINTER2(poolHdl, resId);
    ResColPoolRunCtxT *poolCtx = (ResColPoolRunCtxT *)poolHdl;
    DB_POINTER2(poolCtx->seRunCtx, poolCtx->resPool);
    StatusInter ret = STATUS_OK_INTER;

    // 标记删除流程，只会记录Undo日志，真正归还res在commit/rollback流程
    if (isMarkRelease) {
        ret = ResColSetTrxInfoForRelease(poolCtx, resId, metaId);
        return DbGetExternalErrno(ret);
    }

    // 真正物理归还资源
    ret = ResPoolReleaseSingleResId(poolCtx->resPool, *resId, metaId);
    if (ret != STATUS_OK_INTER) {
        // 理论上不应该出现释放失败的场景，释放失败后，有可能会导致后续不可预期的错误
        SE_ERROR(ret, "ResPool release resId %" PRIu64 "unsucc.", *resId);
    }
    return DbGetExternalErrno(ret);
}

// 在检测资源池是否是否绑定成环时调用
// 在做检测时，绑定操作实际还未发生，需要特殊处理next指针，模拟出“已绑定成环”的情况
static ResColPoolT *ResColExtendedPoolGetNext(const ResColPoolT *resPool, const ResColExtPoolLinkPointT *linkPoint)
{
    DB_POINTER2(resPool, linkPoint);
    // 实际上没有绑定，所以直接返回下一跳的respool
    if (resPool->poolId == linkPoint->linkPointPrev->poolId) {
        return linkPoint->linkPointNext;
    }
    if (!DbIsShmPtrValid(resPool->extendedPool)) {
        return NULL;
    }
    return DbShmPtrToAddr(resPool->extendedPool);
}

// 通过快慢指针的方式来检测是否会循环绑定，外层已经加mgr锁
static StatusInter ResColExtendedPoolHasCycle(ResColPoolT *slowHead, ResColPoolT *fastHead)
{
    DB_POINTER2(slowHead, fastHead);
    // 自身指向指针也作为成环情况处理
    if (slowHead->poolId == fastHead->poolId) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_REFERENCE_HAS_CYCLE, "circulating references between res pools");
        return RES_COL_ERR_POOL_REFERENCE_HAS_CYCLE;
    }
    ResColPoolT *slow = slowHead;
    ResColPoolT *fast = fastHead;
    // 检测如果将slow->fast，是否成环，因此需要建立虚拟指针，模拟成slow->fast的指向关系，
    ResColExtPoolLinkPointT linkPoint = {
        .linkPointPrev = slow,
        .linkPointNext = fast,
    };
    while (slow->poolId != fast->poolId) {
        fast = ResColExtendedPoolGetNext(fast, &linkPoint);
        if (fast == NULL) {
            return STATUS_OK_INTER;
        }
        fast = ResColExtendedPoolGetNext(fast, &linkPoint);
        if (fast == NULL) {
            return STATUS_OK_INTER;
        }
        slow = ResColExtendedPoolGetNext(slow, &linkPoint);
        if (SECUREC_UNLIKELY(!slow)) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "resCol null slow pointer found.");
            return UNEXPECTED_NULL_VALUE_INTER;
        }
    }
    SE_LAST_ERROR(RES_COL_ERR_POOL_REFERENCE_HAS_CYCLE, "circulating references between res pools");
    return RES_COL_ERR_POOL_REFERENCE_HAS_CYCLE;
}

Status ResColBindExtendedPoolImpl(ShmemPtrT resPoolShm, ShmemPtrT extendedPoolShm)
{
    ResColPoolT *resPool = DbShmPtrToAddr(resPoolShm);
    if (resPool == NULL) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
            "bind pool unsucc, novalid res pool (segid: %" PRIu32 " offset: %" PRIu32 ")", resPoolShm.segId,
            resPoolShm.offset);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    ResColPoolT *extendedPool = DbShmPtrToAddr(extendedPoolShm);
    if (extendedPool == NULL) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
            "bind pool unsucc, novalid extended pool(segid: %" PRIu32 " offset: %" PRIu32 ")", extendedPoolShm.segId,
            extendedPoolShm.offset);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    // 检测绑定是否会成环
    ResColMgrT *resColMgr = ResColGetMgr();
    if (SECUREC_UNLIKELY(resColMgr == NULL)) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbSpinLock(&resColMgr->lock);
    StatusInter ret = ResColExtendedPoolHasCycle(resPool, extendedPool);
    if (ret != STATUS_OK_INTER) {
        DbSpinUnlock(&resColMgr->lock);
        return DbGetExternalErrno(ret);
    }

    // 涉及两个资源池加锁的时候，必须按照资源池指向的方向加锁
    ResPoolLock(&resPool->lock, &extendedPool->lock);
    if (DbIsShmPtrValid(resPool->extendedPool)) {
        SE_LAST_ERROR(RES_POOL_ALREADY_BOUND_INTER,
            "bind pool unsucc, already bind extended (poolid: %" PRIu32 " ext seg: %" PRIu32 " offset: %" PRIu32 ")",
            resPool->poolId, resPool->extendedPool.segId, resPool->extendedPool.offset);
        ResPoolUnlock(&resPool->lock, &extendedPool->lock);
        DbSpinUnlock(&resColMgr->lock);
        return GMERR_RESOURCE_POOL_ALREADY_BOUND;
    }
    resPool->extendedPool = extendedPoolShm;
    extendedPool->poolRefCnt += 1;
    ret = ResColSetResPoolExtendResPoolBind(resPool, extendedPool);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(
            RESOURCE_POOL_ERROR_INTER, "extend respool label bind full, pool id: %" PRIu32 "", extendedPool->poolId);
        ResPoolUnlock(&resPool->lock, &extendedPool->lock);
        DbSpinUnlock(&resColMgr->lock);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    extendedPool->labelRefCnt += resPool->labelRefCnt;
    ResPoolUnlock(&resPool->lock, &extendedPool->lock);
    DbSpinUnlock(&resColMgr->lock);
    return GMERR_OK;
}

Status ResColExtendedPoolIsExistedImpl(ShmemPtrT resPoolShm, bool *isExisted)
{
    DB_POINTER(isExisted);
    ResColPoolT *resPool = DbShmPtrToAddr(resPoolShm);
    if (resPool == NULL) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
            "unbind pool unsucc, novalid res pool (segid: %" PRIu32 " offset: %" PRIu32 ")", resPoolShm.segId,
            resPoolShm.offset);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    if (!DbIsShmPtrValid(resPool->extendedPool)) {
        *isExisted = false;
    } else {
        *isExisted = true;
    }
    return GMERR_OK;
}

Status ResColUnbindExtendedPoolImpl(ShmemPtrT resPoolShm)
{
    ResColPoolT *resPool = DbShmPtrToAddr(resPoolShm);
    if (resPool == NULL) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
            "unbind pool unsucc, novalid res pool (segid: %" PRIu32 " offset: %" PRIu32 ")", resPoolShm.segId,
            resPoolShm.offset);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    if (!DbIsShmPtrValid(resPool->extendedPool)) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_UNBIND_FAILED, "unbind pool unsucc, novalid extended pool (poolid: %" PRIu32 ")",
            resPool->poolId);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    ResColPoolT *extendedPool = DbShmPtrToAddr(resPool->extendedPool);
    if (extendedPool == NULL) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
            "unbind pool unsucc, novalid extended pool (segid: %" PRIu32 " offset: %" PRIu32 ")",
            resPool->extendedPool.segId, resPool->extendedPool.offset);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    ResPoolLock(&resPool->lock, &extendedPool->lock);
    uint32_t totalLabelUsed = ResColGetExtendResPoolTotalLabelUsed(resPool, extendedPool);
    if (totalLabelUsed > 0) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_UNBIND_FAILED,
            "unbind ext pool unsucc, still been used(pool id: %" PRIu32 " used res: %" PRIu32 ")", extendedPool->poolId,
            totalLabelUsed);
        ResPoolUnlock(&resPool->lock, &extendedPool->lock);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    if (extendedPool->poolRefCnt == 0) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_UNBIND_FAILED,
            "unbind pool unsucc, extended pool not bound (ext poolid: %" PRIu32 ")", extendedPool->poolRefCnt);
        ResPoolUnlock(&resPool->lock, &extendedPool->lock);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    resPool->extendedPool = DB_INVALID_SHMPTR;
    extendedPool->poolRefCnt -= 1;
    if (extendedPool->poolRefCnt == 0) {
        extendedPool->labelRefCnt = 0;
    }
    ResPoolUnlock(&resPool->lock, &extendedPool->lock);
    return GMERR_OK;
}

Status ResColBindResPoolToLabelImpl(ShmemPtrT resPoolShm, uint32_t metaId)
{
    ResColPoolT *resPool = DbShmPtrToAddr(resPoolShm);
    if (resPool == NULL) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
            "bind table unsucc, novalid res pool (segid: %" PRIu32 " offset: %" PRIu32 ")", resPoolShm.segId,
            resPoolShm.offset);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    Status ret = GMERR_OK;
    ResColPoolT *extendedPool = DbShmPtrToAddr(resPool->extendedPool);
    if (extendedPool != NULL) {
        ret = ResColBindResPoolToLabelImpl(resPool->extendedPool, metaId);
        if (ret != GMERR_OK) {
            SE_LAST_ERROR(RESOURCE_POOL_ERROR_INTER,
                "bind extend pool label unsucc(segid: %" PRIu32 " offset: %" PRIu32 ")", resPool->extendedPool.segId,
                resPool->extendedPool.offset);
            return ret;
        }
    }
    DbSpinLock(&resPool->lock);
    ret = ResColSetResPoolLabelIsBind(resPool, metaId);
    if (ret == GMERR_OK) {
        resPool->labelRefCnt++;
    }
    DbSpinUnlock(&resPool->lock);
    return ret;
}

Status ResColUnbindResPoolToLabelImpl(ShmemPtrT resPoolShm, uint32_t metaId)
{
    ResColPoolT *resPool = DbShmPtrToAddr(resPoolShm);
    if (resPool == NULL) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
            "unbind table unsucc, novalid res pool (segid: %" PRIu32 " offset: %" PRIu32 ")", resPoolShm.segId,
            resPoolShm.offset);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    Status ret = GMERR_OK;
    ResColPoolT *extendedPool = DbShmPtrToAddr(resPool->extendedPool);
    if (extendedPool != NULL) {
        ret = ResColUnbindResPoolToLabelImpl(resPool->extendedPool, metaId);
        if (ret != GMERR_OK) {
            SE_LAST_ERROR(RES_COL_ERR_POOL_UNBIND_FAILED,
                "unbind extend pool label unsucc(segid: %" PRIu32 " offset: %" PRIu32 ")", resPool->extendedPool.segId,
                resPool->extendedPool.offset);
            return ret;
        }
    }
    DbSpinLock(&resPool->lock);
    uint32_t labelUsed = 0;
    if (resPool->labelRefCnt == 0 || ResColGetResPoolLabelUsed(resPool, metaId, &labelUsed) != GMERR_OK) {
        SE_LAST_ERROR(
            RES_COL_ERR_POOL_NOT_BOUND, "unbind table unsucc, no table bound (pool id: %" PRIu32 ")", resPool->poolId);
        DbSpinUnlock(&resPool->lock);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    if (labelUsed > 0) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_UNBIND_FAILED,
            "unbind table unsucc, still has used res (pool id: %" PRIu32 " used res: %" PRIu32 ")", resPool->poolId,
            labelUsed);
        DbSpinUnlock(&resPool->lock);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    ret = ResColSetResPoolLabelUnBind(resPool, metaId);
    if (ret == GMERR_OK) {
        resPool->labelRefCnt--;
    }
    DbSpinUnlock(&resPool->lock);
    return ret;
}

StatusInter ResPoolHandleAllocAndInit(const ResColPoolOpenCfgT *cfg, ResColPoolRunHdl *resPoolHdl)
{
    DB_POINTER2(cfg, resPoolHdl);

    ResColPoolT *resPool = (ResColPoolT *)DbShmPtrToAddr(cfg->resPool);
    if (resPool == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "resPool is NULL. (segid: %" PRIu32 " offset: %" PRIu32 ")",
            cfg->resPool.segId, cfg->resPool.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    void *sessionMemCtx = (cfg->seRunCtx)->sessionMemCtx;
    // 申请资源列容器的运行上下文, 在断连流程释放
    ResColPoolRunCtxT *resPoolRunHdl = DbDynMemCtxAlloc(sessionMemCtx, sizeof(ResColPoolRunCtxT));
    if (resPoolRunHdl == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "malloc res pool ctx unsucc(%s)", resPool->name);
        return OUT_OF_MEMORY_INTER;
    }
    DbSpinLock(&resPool->lock);
    if (resPool->openCnt >= RES_COL_POOL_OPEN_CNT_MAX_NUM) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_OPEN_FAILED, "res pool open unsucc (%s opencnt:%" PRIu32 ")", resPool->name,
            resPool->openCnt);
        DbSpinUnlock(&resPool->lock);
        DbDynMemCtxFree(sessionMemCtx, resPoolRunHdl);
        resPoolRunHdl = NULL;
        return RES_COL_ERR_POOL_OPEN_FAILED;
    }
    resPool->openCnt++;
    DbSpinUnlock(&resPool->lock);

    resPoolRunHdl->resPool = resPool;
    resPoolRunHdl->seRunCtx = cfg->seRunCtx;
    *resPoolHdl = resPoolRunHdl;
    return STATUS_OK_INTER;
}

// 将res pool作为一种事务性资源存在trx里，注意事务性资源和资源列的区别
static StatusInter ResColPoolOpenSaveTrxRes(const ResColPoolRunCtxT *newCtx, const ResColPoolOpenCfgT *config)
{
    DB_POINTER2(newCtx, config);

    SeRunCtxT *seRunCtx = newCtx->seRunCtx;
    TrxT *trx = (TrxT *)seRunCtx->trx;
    if (trx->base.state == TRX_STATE_NOT_STARTED) {
        SE_LAST_ERROR(TRANS_MODE_MISMATCH_INTER, "novalid trx(%" PRIu64 ") state %" PRIu32 ".", trx->base.trxId,
            (uint32_t)trx->base.state);
        return TRANS_MODE_MISMATCH_INTER;
    }
    if (trx->base.state == TRX_STATE_ACTIVE) {
        DmVertexLabelT *vtxLabel = (DmVertexLabelT *)config->vertexLabel;
        if (vtxLabel == NULL) {
            SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "null vertexlabel found.");
            return UNEXPECTED_NULL_VALUE_INTER;
        }

        SeTrxContainerCtxT *trxCntrCtx = NULL;
        bool isFirstResColPoolOpen = false;
        StatusInter ret = TrxStoreContainerCtx(trx, newCtx->resPool->poolLabelId, &trxCntrCtx, &isFirstResColPoolOpen);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "trx store label unsucc");
            return ret;
        }
        if (isFirstResColPoolOpen) {
            trxCntrCtx->trxCntrCtx.ctxHead.type = TRX_RES_POOL_HANDLE;
            trxCntrCtx->trxCntrCtx.label = vtxLabel;
            trxCntrCtx->trxCntrCtx.containerShmAddr = config->resPool;
            TrxSetContainerIsUsed(trxCntrCtx);
            // ResPoolHandle
            if (trxCntrCtx->trxCntrCtx.cntrRunHdl != NULL) {
                return ret;
            }
            ResColPoolOpenCfgT resPoolHandleAllocCfg = {.resPool = config->resPool,
                .seRunCtx = trx->seRunCtx,
                .usrMemCtx = trx->seRunCtx->sessionMemCtx,
                .vertexLabel = vtxLabel};
            ret = ResPoolHandleAllocAndInit(
                &resPoolHandleAllocCfg, (ResColPoolRunHdl *)&trxCntrCtx->trxCntrCtx.cntrRunHdl);
            if (SECUREC_UNLIKELY(ret != STATUS_OK_INTER)) {
                TrxSetContainerNotUsed(trxCntrCtx);
                SE_ERROR(ret, "Slot(%" PRIu16 ") ResColPoolOpenImpl(%" PRIu32 ") alloc and init unsucc",
                    trx->base.trxSlot, newCtx->resPool->poolLabelId);
                return ret;
            }
            trxCntrCtx->trxCntrCtx.ctxHead.isOpened = true;
        }
    }
    return STATUS_OK_INTER;
}

Status ResColPoolOpenImpl(const ResColPoolOpenCfgT *config, ResColPoolRunHdl *poolRunHandle)
{
    DB_POINTER4(config, config->seRunCtx, config->usrMemCtx, poolRunHandle);

    void *sessionMemCtx = (config->seRunCtx)->sessionMemCtx;
    ResColPoolT *resPool = (ResColPoolT *)DbShmPtrToAddr(config->resPool);
    if (resPool == NULL) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
            "res pool open unsucc, novalid res pool (segid: %" PRIu32 " offset: %" PRIu32 ")", config->resPool.segId,
            config->resPool.offset);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    DB_ASSERT(resPool->magicNum == RES_COL_POOL_VALID_MAGIC_NUM);
    // 申请资源列容器的运行上下文, 在断连流程释放
    ResColPoolRunCtxT *newCtx = DbDynMemCtxAlloc(sessionMemCtx, sizeof(ResColPoolRunCtxT));
    if (newCtx == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "malloc res pool ctx unsucc(%s)", resPool->name);
        return GMERR_OUT_OF_MEMORY;
    }

    DbSpinLock(&resPool->lock);
    if (resPool->openCnt >= RES_COL_POOL_OPEN_CNT_MAX_NUM) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_OPEN_FAILED, "res pool open unsucc (%s opencnt:%" PRIu32 ")", resPool->name,
            resPool->openCnt);
        DbSpinUnlock(&resPool->lock);
        DbDynMemCtxFree(sessionMemCtx, newCtx);
        newCtx = NULL;
        return GMERR_RESOURCE_POOL_ERROR;
    }
    resPool->openCnt++;
    DbSpinUnlock(&resPool->lock);

    newCtx->resPool = resPool;
    newCtx->seRunCtx = config->seRunCtx;
    StatusInter ret = ResColPoolOpenSaveTrxRes(newCtx, config);
    if (ret != STATUS_OK_INTER) {
        DbDynMemCtxFree(sessionMemCtx, newCtx);
        newCtx = NULL;
        SE_ERROR(ret, "cannot save trx res (%s)", resPool->name);
        return DbGetExternalErrno(ret);
    }
    *poolRunHandle = newCtx;
    return GMERR_OK;
}

Status ResColPoolCloseImpl(ResColPoolRunHdl poolRunHandle)
{
    DB_POINTER(poolRunHandle);
    ResColPoolRunCtxT *ctx = (ResColPoolRunCtxT *)poolRunHandle;
    ResColPoolT *resPool = ctx->resPool;
    if (resPool == NULL) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST, "res pool close unsucc, resPool ptr null");
        return GMERR_RESOURCE_POOL_ERROR;
    }
    DB_ASSERT(resPool->magicNum == RES_COL_POOL_VALID_MAGIC_NUM);

    DbSpinLock(&resPool->lock);
    if (resPool->openCnt == 0) {
        DbSpinUnlock(&resPool->lock);
        SE_LAST_ERROR(RES_COL_ERR_POOL_CLOSE_FAILED, "res pool not open");
        return GMERR_RESOURCE_POOL_ERROR;
    }
    resPool->openCnt--;
    DbSpinUnlock(&resPool->lock);
    void *sessionMemCtx = ctx->seRunCtx->sessionMemCtx;
    ctx->seRunCtx = NULL;
    DbDynMemCtxFree(sessionMemCtx, ctx);
    ctx = NULL;
    return GMERR_OK;
}

uint32_t ResColPoolGetPoolIdImpl(ResColPoolRunHdl poolHdl)
{
    DB_POINTER(poolHdl);
    ResColPoolRunCtxT *ctx = (ResColPoolRunCtxT *)poolHdl;
    return ctx->resPool->poolId;
}

Status ResColPoolReleaseAllResImpl(ShmemPtrT resPoolShm)
{
    ResColPoolT *resPool = DbShmPtrToAddr(resPoolShm);
    if (resPool == NULL) {
        SE_LAST_ERROR(RES_COL_ERR_POOL_NOT_EXIST,
            "release res unsucc, novalid res pool (segid: %" PRIu32 " offset: %" PRIu32 ")", resPoolShm.segId,
            resPoolShm.offset);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    DbSpinLock(&resPool->lock);
    if (resPool->openCnt != 0) {
        DB_ASSERT(false);  // 预期应该有上层做好并发控制保护，此时不应该有线程open过没有close
        SE_LAST_ERROR(RES_COL_ERR_POOL_CLOSE_FAILED, "res pool not close, openCnt:%" PRIu32, resPool->openCnt);
        DbSpinUnlock(&resPool->lock);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    uint32_t *bitmap = DbShmPtrToAddr(resPool->bitmapShmPtr);
    (void)memset_s(bitmap, resPool->bitmapSizeAligned, 0x00, resPool->bitmapSizeAligned);
    // 只清理used, 否则后续unbindLabel会找不到
    for (int32_t i = 0; i < DM_RES_POOL_MAX_TABLE_NUM; i++) {
        resPool->labelUsed[i].usedNum = 0;
    }
    resPool->curPos = 0;
    DbSpinUnlock(&resPool->lock);
    return GMERR_OK;
}

void ResColPoolProcAmInit(ResColPoolAmT *am)
{
    am->resColMgrCreate = ResColMgrCreateImpl;
    am->resColCreateResPool = ResColCreateResPoolImpl;
    am->resColDestroyResPoolCheck = ResColDestroyResPoolCheckImpl;
    am->resColDestroyResPool = ResColDestroyResPoolImpl;
    am->resColBindExtendedPool = ResColBindExtendedPoolImpl;
    am->resColExtendedPoolIsExisted = ResColExtendedPoolIsExistedImpl;
    am->resColUnbindExtendedPool = ResColUnbindExtendedPoolImpl;
    am->resColBindResPoolToLabel = ResColBindResPoolToLabelImpl;
    am->resColUnbindResPoolToLabel = ResColUnbindResPoolToLabelImpl;
    am->resColPoolOpen = ResColPoolOpenImpl;
    am->resColPoolClose = ResColPoolCloseImpl;
    am->resColPoolAllocResId = ResColPoolAllocResIdImpl;
    am->resColPoolReleaseResId = ResColPoolReleaseResIdImpl;
    am->resColPoolGetPoolId = ResColPoolGetPoolIdImpl;
    am->resColPoolReleaseAllRes = ResColPoolReleaseAllResImpl;
}

void ResColPoolAmInit(void)
{
    ResColPoolAmT resColFuncHandle = {0};
    ResColPoolProcAmInit(&resColFuncHandle);
    ResColPoolViewAmInit(&resColFuncHandle);
    ResColSetAmFunc(&resColFuncHandle);
}

#ifdef __cplusplus
}
#endif
