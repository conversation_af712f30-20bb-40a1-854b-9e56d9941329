/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: implementation of diskann redo
 * Author: wanyi
 * Create: 2024-02-18
 */

#include "se_diskann_redo_am.h"
#include "se_database.h"
#include "se_diskann_redo_replay.h"
#include "se_diskann_utils.h"
#include "se_define.h"

static void DiskAnnRedoSetEdgeFuncVersion2(
    const DiskAnnCtxT *ctxt, const DiskAnnRedoUndoParamT *redoParam, const DiskAnnEdgeT *edge, RedoRunCtxT *redoCtx)
{
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, redoParam->vertexId.pageId);
    RedoLogWrite(redoCtx, REDO_OP_DISKANN_SET_EDGE, &realPageId, (const uint8_t *)(&(redoParam->edgeOffsetFromVertex)),
        SIZE_OF_EDGE_OFFSET_FROM_VERTEX);
    RedoLogAppend(redoCtx, (const uint8_t *)(&redoParam->vertexId.slotId), SIZE_OF_SLOTID);
    RedoLogAppend(redoCtx, (const uint8_t *)(&redoParam->rawVecDim), SIZE_OF_RAWVEC_DIM);
    RedoLogAppend(redoCtx, (const uint8_t *)(&(ctxt->meta.pqVecDim)), SIZE_OF_PQVEC_DIM);
    RedoLogAppend(redoCtx, (const uint8_t *)(&(redoParam->outDegree)), SIZE_OF_OUTDEGREE);
    RedoLogAppend(redoCtx, (const uint8_t *)(&(edge->numNeighbor)), SIZE_OF_NB_NUM);
    if (edge->numNeighbor != 0) {
        RedoLogAppend(redoCtx, (const uint8_t *)((edge->nexts)), GetNbVertexIdsArrSize(redoParam->outDegree));
        RedoLogAppend(redoCtx, (const uint8_t *)((edge->distance)), GetDistArrSize(true, redoParam->outDegree));
    }
}

void DiskAnnRedoSetEdgeFuncVersionLatest(
    const DiskAnnCtxT *ctxt, const DiskAnnRedoUndoParamT *redoParam, const DiskAnnEdgeT *edge, RedoRunCtxT *redoCtx)
{
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, redoParam->vertexId.pageId);
    DiskAnnRedoUndoParamT redoParamData = *redoParam;
    RedoLogWrite(redoCtx, REDO_OP_DISKANN_SET_EDGE, &realPageId, (const uint8_t *)(&redoParamData),
        sizeof(DiskAnnRedoUndoParamT));
    RedoLogAppend(redoCtx, (const uint8_t *)(&(edge->numNeighbor)), SIZE_OF_NB_NUM);
    if (edge->numNeighbor != 0) {
        RedoLogAppend(redoCtx, (const uint8_t *)((edge->nexts)), GetNbVertexIdsArrSize(redoParamData.outDegree));
        if (redoParamData.reserveDist) {
            RedoLogAppend(redoCtx, (const uint8_t *)((edge->distance)),
                GetDistArrSize(redoParamData.reserveDist, redoParamData.outDegree));
        }
    }
}

void DiskAnnRedoSetEdgeFuncImpl(
    const DiskAnnCtxT *ctxt, const DiskAnnRedoUndoParamT *redoParam, const DiskAnnEdgeT *edge, RedoRunCtxT *redoCtx)
{
    DB_POINTER3(redoParam, edge, redoCtx);
    if (AnnIsFirstVersion4DiskAnn(ctxt->fileVersion)) {
        DiskAnnRedoSetEdgeFuncVersion2(ctxt, redoParam, edge, redoCtx);
    } else {
        DiskAnnRedoSetEdgeFuncVersionLatest(ctxt, redoParam, edge, redoCtx);
    }
}

void DiskAnnRedoSetNodeFuncVersion2(const DiskAnnCtxT *ctxt, const DiskAnnNodeT *node, RedoRunCtxT *redoCtx)
{
    uint16_t pqVecDim = ctxt->meta.pqVecDim;
    uint16_t rawVecDim = ctxt->meta.dim;
    uint16_t outDegree = ctxt->meta.outDegree;
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, node->vertexId.pageId);
    RedoLogWrite(redoCtx, REDO_OP_DISKANN_SET_NODE, &realPageId, (const uint8_t *)(&pqVecDim), SIZE_OF_PQVEC_DIM);
    RedoLogAppend(redoCtx, (const uint8_t *)(&(node->vertexId.slotId)), SIZE_OF_SLOTID);
    RedoLogAppend(redoCtx, (const uint8_t *)(&rawVecDim), SIZE_OF_RAWVEC_DIM);
    RedoLogAppend(redoCtx, (const uint8_t *)(&outDegree), SIZE_OF_OUTDEGREE);
    RedoLogAppend(redoCtx, (const uint8_t *)node, sizeof(DiskAnnVertexT));
    RedoLogAppend(redoCtx, (const uint8_t *)((node->vecs)), GetRawVecSize(32, rawVecDim));  // 32是float类型的比特数
    RedoLogAppend(redoCtx, (const uint8_t *)(&(node->numNeighbor)), SIZE_OF_NB_NUM);
    if (node->numNeighbor != 0) {
        RedoLogAppend(redoCtx, (const uint8_t *)((node->nexts)), GetNbVertexIdsArrSize(outDegree));
        RedoLogAppend(redoCtx, (const uint8_t *)((node->distance)), GetDistArrSize(true, outDegree));
    }
}

void DiskAnnRedoSetNodeFuncVersionLatest(const DiskAnnCtxT *ctxt, const DiskAnnNodeT *node, RedoRunCtxT *redoCtx)
{
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, node->vertexId.pageId);
    DiskAnnRedoUndoParamT redoParam = GetRedoUndoParam(ctxt, node->vertexId);
    RedoLogWrite(
        redoCtx, REDO_OP_DISKANN_SET_NODE, &realPageId, (const uint8_t *)(&redoParam), sizeof(DiskAnnRedoUndoParamT));
    RedoLogAppend(redoCtx, (const uint8_t *)node, sizeof(DiskAnnVertexT));
    RedoLogAppend(redoCtx, (const uint8_t *)((node->vecs)), GetRawVecSize(ctxt->meta.numQuantBit, redoParam.rawVecDim));
    RedoLogAppend(redoCtx, (const uint8_t *)(&(node->numNeighbor)), SIZE_OF_NB_NUM);
    if (node->numNeighbor != 0) {
        RedoLogAppend(redoCtx, (const uint8_t *)((node->nexts)), GetNbVertexIdsArrSize(redoParam.outDegree));
        if (ctxt->meta.reserveDist) {
            RedoLogAppend(redoCtx, (const uint8_t *)((node->distance)),
                GetDistArrSize(ctxt->meta.reserveDist, redoParam.outDegree));
        }
    }
}

void DiskAnnRedoSetNodeFuncImpl(const DiskAnnCtxT *ctxt, const DiskAnnNodeT *node, RedoRunCtxT *redoCtx)
{
    DB_POINTER2(node, redoCtx);
    if (AnnIsFirstVersion4DiskAnn(ctxt->fileVersion)) {
        DiskAnnRedoSetNodeFuncVersion2(ctxt, node, redoCtx);
    } else {
        DiskAnnRedoSetNodeFuncVersionLatest(ctxt, node, redoCtx);
    }
}

void DiskAnnRedoSetFlagFuncImpl(const DiskAnnCtxT *ctxt, DiskAnnAddrT vertexId, uint32_t isDelete, RedoRunCtxT *redoCtx)
{
    DB_POINTER2(ctxt, redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, vertexId.pageId);
    RedoLogWrite(redoCtx, REDO_OP_DISKANN_SET_TAG, &(realPageId), (const uint8_t *)(&isDelete), SIZE_OF_FLAG);
    DiskAnnRedoUndoParamT redoParam = GetRedoUndoParam(ctxt, vertexId);
    RedoLogAppend(redoCtx, (const uint8_t *)(&redoParam), sizeof(DiskAnnRedoUndoParamT));
}

void DiskAnnRedoInitMetaFuncImpl(
    const DiskAnnCtxT *ctxt, DiskAnnPageIdT metaPageId, const DiskAnnMetaT *meta, RedoRunCtxT *redoCtx)
{
    DB_POINTER2(meta, redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, metaPageId);
    RedoLogWrite(redoCtx, REDO_OP_DISKANN_INIT_META, &realPageId, (const uint8_t *)(meta), sizeof(DiskAnnMetaT));
}

// LCOV_EXCL_BR_START
void DiskAnnRedoAppendMetaVertexListFuncImpl(const DiskAnnCtxT *ctxt, DiskAnnPageIdT metaPageId,
    DiskAnnPageIdT startVertexId, DiskAnnPageIdT endVertexId, RedoRunCtxT *redoCtx)
{
    DB_POINTER(redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, metaPageId);
    RedoLogWrite(redoCtx, REDO_OP_DISKANN_APPEND_META_LIST, &realPageId, (const uint8_t *)(&startVertexId),
        sizeof(DiskAnnPageIdT));
    RedoLogAppend(redoCtx, (const uint8_t *)(&endVertexId), sizeof(DiskAnnPageIdT));
}

void DiskAnnRedoResetMetaFuncImpl(
    const DiskAnnCtxT *ctxt, DiskAnnPageIdT metaPageId, const DiskAnnMetaT *meta, RedoRunCtxT *redoCtx)
{
    DB_POINTER2(meta, redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, metaPageId);
    RedoLogWrite(
        redoCtx, REDO_OP_DISKANN_RESET_META, &realPageId, (const uint8_t *)(&(meta->numFrozens)), SIZE_OF_FROZEN_NUMS);
}
// LCOV_EXCL_BR_STOP

void DiskAnnRedoSetNewSlaveFuncImpl(const DiskAnnCtxT *ctxt, const DiskAnnVertexT *vertex, RedoRunCtxT *redoCtx)
{
    DB_POINTER3(ctxt, vertex, redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, vertex->vertexId.pageId);
    RedoLogWrite(
        redoCtx, REDO_OP_DISKANN_SET_NEW_SLAVE, &realPageId, (const uint8_t *)(&vertex->flag), sizeof(uint32_t));
    RedoLogAppend(redoCtx, (const uint8_t *)(&vertex->masterId), sizeof(DiskAnnAddrT));
    RedoLogAppend(redoCtx, (const uint8_t *)(&vertex->preSlaveId), sizeof(DiskAnnAddrT));
    RedoLogAppend(redoCtx, (const uint8_t *)(&vertex->nextSlaveId), sizeof(DiskAnnAddrT));
    DiskAnnRedoUndoParamT redoParam = GetRedoUndoParam(ctxt, vertex->vertexId);
    RedoLogAppend(redoCtx, (const uint8_t *)(&redoParam), sizeof(DiskAnnRedoUndoParamT));
}

void DiskAnnRedoSetNextSlaveIdFuncImpl(
    const DiskAnnCtxT *ctxt, DiskAnnAddrT curVertexId, DiskAnnAddrT nextSlaveId, RedoRunCtxT *redoCtx)
{
    DB_POINTER2(ctxt, redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, curVertexId.pageId);
    RedoLogWrite(
        redoCtx, REDO_OP_DISKANN_SET_NEXT_SLAVE_ID, &realPageId, (const uint8_t *)(&nextSlaveId), sizeof(DiskAnnAddrT));
    DiskAnnRedoUndoParamT redoParam = GetRedoUndoParam(ctxt, curVertexId);
    RedoLogAppend(redoCtx, (const uint8_t *)(&redoParam), sizeof(DiskAnnRedoUndoParamT));
}

void DiskAnnRedoSetPreSlaveIdFuncImpl(
    const DiskAnnCtxT *ctxt, DiskAnnAddrT curVertexId, DiskAnnAddrT preSlaveId, RedoRunCtxT *redoCtx)
{
    DB_POINTER2(ctxt, redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, curVertexId.pageId);
    RedoLogWrite(
        redoCtx, REDO_OP_DISKANN_SET_PRE_SLAVE_ID, &realPageId, (const uint8_t *)(&preSlaveId), sizeof(DiskAnnAddrT));
    DiskAnnRedoUndoParamT redoParam = GetRedoUndoParam(ctxt, curVertexId);
    RedoLogAppend(redoCtx, (const uint8_t *)(&redoParam), sizeof(DiskAnnRedoUndoParamT));
}

// LCOV_EXCL_BR_START
void DiskAnnRedoRemoveFromNbsFuncImpl(const DiskAnnCtxT *ctxt, const DiskAnnRedoUndoParamT *redoParam,
    uint32_t idxToRemove, uint32_t lastIdxInNbs, RedoRunCtxT *redoCtx)
{
    DB_POINTER2(redoParam, redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, redoParam->vertexId.pageId);
    RedoLogWrite(
        redoCtx, REDO_OP_DISKANN_REMOVE_FROM_NBS, &realPageId, (const uint8_t *)(&idxToRemove), sizeof(uint32_t));
    RedoLogAppend(redoCtx, (const uint8_t *)(&lastIdxInNbs), sizeof(uint32_t));
    RedoLogAppend(redoCtx, (const uint8_t *)(&redoParam), sizeof(DiskAnnRedoUndoParamT));
}
// LCOV_EXCL_BR_STOP

void DiskAnnRedoSetFrozensFuncImpl(const DiskAnnCtxT *ctxt, DiskAnnPageIdT metaPageId, DiskAnnAddrT *frozens,
    uint16_t numFrozens, RedoRunCtxT *redoCtx)
{
    DB_POINTER2(frozens, redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, metaPageId);
    RedoLogWrite(
        redoCtx, REDO_OP_DISKANN_SET_FROZENS, &realPageId, (const uint8_t *)(&numFrozens), SIZE_OF_FROZEN_NUMS);
    RedoLogAppend(redoCtx, (const uint8_t *)(frozens), GetFrozenPointsSize(numFrozens));
}

void DiskAnnRedoForSetPageHeadFuncImpl(
    const DiskAnnCtxT *ctxt, DiskAnnPageIdT pageId, uint8_t *pageHead, RedoRunCtxT *redoCtx)
{
    DB_POINTER2(pageHead, redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, pageId);
    PageHeadT *head = GetPageHead(pageHead);
    RedoLogWrite(
        redoCtx, REDO_OP_DISKANN_SET_PAGE_HEAD, &realPageId, (const uint8_t *)&(head->beginPos), SIZE_OF_BEGIN_POS);
    RedoLogAppend(redoCtx, (const uint8_t *)&(head->freeSize), SIZE_OF_FREE_SIZE);
}

void DiskAnnRedoForSetDiskAnnNodeHdrFuncImpl(
    const DiskAnnCtxT *ctxt, DiskAnnPageIdT pageId, uint8_t *pageHead, RedoRunCtxT *redoCtx)
{
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, pageId);
    DiskAnnNodeHdrT *diskAnnNodeHdr = GetDiskAnnNodeHdrFromPageHead(pageHead);
    uint32_t sizeOfDiskAnnNodeHdr = GetDiskAnnNodeHdrSize(diskAnnNodeHdr);
    RedoLogWrite(redoCtx, REDO_OP_DISKANN_SET_DISKANN_NODE_HDR, &realPageId, (const uint8_t *)diskAnnNodeHdr,
        sizeOfDiskAnnNodeHdr);
}

void DiskAnnRedoForSetMetaFuncImpl(const DiskAnnCtxT *ctxt, RedoRunCtxT *redoCtx)
{
    DB_POINTER2(ctxt, redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, ctxt->meta.metaPageId);
    RedoLogWrite(redoCtx, REDO_OP_DISKANN_SET_META, &realPageId, (const uint8_t *)&(ctxt->meta), sizeof(DiskAnnMetaT));
}

// LCOV_EXCL_BR_START
void DiskAnnRedoForSetDelNodeListHdrFuncImpl(
    const DiskAnnCtxT *ctxt, DiskAnnPageIdT pageId, uint8_t *pageHead, RedoRunCtxT *redoCtx)
{
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, pageId);
    DiskAnnDelNodeListT *delListHdr = GetDiskAnnNodeListFromPageHead(pageHead);
    uint32_t delListSize = GetDiskAnnNodeListSize(delListHdr);
    RedoLogWrite(
        redoCtx, REDO_OP_DISKANN_SET_DISKANN_DEL_LIST_HDR, &realPageId, (const uint8_t *)delListHdr, delListSize);
}
// LCOV_EXCL_BR_STOP

void DiskAnnRedoForResetNodeFuncImpl(
    const DiskAnnCtxT *ctxt, DiskAnnAddrT vertexId, uint32_t vertexTypeSize, size_t edgeSize, RedoRunCtxT *redoCtx)
{
    DB_POINTER(redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, vertexId.pageId);
    RedoLogWrite(
        redoCtx, REDO_OP_DISKANN_RESET_DISKANN_NODE, &realPageId, (const uint8_t *)&(vertexId), sizeof(DiskAnnAddrT));
    RedoLogAppend(redoCtx, (const uint8_t *)&(vertexTypeSize), SIZE_OF_VERTEX_TYPE_SIZE);
    RedoLogAppend(redoCtx, (const uint8_t *)&(edgeSize), SIZE_OF_EDGE_SIZE);
}

void DiskAnnRedoForAddToNodeListFuncImpl(
    const DiskAnnCtxT *ctxt, DiskAnnAddrT delAddr, DiskAnnAddrT delVertexId, uint16_t nodeSize, RedoRunCtxT *redoCtx)
{
    DB_POINTER(redoCtx);
    PageIdT realPageId = DeserializeDiskAnnPageId(ctxt->pageMgr, delAddr.pageId);
    RedoLogWrite(
        redoCtx, REDO_OP_DISKANN_ADD_TO_NODE_LIST, &realPageId, (const uint8_t *)&(delAddr), sizeof(DiskAnnAddrT));
    RedoLogAppend(redoCtx, (const uint8_t *)&(delVertexId), sizeof(DiskAnnAddrT));
    RedoLogAppend(redoCtx, (const uint8_t *)&(nodeSize), sizeof(uint16_t));
}

// LCOV_EXCL_BR_START
static StatusInter DiskAnnRedoReplayFuncRegister(SeInstanceHdT seIns)
{
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_SET_EDGE, DiskAnnIndexSetEdgeRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_SET_NODE, DiskAnnIndexSetNodeRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_SET_TAG, DiskAnnIndexSetTagRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_INIT_META, DiskAnnIndexInitMetaRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_APPEND_META_LIST, DiskAnnIndexMetaAppendRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_RESET_META, DiskAnnIndexResetMetaRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_SET_NEW_SLAVE, DiskAnnIndexSetNewSlaveRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_SET_NEXT_SLAVE_ID, DiskAnnIndexSetNextSlaveIdRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_SET_PRE_SLAVE_ID, DiskAnnIndexSetPreSlaveRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_REMOVE_FROM_NBS, DiskAnnIndexRemoveFromNbsRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_SET_FROZENS, DiskAnnIndexSetFrozensRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_SET_PAGE_HEAD, DiskAnnIndexSetPageHeadRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_SET_DISKANN_NODE_HDR, DiskAnnIndexSetDiskAnnNodeHdrRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_SET_META, DiskAnnIndexSetMetaRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_SET_DISKANN_DEL_LIST_HDR, DiskAnnIndexSetDelNodeListHdrRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_RESET_DISKANN_NODE, DiskAnnIndexResetNodeRedoReplay);
    REG_REPLAY_FUNC(seIns, REDO_OP_DISKANN_ADD_TO_NODE_LIST, DiskAnnRedoForAddToNodeListRedoReplay);
    return STATUS_OK_INTER;
}
// LCOV_EXCL_BR_STOP

void DiskAnnIndexRedoAmInit(SeInstanceHdT seIns)
{
    DiskAnnIndexRedoAmT redoAm = {
        .redoSetEdgeFunc = DiskAnnRedoSetEdgeFuncImpl,
        .redoSetNodeFunc = DiskAnnRedoSetNodeFuncImpl,
        .redoSetFlagFunc = DiskAnnRedoSetFlagFuncImpl,
        .redoInitMetaFunc = DiskAnnRedoInitMetaFuncImpl,
        .redoAppendMetaVertexListFunc = DiskAnnRedoAppendMetaVertexListFuncImpl,
        .redoResetMetaFunc = DiskAnnRedoResetMetaFuncImpl,
        .redoSetNewSlaveFunc = DiskAnnRedoSetNewSlaveFuncImpl,
        .redoSetNextSlaveIdFunc = DiskAnnRedoSetNextSlaveIdFuncImpl,
        .redoSetPreSlaveIdFunc = DiskAnnRedoSetPreSlaveIdFuncImpl,
        .redoRemoveFromNbsFunc = DiskAnnRedoRemoveFromNbsFuncImpl,
        .redoSetFrozensFunc = DiskAnnRedoSetFrozensFuncImpl,
        .redoSetPageHeadFunc = DiskAnnRedoForSetPageHeadFuncImpl,
        .redoSetDiskAnnNodeHdrFunc = DiskAnnRedoForSetDiskAnnNodeHdrFuncImpl,
        .redoSetMeta = DiskAnnRedoForSetMetaFuncImpl,
        .redoSetDelNodeListHdr = DiskAnnRedoForSetDelNodeListHdrFuncImpl,
        .redoResetNode = DiskAnnRedoForResetNodeFuncImpl,
        .redoAddToNodeList = DiskAnnRedoForAddToNodeListFuncImpl,
    };
    DiskAnnIndexRedoSetAmFunc(&redoAm);
    (void)DiskAnnRedoReplayFuncRegister(seIns);
}
