/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description:
 * Author: wa<PERSON><PERSON><PERSON>
 * Create: 2021/4/27
 */
#ifndef SE_UNDO_TRX_RESOURCE_H
#define SE_UNDO_TRX_RESOURCE_H

#include "se_heap_inner.h"
#include "se_undo.h"
#include "se_trx.h"
#include "se_common.h"
#include "se_trx_lite.h"
#include "db_internal_error.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SE_ROLLBACK_RETRY_TIMES 1

#ifdef FEATURE_SIMPLEREL
#define UNDO_TTREE_INSERT 1
#define UNDO_TTREE_UPDATE 2
#define UNDO_TTREE_DELETE 3
#define UNDO_TTREE_UPDATE_DEL 4

typedef struct HeapData {
    uint64_t undoTargetRollPtr;  // target前一个数据逻辑addr
    uint64_t targetRollPtr;      // target前一个数据逻辑addr
    HpTupleAddr addr;            // 数据master版本逻辑addr
    HeapTupleBufT tupleBuf;      // 回滚过程被 覆盖/删除 的数据内容
    uint8_t *phyAddr;            // 回滚过程被 覆盖/删除 数据的物理addr
                                 // UNDO_TTREE_UPDATE场景为前一版本物理addr，该数据会迁移到后一版本
} HeapDataT;

IndexCtxT **UndoTTreeIdxInit(TrxT *trx, HeapRunCtxT *heapCtx, DmVertexLabelT *vertexLabel);
void UndoSimpleRelIdx(
    uint32_t secIndexNum, HeapDataT *heapData, HeapRunCtxT *heapCtx, uint8_t type, IndexCtxT **idxCtxArray);
void UndoReleaseAllTTreeIdx(IndexCtxT **idxCtxArray, uint32_t secIndexNum);
#endif

typedef StatusInter (*UndoRecEncodeFunc)(
    const UndoRowOpInfoT *rowOpInfo, uint32_t freeSize, uint8_t *recBuf, uint32_t *recSize);
typedef StatusInter (*UndoRecDecodeFunc)(uint8_t *recBuf, UndoRowOpInfoT *rowOpInfo);
typedef StatusInter (*UndoRecRollBackFunc)(
    const UndoRowOpInfoT *rowOpInfo, uint8_t *pageHdr, uint32_t freeSize, uint8_t *recBuf);

typedef StatusInter (*UndoOpFunc)(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);

typedef struct TrxResourceUndoOps {
    UndoOpFunc undoInsFunc;
    UndoOpFunc undoUpdFunc;
    UndoOpFunc undoDelFunc;
    UndoOpFunc undoAllocFunc;
    UndoOpFunc undoReleaseFunc;
    UndoOpFunc undoIdxBuildFunc;
    UndoOpFunc undoContainerRelabelFunc;
} TrxResourceUndoOpsT;

typedef struct TrxResourceUndoRecOps {
    UndoRecEncodeFunc recEncodeFunc;
    UndoRecDecodeFunc recDecodeFunc;
    UndoRecRollBackFunc recRollBackFunc;
} TrxResourceUndoRecOpsT;

StatusInter TrxUndoGetHeapHandle(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, HpRunHdlT *heapCtx);

void UndoRecoveryReleaseLabelHandle(TrxT *trx, uint32_t labelId, DmLabelTypeE labelType, void *label);

static inline void RecoveryUndoTrxInit(TrxT *trx, TrxTypeE trxType, IsolationLevelE isolation)
{
    trx->base.trxType = trxType;
    trx->trx.base.isolationLevel = isolation;
}

inline static void UndoGetRecBaseInfo(const UndoRecBaseHdrT *recBase, UndoRowOpInfoT *rowOpInfo)
{
    rowOpInfo->isRetained = false;
    rowOpInfo->resType = (UndoTrxResTypeE)recBase->resType;
    rowOpInfo->labelType = (DmLabelTypeE)(recBase->labelType);
    rowOpInfo->opType = (UndoRowOpTypeE)recBase->opType;
    rowOpInfo->labelId = recBase->labelId;
    rowOpInfo->isPersistent = recBase->isPersistent;
    rowOpInfo->isRsm = recBase->isRsm;
}

const TrxResourceUndoRecOpsT *UndoGetTrxResUndoRecordOps(UndoTrxResTypeE resType);
const TrxResourceUndoOpsT *UndoGetTrxResUndoOps(TrxT *trx, UndoTrxResTypeE resType);

// Undo record callback of transactional resources
StatusInter HeapUndoRecEncode(const UndoRowOpInfoT *rowOpInfo, uint32_t freeSize, uint8_t *recBuf, uint32_t *recSize);
StatusInter HeapUndoRecDecode(uint8_t *recBuf, UndoRowOpInfoT *rowOpInfo);
StatusInter HeapUndoRecRollBack(const UndoRowOpInfoT *rowOpInfo, uint8_t *pageHdr, uint32_t freeSize, uint8_t *recBuf);

void HeapLiteUndoRecDecode(const UndoLiteOpRecord *record, UndoRowOpTypeE opType, UndoRowOpInfoT *rowOpInfo);

void ClusteredHashLiteUndoRecDecode(const UndoLiteOpRecord *record, UndoRowOpTypeE opType, UndoRowOpInfoT *rowOpInfo);

StatusInter EdgeTopoUndoRecEncode(
    const UndoRowOpInfoT *rowOpInfo, uint32_t freeSize, uint8_t *recBuf, uint32_t *recSize);
StatusInter EdgeTopoUndoRecDecode(uint8_t *recBuf, UndoRowOpInfoT *rowOpInfo);
StatusInter EdgeTopoUndoRecRollBack(
    const UndoRowOpInfoT *rowOpInfo, uint8_t *pageHdr, uint32_t freeSize, uint8_t *recBuf);

StatusInter ResColUndoRecEncode(const UndoRowOpInfoT *rowOpInfo, uint32_t freeSize, uint8_t *recBuf, uint32_t *recSize);
StatusInter ResColUndoRecDecode(uint8_t *recBuf, UndoRowOpInfoT *rowOpInfo);

StatusInter ResColLiteUndoRecDecode(UndoLiteOpRecord *record, int32_t resColIndex, UndoRowOpInfoT *rowOpInfo);

StatusInter BTreeIndexUndoRecEncode(
    const UndoRowOpInfoT *rowOpInfo, uint32_t freeSize, uint8_t *recBuf, uint32_t *recSize);
StatusInter BTreeIndexUndoRecDecode(uint8_t *recBuf, UndoRowOpInfoT *rowOpInfo);

StatusInter LpasMemIndexUndoRecEncode(
    const UndoRowOpInfoT *rowOpInfo, uint32_t freeSize, uint8_t *recBuf, uint32_t *recSize);
StatusInter LpasMemIndexUndoRecDecode(uint8_t *recBuf, UndoRowOpInfoT *rowOpInfo);

StatusInter LpasMemIndexUndoRecEncode(
    const UndoRowOpInfoT *rowOpInfo, uint32_t freeSize, uint8_t *recBuf, uint32_t *recSize);
StatusInter LpasMemIndexUndoRecDecode(uint8_t *recBuf, UndoRowOpInfoT *rowOpInfo);

StatusInter SpaceUndoRecEncode(const UndoRowOpInfoT *rowOpInfo, uint32_t freeSize, uint8_t *recBuf, uint32_t *recSize);
StatusInter SpaceUndoRecDecode(uint8_t *recBuf, UndoRowOpInfoT *rowOpInfo);

StatusInter DiskAnnIndexUndoRecEncode(
    const UndoRowOpInfoT *rowOpInfo, uint32_t freeSize, uint8_t *recBuf, uint32_t *recSize);
StatusInter DiskAnnIndexUndoRecDecode(uint8_t *recBuf, UndoRowOpInfoT *rowOpInfo);

StatusInter CuUndoRecEncode(const UndoRowOpInfoT *rowOpInfo, uint32_t freeSize, uint8_t *recBuf, uint32_t *recSize);
StatusInter CuUndoRecDecode(uint8_t *recBuf, UndoRowOpInfoT *rowOpInfo);

// Rollback process
StatusInter UndoVertexInsert(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter UndoVertexUpdate(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter UndoVertexDelete(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter UndoVertexCreate(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter UndoVertexRelabel(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter UndoPersistSpaceCreate(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter UndoPersistSpaceFileCreate(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);

StatusInter UndoEdgeInsert(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter UndoEdgeDelete(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter UndoEdgeUpdate(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);

StatusInter SeUndoBTreeExec(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t notUsed);
StatusInter UndoBTreeCreate(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);

StatusInter UndoLpasMemBuild(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t notUsed);

StatusInter UndoDiskAnnBuild(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t notUsed);
StatusInter UndoDiskAnnInsert(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t notUsed);
StatusInter UndoDiskAnnDelete(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t notUsed);

StatusInter UndoResColAllocResId(TrxT *trx, const UndoRowOpInfoT *rowOpInfo);

StatusInter UndoVertexForChInsert(TrxT *trx, const UndoRowOpInfoT *rowOpInfo);
StatusInter UndoVertexForChUpdate(TrxT *trx, const UndoRowOpInfoT *rowOpInfo);

StatusInter UndoCuInsert(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);

// Commit process
StatusInter TrxUndoCommitResColRelease(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter TrxUndoCommitTopoDelete(TrxT *trx, const UndoRowOpInfoT *rowOpInfo);
StatusInter TrxUndoCommitTopoUnlinkOldVersion(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t undoRecAddr,
    TrxIdListT *targetTrxIds, uint64_t *cutOffUndoRecAddr);
StatusInter TrxUndoCommitTopoUnlinkMultiOldVersion(
    TrxT *trx, const UndoRowOpInfoT *cutoffRowOpInfo, uint64_t cutOffUndoRecAddr);

// For recovery
StatusInter RecoveryUndoVertexInsert(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter RecoveryUndoVertexUpdate(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter RecoveryUndoVertexDelete(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter RecoveryUndoVertexCreate(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter RecoveryUndoVertexRelabel(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter RecoveryUndoSpaceCreate(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);
StatusInter RecoveryUndoSpaceFileCreate(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);

StatusInter SeUndoRecoveryBTreeExec(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t notUsed);
StatusInter SeUndoRecoveryBTreeCreate(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);

StatusInter RecoveryUndoDiskAnnBuild(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t notUsed);

StatusInter RecoveryUndoLpasMemBuild(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t notUsed);

StatusInter RecoveryUndoCuInsert(TrxT *trx, const UndoRowOpInfoT *rowOpInfo, uint64_t targetRollPtr);

void UndoKeepThreadAlive(const TrxT *trx, uint64_t *splitStartTime, uint32_t undoTotalCnt, uint32_t *undoProcessedCnt);

void UndoSetRecBaseInfo(UndoRecBaseHdrT *recBase, const UndoRowOpInfoT *rowOpInfo, uint32_t totalSize, bool isRollBack);

#ifdef __cplusplus
}
#endif
#endif  // SE_UNDO_TRX_RESOURCE_H
