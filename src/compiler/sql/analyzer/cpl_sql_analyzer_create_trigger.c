/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: for SQL create trigger analyzer
 * Author: SQL
 * Create: 2024-03-25
 */

#include "cpl_sql_analyzer_create_trigger.h"

#ifndef IDS_HAOTIAN
static inline Status SqlVerifyTriggerUnsupportedFeature(const SqlCreateTriggerStmtT *stmt)
{
    if (stmt->isTemp) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupport temporary trigger.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    if (stmt->actTiming == SQL_TRIG_ACT_TIMING_INSTEAD_OF) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupport trigger with instead-of type.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static Status SqlVerifyTriggerName(SqlCreateTriggerStmtT *stmt, SessionT *session)
{
    char *trigName = stmt->triggerName->name;
    if (SqlCheckNameLength(trigName)) {
        SQL_ERROR_LOG(GMERR_NAME_TOO_LONG, "Sql analyzer unable to create the trigger|%s|, name too long.", trigName);
        return GMERR_NAME_TOO_LONG;
    }
    CataKeyT cataKey = {.dbId = session->dbId, .nspId = session->namespaceId, .labelName = trigName};
    DmTriggerInfoT *trigInfo = DmGetTriggerInfoByNameIfExist(DbGetInstanceByMemCtx(session->memCtx), &cataKey);
    // 当前 if not exists 行为, 与建表、建索引保持一致, 一旦同名立即返错
    if (trigInfo != NULL) {
        SQL_ERROR_LOG(GMERR_DUPLICATE_OBJECT, "Sql analyzer: trigger|%s| already exists.", trigName);
        return GMERR_DUPLICATE_OBJECT;
    }

    return GMERR_OK;
}

static inline Status SqlCheckTriggerActCommon(
    const SqlIndexByT *indexedByCaluse, const SqlWithClauseT *withClause, const DbListT *returningClause)
{
    if (indexedByCaluse == NULL && withClause == NULL && returningClause == NULL) {
        return GMERR_OK;
    }
    SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED,
        "Sql analyzer: unsupport indexedByCaluse, withClause, returningClause for trigger act.");
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static inline Status SqlCheckTriggerInsertAct(NodeT *node)
{
    SqlInsertStmtT *insertStmt = (SqlInsertStmtT *)(void *)node;
    if (insertStmt->value->type == DEFAULT_VALUES) {
        SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupport insert default_values for trigger act.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return SqlCheckTriggerActCommon(NULL, insertStmt->withClause, insertStmt->returningClause);
}

static inline Status SqlCheckTriggerDeletetAct(NodeT *node)
{
    SqlDeleteStmtT *deleteStmt = (SqlDeleteStmtT *)(void *)node;
    return SqlCheckTriggerActCommon(deleteStmt->indexBy, deleteStmt->withClause, deleteStmt->returningClause);
}

static inline Status SqlCheckTriggerUpdatetAct(NodeT *node)
{
    SqlUpdateStmtT *updateStmt = (SqlUpdateStmtT *)(void *)node;
    return SqlCheckTriggerActCommon(updateStmt->indexBy, updateStmt->withClause, updateStmt->returningClause);
}

static Status SqlCheckTriggerActUnsupport(NodeT *node)
{
    NodeTagT actTag = node->tag;
    switch (actTag) {
        case T_SQL_INSERT_STMT:
            return SqlCheckTriggerInsertAct(node);
        case T_SQL_DELETE_STMT:
            return SqlCheckTriggerDeletetAct(node);
        case T_SQL_UPDATE_STMT:
            return SqlCheckTriggerUpdatetAct(node);
        default:
            break;
    }
    SQL_ERROR_LOG(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupport trigActTag|%" PRIu32 "|.", (uint32_t)actTag);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status SqlVerifyTriggerActOnCreate(SessionT *session, const SqlCreateTriggerStmtT *stmt)
{
    const DbListT *triggerActStmt = stmt->triggerActStmt;
    uint32_t actCnt = DbListGetItemCnt(triggerActStmt);
    if (actCnt == 0) {
        SQL_ERROR_LOG(GMERR_DATA_EXCEPTION, "Sql analyzer: none trigger acts when create trigger.");
        return GMERR_DATA_EXCEPTION;
    }
    for (uint32_t i = 0; i < actCnt; i++) {
        NodeT *node = *(NodeT **)DbListItem(triggerActStmt, i);
        if (node == NULL) {
            SQL_ERROR_LOG(GMERR_UNEXPECTED_NULL_VALUE,
                "Sql analyzer: actstmt %" PRIu32 " is null, total is %" PRIu32 " .", i, actCnt);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        Status ret = SqlCheckTriggerActUnsupport(node);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status SqlVerifyCreateTrigger(SessionT *session, SqlCreateTriggerStmtT *stmt, DmVertexLabelT **vertexLabel)
{
    Status ret = SqlVerifyTriggerUnsupportedFeature(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(session->memCtx);
    ret = SqlGetNspIdAndVertexLabelByName(
        dbInstance, session->dbId, NULL, stmt->name, &session->namespaceId, vertexLabel);
    if (ret != GMERR_OK || *vertexLabel == NULL) {
        SQL_ERROR_LOG(GMERR_UNDEFINED_TABLE, "Sql analyzer unable to get vertexlabel(%s) for trigger.", stmt->name);
        return GMERR_UNDEFINED_TABLE;
    }

    ret = SqlVerifyTriggerName(stmt, session);
    if (ret != GMERR_OK) {
        return ret;
    }

    return SqlVerifyTriggerActOnCreate(session, stmt);
}

static Status SqlFillTriggerMetaCom(const SessionT *session, const SqlCreateTriggerStmtT *parsedStmt,
    const DmVertexLabelT *vertexLabel, DmTriggerInfoT *triggerInfo)
{
    uint32_t metaNameLen = DM_STR_LEN(parsedStmt->name);
    triggerInfo->metaCommon.metaName = (char *)DbDynMemCtxAlloc(triggerInfo->memCtx, metaNameLen);
    if (triggerInfo->metaCommon.metaName == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to malloc for meta name for trigger info.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memcpy_s(triggerInfo->metaCommon.metaName, metaNameLen, parsedStmt->name, metaNameLen);
    triggerInfo->metaCommon.dbId = session->dbId;
    triggerInfo->metaCommon.namespaceId = session->namespaceId;
    triggerInfo->metaCommon.metaId = vertexLabel->metaCommon.metaId;

    return GMERR_OK;
}

static Status SqlFillTriggerList(
    const SqlCreateTriggerStmtT *parsedStmt, const DmVertexLabelT *vertexLabel, DmTriggerInfoT *triggerInfo)
{
    triggerInfo->trigList = (DbListT *)DbDynMemCtxAlloc(triggerInfo->memCtx, sizeof(DbListT));
    if (triggerInfo->trigList == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to malloc for trigList of trigger info.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(triggerInfo->trigList, sizeof(DbListT), 0, sizeof(DbListT));
    DbCreateList(triggerInfo->trigList, sizeof(DmTriggerT), triggerInfo->memCtx);

    DmTriggerT dmTrigger = {0};
    dmTrigger.trigNameLen = DM_STR_LEN(parsedStmt->triggerName->name);
    dmTrigger.trigName = (char *)DbDynMemCtxAlloc(triggerInfo->memCtx, dmTrigger.trigNameLen);
    if (dmTrigger.trigName == NULL) {
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer unable to alloc memory for trigger name");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memcpy_s(dmTrigger.trigName, dmTrigger.trigNameLen, parsedStmt->triggerName->name, dmTrigger.trigNameLen);
    dmTrigger.trigDmlType = (DmTriggerTypeE)parsedStmt->orgAct.type;
    dmTrigger.conflictStrategy = DM_RESOLVE_ABORT;  // 默认 abort 策略
    dmTrigger.actTiming = (DmTriggerActTimingE)parsedStmt->actTiming;
    dmTrigger.updateCols = parsedStmt->orgAct.updateCols;
    dmTrigger.trigActStmt = parsedStmt->triggerActStmtStr;
    dmTrigger.whenClause = parsedStmt->whenClauseStr;

    Status ret = DbAppendListItem(triggerInfo->trigList, &dmTrigger);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer: unable to append trigList of trigger info.");
    }
    return ret;
}

static Status SqlFillDmTriggerInfo(
    SessionT *session, SqlCreateTriggerStmtT *parsedStmt, DmVertexLabelT *vertexLabel, SqlIrStmtT *irStmt)
{
    DmTriggerInfoT *triggerInfo = NULL;
    Status ret = DmCreateEmptyTriggerInfoWithMemCtx(irStmt->memCtx, &triggerInfo);
    if (ret != GMERR_OK) {
        SQL_ERROR_LOG(ret, "Sql analyzer: unable to create empty trigger info when create trigger.");
        return ret;
    }
    triggerInfo->memCtx = irStmt->memCtx;

    ret = SqlFillTriggerMetaCom(session, parsedStmt, vertexLabel, triggerInfo);
    if (ret != GMERR_OK) {
        DmDestroyTriggerInfo(irStmt->memCtx, triggerInfo);
        return ret;
    }

    ret = SqlFillTriggerList(parsedStmt, vertexLabel, triggerInfo);
    if (ret != GMERR_OK) {
        DmDestroyTriggerInfo(irStmt->memCtx, triggerInfo);
        return ret;
    }

    CreateTriggerStmtT *node = (CreateTriggerStmtT *)DbDynMemCtxAlloc(irStmt->memCtx, sizeof(CreateTriggerStmtT));
    if (node == NULL) {
        DmDestroyTriggerInfo(irStmt->memCtx, triggerInfo);
        SQL_ERROR_LOG(GMERR_OUT_OF_MEMORY, "Sql analyzer: unable to malloc for trigger stmt.");
        return GMERR_OUT_OF_MEMORY;
    }
    node->trigInfo = triggerInfo;
    node->node.tag = T_CREATE_TRIGGER_STMT;

    irStmt->utilityStmt = (NodeT *)(void *)node;
    irStmt->irPlan = NULL;

    return GMERR_OK;
}

Status SqlAnalyzeCreateTrigger(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt)
{
    DB_POINTER3(session, parsedStmt, irStmt);
    DmVertexLabelT *vertexLabel = NULL;
    SqlCreateTriggerStmtT *createStmt = (SqlCreateTriggerStmtT *)(void *)parsedStmt;
    Status ret = SqlVerifyCreateTrigger(session, (SqlCreateTriggerStmtT *)(void *)parsedStmt, &vertexLabel);
    if (ret != GMERR_OK) {
        return SqlCheckIfNotExists4Create(createStmt->ifNotExists, ret);
    }

    ret = SqlFillDmTriggerInfo(session, (SqlCreateTriggerStmtT *)(void *)parsedStmt, vertexLabel, irStmt);
    (void)CataReleaseVertexLabel(vertexLabel);
    return ret;
}
#else
Status SqlAnalyzeCreateTrigger(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt)
{
    DB_POINTER3(session, parsedStmt, irStmt);
    DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "Sql analyzer: unsupported create trigger.");
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif
