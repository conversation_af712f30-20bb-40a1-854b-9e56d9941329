/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: header file for IR logical operator
 * Author: xuxin
 * Create: 2022-07-15
 */

#ifndef CPL_IR_LOGICAL_OP_H
#define CPL_IR_LOGICAL_OP_H

#include "cpl_ir_op.h"
#include "cpl_ir_aa_schema.h"
#include "cpl_optimizer.h"
#ifdef FEATURE_SQL
#include "dm_data_trigger_info_sql.h"
#include "ee_expression.h"
#endif
#ifdef FEATURE_GQL
#include "dm_meta_pathtrigger_info.h"
#include "dm_meta_complex_path.h"
#endif  // FEATURE_GQL
#ifdef FEATURE_STREAM
#include "cpl_stream_compiler.h"
#include "ee_expression.h"
#endif
#ifdef __cplusplus
extern "C" {
#endif

/************************** 融合逻辑算子相关结构体 ********************************/

/**
 * 逻辑算子：AA_extractTuples
 * 语义：从一个关联数组中提取出非零的三元组
 * 输入：一个关联数组
 * 输出：(row，column，value)
 */
typedef struct {
    OpBaseT base;      // IR_LOGOP_EXTRACTTUPLES, arity = 1
    AASchemaT schema;  // 输出关联数组的schema信息
} OpLogicalAAExtractTuplesT;

/**
 * 逻辑算子：AA_transpose
 * 语义：关联数组的转置
 * 输入：一个关联数组
 * 输出：输入关联数组的转置
 */
typedef struct {
    OpBaseT base;      // IR_LOGOP_TRANSPOSE, arity = 1
    AASchemaT schema;  // 输出关联数组的schema信息
} OpLogicalAATransposeT;

/**
 * 关联数组操作符枚举
 */
typedef enum {
    AA_OP_ADD,
    AA_OP_MULTIPLY,
    AA_OP_MAX,
    AA_OP_MIN,
    AA_OP_UNION,
    AA_OP_INTERSECTION,
} AAOpTypeE;

#ifdef FEATURE_GQL
/**
 * AA_mxm算子应用在不同场景下的功能枚举
 * FUNC_NEXT: 计算topology关系中的上一跳或下一跳
 */
typedef enum { FUNC_NEXT = 0, FUNC_CEIL } AAMxmFuncE;

typedef struct {
    AAOpTypeE aaOpAdd;    // 关联数组操作符1
    AAOpTypeE aaOpMulti;  // 关联数组操作符2
} AAMxmFuncCodeT;

/**
 * @brief 根据操作符判断AA_mxm算子所处场景下的功能
 * @param [IN] op1: 关联数组操作符1
 * @param [IN] op2: 关联数组操作符2
 * @return 功能枚举值
 */
AAMxmFuncE JudgeMxmFunc(AAOpTypeE op1, AAOpTypeE op2);
#endif  // FEATURE_GQL

/**
 * 逻辑算子：AA_mxm
 * 语义：关联数组相乘。
 * 应用场景：选择(对输入表左乘一个带选择行信息的数组，输出表为输入表中满足选择条件的行的集合，类似于关系中的选择select);
            投影(对输入表右乘一个带选择列信息的数组，输出表为输入表中满足选择条件的列的集合，类似于关系中的投影project);
            计算topology关系中的上一跳或下一跳;
            重命名
 * 输入：两个关联数组，AA_BinaryOp/AA_Semiring
 * 输出：一个关联数组
 */
typedef struct {
    OpBaseT base;         // IR_LOGOP_MXM, arity = 2
    AASchemaT schema;     // 输出关联数组的schema信息
    AAOpTypeE aaOpAdd;    // 关联数组操作符1
    AAOpTypeE aaOpMulti;  // 关联数组操作符2
#ifdef FEATURE_GQL
    uint32_t deleteMode;
    DmPathTrigDmlE eventType;
#endif  // FEATURE_GQL
} OpLogicalAAMxmT;

/**
 * 逻辑算子：AA_eWiseAdd
 * 语义：关联数组按元素相加
 * 应用场景：数组的并(可用于表示关系代数中的并和笛卡尔积)
 * 输入：两个关联数组，AA_BinaryOp/AA_Semiring
 * 输出：一个关联数组
 */
typedef struct {
    OpBaseT base;       // IR_LOGOP_EWISEADD, arity = 2
    AASchemaT schema;   // 输出关联数组的schema信息
    AAOpTypeE aaOpAdd;  // 关联数组操作符
} OpLogicalAAeWiseAddT;

/**
 * 逻辑算子：AA_eWiseMult
 * 语义：关联数组按元素相乘
 * 应用场景：数组的交
 * 输入：两个关联数组，AA_BinaryOp/AA_Semiring
 * 输出：一个关联数组
 */
typedef struct {
    OpBaseT base;            // IR_LOGOP_EWISEMULT, arity = 2
    AASchemaT schema;        // 输出关联数组的schema信息
    AAOpTypeE semirOpMulti;  // 半环操作符
} OpLogicalAAeWiseMultT;

/**
 * 逻辑算子：AA_apply
 * 语义：数组按元素执行一个函数
 * 应用场景：过滤条件很复杂的选择
 * 输入：一个关联数组，AA_UnaryOp
 * 输出：一个关联数组
 */
typedef struct {
    OpBaseT base;      // IR_LOGOP_APPLY, arity = 2, 第二个input是函数
    AASchemaT schema;  // 输出关联数组的schema信息
} OpLogicalAAApplyT;

/**
 * 逻辑算子：AA_extract
 * 语义：数组按行键/列键选取子数组，当前暂定只支持propAA按属性键投影，所以arity为1。
 * 输入：一个关联数组，AA_Index（当前仅指一组column index，作投影用，目前AA_Index作为逻辑算子中成员存在）
 * 输出：输入关联数组的子数组
 */
typedef struct {
    OpBaseT base;        // IR_LOGOP_EXTRACT, arity = 1
    AASchemaT schema;    // 输出关联数组的schema信息
    uint32_t rowIdxNum;  // 行键index数组长度
    uint32_t *rowIndex;  // 行键index
    uint32_t colIdxNum;  // 列键index数组长度
    uint32_t *colIndex;  // 列键index
    bool isAllProjection;
} OpLogicalAAExtractT;

/**
 * 逻辑算子：AA_Extend_extract
 * 语义：数组按行键/列键选取子数组，当前暂定只支持propAA按属性键投影，所以arity为1。
 *      与AA_extract的区别在于可针对输出关联数组某一列赋常量值
 * 输入：一个关联数组，AA_Index
 * 输出：输入关联数组的子数组
 */
typedef struct {
    OpBaseT base;        // IR_LOGOP_EXTEND_EXTRACT, arity = 1
    AASchemaT schema;    // 输出关联数组的schema信息
    uint32_t rowIdxNum;  // 行键index数组长度
    uint32_t *rowIndex;  // 行键index
    uint32_t colIdxNum;  // 列键index数组长度
    IRExtractIndexT *colIndex;  // 列键index or 常量值，为列键时即投影该列，为常量时即针对输出关联数组下一列赋常量值
} OpLogicalAAExtendExtractT;

/**
 * 逻辑算子：AA_assign
 * 语义：按行键/列键更改子数组的值
 * 输入：一个关联数组，AA_Index(子数组对应的行列键值集合)，AA_values(子数组上更改后的值)
 * 输出：更新后的关联数组
 */
typedef struct {
    OpBaseT base;        // IR_LOGOP_ASSIGN, arity = 2, 第二个input是OpLogicalAAValues
    AASchemaT schema;    // 输出关联数组的schema信息
    uint32_t rowIdxNum;  // 子数组对应的行键index数组长度
    uint32_t *rowIndex;  // 子数组对应的行键index
    uint32_t colIdxNum;  // 子数组对应的列键index数组长度
    uint32_t *colIndex;  // 子数组对应的列键index
} OpLogicalAAAssignT;

/**
 * 逻辑算子：AA_select
 * 语义：按某种条件选取子数组
 * 输入：一个关联数组，AA_UnaryOp
 * 输出：输入关联数组的子数组
 */
typedef struct {
    OpBaseT base;      // IR_LOGOP_SELECT, arity = 2, 第二个input是选择条件表达式
    AASchemaT schema;  // 输出关联数组的schema信息
} OpLogicalAASelectT;

/**
 * 逻辑算子：AA_kroneckerProduct
 * 语义：计算两个关联数组的克罗内克积(kronecker product)
 * 输入：两个关联数组
 * 输出：一个关联数组
 */
typedef struct {
    OpBaseT base;      // IR_LOGOP_KRONECKERPRODUCT, arity = 2
    AASchemaT schema;  // 输出关联数组的schema信息
} OpLogicalAAKroneckerProductT;

/**
 * 逻辑算子：AA_Join
 * 语义：两个关联数组的连接
 * 输入：两个关联数组
 * 输出：一个关联数组
 */
typedef struct {
    OpBaseT base;           // IR_LOGOP_JOIN, arity = 2
    AASchemaT schema;       // 输出关联数组的schema信息
    IRJoinTypeE joinType;   // 连接的类型
    uint32_t joinKeyNum;    // Join key的个数
    uint32_t *leftKeyIds;   // 左表join key的属性id列表
    uint32_t *rightKeyIds;  // 右表join key的属性id列表
#ifdef FEATURE_SQL
    /* 连接条件 */
    ExprT *onExpr;  // on 表达式
#endif
} OpLogicalAAJoinT;

/**
 * 逻辑算子：AA_scan
 * 语义：扫描label
 * 输入：label元数据
 * 输出：携带表中数据的关联数组
 */
typedef struct OpLogicalAAScan {
    OpBaseT base;                 // IR_LOGOP_SCAN, arity = 0
    AASchemaT schema;             // 输出关联数组的schema信息
    DmVertexLabelT *vertexLabel;  // 待scan的Vertexlabel
    DmEdgeLabelT *edgeLabel;      // 待scan的EdgeLabel
    uint32_t pathId;
#if defined(FEATURE_SQL) || defined(FEATURE_GQL)
    bool dontUseIndex;       // 指定不采用索引, default false
    bool isCombine;          // 指定在向标混合检索时，是否自动判断使用前、后过滤
    uint32_t preFilterMax;   // 指定在向标混合检索时，该值作为自动判断的阈值
    DmVlIndexLabelT *index;  // 指定某索引, default NULL
#endif
} OpLogicalAAScanT;

/**
 * 逻辑算子：AA_scanWorkLabel
 * 语义：扫描work VertexLabel表
 * 输入：work label对应的VertexLabel元数据
 * 输出：无
 */
typedef struct OpLogicalAAScan OpLogicalAAScanWorkLabelT;  // IR_LOGOP_SCANWORKLABEL, arity = 0

/**
 * 逻辑算子：AA_argument
 * 语义：从主查询传递的vertex
 * 输入：label元数据
 * 输出：携带表中数据的关联数组
 */
typedef struct OpLogicalArgument {
    OpBaseT base;                 // IR_LOGOP_ARGUMENT, arity = 0
    AASchemaT schema;             // 输出关联数组的schema信息
    DmVertexLabelT *vertexLabel;  // 从主查询传递的vertex
    IRArgumentTypeE type;
    uint32_t pathId;
} OpLogicalArgumentT;

/**
 * 逻辑算子：AA_AGG
 * 语义：从主查询传递的vertex
 * 输入：label元数据
 * 输出：携带表中数据的关联数组
 */
typedef struct OpLogicalAgg {
    OpBaseT base;      // IR_LOGOP_AGG, arity = 1, 默认1，可变
    AASchemaT schema;  // 输出关联数组的schema信息
} OpLogicalAggT;

/**
 * DML类逻辑算子
 * 语义：对label执行INSERT/REPLACE/UPDATE/DELETE/MERGE等操作
 * 输入：label元数据, OpItemTuples(写入的tuple数据)
 * 输出：无
 */
typedef struct OpLogicalAADml {
    OpBaseT base;           // arity = 0
    AASchemaT schema;       // 输出关联数组的schema信息
    DmVertexLabelT *label;  // 待dml的label
#ifdef FEATURE_GQL
    InputTuplesT *tuples;   // 用户写入的tuples
    uint8_t *batchBufHead;  // vertex buf list
    bool isInsertOnSameTable;
    bool isTrigsAllTrue;  // 是否需要生成bitmap来存储字段变化
#endif                    // FEATURE_GQL
} OpLogicalAADmlT;

/**
 * 逻辑算子：AA_replace
 * 语义：对label执行REPLACE操作
 * 输入：label元数据，OpItemTuples(写入的tuple数据)
 * 输出：无
 */
typedef struct OpLogicalAADml OpLogicalAAReplaceT;  // IR_LOGOP_REPLACE, arity = 0

/**
 * 逻辑算子：AA_merge
 * 语义：对VertexLabel表执行Merge写操作
 * 输入：VertexLabel元数据，OpItemTuples(写入的tuple数据)
 * 输出：对于Datalog场景为增量数据的关联数组
 */
typedef struct OpLogicalAADml OpLogicalAAMergeT;  // IR_LOGOP_MERGE, arity = 0

/**
 * 逻辑算子：AA_modifyWorkLabel
 * 语义：对work label执行写操作
 * 输入：work label对应的VertexLabel元数据，OpItemTuples(写入的tuple数据)
 * 输出：无
 */
typedef struct {
    OpBaseT base;           // IR_LOGOP_MODIFYWORKLABEL, arity = 1
    AASchemaT schema;       // 输出关联数组的schema信息
    DmVertexLabelT *label;  // 待modify的work label
    IRDmlTypeT dmlType;     // modify类型，包括插入、删除等
} OpLogicalAAModifyWorkLabelT;

#if defined(FEATURE_TS) || defined(FEATURE_SQL) || defined(FEATURE_GQL) || defined(FEATURE_STREAM)
/**
 * Create/Update/Delete operations
 * 语义：对 label 执行 INSERT/UPDATE/DELETE 操作
 * 输入：AA
 * 输出：None
 */
typedef struct OpLogicalAACud {
    OpBaseT base;
    AASchemaT schema;                  // 输出关联数组的 schema 信息
    DmVertexLabelT *label;             // CUD 算子要操作的 vertex label
    DmConflictTypeE conflictStrategy;  // 冲突策略
    DbListT *returnTargets;            // list<IRExtractIndexT> item type is IRExtractIndexT
    AASchemaT *returningSchema;        // 返回列数据时使用
} OpLogicalAACudT;
/**
 * 逻辑算子 AA_indexFilterScan
 * 语义: 使用指定的 index 扫描 label. 这个算子必须有一个 Item 表达式作为子节点, 指明索引列上的过滤条件.
 * 输入: label 元数据
 * 输出: 携带表中数据的关联数组
 */
typedef OpLogicalAAScanT OpLogicalAAIndexFilterScanT;  // IR_LOGOP_INDEXFILTERSCAN, arity = 1,
                                                       // notIndexed = false

#endif

#if defined(FEATURE_TS) || defined(FEATURE_SQL) || defined(FEATURE_STREAM)
/**
 * 逻辑算子：AA_insert
 * 语义：对label执行INSERT操作
 * 输入：label元数据，OpItemTuples(写入的tuple数据)
 * 输出：无
 */
typedef OpLogicalAACudT OpLogicalAAInsertT;  // IR_LOGOP_INSERT, arity = 1
/**
 * 逻辑算子 OrderBy / ExtendOrderBy
 * 语义：用于根据 OrderByKeyIds 或 orderByList 进行排序
 * 输入：关联数组
 * 输出：关联数组（在 orderByKey 上有序）
 */
typedef struct OpLogicalAAOrderBy {
    OpBaseT base;            // IR_LOGOP_ORDER_BY, arity = 1
    AASchemaT schema;        // 输出关联数组的 schema 信息
    uint32_t orderByKeyNum;  // 排序 key 的数量
    union {
        uint32_t *orderByKeyIds;  // IR_LOGOP_ORDERBY 使用, 排序使用的列 id 的数组
        ExprT *orderByList;       // IR_LOGOP_EXTEND_ORDER_BY 使用, 排序使用的表达式
                                  // 实际类型是 EXPR_OP_TUPLE. 每个元素是一个排序表达式 ExprT *
    };
    IROrderByDirectionE *directions;  // 排序方向数组
    IROrderByNULLPosE *nullPoses;     // NULL 位置数组
} OpLogicalAAOrderByT;

/**
 * 逻辑算子 Extend OrderBy
 * 语义：根据 orderByList 中的每个表达式排序.
 * 输入：关联数组
 * 输出：关联数组（在 orderByList 中的每个表达式对应的值上有序）
 */
typedef OpLogicalAAOrderByT OpLogicalAAExtendOrderByT;  // IR_LOGOP_EXTEND_ORDERBY. 排序元素可以是表达式.

/**
 * 逻辑算子 Distinct
 * 语义：用于对查询结果去重
 * 输入：关联数组
 * 输出：去重后的关联数组
 */
typedef enum IRDistinctType {
    IR_DISTINCT_HASHMAP,  // 哈希表去重
    IR_DISTINCT_ORDERED,  // 排序去重
} IRDistinctTypeE;

typedef struct OpLogicalAADistinct {
    OpBaseT base;                  // IR_LOGOP_DISTINCT, arity = 1
    AASchemaT schema;              // 输出关联数组的 schema 信息
    IRDistinctTypeE distinctType;  // DISTINCT 类型
} OpLogicalAADistinctT;

/**
 * 逻辑算子 Limit
 * 语义：用于获取部分结果
 * 输入：关联数组
 * 输出：从offset开始，row 数量为 limitNum 的关联数组
 */
typedef struct OpLogicalAALimit {
    OpBaseT base;       // IR_LOGOP_LIMIT, arity = 1
    AASchemaT schema;   // 输出关联数组的 schema 信息
    uint32_t limitNum;  // 输出 AASlot的数量
    uint32_t offset;    // 开始计算的offset
} OpLogicalAALimitT;
#endif

#if defined(FEATURE_TS) || defined(FEATURE_SQL) || defined(FEATURE_STREAM)
/**
 * 逻辑算子：AA_build
 * 语义：基于(row，column，value)三元组或是表达式列表构造一个关联数组，
 *      其中，row表示行键向量，column表示列键向量，value是(row，column)对应的值。
 * 输入：(row，column，value)或是表达式列表，基于前者的实现还有待完善。
 * 输出：一个关联数组
 * 作用：当前仅 SQL 使用, 作为 Insert/Update 算子的输入.
 */
typedef struct {
    OpBaseT base;           // IR_LOGOP_BUILD, arity = 0
    AASchemaT schema;       // 输出关联数组的schema信息
    DbListT *exprList;      // 表达式列表
    DmVertexLabelT *label;  // 关联的 vertexLabel
} OpLogicalAABuildT;

/**
 * 逻辑算子：AA_update
 * 语义：对label执行UPDATE操作
 * 输入：label元数据，OpItemTuples(写入的tuple数据)
 * 输出：无
 */
typedef OpLogicalAACudT OpLogicalAAUpdateT;  // IR_LOGOP_UPDATE, arity = 2
#endif

#if defined(FEATURE_SQL) || defined(FEATURE_GQL)
/**
 * 逻辑算子：AA_delete
 * 语义：对label执行DELETE操作
 * 输入：label元数据，OpItemTuples(删除的tuple的key)
 * 输出：无
 */
typedef OpLogicalAACudT OpLogicalAADeleteT;  // IR_LOGOP_DELETE, arity = 1
#endif

#ifdef FEATURE_SQL
/**
 * 逻辑算子：compound operations
 * 语义：并集/交集/差集，复合查询算子的基类，包含 UNION/EXCEPT/INTERSECT
 * 输入：AA
 * 输出：None
 */
typedef struct OpLogicalAACompound {
    OpBaseT base;        // IR_LOGOP_UNION/EXCEPT/INTERSECT, arity = 2
    AASchemaT schema;    // 输出关联数组的schema信息
} OpLogicalAACompoundT;  // 用于复合查询算子通用逻辑复用

typedef struct OpLogicalAAUnion {
    OpBaseT base;            // IR_LOGOP_UNION, arity = 2
    AASchemaT schema;        // 输出关联数组的schema信息
    bool isUnionAll;         // true: 是 UnionAll
    bool isDistinctByProps;  // 仅当 isUnionAll 是 false 时有意义. 表示 union 算子的去重规则.
                             // true:  根据 schema 中的属性的集合来去重. 使用显式的 union 语法时, 这个值一定是 true
                             // false: 根据记录的指针来去重. 当且仅当 multi-index-or 的情况下, 这个值一定是 false
} OpLogicalAAUnionT;

typedef OpLogicalAACompoundT OpLogicalAAExceptT;
typedef OpLogicalAACompoundT OpLogicalAAIntersectT;

/**
 * 逻辑算子：SubQueryScan
 * 语义：标记子查询
 * 输入：subQueryId, 对应父查询SubQueryList下标
 * 输出：None
 */
typedef struct OpLogicalAASubQueryScan {
    OpBaseT base;         // IR_LOGOP_SUB_QUERY_SCAN, arity = 0
    AASchemaT schema;     // 对应父查询的 subQueries 数组中第 subQueryId 个子查询的输出 schema
    uint32_t subQueryId;  // 当前子查询相对外查询的id
} OpLogicalAASubQueryScanT;

/**
 * 逻辑算子：AA_logicalTrig
 * 语义：对 label 执行 INSERT/UPDATE/DELETE 及其相关的被触发操作(INSERT/UPDATE/DELETE)
 * 输入：AA
 * 输出：None
 */
typedef struct OpLogicalAATrig {
    OpBaseT base;              // IR_LOGOP_TRIGGER, arity = 1，IR_LOGOP_(INSERT/UPDATE/DELETE)
    AASchemaT schema;          // 孩子为CUD算子 已包含schema 此处不用填充
    DmTriggerInfoT *trigInfo;  // 关联的所有触发器信息, 外键触发器则为NULL
    DbListT *trigActs;  // <item: DbListT<item: IRPlanT>> 记录所有被触发动作, 内层DbListT对应一个触发器
    DbListT *trigWhenClauses;  // <item: ExprT *> 记录各触发器的触发条件, itemNum 与 trigActs外层list 一致, 可能为空
    DbListT *fkTrigActs;      // <item: IRPlanT> 记录外键约束的被触发动作
    DbListT *isChildKeyList;  // <item: bool> 对应 fkTrigActs，标识是子键还是父键, true代表子键
} OpLogicalAATrigT;
#endif

#if defined(FEATURE_TS) || defined(FEATURE_SQL) || defined(FEATURE_STREAM)

/**
 * 逻辑算子：AA_expr_project
 * 语义：数组按列上的投影表达式选取子数组
 * 输入：一个关联数组.
 * 输出：输入关联数组的子数组
 * 注: projExpr 中表示实体列的 ExprVar 在逻辑算子和物理算子中的意义不相同.
 *      对于 [逻辑算子], projExpr 中的 ExprVar 指向的是投影算子的子节点中的列
 *      这些 ExprVar 一定是 EXPR_OP_LVAR, 对应的 propeId 也是投影算子的子节点的列的 id
 *
 *      对于 [物理算子], 优化器会在后处理阶段, 修正这些 ExprVar 的类型和 propeId, 使投影表达式和执行层对齐
 *      目前只有下述情况会使得 ExprVar 的类型变为 EXPR_OP_RVAR:
 *      例如投影 + JOIN 的情况下, 执行算子投影表达式中的 ExprVar 指向的实际上是 JOIN 的两个孩子, 而非 JOIN 本身
 *      因此执行层投影表达式中出现的右表的列, 对应的类型是 EXPR_OP_RVAR
 *      对应的 propeId 是这个属性在 JOIN 的右孩子的 schema 中的列 ID.
 */
typedef struct {
    OpBaseT base;      // IR_LOGOP_EXPR_PROJECT, arity = 1
    AASchemaT schema;  // 输出关联数组的schema信息
    ExprT *projExpr;   // EXPR_OP_TUPLE. 投影表达式列表
} OpLogicalAAExprProjectT;

/**
 * 逻辑算子：GroupBy
 * 语义：分组聚合
 * 输入：关联数组
 * 输出：关联数组（按照 aggKeyIds 分组）
 * 说明：
 */
typedef struct OpLogicalAAGroupBy {
    OpBaseT base;         // IR_LOGOP_GROUP_BY, arity = 1
    AASchemaT schema;     // 输出关联数组的 schema 信息
    uint32_t aggKeyNum;   // 分组条件 id 的数量
    uint32_t *aggKeyIds;  // 分组条件 id 数组
    uint32_t funcNum;     // 聚合方法数量
#ifdef FEATURE_SQL
    void *expr;  // 表达式树 ExprT, 后续统一
#else
    ExprT *expr;
#endif
    void *havingExpr;              // HAVING 表达式(IRExprT)
    DmPropertySchemaT *funcProps;  // 聚合方法生成的新属性数组，用来生成 schema 的新属性
} OpLogicalAAGroupByT;
#endif

#ifdef FEATURE_STREAM
typedef struct OpLogicalStreamUpsertIntoRef {
    OpBaseT base;
    AASchemaT schema;       // 输出关联数组的schema信息
    StreamRefSlotT *label;  // 待插入的表
    DbListT *insertDataList;
} OpLogicalStreamUpsertIntoRefT;  // IR_LOGICAL_UPSERT_INTO_REF

typedef struct OpLogicalStreamWindowTable {
    OpBaseT base;
    AASchemaT schema;           // 输出关联数组的schema信息
    uint32_t timeColId;         // 时间字段id
    uint32_t slide;             // 步长
    uint32_t size;              // 大小
    uint32_t offset;            // 暂不支持
} OpLogicalStreamWindowTableT;  // IR_LOGOP_STREAM_WINDOW_TABLE

typedef struct OpLogicalStreamWatermarkAssigner {
    OpBaseT base;
    AASchemaT schema;                 // 输出关联数组的schema信息
    uint32_t eventTimeColId;          // 时间时间的属性id
    int64_t delayTime;                // 延迟时间，秒
    bool isTolerant;                  // 是否是宽容的（默认严格递增）
} OpLogicalStreamWatermarkAssignerT;  // IR_LOGOP_STREAM_WATERMARK_ASSIGNER

typedef struct OpLogStreamOverAgg {
    OpBaseT base;
    AASchemaT schema;              // 输出关联数组的 schema 信息
    uint32_t aggKeyNum;            // 分组条件 id 的数量
    uint32_t *aggKeyIds;           // 分组条件 id 数组
    uint32_t funcNum;              // 聚合方法数量
    ExprT *expr;                   // 表达式树 ExprT
    DmPropertySchemaT *funcProps;  // 聚合方法生成的新属性数组，用来生成 schema 的新属性
    bool isOnlyInstant;
} OpLogStreamOverAggT;  // IR_LOGOP_STREAM_OVER_AGG
#endif
#ifdef FEATURE_TS
/**
 * 逻辑算子：BulkInsert
 * 语义：批量插入
 * 输入：无
 * 输出：无
 * 说明：批量插入算子当前约定不返回插入的结果，因为未定义 schema 结构
 */
typedef struct OpLogicalAABulkInsert {
    OpBaseT base;                 // IR_LOGOP_BULK_INSERT, arity = 0
    AASchemaT schema;             // 输出关联数组的schema信息
    uint32_t columnNum;           // 待插入的列数目
    DmVertexLabelT *logicLabel;   // 待插入的逻辑表
    DmVertexLabelT *physicLabel;  // 待插入的物理表，可被复用
    DbListT *insertDataList;      //  <item: BulkInsertArrT> 记录同个分组下的所有 cu 块信息
} OpLogicalAABulkInsertT;

/**
 * 逻辑算子 CopyTo
 * 语义：用于将中间结果集的结果输出到指定文件中
 * 输入：关联数组
 * 输出：无
 */
typedef struct OpLogicalAACopyTo {
    OpBaseT base;      // IR_LOGOP_COPY_TO, arity = 1
    AASchemaT schema;  // 输出关联数组的 schema 信息
    char *targetPath;  // 目标文件路径
    uint32_t pathLen;  // 目标文件路径字符长度
} OpLogicalAACopyToT;
#endif

#ifdef FEATURE_GQL
/**
 * 逻辑算子：PathTraversal
 * 语义：回溯到PATH PATTERN的根节点
 * 输入：PathInfo
 * 输出：根节点的关联数组
 */
typedef struct {
    OpBaseT base;                  // IR_LOGOP_PATHTRAVERSAL, arity = 0
    AASchemaT schema;              // 输出关联数组的schema信息
    DmComplexPathInfoT *pathInfo;  // pathInfo
    DmPathTrigDmlE eventType;
} OpLogicalAAPathTraversalT;

/**
 * 逻辑算子：FilterPathTraversal
 * 语义：回溯到PATH PATTERN的根节点, 同时过滤清理不完备的中间结果集
 * 输入：关联数组，PathInfo
 * 输出：过滤后的关联数组
 */
typedef struct {
    OpBaseT base;                  // IR_LOGOP_FILTERPATHTRAVERSAL, arity = 1
    AASchemaT schema;              // 输出关联数组的schema信息
    DmComplexPathInfoT *pathInfo;  // pathInfo
} OpLogicalAAFilterPathTraversalT;

/**
 * 逻辑算子：TopoScan
 * 语义：扫描label
 * 输入：edgelabel元数据
 * 输出：携带topo的关联数组
 */
typedef struct OpLogicalAATopoScan {
    OpBaseT base;      // IR_LOGOP_TOPOSCAN, arity = 0
    AASchemaT schema;  // 输出关联数组的schema信息
    char *pathName;    // pathName既是输入也是输出
    uint32_t edgeLabelNum;
    DmEdgeLabelT **edgeLabels;
} OpLogicalAATopoScanT;

/**
 * 逻辑算子：PathSeqFilter
 * 语义：按条件顺序扫描过滤指定表
 * 输入：多表的关联数组
 * 输出：过滤后的关联数组
 */
typedef struct OpLogicalAAPathSeqFilter {
    OpBaseT base;                 // IR_LOGOP_PATHSEQFILTER, arity = 1
    AASchemaT schema;             // 输出关联数组的schema信息
    DmVertexLabelT *vertexLabel;  // 待扫描的表
    DbListT conditions;           // 条件,item<DmPathConditionT>
} OpLogicalAAPathSeqFilterT;
#endif  // FEATURE_GQL

/**
 * @brief 创建IR逻辑算子, 并将type和arity赋值
 * @param[in] memCtx 创建IR逻辑算子使用的memCtx
 * @param[in] type IR逻辑算子类型
 * @param[out] op 创建出的IR逻辑算子
 * @return: success或错误码
 */
Status IRCreateLogicalOp(DbMemCtxT *memCtx, IROpTypeE type, OpBaseT **op);

Status IRCreateLogicalOpWithArena(DbArenaT *arena, IROpTypeE type, OpBaseT **op);
/**
 * @brief 销毁IR逻辑算子
          暂不释放各算子内部的DmVertexLabel
          另外只有leaf(arity为0)算子的AASchema中的prope会被释放
          非leaf算子AASchema的prope直接引用的leaf算子的prope，所以不用释放
 * @param[in] memCtx IR逻辑算子内存对应的memCtx
 * @param[in] op 待销毁的IR逻辑算子
 */
void IRDestroyLogicalOp(DbMemCtxT *memCtx, OpBaseT **op);

/**
 * @brief 拷贝IR逻辑算子(算子内的VertexLabel和AASchema中的prope直接引用，不拷贝)
          Copy失败时，暂不考虑内存释放，由上层模块调memCtx接口统一释放
          前提：srcOp必须为合法的IR逻辑算子
 * @param[in] memCtx 拷贝过程中使用的memCtx
 * @param[in] srcOp 被拷贝的源IR逻辑算子
 * @param[out] dstOp 目的IR逻辑算子
 * @return: success或错误码
 */
Status IRCopyLogicalOp(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp);

/**
 * @brief 根据IR逻辑算子类型获取arity
 * @param[in] type IR逻辑算子类型
 * @return: IR逻辑算子的arity
 */
uint32_t IRGetLogicalOpArity(IROpTypeE type);

/**
 * @brief 判断两个IR逻辑算子内容是否相同
 *        前提：op1和op2必须为合法的IR逻辑算子
 * @param[in] op1 第一个IR逻辑算子
 * @param[in] op2 第二个IR逻辑算子
 * @return: true-内容相同, false-内容不同
 */
bool IRIsLogicalOpEqual(const OpBaseT *op1, const OpBaseT *op2);

/**
 * @brief 校验IR逻辑算子是否合法
 * @param[in] op 被校验的IR逻辑算子
 * @param[in] children 子IR算子
 * @return: GMERR_OK-合法, 其他错误码-非法
 */
Status IRCheckLogicalOp(const OpBaseT *op, const OpBaseT **children);

/**
 * @brief 生成IR逻辑算子的AASchema
 * @param[in] memCtx 创建IR逻辑算子AASchema所使用的memCtx
 * @param[in] children 子IR算子的AASchema数组
 * @param[in/out] op 要生成AASchema的IR逻辑算子
 * @return: GMERR_OK-合法, 其他错误码-非法
 */
SO_EXPORT_FOR_TS Status IRGenAASchema4LogicalOp(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op);

/**
 * @brief 更新投影算子colindex
 * @param[in] memCtx 申请内存时使用的memCtx，不考虑释放，后续统一释放
 * @param[in] childOp 孩子节点的OpBaseT指针
 * @param[in/out] extractOp 需要更新的投影算子节点的OpBaseT指针
 * @return: GMERR_OK-合法, 其他错误码-非法
 */
SO_EXPORT Status IRUpdateAAExtract(DbMemCtxT *memCtx, OpBaseT *childOp, OpBaseT *extractOp);

/**
 * @brief 更新扩展投影算子colIndex
 * @param[in] memCtx 申请内存时使用的memCtx，不考虑释放，后续统一释放
 * @param[in] originColIdx 更新前的扩展投影算子colIndex
 * @param[in] colIdxNum 更新前的扩展投影算子的colIndexNum
 * @param[in] childOp 孩子节点的OpBaseT指针
 * @param[in/out] extractOp 需要更新的扩展投影算子节点的OpBaseT指针
 * @return: GMERR_OK-合法, 其他错误码-非法
 */
SO_EXPORT Status IRUpdateAAExtendExtract(
    DbMemCtxT *memCtx, IRExtractIndexT *originColIdx, uint32_t colIdxNum, OpBaseT *childOp, OpBaseT *extractOp);

/**
 * @brief 将逻辑scan算子转变为逻辑WorkLabelScan算子
 * @param[in/out] op 需要更改类型的节点OpBaseT指针
 */
SO_EXPORT void IRLogicalScan2WorkLabelScan(OpBaseT *op);

/**
 * @brief 更新连接的成员信息
 * @param[in] memCtx 申请内存时使用的memCtx，不考虑释放，后续统一释放
 * @param[in] leftChildSchema 连接算子左孩子节点的AASchemaT指针
 * @param[in] rightChildSchema 连接算子右孩子节点的AASchemaT指针
 * @param[in/out] joinOp 指向需要更新的连接算子的指针
 * @return: GMERR_OK-合法, 其他错误码-非法
 */
SO_EXPORT Status IRUpdateJoin(
    DbMemCtxT *memCtx, const AASchemaT *leftChildSchema, const AASchemaT *rightChildSchema, OpLogicalAAJoinT *joinOp);

/**
 * @brief plan中是否包含join类型为anti和not的join
 * @param[in] memCtx 申请内存时使用的memCtx，不考虑释放，后续统一释放
 * @param[in] expr IRPlan
 * @return: true-包含，false-不包含
 */
SO_EXPORT_FOR_TS bool IRIsPlanContainsAntiJoin(IRExprT *expr);

#ifdef __cplusplus
}
#endif
#endif  // CPL_IR_LOGICAL_OP_H
