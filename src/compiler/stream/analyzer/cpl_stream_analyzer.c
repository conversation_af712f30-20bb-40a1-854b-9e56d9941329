/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Stream analyzer implementation
 * Author: Stream Team
 * Create: 2024-08-12
 */
#include "cpl_stream_compiler.h"
#include "cpl_stream_analyzer_create_table.h"
#include "cpl_stream_analyzer_drop.h"
#include "cpl_stream_analyzer_create_sink.h"
#include "cpl_stream_analyzer_create_view.h"
#include "cpl_stream_analyzer_create_ref.h"
#include "cpl_stream_analyzer_upsert_into_ref.h"
#include "cpl_stream_analyzer_alter.h"
#include "dm_meta_namespace.h"

typedef Status (*StreamSqlAnalyzeFunc)(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt);

typedef struct StreamSqlAnalyzeHandle {
    StreamSqlAnalyzeFunc analyze;  // analyzer handle function for sql statement
} StreamSqlAnalyzeHandleT;

#define STREAM_SQL_ANALYZE_TAG_OFFSET(i) ((uint32_t)(i) - (uint32_t)T_STREAM_BEGIN)
static StreamSqlAnalyzeHandleT g_streamAnalyzeHandle[] = {
    [STREAM_SQL_ANALYZE_TAG_OFFSET(T_STREAM_CREATE_TABLE_STMT)] = {StreamSqlAnalyzeCreateTable},
    [STREAM_SQL_ANALYZE_TAG_OFFSET(T_STREAM_DROP_TABLE_STMT)] = {StreamSqlAnalyzeDrop},
    [STREAM_SQL_ANALYZE_TAG_OFFSET(T_STREAM_CREATE_SINK_STMT)] = {StreamSqlAnalyzeCreateSink},
    [STREAM_SQL_ANALYZE_TAG_OFFSET(T_STREAM_CREATE_VIEW_STMT)] = {StreamSqlAnalyzeCreateView},
    [STREAM_SQL_ANALYZE_TAG_OFFSET(T_STREAM_CREATE_REF_STMT)] = {StreamSqlAnalyzeCreateRef},
    [STREAM_SQL_ANALYZE_TAG_OFFSET(T_STREAM_DROP_REF_STMT)] = {StreamSqlAnalyzeDropRef},
    [STREAM_SQL_ANALYZE_TAG_OFFSET(T_STREAM_UPSERT_INTO_REF)] = {StreamSqlAnalyzeUpsertIntoRef},
    [STREAM_SQL_ANALYZE_TAG_OFFSET(T_STREAM_ALTER_STMT)] = {StreamSqlAnalyzeAlter},
    [STREAM_SQL_ANALYZE_TAG_OFFSET(T_STREAM_ALTER_UNION_STMT)] = {StreamSqlAnalyzeAlterUnion},
};

Status StreamSqlAnalyze(SessionT *session, DbMemCtxT *memCtx, NodeT *parsedStmt, SqlIrStmtT *irStmt)
{
    DB_POINTER4(session, memCtx, parsedStmt, irStmt);
    if (parsedStmt->tag < T_STREAM_BEGIN || parsedStmt->tag >= T_STREAM_END) {
        DB_LOG_ERROR(GMERR_INVALID_PROPERTY, "stmt tag(%u).", parsedStmt->tag);
        return GMERR_INVALID_PROPERTY;
    }
    irStmt->tag = parsedStmt->tag;
    irStmt->memCtx = memCtx;
    // labelList用途: 用于存放暂时性的label, 解析后释放
    // 生命周期: 活动范围为解析函数
    // 释放方式: 随请求结束, ServiceEntry最后destroy memCtx统一释放
    irStmt->labelList = DbDynMemCtxAlloc(memCtx, sizeof(DbListT));
    if (irStmt->labelList == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(irStmt->labelList, sizeof(DmVertexLabelT *), memCtx);
    StreamSqlAnalyzeHandleT *handle = &g_streamAnalyzeHandle[STREAM_SQL_ANALYZE_TAG_OFFSET(parsedStmt->tag)];
    if (SECUREC_UNLIKELY(handle == NULL || handle->analyze == NULL)) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "unregister handle for tag(%u).", parsedStmt->tag);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    Status ret = handle->analyze(session, parsedStmt, irStmt);
    ReleaseLabelFromList(irStmt->labelList);
    // 直接置为空，其他SQL语句进行analyze时会重新创建。不会内存泄漏，由传入的memCtx的destroy兜底释放
    irStmt->labelList = NULL;
    return ret;
}

bool GetIfNotExistsByTag(NodeTagT tag, void *stmt)
{
    switch (tag) {
        case T_STREAM_CREATE_TABLE_STMT: {
            SqlCreateStreamStmtT *createTableStmt = (SqlCreateStreamStmtT *)stmt;
            return createTableStmt->ifNotExists;
        }
        case T_STREAM_CREATE_VIEW_STMT: {
            SqlCreateViewStmtT *createViewStmt = (SqlCreateViewStmtT *)stmt;
            return createViewStmt->ifNotExists;
        }
        case T_STREAM_CREATE_REF_STMT: {
            SqlCreateStreamRefStmtT *createRefStmt = (SqlCreateStreamRefStmtT *)stmt;
            return createRefStmt->ifNotExists;
        }
        case T_STREAM_CREATE_SINK_STMT: {
            SqlCreateSinkStmtT *createSinkStmt = (SqlCreateSinkStmtT *)stmt;
            return createSinkStmt->ifNotExists;
        }
        default:
            return false;
    }
}
