/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: the interface for STREAM create table analyzer
 * Author: Stream Team
 * Create: 2024-08-12
 */

#ifndef CPL_STREAM_ANALYZER_CREATE_TABLE_H
#define CPL_STREAM_ANALYZER_CREATE_TABLE_H

#include "cpl_stream_analyzer_common.h"

#define STREAM_WATERMARK_MIN 1     // 规格，水位延迟最小为1
#define STREAM_WATERMARK_MAX 3600  // 规格，水位延迟最大为3600

Status StreamSqlAnalyzeCreateTable(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt);
#endif /* CPL_STREAM_ANALYZER_CREATE_TABLE_H */
