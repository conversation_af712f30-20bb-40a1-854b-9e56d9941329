/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: query parse yang subtree
 * Note1：memCtx生命周期为请求级别，请求结束后在函数FastpathPrepareCommand中统一释放
 * Note2: 模块不涉及并发，也不支持并发
 * Author:
 * Create: 2023-06-02
 */

#include "adpt_string.h"
#include "dm_yang_interface.h"
#include "gmc_yang_types.h"
#include "cpl_public_parser_ddl_utils.h"
#include "cpl_public_verifier_ddl.h"
#include "cpl_log.h"
#include "cpl_yang_parser_subtree.h"

#ifdef __cplusplus
extern "C" {
#endif

static Status QryParseSingleSubtreeObject(
    QryStmtT *stmt, const char *key, DbJsonT *value, DmSubtreeT *tree, DmSubtreeT **prevChild);
static Status QryParseSubtree(QryStmtT *stmt, DbJsonT *jsonNode, DmSubtreeT *tree);

static Status QryCheckKeyAndValue(const char *key, const DbJsonT *value)
{
    if (key == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Parse subtree json, key=null");
        return GMERR_INVALID_JSON_CONTENT;
    }
    if (value == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Parse subtree json, value=null, key=%s", key);
        return GMERR_INVALID_JSON_CONTENT;
    }
    return GMERR_OK;
}

static Status QryLinkSubtree(const QryStmtT *stmt, DmSubtreeT *tree, DmSubtreeT *childTree)
{
    DB_POINTER3(stmt, tree, childTree);

    const char *srcName = DmGetSubtreeName(tree);
    const char *destName = DmGetSubtreeName(childTree);
    DmTreeDescT **childDesc = tree->desc->childDesc;
    for (uint32_t i = 0; i < tree->desc->childNum; ++i) {
        if (DmIsMatchTreeDesc(childDesc[i], childTree->desc)) {
            if (tree->childTree[i] != NULL) {
                DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DUPLICATE_OBJECT,
                    "Repeated insertion of subtree!, source name=%s, dest name=%s", srcName, destName);
                return GMERR_DUPLICATE_OBJECT;
            }
            childTree->parent = tree;
            tree->childTree[i] = childTree;
            return GMERR_OK;
        }
    }

    DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT,
        "Inv json, source and dest has no relationship, source name=%s, dest name=%s", srcName, destName);
    return GMERR_INVALID_JSON_CONTENT;
}

// 判断json key是否设置为empty，[null]代表设置为empty
static bool QryDbJsonArrayIsEmpty(const DbJsonT *value)
{
    DB_POINTER(value);
    // value不为NULL，说明array中有值，因此item不可能为NULL
    DbJsonT *item = DbJsonArrayGet(value, 0);
    if (item == NULL) {
        return false;
    }
    if (DbJsonGetArraySize(value) == 1 && DbJsonGetType(item) == DB_JSON_NULL) {
        return true;
    }

    return false;
}

static inline bool QryDbJsonIsEmpty(DbJsonT *value)
{
    DB_POINTER(value);

    DbJsonT *item = DbJsonObjectIter(value);
    if (item == NULL) {
        return true;
    }
    return false;
}

static Status QrySetStringValue(DbMemCtxT *memCtx, const char *name, const char *value, DmValueT *val)
{
    uint32_t size = (uint32_t)strlen(value) + 1;
    /* memCtx用途：生成qe需要的上下文
     * 生命周期：短进程级别
     * 释放方式：由QryParseField释放
     * 兜底清空措施：QryReleaseQryLabelCtx中如果需要则进行销毁
     * 并发：请求解析过程不涉及并发
     */
    val->value.strAddr = DbDynMemCtxAlloc(memCtx, size);
    if (val->value.strAddr == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Alloc mem when set property %s str value", name);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(val->value.strAddr, size, value);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "Copy str when set property %s str value", name);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    val->value.length = DmIsYangEmptyType(val->type) ? DM_YANG_EMPTY_REAL_SIZE : size;
    return GMERR_OK;
}

static Status QrySetBufferValue(DbMemCtxT *memCtx, const char *name, const char *value, DmValueT *val)
{
    DB_POINTER4(memCtx, name, value, val);

    if (!DM_TYPE_NEED_MALLOC(val->type) && !DmIsYangEmptyType(val->type)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATATYPE_MISMATCH, "Property %s is not str. type=%" PRIu32 "", name, (uint32_t)val->type);
        return GMERR_DATATYPE_MISMATCH;
    }
    if (val->type == DB_DATATYPE_STRING || DmIsYangEmptyType(val->type)) {
        return QrySetStringValue(memCtx, name, value, val);
    }

    const char *hex = NULL;
    Status ret = DbStrIsHexPrefixed(value, &hex);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t len = (uint32_t)strlen(hex);
    if (len == 0) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint32_t size = DbStrGetHexToBytesSize(len);
    /* memCtx用途：生成qe需要的上下文
     * 生命周期：短进程级别
     * 释放方式：由QryParseField释放
     * 兜底清空措施：QryReleaseQryLabelCtx中如果需要则进行销毁
     * 并发：请求解析过程不涉及并发
     */
    uint8_t *bytes = DbDynMemCtxAlloc(memCtx, size);
    if (bytes == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Set buf value.");
        return GMERR_OUT_OF_MEMORY;
    }
    ret = DbStrHexToBytes(hex, bytes, len);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (val->type == DB_DATATYPE_BITMAP) {
        val->value.beginPos = 0;
        val->value.endPos = (uint16_t)(size * BYTE_LENGTH - 1);  // bit count
    }
    val->value.strAddr = bytes;
    val->value.length = size;
    return GMERR_OK;
}

static Status QrySetIntegerValue(const char *name, const int64_t value, DmValueT *val)
{
    DB_POINTER(val);
    switch (val->type) {
        case DB_DATATYPE_CHAR:
        case DB_DATATYPE_UCHAR:
        case DB_DATATYPE_INT8:
        case DB_DATATYPE_UINT8:
        case DB_DATATYPE_BITFIELD8:
        case DB_DATATYPE_PARTITION:
            val->value.ubyteValue = (uint8_t)value;
            break;
        case DB_DATATYPE_INT16:
        case DB_DATATYPE_UINT16:
        case DB_DATATYPE_BITFIELD16:
            val->value.ushortValue = (uint16_t)value;
            break;
        case DB_DATATYPE_INT32:
        case DB_DATATYPE_UINT32:
        case DB_DATATYPE_BITFIELD32:
            val->value.uintValue = (uint32_t)value;
            break;
        case DB_DATATYPE_INT64:
        case DB_DATATYPE_UINT64:
        case DB_DATATYPE_BITFIELD64:
        case DB_DATATYPE_RESOURCE:
            val->value.ulongValue = (uint64_t)value;
            break;
        case DB_DATATYPE_DOUBLE:
        case DB_DATATYPE_BYTES:
        case DB_DATATYPE_TIME:
        case DB_DATATYPE_NULL:
        case DB_DATATYPE_STRING:
        case DB_DATATYPE_BITMAP:
        case DB_DATATYPE_BOOL:
        case DB_DATATYPE_FLOAT:
        case DB_DATATYPE_FIXED:
        default:
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_DATATYPE_MISMATCH, "Property %s is not int, type=%" PRIu32 "", name, (uint32_t)val->type);
            return GMERR_DATATYPE_MISMATCH;
    }

    return GMERR_OK;
}

static Status QrySetBoolValue(const char *name, bool value, DmValueT *val)
{
    DB_POINTER(val);

    if (val->type != DB_DATATYPE_BOOL) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATATYPE_MISMATCH, "Property %s is not bool. type=%" PRIu32 "", name, (uint32_t)val->type);
        return GMERR_DATATYPE_MISMATCH;
    }

    val->value.boolValue = value;

    return GMERR_OK;
}

static Status QrySetRealValue(const char *name, const DbJsonT *value, DmValueT *val)
{
    DB_POINTER(val);

    if (val->type == DB_DATATYPE_DOUBLE) {
        val->value.doubleValue = (double)DbJsonRealValue(value);
    } else if (val->type == DB_DATATYPE_FLOAT) {
        val->value.floatValue = (float)DbJsonRealValue(value);
    } else {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_DATATYPE_MISMATCH, "%s is not real, type=%" PRIu32 "", name, (uint32_t)val->type);
        return GMERR_DATATYPE_MISMATCH;
    }
    return GMERR_OK;
}

static Status QrySetValueByJson(DbMemCtxT *memCtx, const char *key, const DbJsonT *value, DmValueT *dmValue)
{
    Status ret = GMERR_OK;
    switch (DbJsonGetType(value)) {
        case DB_JSON_STRING:
            ret = QrySetBufferValue(memCtx, key, DbJsonStringValue(value), dmValue);
            break;
        case DB_JSON_INTEGER:
            ret = QrySetIntegerValue(key, DbJsonIntegerValue(value), dmValue);
            break;
        case DB_JSON_REAL:
            ret = QrySetRealValue(key, value, dmValue);
            break;
        case DB_JSON_TRUE:
            ret = QrySetBoolValue(key, true, dmValue);
            break;
        case DB_JSON_FALSE:
            ret = QrySetBoolValue(key, false, dmValue);
            break;
        case DB_JSON_NULL:
            return GMERR_OK;
        case DB_JSON_ARRAY:
        case DB_JSON_OBJECT:
        default:
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_INVALID_OBJECT_DEFINITION, "Value type is unexpected when parse subtree field, key=%s", key);
            return GMERR_INVALID_OBJECT_DEFINITION;
    }
    return ret;
}

static Status QryParseField(const char *key, const DbJsonT *value, DmSubtreeT *tree)
{
    DB_POINTER3(key, value, tree);

    DmValueT propertyValue = {0};
    Status ret = DmSubtreeGetPropeTypeByName(tree, key, &(propertyValue.type));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get filter subtree prope type, name:%s", key);
        return ret;
    }
    ret = QrySetValueByJson(tree->memCtx, key, value, &propertyValue);
    if (ret == GMERR_OK) {
        ret = DmSubtreeSetPropeByName(key, propertyValue, tree);
    }
    if (DM_TYPE_NEED_MALLOC(propertyValue.type) && propertyValue.value.strAddr != NULL) {
        DbDynMemCtxFree(tree->memCtx, propertyValue.value.strAddr);
        propertyValue.value.strAddr = NULL;
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Set filter subtree prope, name:%s", key);
    }
    return ret;
}

static Status QryParseSubtreePrepare(QryStmtT *stmt, const char *key, DmSubtreeT *tree, DmSubtreeT **childTree)
{
    DmNodeT *childNode = NULL;
    Status ret = DmSubtreeGetNodeByName(tree, key, &childNode);
    if (ret == GMERR_OK) {
        // node 孩子
        ret = DmCreateFilterNodeSubtree(tree->memCtx, tree, childNode, childTree);
    } else {
        // 不是node孩子，则可能是边孩子
        QryLabelT *qryLabel = NULL;
        ret = QryGetQryLabelByName(stmt, key, &qryLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
        DbResetLastErrorInfo();
        ret = DmCreateFilterSubtree(tree->memCtx, qryLabel->desc, childTree);
    }
    return ret;
}

static Status QryParseEmptySubtree(QryStmtT *stmt, const char *key, DmSubtreeT *tree)
{
    // 失败说明key非字段，为vertex或node
    DmSubtreeT *childTree = NULL;
    Status ret = QryParseSubtreePrepare(stmt, key, tree, &childTree);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parse empty subTree, name=%s", key);
        return ret;
    }

    ret = QryLinkSubtree(stmt, tree, childTree);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmSetFilterType(childTree, DM_FILTER_CONTAINER);
    return GMERR_OK;
}

static Status QryParseSubtreeList(QryStmtT *stmt, const char *key, const DbJsonT *value, DmSubtreeT *tree)
{
    DB_POINTER4(stmt, key, value, tree);

    size_t arraySize = DbJsonGetArraySize(value);

    // 解析vertex下的或条件，多颗子树
    DmSubtreeT *prevChild = NULL;
    Status ret;
    for (size_t i = 0; i < arraySize; ++i) {
        DbJsonT *item = DbJsonArrayGet(value, i);
        if (DbJsonGetType(item) != DB_JSON_OBJECT) {
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_INVALID_JSON_CONTENT, "Subtree json unexpected, non-vertex. key:%s", key);
            return GMERR_INVALID_JSON_CONTENT;
        }
        ret = QryParseSingleSubtreeObject(stmt, key, item, tree, &prevChild);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status QryParseSubtreeDbJsonArray(QryStmtT *stmt, const char *key, const DbJsonT *value, DmSubtreeT *tree)
{
    DB_POINTER4(stmt, key, value, tree);

    if (DbJsonGetArraySize(value) == 0) {  // 比如T0:[]
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Parse json array, valueSize=0, key=%s", key);
        return GMERR_INVALID_JSON_CONTENT;
    }

    // 是否为[null]
    if (QryDbJsonArrayIsEmpty(value)) {
        Status ret = DmSubtreeSetPropeEmptyByNameSilent(key, tree);
        if (ret == GMERR_OK) {
            DmSetFilterType(tree, DM_FILTER_LEAF);
            return GMERR_OK;
        }
        // 失败说明key非字段，为节点
        return QryParseEmptySubtree(stmt, key, tree);
    }

    if (DbJsonGetType(DbJsonArrayGet(value, 0)) == DB_JSON_OBJECT) {
        return QryParseSubtreeList(stmt, key, value, tree);
    }
    DB_LOG_ERROR_AND_SET_LASTERR(
        GMERR_INVALID_JSON_CONTENT, "Subtree json unexpected, array contains non-vertex. key:%s", key);
    return GMERR_INVALID_JSON_CONTENT;
}

static Status QryLinkRelatedSubtree(
    const QryStmtT *stmt, DmSubtreeT *tree, DmSubtreeT *childTree, DmSubtreeT **prevChild)
{
    DB_POINTER3(stmt, tree, childTree);

    childTree->parent = tree;
    if (prevChild == NULL) {
        return QryLinkSubtree(stmt, tree, childTree);
    }

    // 传入的上一个孩子为空，则直接连接父节点上，否则连接到上一个孩子的nextTree上
    if (*prevChild == NULL) {
        Status ret = QryLinkSubtree(stmt, tree, childTree);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        (*prevChild)->nextTree = childTree;
    }

    // 出参prevChild赋值为当前子树
    *prevChild = childTree;
    return GMERR_OK;
}

static Status QryParseSingleSubtreeObject(
    QryStmtT *stmt, const char *key, DbJsonT *value, DmSubtreeT *tree, DmSubtreeT **prevChild)
{
    DB_POINTER3(stmt, value, tree);

    DmSubtreeT *childTree = NULL;
    Status ret = QryParseSubtreePrepare(stmt, key, tree, &childTree);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Create child filter subtree, name:%s", key);
        return ret;
    }

    ret = QryLinkRelatedSubtree(stmt, tree, childTree, prevChild);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (QryDbJsonIsEmpty(value)) {
        DmSetFilterType(tree, DM_FILTER_LEAF);
        return GMERR_OK;
    }
    return QryParseSubtree(stmt, value, childTree);
}

static Status QryParseSubtree(QryStmtT *stmt, DbJsonT *jsonNode, DmSubtreeT *tree)
{
    DB_POINTER3(stmt, jsonNode, tree);

    Status ret = GMERR_OK;
    void *iter = DbJsonObjectIter(jsonNode);
    while (iter) {
        const char *key = DbJsonObjectIterKey(iter);
        DbJsonT *value = DbJsonObjectIterValue(iter);

        ret = QryCheckKeyAndValue(key, value);
        if (ret != GMERR_OK) {
            return ret;
        }

        if (DbJsonGetType(value) == DB_JSON_OBJECT) {
            DmSetFilterType(tree, DM_FILTER_CHILD);
            ret = QryParseSingleSubtreeObject(stmt, key, value, tree, NULL);
        } else if (DbJsonGetType(value) == DB_JSON_ARRAY) {
            DmSetFilterType(tree, DM_FILTER_CHILD);
            ret = QryParseSubtreeDbJsonArray(stmt, key, value, tree);
        } else {
            DmSetFilterType(tree, DM_FILTER_CONTENT);
            ret = QryParseField(key, value, tree);
        }

        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Parse subtree json, key=%s", key);
            return ret;
        }

        iter = DbJsonObjectIterNext(jsonNode, iter);
    }

    return GMERR_OK;
}

static Status QryParseSubtreeJson(QryStmtT *stmt, DbMemCtxT *memCtx, const TextT *subtreeJson, const char *rootName,
    QrySubtreeFilterItemT *filterItem)
{
    DB_POINTER4(stmt, subtreeJson, rootName, filterItem);

    DbJsonT *jsonRoot = DbLoadJsonSrv(subtreeJson->str, DB_JSON_REJECT_DUPLICATES);
    if (jsonRoot == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_JSON_CONTENT, "Unexpected subtree json :%s.", subtreeJson->str);
        return GMERR_INVALID_JSON_CONTENT;
    }

    QryLabelT *qryLabel = NULL;
    Status ret = QryGetQryLabelByName((QryStmtT *)stmt, rootName, &qryLabel);
    if (ret != GMERR_OK) {
        DbJsonDelete(jsonRoot);
        return ret;
    }
    if (!DmIsRootYangVertexLabel4QE(qryLabel->def.vertexLabel)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATATYPE_MISMATCH, "%s is not root.", rootName);
        DbJsonDelete(jsonRoot);
        return GMERR_DATATYPE_MISMATCH;
    }
    DmSubtreeT *tree = NULL;
    ret = DmCreateFilterSubtree(memCtx, qryLabel->desc, &tree);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "Create empty filter tree, vertexName=%s when parse subtree filter", rootName);
        DbJsonDelete(jsonRoot);
        return ret;
    }
    ret = QryParseSubtree(stmt, jsonRoot, tree);
    DbJsonDelete(jsonRoot);
    if (ret != GMERR_OK) {
        return ret;
    }
    filterItem->filter = tree;
    return ret;
}

static inline Status SwitchNodetree(DmSubtreeT *tree)
{
    if (DmIsVertexSubtree(tree)) {
        return GMERR_OK;
    }
    return DmNodeSetElementIndex(tree->node, tree->nodeElemIdx);
}

static DmTreeDescT *GetTreeDesc(QryStmtT *stmt, DmSubtreeT *parent, uint32_t id, uint32_t type)
{
    if (type == DM_TREE_DESC_VERTEX) {
        QryLabelT *qryLabel = NULL;
        Status ret = QryGetQryLabelById(stmt, id, &qryLabel);
        if (ret != GMERR_OK) {
            return NULL;
        }
        return qryLabel->desc;
    }
    if (parent == NULL) {
        return NULL;
    }
    return parent->desc->childDesc[id];
}

static Status QryParseAndGetTreeDesc(QryStmtT *stmt, DmSubtreeT *parent, FixBufferT *buf, DmTreeDescT **desc)
{
    // 节点类型
    uint32_t treeDescType = 0;
    Status ret = FixBufGetUint32(buf, &treeDescType);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t id = 0;
    ret = FixBufGetUint32(buf, &id);
    if (ret != GMERR_OK) {
        return ret;
    }
    *desc = GetTreeDesc(stmt, parent, id, treeDescType);
    if (*desc == NULL) {
        DB_LOG_ERROR(ret, "Get subtree desc, id(%" PRIu32 "), type:(%" PRIu32 ").", id, treeDescType);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

static Status QryParseSingleSubtreeObj(
    QryStmtT *stmt, DbMemCtxT *memCtx, DmSubtreeT *parent, FixBufferT *buf, DmSubtreeT **filterTree)
{
    DmTreeDescT *desc = NULL;
    Status ret = QryParseAndGetTreeDesc(stmt, parent, buf, &desc);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmSubtreeT **currTree = filterTree;  // 表示当前处理的树节点，在循环中会切换到兄弟树
    bool isExist = true;  // 表示节点是否存在，在循环中用于判断退出，此处使用while循环是为了减少递归调用深度，避免栈溢出
    while (isExist) {
        TextT subtreeBuf = {0};
        ret = FixBufGetObject(buf, &subtreeBuf);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DmDeSerializeSubtreeWithMemCtx(memCtx, (uint8_t *)subtreeBuf.str, subtreeBuf.len, desc, currTree);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Deseri subtree node.");
            return ret;
        }
        // 如果subtree是node类型，需要初始化, vertex类型就不必初始化直接返回
        ret = DmInitChildSubtree(parent, (*currTree), desc);
        if (ret != GMERR_OK) {
            return ret;
        }
        if ((ret = SwitchNodetree((*currTree))) != GMERR_OK) {
            return ret;
        }
        if ((*currTree)->type != DM_TREE_FILTER) {
            DB_LOG_ERROR(
                GMERR_DATA_EXCEPTION, "Deseri subtree node, tree type(%" PRIu32 ").", (uint32_t)((*currTree)->type));
            return GMERR_DATA_EXCEPTION;
        }
        // 获取子树和兄弟树的的flag信息,代表子树或兄弟树是否存在
        uint8_t *treeFlag = FixBufGetData(buf, ((*currTree)->desc->childNum + 1) * sizeof(uint8_t));
        if (treeFlag == NULL) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Get treeFlag from buffer.");
            return GMERR_DATA_EXCEPTION;
        }
        for (uint32_t i = 0; i < (*currTree)->desc->childNum; ++i) {
            if (treeFlag[i] == 0) {
                continue;
            }
            ret = QryParseSingleSubtreeObj(stmt, memCtx, (*currTree), buf, &(*currTree)->childTree[i]);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        isExist = treeFlag[(*currTree)->desc->childNum];
        currTree = &(*currTree)->nextTree;
    }
    return ret;
}

static Status QryParseSubtreeFilterDesc(FixBufferT *req, QrySubtreeFilterItemT *filterItem)
{
    Status ret = FixBufGetUint32(req, &filterItem->jsonFlag);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get json flag when parse subtree filter");
        return ret;
    }
    ret = FixBufGetUint32(req, &filterItem->maxDepth);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get filter max depth when parse subtree filter");
        return ret;
    }

    uint32_t isLocationFilter;
    ret = FixBufGetUint32(req, &isLocationFilter);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get location filter flag when parse subtree filter");
        return ret;
    }
    filterItem->isLocationFilter = isLocationFilter & GMC_LOCATION_FILTER;

    ret = FixBufGetUint32(req, &filterItem->defaultMode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get default mode when parse subtree filter");
        return ret;
    }
    ret = FixBufGetUint32(req, &filterItem->configFilter);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get configFilter when parse subtree filter");
        return ret;
    }
    return GMERR_OK;
}

static Status SetCurrFilter(QrySubtreeFilterDescT *filterDesc)
{
    // filterList中必然至少存在一棵树
    if (filterDesc->lastIndex >= filterDesc->filterList->count || filterDesc->filterList->count < 1) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_INVALID_PARAMETER_VALUE,
            "Set current filter, last index:%" PRIu32 ", count:%" PRIu32 ".", filterDesc->lastIndex,
            filterDesc->filterList->count);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    filterDesc->currFilter = (QrySubtreeFilterItemT *)DbGaListGet(filterDesc->filterList, filterDesc->lastIndex);
    if (filterDesc->currFilter == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Get currFilter from filterList by lastIndex.");
        return GMERR_DATA_EXCEPTION;
    }
    filterDesc->filterLabel = filterDesc->currFilter->filter->desc->vertexLabel;
    return GMERR_OK;
}

static inline bool ListFilterCursorIsEmpty(QryStmtT *stmt)
{
    DmSubtreeBatchLastTreeNodeT *lastTreeNodeInfo = &(stmt->session->qryLabelCtx->lastTreeNode);
    if (lastTreeNodeInfo->lastFilter != NULL && lastTreeNodeInfo->lastfilterRowId != 0) {
        return false;
    }
    return true;
}

Status QryParseSubtreeCursor(QryStmtT *stmt, QrySubtreeFilterDescT *filterDesc)
{
    DB_POINTER2(stmt, filterDesc);
    FixBufferT *req = stmt->session->req;
    Status ret = FixBufGetUint32(req, &filterDesc->lastIndex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get last Scan Cursor labelId.");
        return ret;
    }

    // 上一次索引为0，校验path树是否存在
    if (filterDesc->lastIndex == 0 && filterDesc->path == NULL && ListFilterCursorIsEmpty(stmt)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Filter path is null.");
        return GMERR_DATA_EXCEPTION;
    }
    // 设置当前过滤器
    return SetCurrFilter(filterDesc);
}

Status QryParseSubtreeFilter(QryStmtT *stmt)
{
    QrySubtreeFilterDescT *filterDesc = NULL;
    Status ret = QryCtxAllocMem(stmt->context, sizeof(QrySubtreeFilterDescT), (char **)&filterDesc);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Alloc mem for parse subtree filter.");
        return ret;
    }
    FixBufferT *req = stmt->session->req;
    if (stmt->session->qryLabelCtx == NULL) {
        ret = QryInitQryLabelCtx(stmt->session);
        if (ret != GMERR_OK) {
            QryCtxFreeMem(stmt->context, (char *)filterDesc);
            return ret;
        }
    }

    filterDesc->filterList = &stmt->session->qryLabelCtx->filterList;
    filterDesc->memCtx = stmt->session->qryLabelCtx->filterMemCtx;
    filterDesc->filterMode = stmt->session->qryLabelCtx->filterMode;
    filterDesc->defaultPrefix = stmt->session->qryLabelCtx->defaultPrefix;

    stmt->context->entry = (void *)filterDesc;
    stmt->context->isYangTrx = true;
    // 先获取hasCursor，如果hasCursor不为0，说明不是第一次查询，只需要解析cursor
    uint32_t hasCursor;
    ret = FixBufGetUint32(req, &hasCursor);
    if (ret != GMERR_OK) {
        QryCtxFreeMem(stmt->context, (char *)filterDesc);
        DB_LOG_ERROR(ret, "Parse subtree filter.");
        return ret;
    }
    if (hasCursor == DM_YANG_HAS_CURSOR) {
        // 只有分批返回时，才需对path进行赋值
        filterDesc->path = stmt->session->qryLabelCtx->path;
        ret = QryParseSubtreeCursor(stmt, filterDesc);
        if (ret != GMERR_OK) {
            QryCtxFreeMem(stmt->context, (char *)filterDesc);
            QryReleaseQryLabelCtx(stmt->session->qryLabelCtx);
        }
        return ret;
    }

    // 分批查询未完成就发起新请求的情况下，qryLabelCtx 可能有残留资源，故此处进行一次释放
    QryReleaseQryLabelCtx(stmt->session->qryLabelCtx);
    ret = QryParseSubtreeFilters(stmt, filterDesc);
    if (ret != GMERR_OK) {
        QryCtxFreeMem(stmt->context, (char *)filterDesc);
        QryReleaseQryLabelCtx(stmt->session->qryLabelCtx);
        return ret;
    }

    return GMERR_OK;
}

bool SetHasNestedList(DmSubtreeT *tree)
{
    bool hasNestedList = false;
    for (uint32_t i = 0; i < tree->desc->childNum; i++) {
        if (tree->childTree[i] == NULL) {
            continue;
        }
        if (SetHasNestedList(tree->childTree[i]) ||
            (DmIsVertexSubtree(tree->childTree[i]) && DmIsListVertexLabel4QE(tree->childTree[i]->desc->vertexLabel))) {
            hasNestedList = true;
        }
    }
    tree->hasNestedList = hasNestedList;
    return hasNestedList;
}

static void InitListFilter(DmSubtreeT *filter)
{
    (void)SetHasNestedList(filter);
}

static Status QryParseSingleSubtreeFilter(
    QryStmtT *stmt, FixBufferT *req, QrySubtreeFilterDescT *filterDesc, QrySubtreeFilterItemT *filterItem)
{
    DB_POINTER4(stmt, req, filterDesc, filterItem);
    DbMemCtxT *memCtx = filterDesc->memCtx;
    uint32_t filterMode = filterDesc->filterMode;

    Status ret = QryParseSubtreeFilterDesc(req, filterItem);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (filterMode == (uint32_t)GMC_FETCH_JSON) {
        TextT rootName = {0};
        ret = FixBufGetText(req, &rootName);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get root name when parse subtree filter");
            return ret;
        }
        TextT subtreeJson = {0};
        ret = FixBufGetText(req, &subtreeJson);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get subtree json when parse subtree filter, root=%s", rootName.str);
            return ret;
        }
        ret = QryParseSubtreeJson(stmt, memCtx, &subtreeJson, rootName.str, filterItem);
        if (ret != GMERR_OK) {
            return ret;
        }
        InitListFilter(filterItem->filter);
        return DbGaListInsert(filterDesc->filterList, filterItem);
    }

    // obj模式解析查询树
    ret = QryParseSingleSubtreeObj(stmt, memCtx, NULL, req, &filterItem->filter);
    if (ret != GMERR_OK) {
        return ret;
    }
    InitListFilter(filterItem->filter);
    return DbGaListInsert(filterDesc->filterList, filterItem);
}

static Status CompareFilterSubtreesByRootVertexLabelName(const void *item1, const void *item2, int32_t *result)
{
    const DmSubtreeT *filterTree1 = ((const QrySubtreeFilterItemT *)item1)->filter;
    const DmSubtreeT *filterTree2 = ((const QrySubtreeFilterItemT *)item2)->filter;
    if (filterTree1 == NULL || filterTree2 == NULL) {
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    *result = strcmp(filterTree1->desc->vertexLabel->metaVertexLabel->topRecordName,
        filterTree2->desc->vertexLabel->metaVertexLabel->topRecordName);
    return GMERR_OK;
}

static void QryCopyFilterItem(QrySubtreeFilterItemT *src, QrySubtreeFilterItemT *dest)
{
    if (src == NULL || dest == NULL) {
        return;
    }
    dest->configFilter = src->configFilter;
    dest->defaultMode = src->defaultMode;
    dest->jsonFlag = src->jsonFlag;
    dest->maxDepth = src->maxDepth;
    return;
}

static Status QryParseFullSubtreeFilters(
    QryStmtT *stmt, QrySubtreeFilterItemT *subtreeFilterItem, QrySubtreeFilterDescT *filterDesc)
{
    // 过滤获取namespace下根节点labelId链表
    Status ret;
    QryLabelT *qryLabel = NULL;
    DmVertexLabelT *vertexLabel = NULL;
    QrySubtreeFilterItemT *filterItem = NULL;
    DbOamapIteratorT iter = 0;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
    // 获取数据集下所有的根节点label, 创建对应的QrySubtreeFilterItemT 放到链表filterList中
    while ((vertexLabel = CataFetchVertexLabelByNspId(stmt->session->namespaceId, &iter, dbInstance)) != NULL) {
        if (!DmIsYangVertexLabel(vertexLabel) || !DmIsRootYangVertexLabel(vertexLabel) ||
            DmIsYangNpaVertexLabel(vertexLabel)) {
            (void)CataReleaseVertexLabel(vertexLabel);
            continue;
        }

        ret = QryGetQryLabelById((QryStmtT *)stmt, vertexLabel->metaCommon.metaId, &qryLabel);
        (void)CataReleaseVertexLabel(vertexLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
        DmSubtreeT *tree = NULL;
        ret = DmCreateFilterSubtree(filterDesc->memCtx, qryLabel->desc, &tree);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Create filter subtree name=%s.", vertexLabel->metaVertexLabel->topRecordName);
            return ret;
        }
        // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
        ret = QryAllocMem(filterDesc->memCtx, sizeof(QrySubtreeFilterItemT), (char **)&filterItem);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Alloc mem for parse full subtree filters.");
            return ret;
        }
        QryCopyFilterItem(subtreeFilterItem, filterItem);
        filterItem->filter = tree;
        ret = DbGaListInsert(filterDesc->filterList, filterItem);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Insert filterItem.");
            return ret;
        }
    }
    ret = DbGaListSort(filterDesc->filterList, CompareFilterSubtreesByRootVertexLabelName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Sort filter subtrees.");
        return ret;
    }
    // 设置当前过滤器
    return SetCurrFilter(filterDesc);
}

static Status QryParseSubtreeModelByName(QryStmtT *stmt, const char *rootName, QrySubtreeFilterDescT *filterDesc)
{
    QryLabelT *qryLabel = NULL;
    Status ret = QryGetQryLabelByName((QryStmtT *)stmt, rootName, &qryLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!DmIsYangVertexLabel(qryLabel->desc->vertexLabel) || !DmIsRootYangVertexLabel4QE(qryLabel->desc->vertexLabel)) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_INVALID_PARAMETER_VALUE, "Inv filter root name when parse subtree filter model.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DmSubtreeT *treeNode = NULL;
    // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
    ret = DmCreateEmptySubtree(filterDesc->memCtx, qryLabel->desc, DM_TREE_MODEL, &treeNode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Create empty subtree.");
        return ret;
    }
    QrySubtreeFilterItemT *filterItem = NULL;
    // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
    ret = QryAllocMem(filterDesc->memCtx, sizeof(QrySubtreeFilterItemT), (char **)&filterItem);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Alloc filter item.");
        return ret;
    }
    filterItem->filter = treeNode;
    ret = DbGaListInsert(filterDesc->filterList, filterItem);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Insert filter list.");
        return ret;
    }
    return SetCurrFilter(filterDesc);
}

static Status QryParseSubtreeModelAllRoot(QryStmtT *stmt, QrySubtreeFilterDescT *filterDesc)
{
    QryLabelT *qryLabel = NULL;
    DmVertexLabelT *vertexLabel = NULL;
    QrySubtreeFilterItemT *filterItem = NULL;
    DbOamapIteratorT iter = 0;
    Status ret = GMERR_OK;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
    // 获取数据集下所有的根节点label, 创建对应的QrySubtreeFilterItemT 放到链表filterList中
    while ((vertexLabel = CataFetchVertexLabelByNspId(stmt->session->namespaceId, &iter, dbInstance)) != NULL) {
        if (!DmIsYangVertexLabel(vertexLabel) || !DmIsRootYangVertexLabel4QE(vertexLabel)) {
            (void)CataReleaseVertexLabel(vertexLabel);
            continue;
        }
        ret = QryGetQryLabelById((QryStmtT *)stmt, vertexLabel->metaCommon.metaId, &qryLabel);
        (void)CataReleaseVertexLabel(vertexLabel);
        if (ret != GMERR_OK) {
            return ret;
        }
        DmSubtreeT *treeNode = NULL;
        // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
        ret = DmCreateEmptySubtree(filterDesc->memCtx, qryLabel->desc, DM_TREE_MODEL, &treeNode);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Create empty subtree.");
            return ret;
        }
        // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
        ret = QryAllocMem(filterDesc->memCtx, sizeof(QrySubtreeFilterItemT), (char **)&filterItem);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Alloc filter item.");
            return ret;
        }
        filterItem->filter = treeNode;
        ret = DbGaListInsert(filterDesc->filterList, filterItem);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Insert filter list when parse subtree model all root.");
            return ret;
        }
    }
    ret = DbGaListSort(filterDesc->filterList, CompareFilterSubtreesByRootVertexLabelName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Sort filter subtrees.");
        return ret;
    }
    // 设置当前过滤器
    return SetCurrFilter(filterDesc);
}

static Status QryParseSubtreeModel(
    QryStmtT *stmt, uint32_t filterCount, FixBufferT *req, QrySubtreeFilterDescT *filterDesc)
{
    DB_POINTER2(stmt, filterDesc);
    if (filterCount != 0) {
        QrySubtreeFilterItemT *filterItem = NULL;
        // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
        Status ret = QryAllocMem(filterDesc->memCtx, sizeof(QrySubtreeFilterItemT), (char **)&filterItem);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Alloc filter item.");
            return ret;
        }
        ret = QryParseSubtreeFilterDesc(req, filterItem);
        if (ret != GMERR_OK) {
            return ret;
        }
        uint32_t hasRootName = 0;
        ret = FixBufGetUint32(req, &hasRootName);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get hasRootName when parse subtree model");
            return ret;
        }
        TextT rootName = {0};
        if (hasRootName == 1) {
            ret = FixBufGetText(req, &rootName);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get root name when parse subtree filter");
                return ret;
            }
            return QryParseSubtreeModelByName(stmt, rootName.str, filterDesc);
        }
    }
    return QryParseSubtreeModelAllRoot(stmt, filterDesc);
}

static Status QryParseFullSubtree(
    QryStmtT *stmt, uint32_t filterCount, FixBufferT *req, QrySubtreeFilterDescT *filterDesc)
{
    Status ret = GMERR_OK;
    QrySubtreeFilterItemT *filterItem = NULL;
    if (filterCount != 0) {
        // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
        ret = QryAllocMem(filterDesc->memCtx, sizeof(QrySubtreeFilterItemT), (char **)&filterItem);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Alloc mem for parse full subtree.");
            return ret;
        }
        ret = QryParseSubtreeFilterDesc(req, filterItem);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return QryParseFullSubtreeFilters(stmt, filterItem, filterDesc);
}

Status QryParseSubtreeFiltersDefaultPrefix(FixBufferT *req, QrySubtreeFilterDescT *filterDesc)
{
    uint32_t hasDefaultPrefix = false;
    Status ret = FixBufGetUint32(req, &hasDefaultPrefix);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get yang namespace flag when parse subtree filter.");
        return ret;
    }
    if ((bool)hasDefaultPrefix) {
        TextT defaultPrefix = {0};
        ret = FixBufGetText(req, &defaultPrefix);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get filter mode when parse subtree filter.");
            return ret;
        }
        ret = QryAllocMem(filterDesc->memCtx, defaultPrefix.len, (char **)&filterDesc->defaultPrefix);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Alloc mem for parse subtree filter.");
            return ret;
        }
        errno_t err = strcpy_s(filterDesc->defaultPrefix, defaultPrefix.len, defaultPrefix.str);
        if (SECUREC_UNLIKELY(err != EOK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_MEMORY_OPERATE_FAILED, "Copy str when set default prefix str value.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    return GMERR_OK;
}

Status QryParseSubtreeFilters(QryStmtT *stmt, QrySubtreeFilterDescT *filterDesc)
{
    DB_POINTER2(stmt, filterDesc);
    FixBufferT *req = stmt->session->req;
    Status ret = FixBufGetUint32(req, &filterDesc->filterMode);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get filter mode when parse subtree filter.");
        return ret;
    }

    if (filterDesc->filterMode >= (uint32_t)GMC_FETCH_BUTT) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_INVALID_PARAMETER_VALUE, "Unexpected filter mode when parse subtree filter.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    ret = QryParseSubtreeFiltersDefaultPrefix(req, filterDesc);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t filterCount = 0;
    ret = FixBufGetUint32(req, &filterCount);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get filter list count when parse subtree filter.");
        return ret;
    }
    // 全量查询
    if (DmSubtreeFilterIsFetchFullMode(filterDesc->filterMode)) {
        return QryParseFullSubtree(stmt, filterCount, req, filterDesc);
    }
    // 模型查询
    if (filterDesc->filterMode == (uint32_t)GMC_FETCH_MODEL ||
        filterDesc->filterMode == (uint32_t)GMC_FETCH_MODEL_LIST) {
        return QryParseSubtreeModel(stmt, filterCount, req, filterDesc);
    }

    for (uint32_t i = 0; i < filterCount; i++) {
        QrySubtreeFilterItemT *filterItem = NULL;
        // memCtx：统一释放，详见头文件注释Note1；并发：不涉及，详见头文件注释Note2
        ret = QryAllocMem(filterDesc->memCtx, sizeof(QrySubtreeFilterItemT), (char **)&filterItem);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Alloc mem for parse subtree filter.");
            return ret;
        }
        ret = QryParseSingleSubtreeFilter(stmt, req, filterDesc, filterItem);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "Get filter %u when parse subtree filter", i);
            return ret;
        }
    }

    // 设置当前过滤器
    return SetCurrFilter(filterDesc);
}

#ifdef __cplusplus
}
#endif
