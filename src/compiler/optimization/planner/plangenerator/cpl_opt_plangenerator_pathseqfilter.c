/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: PathSeqFilter plan generator rule of planner
 * Author: gongsai
 * Create: 2024-2-27
 */

#ifdef FEATURE_GQL
#include "dm_meta_complex_path.h"
#include "cpl_opt_plangenerator.h"
#include "cpl_opt_plangenerator_common.h"
#include "cpl_opt_log.h"

#ifdef __cplusplus
extern "C" {
#endif

Status Phy2PlanPathSeqFilterRule(PlannerInfoT *info, const IRExprT *phyExp, PlanT **plan)
{
    PathSeqFilterT *planPathSeqFilter = NULL;
    Status ret =
        InitExecPlanByType(info, (PlanT **)&planPathSeqFilter, NULL, T_GQL_PATH_SEQFILTER, sizeof(PathSeqFilterT));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init plan in Phy2PlanPathSeqFilterRule.");
        return ret;
    }
    IRExprT *child = phyExp->children[0];
    ret = TransPhyExpr2Plan(info, child, &(planPathSeqFilter->base.leftTree));
    if (ret != GMERR_OK) {
        return ret;
    }

    OpPhyPathSeqFilterT *phyPathSeqFilter = (OpPhyPathSeqFilterT *)(void *)phyExp->op;
    DbMemCtxT *memCtx = info->memCtx;
    planPathSeqFilter->vertexLabelId = phyPathSeqFilter->vertexLabel->metaCommon.metaId;
    DbCreateList(&planPathSeqFilter->conditions, sizeof(DmPathConditionT), memCtx);
    uint32_t condNum = DbListGetItemCnt(&phyPathSeqFilter->conditions);
    for (uint32_t i = 0; i < condNum; ++i) {
        DmPathConditionT *srcCond = (DmPathConditionT *)DbListItem(&phyPathSeqFilter->conditions, i);
        DmPathConditionT *tmpCond = (DmPathConditionT *)DbDynMemCtxAlloc(memCtx, sizeof(DmPathConditionT));
        if (tmpCond == NULL) {
            // 内存释放点：前面[0,i-1]的tmpCond所在memctx，统一在外部GeneratePathTrigPlanCtxForPathSearching函数异常分支中释放
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc tmpCond size is %zu.", sizeof(DmPathConditionT));
            return GMERR_OUT_OF_MEMORY;
        }
        tmpCond->propId = srcCond->propId;
        ret = DmValueCopy(memCtx, &srcCond->value, &tmpCond->value);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(memCtx, tmpCond);
            DB_LOG_ERROR(ret, "translate the operator PathSeqFilter.");
            return ret;
        }
        ret = DbAppendListItem(&planPathSeqFilter->conditions, tmpCond);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(memCtx, tmpCond);
            DB_LOG_ERROR(ret, "translate the operator PathSeqFilter.");
            return ret;
        }
    }

    *plan = (PlanT *)(void *)planPathSeqFilter;
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
#endif  // FEATURE_GQL
