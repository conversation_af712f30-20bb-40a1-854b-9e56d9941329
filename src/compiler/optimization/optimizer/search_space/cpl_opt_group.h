/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: header file for CBO base class of group, storing logical equivalent m-expression
 * Author: g<PERSON><PERSON>hao
 * Create: 2022-10-09
 */

#ifndef CPL_OPT_GROUP_H
#define CPL_OPT_GROUP_H

#include "db_list.h"

#include "cpl_opt_statistics.h"
#include "cpl_opt_cost_physical.h"
#include "cpl_opt_multi_expression.h"
#include "cpl_opt_context.h"
#include "cpl_opt_memo.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    bool extended;   // Group是否已被extend
    bool optimized;  // Group是否已优化完成
} GroupStateT;       // 表示Group当前的状态

typedef struct Group {
    uint32_t grpId;         // Group的Id
    MExprT *firstLogMExpr;  // 第一个Logical Multi-Expression
    MExprT *lastLogMExpr;   // 最后一个Logical Multi-Expression
    MExprT *firstPhyMExpr;  // 第一个Physical Multi-Expression
    MExprT *lastPhyMExpr;   // 最后一个Physical Multi-Expression
    GroupStateT state;      // 表示Group当前的状态
    StatsT stats;           // Group的statistics
    double lowerBound;      // Group的cost下界
    DbListT winners;        // 存的是WinnerT
} GroupT;

typedef struct Winner {
    MExprT *mExpr;       // 该Winner对应的Multi-Expression
    PhyPropT *physProp;  // 该Winner包含的物理属性
    double cost;         // 该Winner的物理代价
    bool done;           // 该Winner是否已被优化结束
} WinnerT;

// GetGroupOptStatusInCtx 函数的返回值. 一共有 4 种 case, 按顺序对应四种枚举类型
// CASE 1: 该 Group 已经指定的 Context 下优化过, 没有满足要求的物理计划, 也不可能再找到合法的物理计划
// CASE 2: 该 Group 已经指定的 Context 下优化过, 有满足要求的物理计划, 且一定是代价最低的计划
// CASE 3: 该 Group 从未在当前的 物理属性 要求下优化过
// CASE 4: 该 Group 已经在指定的 Context 下优化过, 且找不到物理计划. 但在当前的 ctx 下再次优化可能会找到合法的物理计划.
// CASE 4 产生的场景是上一次优化指定的代价上界 < 此次优化指定的代价上界. 更高的代价上界意味着可能搜索到合法的物理计划.
typedef enum GroupOptStatus {
    DONE_OPTIMIZED_HAS_NO_PLAN,
    DONE_OPTIMIZED_HAS_OPTIMAL_PLAN,
    NEVER_OPTIMIZED,
    OPTIMIZED_ON_LOWER_UPPER_BOUND
} GroupOptStatusE;

/**
 * @brief 根据Multi-Expression创建Group，Group的内存申请由函数内部实现
 * @param[in] memo 搜索空间
 * @param[in] mExpr 输入的Multi-Expression
 * @param[in/out] group 输入声明的Group，输出新创建的Group
 * @return success或错误码
 */
Status InitGroup(MemoT *memo, MExprT *mExpr, GroupT *group);

/**
 * @brief 获取在指定 Context 下的 Group 的优化状态, 同时返回对应的 Winner. 对应 Columbia 的 SearchCircle
 * 有以下四种可能的状态 (可以参考 GroupOptStatusE 的定义)
   (1) There is no possibility of satisfying Context
   (2) There is a non-null winner which satisfies Context
   (3) More search is needed, and there has been no search
       for this property before
   (4) More search is needed, and there has been a search for
       this property before.
 * @param[in]  group
 * @param[in]  context
 * @param[out] winner  在指定 context 下的 group 的 winner (二级指针). 有可能为 NULL
 * @return Group 在当前 Context 下的优化状态
 */
GroupOptStatusE GetGroupOptStatusByCtx(GroupT *group, OptCtxT *context, WinnerT **winner);

/**
 * @brief 查找Winners中满足指定物理属性的Winner
 * @param[in] group 待查找的Group
 * @param[in] phyProp 物理属性
 * @return 满足该物理属性的Winner的指针，若不存在返回NULL
 */
WinnerT *GetWinnerByPhyProp(GroupT *group, PhyPropT *phyProp);

/**
 * @brief 将指定的 winner 添加到对应的 Group 中 (浅拷贝)
 * @param[in] winner 要添加到 group 中的新 winner
 * @param[in] group  目标 Group
 * @return 执行成功返回 GMERR_OK, 否则返回相应的错误码
 */
Status AddWinnerToGroup(WinnerT *winner, GroupT *group);

#ifdef __cplusplus
}
#endif
#endif  // CPL_OPT_GROUP_H
