%{
#include "cpl_public_sql_parser_common.h"

// function defined in lexer file
extern int ts_lex(void);
extern int core_yylex(void);
extern void ts_error(const char *msg);

%}

%union {
    int64_t ival64;
    double fval;
    uint32_t uival32;
    uint8_t uival8;
    char *str;
    bool boolean;
    struct {
        uint32_t index;
        char *name;
        bool isIndexed;
    } para;
    DbListT list;
    DbListT *plist;
    NodeT *node;
    SqlColumnDefT *column;
    SqlTableRefT *qname;
    SqlExprT *expr;
    ColumnCompressModeE columnCompressMode;
    SqlIndexInfoT *indexName;
    SqlCreateIdxInTblT *indexItem;
    SqlIndexColumnT *indexColumn;
    SqlTargetT *target;
    SqlGroupByT *groupby;
    SqlOrderByT *orderby;
    SqlLimitT *limit;
    SqlNullsPosTypeE nullspos;
    SqlFullNameT  *fname;
    SqlCreateWithT *cwith;
    SqlCopyToStmtT *copyto;
    SqlSetItemT *setItem;
    DmPrivilegeE privilegeVal;
}

%define api.prefix "ts_"
%file-prefix "ts"

%left       OR
%left       AND
%right      NOT
%left       IS IS_NOT ISNULL NOTNULL BETWEEN S_IN LIKE NOT_EQUALS EQUALS
%right      ESCAPE
%left       '<' '>' LESS_EQUALS GREATER_EQUALS
%left       '&' '|' LSHIFT RSHIFT
%left       '+' '-'
%left       '*' '/' '%'
%left       CONCAT
%right      UMINUS   // operation priority for unary operator '+' '-'
%right      '~'
%left       '(' ')'

/* ts unused token */
%token S_CHECK ESCAPE GLOB
%token <para> PARAMETER_TS PARAMETER
%token L2_DISTANCE COSINE_DISTANCE SAVEPOINT RELEASE CURRENT_DATE CURRENT_TIME CURRENT_TIMESTAMP DO NOTHING HAVING
%token EXPLAIN QUERY PLAN LOGICAL PHYSICAL
%token UNION EXCEPT INTERSECT
%token TRIGGER BEFORE AFTER INSTEAD OF FOR EACH ROW WHEN RETURNING
%token S_BEGIN COMMIT END TRANSACTION VIEW
%token FOREIGN RESTRICT CASCADE NOACTION DEFERRABLE INITIALLY REFERENCES DEFERRED IMMEDIATE COLLATE NOCASE BINARY

/* token for common */
%token <ival64> SEMICOLON
%token <boolean> S_TRUE S_FALSE
%token <str> SQL_ID SQL_STRING INTEGER_STRING DOUBLE_STRING
%token FAIL IGNORE REPLACE ROLLBACK ABORT ON ON_CONFLICT ASC DESC OR INDEX INDEXED BY DEFAULT USING
%token JOIN INNER OUTER CROSS NATURAL FULL LEFT RIGHT

/* token for alter table */
%token COLUMN ADD RENAME ALTER

/* token for create table */
%token CREATE TABLE S_PRIMARY S_UNIQUE TEMPORARY S_IF EXISTS NOT S_NULL WITH AUTOINCREMENT CONSTRAINT KEY
%token CODEC NO

/* token for copy to statement */
%token COPY TO

/* token for insert statement */
%token INSERT INTO VALUES

/* token for delete statement */
%token DELETE

/* token for update statement */
%token UPDATE SET

/* token for select statement */
%token SELECT FROM WHERE AS
%token GROUP ORDER NULLS LIMIT OFFSET

/* token for drop statement */
%token DROP

/* token for truncate statement */
%token TRUNCATE

/* token for aggregate function */
%token SUM MAX MIN AVG COUNT

/* token for distinct (only support aggregate function now) */
%token ALL DISTINCT

/* token for grant revoke */
%token GRANT REVOKE PUBLIC PRIVILEGES

/* common non-terminals */
%type <list> cmdlist
%type <node> cmd

%type <str> name opt_dot_name opt_as_name type_name file_name
%type <qname> qualified_name table_name string_name
%type <expr> expr expr_base literal_value column_ref char_len_value parameter

/* non-terminals for create table/view statement */
%type <node> create_table create_view
%type <column> column_def
%type <boolean> opt_temp opt_not_exists opt_exists
%type <uival32> opt_table_options opt_type_char_len
%type <uival8> opt_sort_order
%type <columnCompressMode> opt_column_compress_mode column_compress_mode
%type <plist> column_list create_with_clause create_with_list set_list index_column_list index_list indexed_columns opt_index
%type <cwith> create_with_single
%type <setItem> set_single
%type <indexName> index_name
%type <indexItem> index_item
%type <indexColumn> index_column

/* non-terminals for select statement */
%type <node> copy_to_stmt
%type <copyto> simple_copy_to

/* non-terminals for select statement */
%type <node> select_stmt simple_select
%type <plist> target_list from_clause opt_groupby opt_orderby groupby_list orderby_list insert_list name_list
%type <target> target_single
%type <groupby> groupby_single
%type <orderby> orderby_single
%type <nullspos> opt_nulls_position
%type <limit> opt_limit
%type <plist> from_list func_arg_list

%type <expr> opt_where opt_having
%type <plist> expr_list

/* non-terminals for drop statement */
%type <node> drop_stmt
%type <fname> drop_name
%type <ival64> drop_type

/* non-terminals for Truncate statement */
%type <node> truncate_stmt
%type <fname> truncate_name
%type <ival64> truncate_type

/* non-terminals for insert into table statement */
%type <node> insert_into_stmt

/* non-terminals for alter table statement */
%type <node> alter_table

/* non-terminals for aggregate functions */
%type <expr> agg_func

/* non-terminals for grant revoke */
%type <node> grant_stmt revoke_stmt
%type <privilegeVal> priv_spec priv_list all_priv priv_type priv_suffix
%type <plist> user_list user_process_list
%type <str> user_process

%start cmdlist

%%
cmdlist: cmdlist SEMICOLON cmd
    {
        //append to SqlPList
        SqlParsedListT *sqlPList = SqlGetParseList();
        SqlListAppend(&sqlPList->cmdList, &($3));
    }
    | cmd
    {
        //alloc SqlPList and append
        SqlParsedListT *sqlPList = SqlGetParseList();
        DbCreateListWithExtendSize(&sqlPList->cmdList, sizeof(NodeT *), SQL_LIST_EXTEND_SIZE, sqlPList->memCtx);
        SqlListAppend(&sqlPList->cmdList, &($1));
    }
    ;

cmd:
    create_table
    | copy_to_stmt
    | select_stmt
    | drop_stmt
    | insert_into_stmt
    | alter_table
    | create_view
    | grant_stmt
    | revoke_stmt
    | truncate_stmt
    ;

create_table :
    CREATE opt_temp TABLE opt_not_exists qualified_name '(' column_list opt_index ')' create_with_clause opt_table_options
        {
            SqlCreateTableStmtT *createStmt = (SqlCreateTableStmtT *)SqlCreateStruct(sizeof(SqlCreateTableStmtT));
            createStmt->tag = T_TS_CREATE_TABLE_STMT;
            createStmt->isTemp = $2;
            SqlParserConditionAbort($2 != false, "do not support temp table now", GMERR_FEATURE_NOT_SUPPORTED);
            createStmt->ifNotExists = $4;
            createStmt->name = $5;
            createStmt->columns = $7;
            createStmt->idxList = $8;
            createStmt->constraints = NULL;
            createStmt->createWith = $10;
            createStmt->tabFlags = $11;
            createStmt->query = NULL;
            $$ = (NodeT *)createStmt;
        }
    | CREATE opt_temp TABLE opt_not_exists qualified_name AS select_stmt
        {
            // not support now
            $$ = NULL;
        }
    ;

create_view:
    CREATE opt_temp VIEW opt_exists qualified_name create_with_clause AS select_stmt
        {
            SqlCreateTableStmtT *createViewStmt = (SqlCreateTableStmtT *)SqlCreateStruct(sizeof(SqlCreateTableStmtT));
            createViewStmt->tag = T_TS_CREATE_VIEW_STMT;
            createViewStmt->isTemp = $2;
            SqlParserConditionAbort($2 != false, "do not support temp view now", GMERR_FEATURE_NOT_SUPPORTED);
            createViewStmt->ifNotExists = $4;
            SqlParserConditionAbort($5->database != NULL, "do not support schema name now", GMERR_FEATURE_NOT_SUPPORTED);
            createViewStmt->name = $5;
            createViewStmt->columns = NULL;
            createViewStmt->constraints = NULL;
            createViewStmt->createWith = $6;
            createViewStmt->tabFlags = 0;
            createViewStmt->query = $8;
            $$ = (NodeT *)createViewStmt;
        }
    ;

opt_temp:
    TEMPORARY   { $$ = true; }
    | /*EMPTY*/ { $$ = false; }
    ;

opt_not_exists:
    S_IF NOT EXISTS { $$ = true; }
    | /*EMPTY*/     { $$ = false; }
    ;

opt_exists:
    S_IF EXISTS { $$ = true; }
    | /*EMPTY*/ { $$ = false; }
    ;

qualified_name:
    name opt_dot_name
        {
            SqlTableRefT *tableRef = (SqlTableRefT *)SqlCreateStruct(sizeof(SqlTableRefT));
            if ($2 == NULL) {
                tableRef->database = NULL;
                tableRef->tableName = $1;
            } else {
                char *tableName = SqlCatTableName($1, $2);
                tableRef->database = NULL;
                tableRef->tableName = tableName;
            }
            tableRef->aliasName = NULL;
            $$ = tableRef;
        }
    ;

column_list:
    column_list ',' column_def
        {
            ($3)->colIdx = DbListGetItemCnt($1);
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    | column_def
        {
            DbListT *columnList = SqlCreateList(sizeof(SqlColumnDefT *));
            ($1)->colIdx = 0;
            SqlListAppend(columnList, &($1));
            $$ = columnList;
        }
    ;

column_def:
    name type_name opt_type_char_len opt_column_compress_mode
        {
            SqlColumnDefT *columnDef = (SqlColumnDefT *)SqlCreateStruct(sizeof(SqlColumnDefT));
            columnDef->hName = DbStrToHash32($1);
            columnDef->colName = $1;
            columnDef->typeName = $2;
            columnDef->charLen = $3;
            columnDef->columnCompressMode = $4;
            columnDef->constraints = NULL;
            $$ = columnDef;
        }
    ;

opt_index:
    ',' index_list
        {
            $$ = $2;
        }
    |
        {
            $$ = NULL;
        }
    ;

index_list:
    index_list ',' index_item
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    | index_item
        {
            DbListT *indexItems = SqlCreateList(sizeof(SqlCreateIdxInTblT *));
            SqlListAppend(indexItems, &($1));
            $$ = indexItems;
        }
    ;

index_item:
    INDEX index_name indexed_columns
        {
            SqlCreateIdxInTblT *indexItem = (SqlCreateIdxInTblT *)SqlCreateStruct(sizeof(SqlCreateIdxInTblT));
            indexItem->indexInfo = $2;
            indexItem->indexColList = $3;
            $$ = indexItem;
        }
    ;

indexed_columns:
    '(' index_column_list ')'
        {
            $$ = $2;
        }
    ;

index_column_list:
    index_column_list ',' index_column
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    | index_column
        {
            DbListT *indexCols = SqlCreateList(sizeof(SqlIndexColumnT *));
            SqlListAppend(indexCols, &($1));
            $$ = indexCols;
        }
    ;

index_column:
    column_ref
        {
            SqlIndexColumnT *indexColumn = (SqlIndexColumnT *)SqlCreateStruct(sizeof(SqlIndexColumnT));
            indexColumn->columnExpr = $1;
            indexColumn->distanceFuncName = NULL;
            indexColumn->collate = NULL;
            indexColumn->sortType = SQL_SORT_UNDEFINED;
            $$ = indexColumn;
        }
    ;

index_name:
    name opt_dot_name
        {
            SqlIndexInfoT *indexName = (SqlIndexInfoT *)SqlCreateStruct(sizeof(SqlIndexInfoT));
            if ($2 == NULL) {
                indexName->database = NULL;
                indexName->indexName = $1;
            } else {
                char *catName = SqlCatTableName($1, $2);
                indexName->database = NULL;
                indexName->indexName = catName;
            }
            $$ = indexName;
        }
    ;

parameter:
    PARAMETER
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            if (sqlPList->unusedPos > 0) {
                // unusedPos > 0说明有多于一条语句。
                // 因为只有Prepare语句会有绑定参数，而Prepare只会执行一条语句，因此后续绑定参数不再进行解析
                $$ = NULL;
            } else {
                if ($1.isIndexed == true || $1.name != NULL) {
                    SqlParserConditionAbort(true, "Unable to make indexed para", GMERR_SYNTAX_ERROR);
                }
                Status ret = SqlParserExprMakePara(sqlPList, $1.index, $1.name, &$$);
                SqlParserConditionAbort($$ == NULL || ret != GMERR_OK, "Failed to make tagged para", ret);
            }
        }

type_name:
    name        { $$ = $1; }
    | /*EMPTY*/ { $$ = NULL; }
    ;

opt_type_char_len:
    '(' char_len_value ')'
    {
        SqlExprConstT *constVal =  CastToSqlExprConst($2->op);
        SqlParserConditionAbort(constVal->arg.value.longValue <= 0,
            "Char length should be larger than 0.", GMERR_INVALID_PARAMETER_VALUE);
        $$ = constVal->arg.value.longValue;
    }
    | /*EMPTY*/ { $$ = 0; }
    ;


opt_column_compress_mode:
    CODEC '(' column_compress_mode ')'
    {
       $$ = $3;
    }
    | /*EMPTY*/ { $$ = 0; }
    ;

column_compress_mode:
   NO
   {
      $$ = COMPRESS_NONE;
   }
   | /*EMPTY*/ { $$ = 0; }
   ;

opt_sort_order:
    ASC         { $$ = SQL_SORT_ASC; }
    | DESC      { $$ = SQL_SORT_DESC; }
    | /*EMPTY*/ { $$ = SQL_SORT_UNDEFINED; }
    ;

create_with_clause:
    WITH '(' create_with_list ')'
        {
            $$ = $3;
        }
    | /*EMPTY*/
        {
            SqlParseAddError();
        }
    ;

create_with_list:
    create_with_single
        {
            DbListT *cwithList = (DbListT *)SqlCreateList(sizeof(SqlCreateWithT));
            SqlListAppend(cwithList, &($1));
            $$ = cwithList;
        }
    | create_with_list ',' create_with_single
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

create_with_single:
    name EQUALS SQL_STRING
        {
            SqlCreateWithT *cwith = (SqlCreateWithT *)SqlCreateStruct(sizeof(SqlCreateWithT));
            cwith->key = $1;
            cwith->value = $3;
            $$ = cwith;
        }
    | name EQUALS INTEGER_STRING
        {
            SqlCreateWithT *cwith = (SqlCreateWithT *)SqlCreateStruct(sizeof(SqlCreateWithT));
            cwith->key = $1;
            cwith->value = $3;
            $$ = cwith;
        }
    | name EQUALS S_TRUE
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlCreateWithT *cwith = (SqlCreateWithT *)SqlCreateStruct(sizeof(SqlCreateWithT));
            TextT dstText;
            Status ret = DbCopyStrToText(sqlPList->memCtx, &dstText, "true", strlen("true"));
            SqlParserConditionAbort(ret != GMERR_OK, "Unable to copy const string", ret);
            cwith->key = $1;
            cwith->value = dstText.str;
            $$ = cwith;
        }
    | name EQUALS S_FALSE
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlCreateWithT *cwith = (SqlCreateWithT *)SqlCreateStruct(sizeof(SqlCreateWithT));
            TextT dstText;
            Status ret = DbCopyStrToText(sqlPList->memCtx, &dstText, "false", strlen("false"));
            SqlParserConditionAbort(ret != GMERR_OK, "Unable to copy const string", ret);
            cwith->key = $1;
            cwith->value = dstText.str;
            $$ = cwith;
        }
    ;

opt_table_options:
    /*EMPTY*/ { $$ = 0; }
    ;

alter_table:
    ALTER TABLE opt_exists qualified_name ADD opt_column column_def
        {
            SqlAlterTableStmtT *alterTableStmt = (SqlAlterTableStmtT *)SqlCreateStruct(sizeof(SqlAlterTableStmtT));
            alterTableStmt->tag = T_TS_ALTER_TABLE_STMT;
            alterTableStmt->ifExists = $3;
            alterTableStmt->tableName = $4;
            alterTableStmt->column = $7;
            alterTableStmt->type = T_ADD_COLUMN;
            $$ = (NodeT *)alterTableStmt;
        }
    | ALTER TABLE opt_exists qualified_name SET '(' set_list ')'
    	{
   	    SqlAlterTableStmtT *alterTableStmt = (SqlAlterTableStmtT *)SqlCreateStruct(sizeof(SqlAlterTableStmtT));
            alterTableStmt->tag = T_TS_ALTER_TABLE_STMT;
            alterTableStmt->ifExists = $3;
            alterTableStmt->tableName = $4;
            alterTableStmt->setList = $7;
            alterTableStmt->type = T_SET_FEATURE;
            $$ = (NodeT *)alterTableStmt;
    	}
    ;

set_list:
    set_single
        {
            DbListT *setItemList = (DbListT *)SqlCreateList(sizeof(SqlSetItemT));
            SqlListAppend(setItemList, &($1));
            $$ = setItemList;
        }
    | set_list ',' set_single
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

set_single:
    create_with_single
    	{
    	    $$ = (SqlSetItemT *)$$;
    	}
    |
    	{
    	    SqlParseAddError();
    	}
    ;

opt_column:
    COLUMN
    | /* EMPTY */
    ;

/*
    Grammar rules for common non-terminals
*/

name:
    SQL_ID { $$ = $1; }
    ;

opt_dot_name:
    '.' name { $$ = $2; }
    |   { $$ = NULL; }
    ;

opt_as_name:
    AS name { $$ = $2; }
    | SQL_ID { $$ = $1; }
    | { $$ = NULL; }
    ;

/*
    Copy To Statements
*/
copy_to_stmt:
    simple_copy_to
        {
            $$ = (NodeT *)$1;
        }
    ;

simple_copy_to:
    COPY '(' select_stmt ')' TO file_name
        {
            SqlCopyToStmtT *copyTo = (SqlCopyToStmtT *)SqlCreateStruct(sizeof(SqlCopyToStmtT));
            copyTo->tag = T_TS_COPY_TO_STMT;
            copyTo->queryStmt = $3;
            copyTo->fileNameLen = strlen($6) + 1;
            copyTo->fileName = $6;
            $$ = copyTo;
        }

file_name:
    SQL_STRING    { $$ = $1; }
    ;

/*
    Simple Select Statements
*/
select_stmt:
    simple_select
        {
            $$ = $1;
        }
    /* DO NOT SUPPORT MULTI SELECT NOW*/
    ;

simple_select:
    SELECT target_list from_clause opt_where
        opt_groupby opt_having opt_orderby opt_limit
        {
            SqlSelectStmtT *select = (SqlSelectStmtT *)SqlCreateStruct(sizeof(SqlSelectStmtT));
            select->tag = T_TS_SELECT_STMT;
            select->targetList = $2;
            select->fromClause = $3;
            select->whereClause = $4;
            select->groupBy = $5;
            select->havingClause = $6;
            select->orderBy = $7;
            select->limitClause = $8;
            select->multiSelectOp = SELECT_SIMPLE;
            select->pPrior = NULL;
            select->pNext = NULL;
            select->trigSrcSchema = NULL;
            select->withClause = NULL;
            select->withLink = NULL;
            select->isDistinct = false;
            select->isInConQryView = false;
            $$ = (NodeT *)select;
        }
    ;


target_list:
    target_single
        {
            DbListT *newList = SqlCreateList(sizeof(SqlTargetT *));
            SqlListAppend(newList, &($1));
            $$ = newList;
        }
    | target_list ',' target_single
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

target_single:
    expr opt_as_name
        {
            SqlTargetT *target = (SqlTargetT *)SqlCreateStruct(sizeof(SqlTargetT));
            target->isStar = false;
            target->value = $1;
            target->aliasName = $2;
            $$ = target;
        }
    | name '.' '*'
        {
            SqlTargetT *target = (SqlTargetT *)SqlCreateStruct(sizeof(SqlTargetT));
            target->isStar = true;
            target->aliasName = NULL;
            SqlParsedListT *sqlPList = SqlGetParseList();
            target->value = SqlExprMakeColumn(sqlPList->memCtx, NULL, $1, NULL);
            SqlParserConditionAbort(target->value == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
            $$ = target;
        }
    | '*'
        {
            SqlTargetT *target = (SqlTargetT *)SqlCreateStruct(sizeof(SqlTargetT));
            target->isStar = true;
            target->aliasName = NULL;
            // to make test easy
            SqlParsedListT *sqlPList = SqlGetParseList();
            target->value = SqlExprMakeColumn(sqlPList->memCtx, NULL, NULL, "*");
            SqlParserConditionAbort(target->value == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
            $$ = target;
        }
    ;

from_clause:
    FROM from_list
        {
            $$ = $2;
        }
    | /* EMPTY */ { $$ = NULL; }
    ;

from_list:
    table_name
        {
            $$ = SqlCreateList(sizeof(SqlSrcItemT *));
            SqlSrcAddTable($$, $1, NULL, NULL);
        }
    /* DO NOT SUPPORT MULTI TABLES NOW*/
    ;

opt_where:
    WHERE expr
        {
            $$ = $2;
        }
    | /* EMPTY */
        { $$ = NULL; }
    ;

opt_groupby:
    GROUP BY groupby_list
        {
            $$ = $3;
        }
    | /* EMPTY */
        { $$ = NULL; }
    ;

groupby_list:
    groupby_single
        {
            DbListT *groupByList = SqlCreateList(sizeof(SqlGroupByT *));
            SqlListAppend(groupByList, &($1));
            $$ = groupByList;
        }
    | groupby_list ',' groupby_single
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

groupby_single:
    column_ref
        {
            SqlGroupByT *groupBy = (SqlGroupByT *)SqlCreateStruct(sizeof(SqlGroupByT));
            groupBy->key = $1;
            $$ = groupBy;
        }
    ;

opt_having:    /* EMPTY */ { $$ = NULL; }
    ;

opt_orderby:
    ORDER BY orderby_list
        {
            $$ = $3;
        }
    |/* EMPTY */ { $$ = NULL; }
    ;

orderby_list:
    orderby_single
        {
            DbListT *orderByList = SqlCreateList(sizeof(SqlOrderByT *));
            SqlListAppend(orderByList, &($1));
            $$ = orderByList;
        }
    | orderby_list ',' orderby_single
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

orderby_single:
    expr opt_sort_order opt_nulls_position
        {
            SqlOrderByT *orderBy = (SqlOrderByT *)SqlCreateStruct(sizeof(SqlOrderByT));
            orderBy->sortExpr = $1;
            if ($2 == SQL_SORT_UNDEFINED) {
                orderBy->order = SQL_SORT_ASC;
            }
            else {
                orderBy->order = $2;
            }
            orderBy->nullsPos = $3;
                        $$ = orderBy;
        }
    ;

opt_nulls_position:
    NULLS name
        {
            SqlParserConditionAbort($2 == NULL, "Unable to parse empty nulls position.", GMERR_SYNTAX_ERROR);
            if (DbStrCmp($2, "first", true) == 0) {
                $$ = SQL_SORT_NULLS_FIRST;
            } else if (DbStrCmp($2, "last", true) == 0) {
                $$ = SQL_SORT_NULLS_LAST;
            } else {
                SqlParserConditionAbort(true, "Unable to parse unexpected nulls position.", GMERR_SYNTAX_ERROR);
            }
        }
    | /* EMPTY */ { $$ = SQL_SORT_NULLS_FIRST; }
    ;

func_arg_list:
    expr
        {
            DbListT *newList = SqlCreateList(sizeof(SqlExprT *));
            SqlListAppend(newList, &($1));
            $$ = newList;
        }
    | func_arg_list ',' expr
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

agg_func:
    name '(' '*' ')'
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlAggFuncArgsT aggArgs = {
                .aggStar = true,
                .aggDistinct = false
            };
            SqlExprFuncTypeE funcType = FUNC_TYPE_AGG_END;
            Status ret = SqlExprGetFuncTypeByName($1, &funcType);
            SqlParserConditionAbort(ret != GMERR_OK, "Function not support", ret);
            $$ = SqlExprMakeFunc(sqlPList->memCtx, funcType, NULL, aggArgs);
            SqlParserConditionAbort($$ == NULL, "Make expr with problem.", GMERR_OUT_OF_MEMORY);
        }
    | name '(' func_arg_list ')'
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlAggFuncArgsT aggArgs = {
                .aggStar = false,
                .aggDistinct = false
            };
            SqlExprFuncTypeE funcType = FUNC_TYPE_AGG_END;
            Status ret = SqlExprGetFuncTypeByName($1, &funcType);
            SqlParserConditionAbort(ret != GMERR_OK, "Function not support", ret);
            $$ = SqlExprMakeFunc(sqlPList->memCtx, funcType, $3, aggArgs);
            SqlParserConditionAbort($$ == NULL, "Make expr with problem.", GMERR_OUT_OF_MEMORY);
        }
    | name '(' DISTINCT func_arg_list ')'
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            SqlAggFuncArgsT aggArgs = {
                .aggStar = false,
                .aggDistinct = true
            };
            SqlExprFuncTypeE funcType = FUNC_TYPE_AGG_END;
            Status ret = SqlExprGetFuncTypeByName($1, &funcType);
            SqlParserConditionAbort(ret != GMERR_OK, "Function not support", ret);
            $$ = SqlExprMakeFunc(sqlPList->memCtx, funcType, $4, aggArgs);
            SqlParserConditionAbort($$ == NULL, "Make expr with problem.", GMERR_OUT_OF_MEMORY);
        }
    ;

opt_limit:
    LIMIT literal_value
        {
            SqlLimitT *limit = (SqlLimitT *)SqlCreateStruct(sizeof(SqlLimitT));
            limit->offset = NULL;
            limit->num = $2;
            $$ = limit;
        }
    | LIMIT literal_value ',' literal_value
        {
            SqlLimitT *limit = (SqlLimitT *)SqlCreateStruct(sizeof(SqlLimitT));
            limit->offset = $2;
            limit->num = $4;
            $$ = limit;
        }
    | LIMIT literal_value OFFSET literal_value
        {
            SqlLimitT *limit = (SqlLimitT *)SqlCreateStruct(sizeof(SqlLimitT));
            limit->offset = $4;
            limit->num = $2;
            $$ = limit;
        }
    | /* EMPTY */ { $$ = NULL; }
    ;

string_name:
    SQL_STRING
        {
            SqlTableRefT *tableRef = (SqlTableRefT *)SqlCreateStruct(sizeof(SqlTableRefT));
            tableRef->database = NULL;
            tableRef->tableName = $1;
            tableRef->aliasName = NULL;
            $$ = tableRef;
        }
    ;

table_name:
    qualified_name opt_as_name
        {
	        ($1)->aliasName = $2;
	        $$ = $1;
        }
    | string_name opt_as_name
        {
	        ($1)->aliasName = $2;
	        $$ = $1;
        }
    ;

expr_list:
    expr
        {
            DbListT *exprList = SqlCreateList(sizeof(SqlExprT *));
            SqlListAppend(exprList, &($1));
            $$ = exprList;
        }
    | expr_list ',' expr
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

/* for expr operator and item */
expr:
    expr_base
        {
            $$ = $1;
        }
    // UMINUS for precedence declarations
    | '+' expr    %prec UMINUS
        {
            $$ = SqlParserExprMakeUnary($2, SQL_EXPR_OP_UPLUS);
        }
    | '-' expr    %prec UMINUS
        {
            $$ = SqlParserExprMakeUnary($2, SQL_EXPR_OP_UMINUS);
        }
    | NOT expr
        {
            $$ = SqlParserExprMakeUnary($2, SQL_EXPR_OP_NOT);
        }
    | expr ISNULL
        {
            $$ = SqlParserExprMakeUnary($1, SQL_EXPR_OP_ISNULL);
        }
    | expr NOTNULL
        {
            $$ = SqlParserExprMakeUnary($1, SQL_EXPR_OP_NOTNULL);
        }
    | expr NOT S_NULL %prec NOTNULL
        {
            $$ = SqlParserExprMakeUnary($1, SQL_EXPR_OP_NOTNULL);
        }
    | '~' expr
        {
            $$ = SqlParserExprMakeUnary($2, SQL_EXPR_OP_BITNOT);
        }
    | expr '+' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_ADD);
        }
    | expr '-' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_SUB);
        }
    | expr '*' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_MULTI);
        }
    | expr '/' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_DIV);
        }
    | expr '%' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_MOD);
        }
    | expr AND expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_AND);
        }
    | expr OR expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_OR);
        }
    | expr '<' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_LT);
        }
    | expr '>' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_GT);
        }
    | expr LESS_EQUALS expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_LE);
        }
    | expr GREATER_EQUALS expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_GE);
        }
    | expr EQUALS expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_EQ);
        }
    | expr NOT_EQUALS expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_NE);
        }
    | expr '|' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_BITOR);
        }
    | expr '&' expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_BITAND);
        }
    | expr CONCAT expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_CONCAT);
        }
    | expr IS expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_IS);
        }
    | expr LSHIFT expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_LSHIFT);
        }
    | expr RSHIFT expr
        {
            $$ = SqlParserExprMakeBinary($1, $3, SQL_EXPR_OP_RSHIFT);
        }
    | expr LIKE expr
         {
            SqlExprT *expr = (SqlExprT *)SqlCreateStruct(sizeof(SqlExprT) + sizeof(SqlExprLikeT));
            SqlExprLikeT *exprLike = (SqlExprLikeT *)(void *)((uint8_t *)expr + sizeof(SqlExprT));
            exprLike->expr.type = SQL_EXPR_OP_LIKE;
            exprLike->isNot = false;
            exprLike->isGlobMatch = false;
            exprLike->src = $1;
            exprLike->dst = $3;
            SqlParsedListT *sqlPList = SqlGetParseList();
            DmValueT value = {};
            value.type = DB_DATATYPE_FIXED;
            value.value.strAddr = DbDynMemCtxAlloc(sqlPList->memCtx, 2);
            SqlParserConditionAbort(value.value.strAddr == NULL, "Unable to alloc string.", GMERR_OUT_OF_MEMORY);
            memcpy_s((char *)value.value.strAddr, 2, "\\", 2);
            value.value.length = 2;
            $$ = SqlExprMakeConst(sqlPList->memCtx, &value, NULL);
            SqlParserConditionAbort($$ == NULL, "Unable to make expr.", GMERR_OUT_OF_MEMORY);
            exprLike->escape = $$;
            expr->op = (SqlOpBaseT *)(void *)exprLike;
            $$ = expr;
         }
    | expr LIKE expr ESCAPE expr
         {
             SqlExprT *expr = (SqlExprT *)SqlCreateStruct(sizeof(SqlExprT) + sizeof(SqlExprLikeT));
             SqlExprLikeT *exprLike = (SqlExprLikeT *)(void *)((uint8_t *)expr + sizeof(SqlExprT));
             exprLike->expr.type = SQL_EXPR_OP_LIKE;
             exprLike->isNot = false;
             exprLike->isGlobMatch = false;
             exprLike->src = $1;
             exprLike->dst = $3;
             exprLike->escape = $5;
             expr->op = (SqlOpBaseT *)(void *)exprLike;
             $$ = expr;
         }
    | '(' expr ')'
        {
            $$ = $2;
        }
    | '(' expr_list ',' expr ')'
        {
            SqlListAppend($2, &($4));
            SqlExprListT *exprList = (SqlExprListT *)SqlCreateStruct(sizeof(SqlExprListT));
            exprList->expr.type = SQL_EXPR_ITEM_LIST;
            exprList->exprList = $2;
            $$ = (SqlExprT *)exprList;
        }
    ;

/* for expr item */
expr_base:
    literal_value
        {
            $$ = $1;
        }
    | column_ref
        {
            $$ = $1;
        }
    | parameter
    	{
            $$ = $1;
        }
    | agg_func
        {
            $$ = $1;
        }
    ;

char_len_value:
    INTEGER_STRING
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            DmValueT value = {};
            value.type = DB_DATATYPE_INT64;
            errno = 0;
            value.value.longValue = strtoll($1, NULL, 10);
            if (errno == ERANGE) {
                SqlParserConditionAbort(true, "Unsupported int const in parser", GMERR_FIELD_OVERFLOW);
            }
            TextT dstText;
            Status ret = DbCopyStrToText(sqlPList->memCtx, &dstText, $1, strlen($1));
            SqlParserConditionAbort(ret != GMERR_OK, "Unable to copy const string", ret);
            $$ = SqlExprMakeConst(sqlPList->memCtx, &value, dstText.str);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    ;

literal_value:
    INTEGER_STRING
        {
            Status ret = GMERR_OK;
            SqlParsedListT *sqlPList = SqlGetParseList();
            DmValueT value = {};
            value.type = DB_DATATYPE_INT64;
            errno = 0;
            value.value.longValue = strtoll($1, NULL, 10);
            TextT dstText;
            ret = DbCopyStrToText(sqlPList->memCtx, &dstText, $1, strlen($1));
            SqlParserConditionAbort(ret != GMERR_OK, "Unable to copy const string", ret);
            $$ = SqlExprMakeConst(sqlPList->memCtx, &value, dstText.str);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    | SQL_STRING
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            DmValueT value = {};
            value.type = DB_DATATYPE_FIXED;
            value.value.strAddr = $1;
            value.value.length = strlen($1) + 1;
            $$ = SqlExprMakeConst(sqlPList->memCtx, &value, $1);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    | DOUBLE_STRING
        {
            Status ret = GMERR_OK;
            SqlParsedListT *sqlPList = SqlGetParseList();
            DmValueT value = {};
            value.type = DB_DATATYPE_DOUBLE;
            value.value.doubleValue = strtod($1, NULL);
            if (errno == ERANGE) {
                SqlParserConditionAbort(true, "Unable to convert double const", GMERR_FIELD_OVERFLOW);
            }
            TextT dstText;
            ret = DbCopyStrToText(sqlPList->memCtx, &dstText, $1, strlen($1));
            SqlParserConditionAbort(ret != GMERR_OK, "Unable to copy const string", ret);
            $$ = SqlExprMakeConst(sqlPList->memCtx, &value, dstText.str);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    ;

column_ref:
    /* one SQL_STRING cannot be a column*/
    SQL_ID
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            $$ = SqlExprMakeColumn(sqlPList->memCtx, NULL, NULL, $1);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    | name '.' name
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            $$ = SqlExprMakeColumn(sqlPList->memCtx, NULL, $1, $3);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    | name '.' name '.' name
        {
            SqlParsedListT *sqlPList = SqlGetParseList();
            char *tableName = SqlCatTableName($1, $3);
            $$ = SqlExprMakeColumn(sqlPList->memCtx, NULL, tableName, $5);
            SqlParserConditionAbort($$ == NULL, "Make expr unsuccesfully", GMERR_OUT_OF_MEMORY);
        }
    ;

/*
 *  Drop  Statements
*/
drop_stmt:
    DROP drop_type drop_name
        {
            SqlDropStmtT *dropTableStmt = (SqlDropStmtT *)SqlCreateStruct(sizeof(SqlDropStmtT));
            dropTableStmt->tag = T_TS_DROP_TABLE_STMT;
            dropTableStmt->ifExists = NULL;
            dropTableStmt->name = $3;
            dropTableStmt->dropElemType = $2;
            $$ = (NodeT *)dropTableStmt;
        }
    ;

drop_type:
    TABLE {$$ = DROP_TABLE; }
    | VIEW {$$ = DROP_VIEW; }
    ;

drop_name:
    name opt_dot_name
        {
            SqlFullNameT *fullName = (SqlFullNameT *)SqlCreateStruct(sizeof(SqlFullNameT));
            if ($2 == NULL) {
                fullName->database = NULL;
                fullName->name = $1;
            } else {
                char *tableName = SqlCatTableName($1, $2);
                fullName->database = NULL;
                fullName->name = tableName;
            }
            $$ = fullName;
        }
    ;

/*
 *  Truncate Statements
*/
truncate_stmt:
    TRUNCATE truncate_type truncate_name
        {
            SqlTruncateStmtT *truncateTableStmt = (SqlTruncateStmtT *)SqlCreateStruct(sizeof(SqlTruncateStmtT));
            truncateTableStmt->tag = T_TS_TRUNCATE_TABLE_STMT;
            truncateTableStmt->ifExists = NULL;
            truncateTableStmt->name = $3;
            truncateTableStmt->type = $2;
            $$ = (NodeT *)truncateTableStmt;
        }
    ;

truncate_type:
    TABLE {$$ = TRUNCATE_TABLE; }
    ;

truncate_name:
    name opt_dot_name
        {
            SqlFullNameT *fullName = (SqlFullNameT *)SqlCreateStruct(sizeof(SqlFullNameT));
            if ($2 == NULL) {
                fullName->database = NULL;
                fullName->name = $1;
            } else {
                char *tableName = SqlCatTableName($1, $2);
                fullName->database = NULL;
                fullName->name = tableName;
            }
            $$ = fullName;
        }
    ;

insert_into_stmt:
    INSERT INTO table_name insert_list select_stmt
    	{

	    SqlInsertStmtT *insert = (SqlInsertStmtT *)SqlCreateStruct(sizeof(SqlInsertStmtT));
	    insert->tag = T_SQL_INSERT_STMT;
	    insert->table = $3;
	    insert->columnList = $4;
	    insert->value = (SqlInsertValueT *)SqlCreateStruct(sizeof(SqlInsertValueT));
	    insert->value->type = SELECT_SUBQUERY;
	    insert->value->selectSubquery = (NodeT *)$5;
	    $$ = (NodeT *)insert;
    	}
    ;

insert_list:
    '(' name_list ')'
	{
	    $$ = $2;
	}
    |
        {
            $$ = NULL;
        }
    ;

name_list:
    name_list ',' name
       {
           SqlListAppend($1, &($3));
           $$ = $1;
       }
   | name
       {
           DbListT *nameList = (DbListT *)SqlCreateList(sizeof(char *));
           SqlListAppend(nameList, &($1));
           $$ = nameList;
       }
   ;

grant_stmt:
    GRANT priv_spec ON qualified_name TO user_list
        {
            SqlGrantRevokeStmt *grantRevokeStmt = (SqlGrantRevokeStmt *)SqlCreateStruct(sizeof(SqlGrantRevokeStmt));
            grantRevokeStmt->tag = T_TS_GRANT_REVOKE_STMT;
            grantRevokeStmt->isGrant = true;
            grantRevokeStmt->privileges = $2;
            grantRevokeStmt->targetName = $4;
            grantRevokeStmt->userList = $6;
            if (grantRevokeStmt->userList == NULL) {
                grantRevokeStmt->isToPublic = true;
            }
            $$ = (NodeT *)grantRevokeStmt;
        }
    ;

revoke_stmt:
    REVOKE priv_spec ON qualified_name FROM user_list
        {
            SqlGrantRevokeStmt *grantRevokeStmt = (SqlGrantRevokeStmt *)SqlCreateStruct(sizeof(SqlGrantRevokeStmt));
            grantRevokeStmt->tag = T_TS_GRANT_REVOKE_STMT;
            grantRevokeStmt->isGrant = false;
            grantRevokeStmt->privileges = $2;
            grantRevokeStmt->targetName = $4;
            grantRevokeStmt->userList = $6;
            if (grantRevokeStmt->userList == NULL) {
                grantRevokeStmt->isToPublic = true;
            }
            $$ = (NodeT *)grantRevokeStmt;
        }
    ;

priv_spec:
    priv_list
        {
            $$ = $1;
        }
    | all_priv
        {
            $$ = $1;
        }
    ;

priv_list:
    priv_type
        {
            $$ = $1;
        }
    | priv_list ',' priv_type
        {
            $$ = $1 | $3;
        }
    ;
priv_type:
    SELECT
        {
            $$ = DM_OBJ_SELECT_PRIV;
        }
    | INSERT
        {
            $$ = DM_OBJ_INSERT_PRIV;
        }
    | UPDATE
        {
            $$ = DM_OBJ_UPDATE_PRIV;
        }
    | DELETE
        {
            $$ = DM_OBJ_DELETE_PRIV;
        }
    ;

all_priv:
    ALL priv_suffix
        {
            $$ = (DM_OBJ_SELECT_PRIV | DM_OBJ_INSERT_PRIV | DM_OBJ_UPDATE_PRIV | DM_OBJ_DELETE_PRIV);
        }
    ;

priv_suffix:
    PRIVILEGES 
        {
            $$ = 0;
        }
    | 
        { 
            $$ = 0;
        }
    ;

user_list:
    user_process_list
    {
        $$ = $1;
    }
    | PUBLIC
        {
            $$ = NULL;
        }
    ;

user_process_list:
    user_process
        {
            DbListT *nameList = (DbListT *)SqlCreateList(sizeof(char *));
            SqlListAppend(nameList, &($1));
            $$ = nameList;
        }
    | user_process_list ',' user_process
        {
            SqlListAppend($1, &($3));
            $$ = $1;
        }
    ;

user_process:
    SQL_STRING
        {
            $$ = $1;
        }
    ;
%%
