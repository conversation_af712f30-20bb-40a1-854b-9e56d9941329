/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: db_instance.h
 * Description: header file of db instance management
 * Author: wenming
 * Create: 2023-12-21
 */

#ifndef DB_INSTANCE_H
#define DB_INSTANCE_H

#include "db_linkedlist.h"
#include "db_inter_process_mgr.h"
#include "db_rwlatch.h"
#include "se_common.h"
#include "se_recovery.h"
#include "gme_api.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_TABLE_LOCK_COUNT 128
#define DB_DEFAULT_INSTANCE_CNT 16

typedef enum EnumDbInstanceStatus {
    DB_INSTANCE_OFFLINE,
    DB_INSTANCE_ONLINE,
} DbInstanceStatusE;

typedef enum EnumMultiInstanceSwitch {
    DB_MULTI_INSTANCE_SWITCH_DEFAULT = -1,
    DB_MULTI_INSTANCE_SWITCH_OFF,
    DB_MULTI_INSTANCE_SWITCH_ON,
} DbMultiInstanceSwitchE;

#define MAX_TABLE_LOCK_COUNT 128

typedef struct TagDbInstanceT {
    TagLinkedListT linkedNode;
    DbLatchT latch;
    DbInstanceStatusE status;
    uint32_t refCnt;
    uint32_t instanceId;
    uint32_t multiInsTag;             // multiple instance tag
    volatile uint32_t dbIsEmergency;  // 内存申请时，memset为 0 - SE_PERSIST_EMERGENCY_FALSE （当前只有数存使用）
    uint64_t dbStartTime;
    uint64_t dbInitDelay;
    uint64_t maxSysShmSize;
    uint64_t maxAppShmSize;
    uint64_t maxShmemSize;
    uint64_t maxSysDynSize;
    uint64_t maxAppDynSize;
    RecoveryStateE recoveryState;
    void *cfgMgr;
    void *topDynMemCtx;
    void *sysDynMemCtx;
    void *appDynMemCtx;
    void *topShmMemEntry;
    void *shmemCtxDetachMgr;
    void *topShmMemCtx;
    void *sysShmMemCtx;
    void *appShmMemCtx;
    void *escapeMemCtx;
    void *memCtxPool;  // for sql, DbMemCtxPoolT
    void *cataLog;
    void *embCatalog;  // for sql, CataCacheMgrT
    void *miniSessionPool;
    void *embSessionPool;   // for sql, SessionPoolT
    void *embSysTable;      // for sql SysTableGlobalT
    void *embOptCache;      // for sql OptCacheT
    void *embQryLoadTable;  // for sql, QueryLoadTableT
    void *embDbViewDef;
    void *embSysviewDef;
    void *embMultiVersionSysviewInfo;
    void *embSingleVersionSysviewInfo;
    void *seIns;
    void *drtIns;
    void *svIns;
    void *deIns;
    void *opHistoryMgr;
    SharedModeInfoT sharedModeInfo;
#ifdef FEATURE_SIMPLEREL
    void *simpleRelMgr;  // EmbSimpRelMgrT
#endif
    uint16_t connCount;
} DbInstanceT;

typedef struct TagDbInstanceMgrT {
    DbLatchT latch;
    uint16_t maxInstanceId;
    uint16_t instanceCnt;
    uint16_t extraInsSize;
    DbInstanceT *instances[DB_DEFAULT_INSTANCE_CNT];
    DbInstanceT **extraInstances;
    DbInstanceT *globalInstance;
    bool isGlobalInit;
} DbInstanceMgrT;

GMDB_EXPORT Status DbInstanceMgrInit(DbCommonInitCfgT *initCfg);

GMDB_EXPORT DbInstanceMgrT *DbGetInstanceMgr(void);

GMDB_EXPORT void DbInstanceGlobalLock(void);

GMDB_EXPORT void DbInstanceGlobalUnLock(void);

GMDB_EXPORT Status DbGetOrAllocInstance(const char *cfgParameter, DbInstanceT **instance);

GMDB_EXPORT Status DbGetInstanceById(uint32_t instanceId, DbInstanceT **instance);

GMDB_EXPORT Status DbGetInstanceByIdNolock(uint32_t instanceId, DbInstanceT **instance);

GMDB_EXPORT void DbReleaseInstance(DbInstanceT *instance);

GMDB_EXPORT DbMultiInstanceSwitchE DbGetMultiInstanceSwitch(void);

GMDB_EXPORT bool DbIsMultiInstanceEnabled(void);

GMDB_EXPORT bool DbNeedGlobalFinalize(DbInstanceT *instance);

GMDB_EXPORT void DbSetMultiInstanceSwitch(DbMultiInstanceSwitchE status);

GMDB_EXPORT DbInstanceT *DbGetGlobalInstance(void);

GMDB_EXPORT Status DbGetOrAllocGlobalInstance(const char *cfgParameter, DbInstanceT **instance);

#ifdef __cplusplus
}
#endif

#endif /* DB_INSTANCE_H */
