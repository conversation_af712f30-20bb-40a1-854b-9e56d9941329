/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Task management of the distributed synchronization module.
 * Author: liufuchenxing
 * Create: 2024-02-18
 */

#include "de_sync_task.h"

#define MAX_SYNC_TASK_NUM 32

StatusInter SyncTaskQueueInit(void *deDynMemCtx, SyncTaskQueueT **taskQueue)
{
    DB_POINTER2(deDynMemCtx, taskQueue);
    SyncTaskQueueT *taskQueuePtr = DbDynMemCtxAlloc(deDynMemCtx, sizeof(SyncTaskQueueT));
    if (SECUREC_UNLIKELY(taskQueuePtr == NULL)) {
        DB_LOG_ERROR(OUT_OF_MEMORY_INTER, "wrong taskQueue-alloc, size = %zu", sizeof(SyncTaskQueueT));
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s(taskQueuePtr, sizeof(SyncTaskQueueT), 0, sizeof(SyncTaskQueueT));

    taskQueuePtr->queue.count = 0u;
    taskQueuePtr->deDynMemCtx = deDynMemCtx;
    DbLinkedListInit(&taskQueuePtr->queue.linkedList);
    *taskQueue = taskQueuePtr;
    return STATUS_OK_INTER;
}

StatusInter SyncTaskAdd(SyncTaskQueueT *taskQueue, SyncTaskT *task)
{
    DB_POINTER2(taskQueue, task);
    DbSpinLock(&taskQueue->lock);
    if (taskQueue->queue.count >= MAX_SYNC_TASK_NUM) {
        DbSpinUnlock(&taskQueue->lock);
        DB_LOG_ERROR(INT_SYNC_TASK_OVER_LIMIT, "sync task is over limit");
        return INT_SYNC_TASK_OVER_LIMIT;
    }
    DbLinkedListInsert(&taskQueue->queue.linkedList, &task->linkedNode);
    taskQueue->queue.count++;
    DbSpinUnlock(&taskQueue->lock);
    return STATUS_OK_INTER;
}

SyncTaskT *SyncTaskPeek(SyncTaskQueueT *taskQueue)
{
    DB_POINTER(taskQueue);
    DbSpinLock(&taskQueue->lock);
    if (DbLinkedListEmpty(&taskQueue->queue.linkedList)) {
        DbSpinUnlock(&taskQueue->lock);
        return NULL;
    }
    SyncTaskT *task = LIST_HEAD(&taskQueue->queue.linkedList, SyncTaskT, linkedNode);
    DB_ASSERT(task != NULL);
    DbSpinUnlock(&taskQueue->lock);
    return task;
}

void SyncTaskFree(void *deDynMemCtx, SyncTaskT *task)
{
    if (task == NULL) {
        return;
    }
    if (task->equipIds != NULL) {
        DbDynMemCtxFree(deDynMemCtx, task->equipIds);
        task->equipIds = NULL;
    }
    DbDynMemCtxFree(deDynMemCtx, task);
}

void SyncTaskRemove(SyncTaskQueueT *taskQueue, SyncTaskT *task)
{
    DB_POINTER3(taskQueue, taskQueue->deDynMemCtx, task);
    DbSpinLock(&taskQueue->lock);
    if (DbLinkedListEmpty(&taskQueue->queue.linkedList)) {
        DbSpinUnlock(&taskQueue->lock);
        return;
    }
    DbLinkedListRemove(&task->linkedNode);
    task->linkedNode.prev = NULL;
    task->linkedNode.next = NULL;
    taskQueue->queue.count--;

    SyncTaskFree(taskQueue->deDynMemCtx, task);
    DbSpinUnlock(&taskQueue->lock);
}

void SyncTaskQueueFree(SyncTaskQueueT *taskQueue)
{
    if (taskQueue == NULL) {
        return;
    }
    SyncTaskT *curr = NULL;
    SyncTaskT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(curr, tmp, &taskQueue->queue.linkedList, linkedNode)
    {
        SyncTaskRemove(taskQueue, curr);
    }
}
