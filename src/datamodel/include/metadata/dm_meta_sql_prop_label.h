/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: provide interfaces involving property label
 * Author: ouyangying
 * Create: 2024-04-24
 */

#ifndef DM_META_SQL_PROP_LABEL_H
#define DM_META_SQL_PROP_LABEL_H

#include "db_privileges.h"
#include "dm_meta_prop_strudefs.h"
#include "dm_meta_basic.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief:
 * 重命名表名
 * @param vertexLabel [in/out]: 需要重命名的表
 * @param source [in]: 初始表名
 * @param target [in]: 目标表名
 * @return: success或错误码
 */
SO_EXPORT Status DmMetaDataRenameVertexLabel(DmVertexLabelT *vertexLabel, char *source, char *target);

/**
 * @brief:
 * 重命名列名
 * @param vertexLabel [in/out]: 需要重命名的表
 * @param source [in]: 初始列名
 * @param target [in]: 目标列名
 * @return: success或错误码
 */
SO_EXPORT Status DmMetaDataRenameProperty(DmVertexLabelT *vertexLabel, char *source, char *target);

/**
 * @brief:
 * 新增列
 * @param vertexLabel [in/out]: 需要新增列的表
 * @param properties [in/out]: 需要新增的列
 * @return: success或错误码
 */
SO_EXPORT Status DmMetaDataAddProperty(DmVertexLabelT *vertexLabel, DmPropertySchemaT *properties);

/**
 * @brief:
 * 删除列
 * @param vertexLabel [in]: 需要删除列的表
 * @param propeName [in]: 需要删除的列名
 * @return: success或错误码
 */
SO_EXPORT Status DmMetaDataDropProperty(DmVertexLabelT *vertexLabel, const char *propeName);

/**
 * @brief:
 * 用新的schema重建recordDesc
 * @param vertexLabel [in/out]: 需要重建的vertexLabel
 * @return: success或内部错误码
 */
SO_EXPORT Status CataResetRecordDesc(DmVertexLabelT *vertexLabel);

#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* DM_META_SQL_PROP_LABEL_H */
