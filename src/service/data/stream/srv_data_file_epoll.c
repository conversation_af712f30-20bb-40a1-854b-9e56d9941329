/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: srv_data_file_epoll realization
 * Author:
 * Create: 2024-11-25
 */
#include "ee_cmd_epoll.h"
#include "ee_cmd_epoll_map.h"
#include "ee_cmd_epoll_stream_callback.h"
#include "dm_data_stream.h"
#include "srv_data_stream_forward.h"
#include "srv_data_stream.h"

#define STREAM_MAX_HOSTNAME_STRING_LEN 256u

Status DbFileRead(int32_t fd, void *buf, size_t count, size_t *readCount, DbIoFuncT *streamCtx)
{
    DB_POINTER(buf);
    ssize_t ret = streamCtx->read(fd, buf, count);
    if (ret == -1) {
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "read file in hpe, os no %" PRId32 ".", (int32_t)errno);
        return GMERR_FILE_OPERATE_FAILED;
    }

    if (readCount != NULL) {
        *readCount = (size_t)ret;
    }
    return GMERR_OK;
}

Status ConstructDmVertexForFile(DmVertexT **vertex, DmStreamVertexLabelT *vertexLabel)
{
    DbListT *sourceList = vertexLabel->streamMeta.sourceMeta.defaultExpr;
    for (uint32_t i = 0; i < sourceList->count; i++) {
        if (i == 0) {
            // 处理文件的那一列逻辑
            DmValueT propertyValue = {0};
            propertyValue.type = DB_DATATYPE_FIXED;
            propertyValue.value.strAddr = vertexLabel->streamMeta.sourceMeta.fileContent;
            propertyValue.value.length = vertexLabel->base.metaVertexLabel->schema->properties[0].size;
            Status ret = DmVertexSetPropeById(0, propertyValue, *vertex);
            if (ret != GMERR_OK) {
                return ret;
            }
            (*vertex)->record->propeIsSetValue[0] = DM_PROPERTY_IS_NOT_NULL;
        }
    }
    return GMERR_OK;
}

// 执行文件插入数据到流表操作
Status ExecInsertFileStreamTable(AAT *aa, DmVertexDescT *vertexDesc, DmStreamVertexLabelT *streamVertexLabel)
{
    Status ret = GMERR_OK;

    AASlotT *slot = NULL;
    ret = AAPopPropSlot(aa, vertexDesc, NULL, &slot);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 填充vertex
    ret = ConstructDmVertexForFile(&slot->dmVertex, streamVertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = ReplaceNullValuesWithDefault(slot->dmVertex, streamVertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "can not replace null value");
        return ret;
    }
    ret = AAPushPropSlot(aa, slot);
    if (ret != GMERR_OK) {
        DeleteAASlot(slot);
        return ret;
    }
    return ret;
}

Status ProcessRemainingContent(
    char **token, char *remainingContent, DmStreamVertexLabelT *cataStreamlabel, uint32_t maxFilePerLineSize)
{
    uint32_t remainingContentLen = strlen(remainingContent);
    char *newToken = (char *)DbDynMemCtxAlloc(cataStreamlabel->streamMemCtx, sizeof(char) * (maxFilePerLineSize + 1));
    if (newToken == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t ret = strcpy_s(newToken, maxFilePerLineSize + 1, remainingContent);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(cataStreamlabel->streamMemCtx, newToken);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    size_t totalLength = remainingContentLen + strlen(*token);
    if (totalLength > maxFilePerLineSize) {
        size_t remainingSpace = maxFilePerLineSize - remainingContentLen;
        // 如果剩余空间大于 0，截取 token 中的部分
        if (remainingSpace > 0) {
            errno_t strncatRet = strncat_s(newToken, maxFilePerLineSize + 1, *token, remainingSpace);
            if (strncatRet != EOK) {
                return GMERR_DATA_EXCEPTION;
            }
        }
    } else {
        errno_t strncatRet = strcat_s(newToken, maxFilePerLineSize + 1, *token);
        if (strncatRet != EOK) {
            return GMERR_DATA_EXCEPTION;
        }
    }
    *token = newToken;
    cataStreamlabel->streamMeta.sourceMeta.fileContentRemaining = NULL;
    return GMERR_OK;
}

Status GetStreamLabel(int32_t fd, DmStreamVertexLabelT **cataStreamlabel)
{
    DbOamapT *map = GetEpollMap();
    // get streamVertexLabel from fd
    DbRWSpinLockT *epollMappingLock = GetEpollMappingLock();
    DbRWSpinRLock(epollMappingLock);
    uint32_t *metaId = (uint32_t *)DbOamapLookup(map, fd, &fd, NULL);
    if (metaId == NULL) {
        DbRWSpinRUnlock(epollMappingLock);
        return GMERR_DATA_CORRUPTED;
    }
    DbRWSpinRUnlock(epollMappingLock);
    return CataGetStreamVertexLabelById(*metaId, cataStreamlabel);
}

bool ReadBufFromFd(char **buf, int32_t fd)
{
    // 从 fd 读取数据
    size_t readCount = 0;
    int32_t epollId = GetEpollId();
    DbIoFuncT *streamCtx = DbGetStreamIoCtxPtr();
    DbFileRead(fd, *buf, READ_FILE_CONTENT_BUFFER_LEN, &readCount, streamCtx);
    if (readCount <= 0) {
        DB_LOG_ERROR(GMERR_FILE_OPERATE_FAILED, "read fd.");
        streamCtx->epollCtl(epollId, EPOLL_CTL_DEL, fd, NULL);
        streamCtx->close(fd);
    } else {
        (*buf)[readCount] = '\0';
        char ch = (*buf)[readCount - 1];
        if (strchr(LINE_DELIMITERS, ch) != NULL) {
            return true;
        }
    }
    return false;
}

void ProcessToken(
    char *token, DmStreamVertexLabelT *cataStreamlabel, char *saveptr, bool isLastInjected, DbInstanceHdT dbInstance)
{
    DmVertexDescT *vertexDesc = NULL;
    Status ret = DmGetVertexDescByVertexLabel(&cataStreamlabel->base, &vertexDesc);
    if (ret != GMERR_OK) {
        return;
    }

    AAT *aa = NULL;
    // 不合理
    ret = NewNoLimitedAA(cataStreamlabel->base.memCtx, &aa);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "ReadCallback-NewNoLimitedAA.");
        return;
    }

    // 后续数据流转
    SessionT *session = NULL;
    ret = QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, dbInstance, &session);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "can not alloc session when forward.");
        DeleteAA(aa);
        return;
    }
    uint32_t maxFilePerLineSize = cataStreamlabel->base.metaVertexLabel->schema->properties[0].size;
    uint32_t tokenSize = sizeof(char) * (maxFilePerLineSize + 1);
    while (token != NULL) {
        char *tempToken = (char *)DbDynMemCtxAlloc(cataStreamlabel->streamMemCtx, tokenSize);
        if (tempToken == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, " ");
            break;  // 处理内存分配失败
        }
        (void)memset_s(tempToken, tokenSize, 0x00, tokenSize);
        if (strlen(token) > maxFilePerLineSize) {
            token[maxFilePerLineSize] = '\0';
        }
        ret = strcpy_s(tempToken, maxFilePerLineSize + 1, token);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(cataStreamlabel->streamMemCtx, tempToken);
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, " ");
            break;
        }
        token = strtok_r(NULL, LINE_DELIMITERS, &saveptr);
        if (token == NULL && !isLastInjected) {
            cataStreamlabel->streamMeta.sourceMeta.fileContentRemaining = tempToken;
            break;
        }
        cataStreamlabel->streamMeta.sourceMeta.fileContent = tempToken;
        ExecInsertFileStreamTable(aa, vertexDesc, cataStreamlabel);
    }
    StreamForwardNextByAAImpl(session, (DmVertexLabelT *)cataStreamlabel, aa);
    QrySessionRelease(session);
    DeleteAA(aa);
}

// 回调函数
void ReadCallbackImpl(int32_t fd)
{
    DmStreamVertexLabelT *cataStreamlabel = NULL;
    Status ret = GetStreamLabel(fd, &cataStreamlabel);
    if (ret != GMERR_OK) {
        return;
    }
    char *buf = (char *)DbDynMemCtxAlloc(cataStreamlabel->streamMemCtx, sizeof(char) * READ_FILE_CONTENT_BUFFER_LEN);
    if (buf == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "stream file epoll read buffer.");
        CataReleaseVertexLabel(&cataStreamlabel->base);
        return;
    }
    bool isLastInjected = ReadBufFromFd(&buf, fd);

    char *token;

    // 获取表定义文件单行最大长度
    uint32_t maxFilePerLineSize = cataStreamlabel->base.metaVertexLabel->schema->properties[0].size;

    // 使用 strtok_r 来分割字符串
    char *saveptr = NULL;  // 保存上下文
    token = strtok_r(buf, LINE_DELIMITERS, &saveptr);
    char *remainingContent = cataStreamlabel->streamMeta.sourceMeta.fileContentRemaining;
    if (remainingContent != NULL) {
        ret = ProcessRemainingContent(&token, remainingContent, cataStreamlabel, maxFilePerLineSize);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "alloc session when forward.");
            DbDynMemCtxFree(cataStreamlabel->streamMemCtx, buf);
            CataReleaseVertexLabel(&cataStreamlabel->base);
            return;
        }
    }
    ProcessToken(token, cataStreamlabel, saveptr, isLastInjected, DbGetInstanceByMemCtx(cataStreamlabel->base.memCtx));
    DbDynMemCtxFree(cataStreamlabel->streamMemCtx, buf);
    CataReleaseVertexLabel(&cataStreamlabel->base);
}

void StreamServiceInitCallback(void)
{
    StreamAmT am = {
        .ReadCallbackFunc = ReadCallbackImpl,
    };
    StreamAmInit(&am);
}
