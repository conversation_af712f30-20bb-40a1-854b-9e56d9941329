/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: time-series data service
 * Author: yuanjincheng
 * Create: 2025-01-07
 */
#include "srv_data_ts.h"

#define TS_MAX_FILE_LINE 65535

static Status ExecuteSqlWithoutConnection(DbMemCtxT *memCtx, char *createSql)
{
    SessionT *session = NULL;
    Status ret =
        QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, DbGetInstanceByMemCtx(memCtx), &session);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc session when import ts table for DbStart.");  // LCOV_EXCL_LINE
        return ret;
    }
    TsQryStmtT stmt = {0};
    InitTsStmt(memCtx, session, &stmt);
    stmt.isPrepareSql = false;
    session->sqlCtx->currentFusionStmt = (void *)&stmt;
    SqlParsedListT parsedList = {0};
    ret = TsSqlCmdProcess(&stmt, &parsedList, createSql);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "exec ts sql cmd: %s for DbStart.", createSql);  // LCOV_EXCL_LINE
        goto RELEASE;
    }
RELEASE:
    QrySessionRelease(session);
    return ret;
}

static Status RemoveLastNewLineAndCheckCreateSql(char *str)
{
    if (str == NULL || strlen(str) <= 1) {
        return GMERR_OK;
    }

    // 替换最后一个字符为空字符(\n与\r)
    size_t posR = strcspn(str, "\r");
    char *tmpR = str + posR;
    *tmpR = '\0';
    size_t posN = strcspn(str, "\n");
    char *tmpN = str + posN;
    *tmpN = '\0';

    // check create sql
    if (strncasecmp(str, "create", strlen("create")) != 0) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "not create stmt in ts sql load schema, curLine:%s.", str);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

Status ServerImportTsTablesImpl(char *filePath, DbMemCtxT *memCtx)
{
    char *buffer = DbDynMemCtxAlloc(memCtx, TS_MAX_FILE_LINE);
    if (buffer == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc buffer when import ts tables.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(buffer, TS_MAX_FILE_LINE, 0, TS_MAX_FILE_LINE);
    size_t rowLen = 0;
    int32_t fd = DB_INVALID_FD;
    Status ret = DbOpenFile(filePath, READ_ONLY, PERM_GRPR, &fd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "open gmsql file, file:%s, ret = %" PRId32, filePath, (int32_t)ret);
        DbDynMemCtxFree(memCtx, buffer);
        return ret;
    }

    uint32_t lineNo = 0;
    while (true) {
        lineNo++;
        ret = DbReadLine(fd, buffer, TS_MAX_FILE_LINE, &rowLen);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "read gmsql file, lineNo: %" PRId32 ", ret = %" PRId32, lineNo, (int32_t)ret);
            goto FINISH;
        }
        if (rowLen == 0) {
            break;
        }
        // empty line continue
        if (strcmp(buffer, "\r\n") == 0 || strcmp(buffer, "\n") == 0 || strcmp(buffer, "\r") == 0) {
            continue;
        }
        // sql command preprocess
        ret = RemoveLastNewLineAndCheckCreateSql(buffer);
        if (ret != GMERR_OK) {
            continue;
        }
        // execute create sql
        ret = ExecuteSqlWithoutConnection(memCtx, buffer);
        if (ret != GMERR_OK) {
            continue;
        }
        (void)memset_s(buffer, TS_MAX_FILE_LINE, 0, TS_MAX_FILE_LINE);
    }
FINISH:
    DbCloseFile(fd);
    DbDynMemCtxFree(memCtx, buffer);
    return ret;
}
