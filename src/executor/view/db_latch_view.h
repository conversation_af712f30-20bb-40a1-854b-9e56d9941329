/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: db_latch_view.c
 * Description: sysview file for storage view
 * Author: wuchenyu
 * Create: 2021-12-22
 */
#ifndef DB_LATCH_VIEW_H
#define DB_LATCH_VIEW_H

#include "adpt_define.h"
#include "sysview.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#ifdef LATCH_CONFLICT_DEBUG

#define STORAGE_LATCH_CONFLICT_INFO_STAT \
    "[{\
        \"type\":\"record\",\
        \"name\":\"V$STORAGE_LATCH_CONFLICT_INFO_STAT\",\
        \"fields\": [\
            {\"name\":\"SESSION_ID\",\"type\":\"uint32\"},\
            {\"name\":\"READ_CONFLICT_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"READ_AVERAGE_WAIT_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"READ_TOTAL_WAIT_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"READ_MAX_WAIT_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"WRITE_CONFLICT_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"WRITE_AVERAGE_WAIT_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"WRITE_TOTAL_WAIT_COUNT\",\"type\":\"uint32\"},\
            {\"name\":\"WRITE_MAX_WAIT_COUNT\",\"type\":\"uint32\"}\
        ]\
}]"

Status SvQueryProcStorageEngineLatchConflictStat(void *vertex, SvCursorT *cursor);

#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* DB_LATCH_VIEW_H */
