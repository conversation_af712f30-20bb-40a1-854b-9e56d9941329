/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: Implementation of making schedule msg
 * Author: zhaodu
 * Create: 2021-07-27
 */

#include "drt_connection.h"
#include "drt_proc_context.h"
#include "ee_dml_desc.h"
#include "ee_schedule.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

static Status QryFormatScheduleMsgHeader(
    FixBufferT *req, const DrtInstanceT *ins, uint32_t opCode, uint32_t extraFlags, uint16_t stmtId)
{
    // 先初始化为合法状态，这样外面异常分支可以统一FixBufRelease
    // 使用ins->scheMgr.memctx而非stmt->session->conn->msgMemCtx的原因是
    // 后者是用于向客户端推送消息的，本来就可能不够用，不能多占用，否则有可能因此导致对应的调度任务投递失败
    FixBufInit(req, NULL, 0, 0, FIX_BUF_FLAG_EXTEND_BUFFER, ins->scheMgr.memctx);

    // 申请空间并预留 MsgHeaderT + OpHeaderT
    Status ret = FixBufExtend(req, CS_PACK_SIZE);
    if (ret != GMERR_OK) {
        return ret;
    }
    FixBufInitPut(req, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE);
    MsgHeaderT *msgHdr = RpcPeekMsgHeader(req);
    OpHeaderT *opHdr = ProtocolPeekFirstOpHeader(req);

    MsgHeaderT msg = {};
    msg.serviceId = DRT_SERVICE_STMT;
    if (SECUREC_UNLIKELY((opCode < MSG_OP_RPC_DCL_BEGIN || opCode >= MSG_OP_RPC_DCL_END))) {
        msg.modelType = MODEL_FASTPATH;
    } else {
        msg.modelType = MODEL_PUBLIC;
    }
    msg.reqTimeOut = CS_NEVER_TIMEOUT;
    msg.priority = MSG_PRIO_NORMAL;
    msg.flags = CS_FLAG_SELF_SCHEDULE | extraFlags;
    msg.stmtId = stmtId;
    msg.msgMagicNum = MSG_VERIFY_NUMBER;
    msg.protocolVersion = MSG_PROTOCOL_VERSION_PRIVATE;

    OpHeaderT op = {};
    op.opCode = opCode;

    *msgHdr = msg;
    *opHdr = op;
    return GMERR_OK;
}

Status QryAddSysNotifyTask(DrtConnectionT *conn)
{
    DB_POINTER(conn);
    FixBufferT req;

    DrtInstanceT *ins = DrtGetInstance(NULL);
    if (ins == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "instance is null in AddSysNotifyTask.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    // 这个操作实际不用stmt的参与，但为了和其他订阅自调度消息统一，使用 stmtId == 0
    Status ret = QryFormatScheduleMsgHeader(&req, ins, MSG_OP_RPC_SYS_NOTIFY, 0, 0);
    if (ret != GMERR_OK) {
        goto FAIL;
    }

    DrtProcCtxT *procCtx = NULL;
    ret = DrtAllocProcCtx(ins, conn, &req, PROC_CTX_TYPE_NO_MSG, &procCtx);
    if (ret != GMERR_OK) {
        goto FAIL;
    }
    DrtScheProcCtxPushBack(ins, procCtx);
    return GMERR_OK;

FAIL:
    DB_LOG_ERROR_AND_SET_LASTERR(ret, "schedule sys notify task.");
    FixBufRelease(&req);
    return ret;
}

Status QryAddDmlExecuteTask(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    FixBufferT *req = stmt->session->req;
    MsgHeaderT *reqHeader = RpcPeekMsgHeader(req);
    reqHeader->flags |= CS_FLAG_SELF_SCHEDULE | CS_FLAG_NOT_FIRST_SPLIT;
    OpHeaderT *reqOpHeader = ProtocolPeekFirstOpHeader(req);
    reqOpHeader->opCode = MSG_OP_RPC_EXEC;

    DrtInstanceT *ins = stmt->session->drtInst;
    if (SECUREC_UNLIKELY(ins == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "instance is null in AddDmlExecuteTask.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    DrtScheProcCtxPushFront(ins, stmt->session->procCtx);
    return GMERR_OK;
}

Status QryAddLongTrxRollBackTask(uint32_t connId, uint64_t trxId)
{
    // 不是本session调用，不能访问session内容，否则有并发问题
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "instance is null in AddLongTrxRollBackTask.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    DrtConnectionT *conn = DrtAttachConnById(&drtIns->connMgr, (uint16_t)connId);
    if (conn == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "conn is null, connId=%" PRIu32 ".", connId);
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    FixBufferT req = {0};
    Status ret = QryFormatScheduleMsgHeader(&req, drtIns, MSG_OP_RPC_TX_ROLLBACK, 0, 0);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "format scheMsgHeader, connId=%" PRIu32 ".", connId);
        FixBufRelease(&req);
        DrtDetachConnection(conn);
        return ret;
    }

    ret = FixBufPutUint64(&req, trxId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "put trxId=%" PRIu64 " connId=%" PRIu32 ".", trxId, connId);
        FixBufRelease(&req);
        DrtDetachConnection(conn);
        return ret;
    }
    uint32_t size = FixBufGetPos(&req);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(&req);
    op->len = size - MSG_HEADER_ALIGN_SIZE;

    DrtProcCtxT *procCtx = NULL;
    // 使用PROC_CTX_TYPE_NO_RESP类型表示该请求不用发送给客户端，避免加塞导致下一个请求被误清理，客户端收不到响应。
    ret = DrtAllocProcCtx(drtIns, conn, &req, PROC_CTX_TYPE_NO_RESP, &procCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "procCtx is null, connId=%" PRIu32 ".", connId);
        FixBufRelease(&req);
        DrtDetachConnection(conn);
        return ret;
    }
    DB_LOG_WARN(
        GMERR_ACTIVE_TRANSACTION, "Add long trx rollback task, trxId=%" PRIu64 " ,connId=%" PRIu32 ".", trxId, connId);
    procCtx->msgHeader->priority = MSG_PRIO_HIGH;
    DrtScheProcCtxPushFront(drtIns, procCtx);
    DrtDetachConnection(conn);
    return GMERR_OK;
}

Status QryAddTableSpaceAlarmDfxTask(QryStmtT *stmt)
{
    FixBufferT req = {0};
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "instance isnull in QryAddTableSpaceAlarmDfxTask.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    DrtConnectionT *conn = stmt->session->conn;
    // QryExecuteTableSpaceAlarmDfx
    Status ret = QryFormatScheduleMsgHeader(&req, drtIns, MSG_OP_RPC_TABLESPACE_ALARM, 0, 0);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "format scheMsgHeader in add TableSpaceAlarmDfx task.");
        FixBufRelease(&req);
        return ret;
    }

    DrtProcCtxT *procCtx = NULL;
    ret = DrtAllocProcCtx(drtIns, conn, &req, PROC_CTX_TYPE_NO_RESP, &procCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "alloc procCtx in add TableSpaceAlarmDfx task.");
        FixBufRelease(&req);
        return ret;
    }

    procCtx->msgHeader->priority = MSG_PRIO_HIGH;
    DrtScheProcCtxPushBack(drtIns, procCtx);
    return ret;
}

#ifdef EXPERIMENTAL_GUANGQI
Status QryAddCloneOrMergeTaskSetParameter(uint32_t connId, CloneOrMergeTaskInfoT *info, FixBufferT *req)
{
    Status ret = FixBufPutUint32(req, info->cloneId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "put cloneId=%" PRIu32 " targetConnId=%" PRIu32 ".", info->cloneId, connId);
        return ret;
    }
    ret = FixBufPutUint16(req, info->connId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "put curConnId=%" PRIu16 " targetConnId=%" PRIu32 ".", info->connId, connId);
        return ret;
    }
    if (info->opCode == MSG_OP_RPC_TX_MERGE) {
        ret = FixBufPutUint64(req, info->trxId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "put trxId=%" PRIu64 " targetConnId=%" PRIu32 ".", info->trxId, connId);
            return ret;
        }
    }
    return GMERR_OK;
}

Status QryAddCloneOrMergeTask(uint32_t connId, CloneOrMergeTaskInfoT *info)
{
    // 不是本session调用，不能访问session内容，否则有并发问题
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "instance is null in QryAddCloneOrMergeTask.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    DrtConnectionT *conn = DrtAttachConnById(&drtIns->connMgr, (uint16_t)connId);
    if (conn == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "conn is null, connId=%" PRIu32 ".", connId);
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    FixBufferT req = {0};
    Status ret = QryFormatScheduleMsgHeader(&req, drtIns, info->opCode, 0, 0);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "format scheMsgHeader, connId=%" PRIu32 ".", connId);
        goto ERROR;
    }

    QryAddCloneOrMergeTaskSetParameter(connId, info, &req);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "set parameter, connId=%" PRIu32 ".", connId);
        goto ERROR;
    }

    uint32_t size = FixBufGetPos(&req);
    OpHeaderT *op = ProtocolPeekFirstOpHeader(&req);
    op->len = size - MSG_HEADER_ALIGN_SIZE;

    DrtProcCtxT *procCtx = NULL;
    // 使用PROC_CTX_TYPE_NO_RESP类型表示该请求不用发送给客户端，避免加塞导致下一个请求被误清理，客户端收不到响应。
    ret = DrtAllocProcCtx(drtIns, conn, &req, PROC_CTX_TYPE_NO_RESP, &procCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "procCtx is null, targetConnId=%" PRIu32 ".", connId);
        goto ERROR;
    }
    procCtx->msgHeader->priority = MSG_PRIO_HIGH;
    DrtScheProcCtxPushFront(drtIns, procCtx);
    DrtDetachConnection(conn);
    return GMERR_OK;
ERROR:
    FixBufRelease(&req);
    DrtDetachConnection(conn);
    return ret;
}
#endif

#ifdef __cplusplus
}
#endif
