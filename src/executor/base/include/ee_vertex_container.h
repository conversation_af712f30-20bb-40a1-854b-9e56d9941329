/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation for
 * Author: tianyabo
 * Create: 2023-07-29
 */

#ifndef EE_VERTEX_CONTAINER_H
#define EE_VERTEX_CONTAINER_H

#include "db_tuple_buffer.h"
#include "se_common.h"
#include "ee_vertex_map.h"
#include "container_access.h"

#ifdef __cplusplus
extern "C" {
#endif

#define QRY_BIG_OBJ_SIZE (32 * 1024)

// 该结构体主要存放EE层和SE/索引交互的句柄或上下文
typedef struct QryVertexContainer {
    union {
        ContainerHdlT containerHdl;  // 存储容器
        ChLabelRunHdlT chRunHdl;
        HpRunHdlT hpRunHdl;
    };
    IndexCtxT *pkCtx;                 // 主键上下文
    IndexCtxT **secIdxCtx;            // 二级索引上下文
    QryObjectMapNodeT *emptyObjNode;  // 对DmVertexT和DmObjectT的包装，可以缓存在session的qryObjectMap中
    TupleBufT tupleBuf;               // 用于读取存储中元组
} QryVertexContainerT;

Status QryAllocVertexContainer(DbMemCtxT *memCtx, QryVertexContainerT **vertexContainer);

// 该函数调用处一定要将vertexContainer置空
inline static void QryReleaseVertexContainer(DbMemCtxT *memCtx, QryVertexContainerT *vertexContainer)
{
    DB_POINTER(memCtx);
    if (vertexContainer != NULL) {
        DbDynMemCtxFree(memCtx, vertexContainer);
    }
    return;
}

#ifdef __cplusplus
}
#endif
#endif  // EE_VERTEX_CONTAINER_H
