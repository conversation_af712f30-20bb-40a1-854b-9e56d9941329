/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: header file of DDL executor
 * Author: zhulixia
 * Create: 2020-08-27
 */

#ifndef EE_DDL_KV_H
#define EE_DDL_KV_H

#include "ee_stmt.h"
#include "ee_ddl_desc.h"
#include "db_instance.h"

#ifdef __cplusplus
extern "C" {
#endif

Status QryExecuteCreateKvTable(QryStmtT *stmt);
Status QryExecuteDropKvTable(QryStmtT *stmt);
Status QryExecuteTruncateKvTable(QryStmtT *stmt);
Status QryCreateGlobalKvTable(SeRunCtxHdT seRunCtx, uint32_t nspId, uint32_t tspId);
Status QryCheckDropKvTable(DmKvLabelT *label);
Status QryDropKvTable(SeRunCtxHdT seRunCtx, DmKvLabelT *label);
Status QryTruncateKvTable(QryStmtT *stmt, DmKvLabelT *kvTable);
Status QryExecuteGetKvTable(QryStmtT *stmt);
Status QryExecuteCreateKvTableForDbStart(
    SessionT *session, SeRunCtxHdT seRunCtx, DbMemCtxT *memCtx, QryCreateKvTableDescT *desc);
Status QryChangeKvTableTrxInfoWithCCMode(DmKvLabelT *label);
Status QryExecuteGetWarmRebootTables(QryStmtT *stmt);
bool QryWarmRebootVertexLabelFilter(const void *metaLabel);

#ifdef __cplusplus
}
#endif
#endif  // EE_DDL_KV_H
