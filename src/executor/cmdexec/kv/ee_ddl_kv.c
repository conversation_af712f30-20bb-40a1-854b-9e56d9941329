/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: kv table ddl
 * Author: wangsheng
 * Create: 2021-04-01
 */

#include "ee_ddl_kv.h"
#include "ee_cmd.h"
#include "dm_meta_kv_label.h"
#include "ee_context.h"
#include "se_heap.h"
#include "ee_concurrency_control.h"
#include "dm_cache_basic.h"
#include "dm_meta_basic.h"
#include "dm_meta_tablespace.h"
#include "ee_ddl_tablespace.h"
#include "dm_cache_multi_ver_mgr.h"
#include "ee_rsm.h"
#include "ee_rsm_recovery.h"
#include "cpl_public_parser_ddl.h"
#include "db_instance.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

static void RemoveKvTableLatch(DmKvLabelT *label)
{
    RemoveLabelLatchWhenCreateFailed(label->labelLatchId, label->labelLatchVersionId, NULL);
    label->labelLatchShmAddr = DB_INVALID_SHMPTR;
}

static void RollbackCreateKvTable(SeRunCtxHdT seRunCtx, DmKvLabelT *label)
{
    DB_POINTER(label);
    // free rsm info if exist
    QryRsmFreeRsmInfo(label->rsmInfo);
    ShmemPtrT heapShmAddr = label->heapShmAddr;
    if (heapShmAddr.offset != 0 && heapShmAddr.segId != 0) {
        HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = heapShmAddr,
            .isPersistent = label->metaCommon.isPersistent,
            .isUseRsm = label->metaCommon.isUseRsm,
            .instanceId = DbGetProcGlobalId()};
        (void)HeapLabelDrop(seRunCtx, &heapCntrAcsInfo, QryRsmGetRsmUndo(label->rsmInfo));
    }
    DmKvIndexLabelT *index = label->index;
    if (index != NULL) {
        ShmemPtrT shmAddr = index->idxLabelBase.shmAddr;
        if (shmAddr.offset != 0 && shmAddr.segId != 0) {
            IdxDrop(seRunCtx, (uint8_t)index->idxLabelBase.indexType, index->idxLabelBase.shmAddr);
        }
    }
    RemoveKvTableLatch(label);
}

static Status InitLabelHeapCfg(DmKvLabelT *label, HeapAccessCfgT *heapCfg)
{
    DB_POINTER2(label, heapCfg);
    heapCfg->pageType = HEAP_VAR_LEN_ROW_PAGE;
    heapCfg->tupleType = HEAP_TUPLE_TYPE_KV;
    heapCfg->seInstanceId = DbGetInstanceId(DbGetInstanceByMemCtx(label->memCtx));
    heapCfg->isYangBigStore = false;     // yang不涉及kv表
    heapCfg->isStatusMergeSubs = false;  // kv表不涉及新订阅
    heapCfg->isPersistent = label->metaCommon.isPersistent;
    heapCfg->labelId = label->metaCommon.metaId;
    heapCfg->ccType = label->ccType;
    heapCfg->isolation = label->isolationLevel;
    heapCfg->trxType = label->trxType;
#ifdef FEATURE_GQL
    heapCfg->skipRowLockPessimisticRR = label->skipRowLockPessimisticRR;
#endif
    heapCfg->isUseRsm = label->metaCommon.isUseRsm;
    heapCfg->tableSpaceId = heapCfg->isUseRsm ? label->metaCommon.rsmTablespaceId : label->metaCommon.tablespaceId;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(label->memCtx);
    Status ret = CataGetTspMgrIdxById(heapCfg->tableSpaceId, &heapCfg->tableSpaceIndex, dbInstance);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "get tablespaceIndex from catalog, tspId=%" PRIu32 ".", heapCfg->tableSpaceId);
        // LCOV_EXCL_STOP
        return ret;
    }
    ret = CataGetTspMgrIdxById(label->metaCommon.tablespaceId, &heapCfg->fsmTableSpaceIndex, dbInstance);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "get fsmTableSpaceIndex from catalog, tspId=%" PRIu32 ".", label->metaCommon.tablespaceId);
        // LCOV_EXCL_STOP
        return ret;
    }

    // 废弃
    if (label->maxRecordNumCheck) {
        heapCfg->maxItemNum = label->maxRecordNum;
    } else {
        heapCfg->maxItemNum = DB_MAX_UINT64;
    }
    return GMERR_OK;
}

void InitLabelFreeRes(SeRunCtxHdT seRunCtx, DmKvLabelT *label)
{
    QryRsmFreeRsmInfo(label->rsmInfo);
    if (DbIsShmPtrValid(label->heapShmAddr)) {
        HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = label->heapShmAddr,
            .isPersistent = label->metaCommon.isPersistent,
            .isUseRsm = label->metaCommon.isUseRsm,
            .instanceId = DbGetProcGlobalId()};
        (void)HeapLabelDrop(seRunCtx, &heapCntrAcsInfo, QryRsmGetRsmUndo(label->rsmInfo));
    }
    RemoveKvTableLatch(label);
    return;
}

static Status QryCreateHeapLabel(SeRunCtxHdT seRunCtx, DmKvLabelT *label)
{
    // 基于存储层的要求初始化heapCfg
    HeapAccessCfgT heapCfg = {0};
    Status ret = InitLabelHeapCfg(label, &heapCfg);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (QryRsmIsLoad(label->rsmInfo)) {
        return QryRsmKvHeapLabelRecovery(label, &heapCfg, seRunCtx);
    }
    ret = HeapLabelCreate(seRunCtx, &heapCfg, &label->heapShmAddr);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (label->metaCommon.isUseRsm) {
        ShmemPtrT containerInfoShmAddr;
        ret = HeapLabelRecoveryInfoCreate(&heapCfg, &containerInfoShmAddr);  // 需要在HeapLabelCreate之后
        if (ret != GMERR_OK) {
            return ret;
        }
        QryRsmSaveContainerInfo(label->rsmInfo, RSM_KV_LABEL, containerInfoShmAddr);
    }
    return GMERR_OK;
}

Status InitLabel(SeRunCtxHdT seRunCtx, DmKvLabelT *label)  // 可以公共使用
{
    DB_POINTER(label);
    Status ret;
    // 为当前点标签分配一个唯一的labelId
    if (label->metaCommon.metaId == 0) {
        ret = CataGenerateUuid(DbGetInstanceByMemCtx(label->memCtx), &label->metaCommon.metaId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "generate label uuid for kv.name=%s", label->metaCommon.metaName);  // LCOV_EXCL_LINE
            return ret;
        }
    }

    ret = InitLabelLatch(&label->labelLatchId, &label->labelLatchVersionId, &label->labelLatchShmAddr,
        DbGetInstanceByMemCtx(label->memCtx));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "init label latch.");  // LCOV_EXCL_LINE
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_CREATE_KV_INIT_LATCH);

    label->heapShmAddr = DB_INVALID_SHMPTR;  // InitLabelFreeRes依赖heapShmAddr
    label->rsmInfo = NULL;
    ret = QryRsmCreateRsmInfo(DM_BASE_LABEL(label), KV_TABLE, &label->rsmInfo);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "init rsm tableinfo for kv.name=%s", label->metaCommon.metaName);  // LCOV_EXCL_LINE
        goto ERR;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_CREATE_KV_CREATE_RSM_INFO);

    ret = QryCreateHeapLabel(seRunCtx, label);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto ERR;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_CREATE_KV_CREATE_HEAP);

    label->trxId = 0;
    label->trxIdLastModify = 0;
    label->trxCommitTime = 0;
    return GMERR_OK;
ERR:
    InitLabelFreeRes(seRunCtx, label);
    return ret;
}

Status InitIndex(
    SeRunCtxHdT seRunCtx, DmKvIndexLabelT *index, uint32_t labelId, uint32_t tspId, bool isUseRsm)  // 可以公共使用
{
    DB_POINTER(index);
    index->idxLabelBase.srcLabelId = labelId;
    DbInstanceT *dbInstance = NULL;
    Status ret = DbGetInstanceById(seRunCtx->instanceId, &dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = CataGenerateUuid(dbInstance, &index->idxLabelBase.indexId);
    if (ret != GMERR_OK) {
        DbReleaseInstance(dbInstance);
        DB_LOG_ERROR(ret, "generate uuid from catalog, tspId=%" PRIu32 ".", tspId);  // LCOV_EXCL_LINE
        return ret;
    }
    IndexMultiVersionTypeE indexMultiVersionType =
        index->idxLabelBase.indexType == LIST_LOCALHASH_INDEX ? INDEX_MULTI_VERSION_TYPE : INDEX_ONE_VERSION_TYPE;

    uint32_t tablespaceIndex;
    ret = CataGetTspMgrIdxById(tspId, &tablespaceIndex, dbInstance);
    DbReleaseInstance(dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get tablespaceIndex from catalog, tspId=%" PRIu32 ".", tspId);  // LCOV_EXCL_LINE
        return ret;
    }
    // 创建索引空间
    IndexMetaCfgT indexCfg = {
        .indexId = index->idxLabelBase.indexId,
        .indexCap = index->idxLabelBase.indexLabelCfg.initHashCapacity & 0x7FFFFFFF,
        .idxConstraint = PRIMARY,
        // 根据事务设置索引的多版本类型
        .indexMultiVersionType = indexMultiVersionType,
        // 主键索引不会使用该成员，默认为1即可
        .nullInfoBytes = 1,
        .isUseClusteredHash = false,
        .isLabelLatchMode = index->idxLabelBase.isLabelLatchMode,
        .idxType = index->idxLabelBase.indexType,
        .realIdxType = index->idxLabelBase.indexLabelCfg.hashIdxType,
        .tableSpaceId = tspId,
        .tableSpaceIndex = tablespaceIndex,
        .isMemFirst = true,
        .isVertexUseRsm = isUseRsm,
        .keyDataType = SE_INDEX_DATATYPE_DM_VALUE,
    };
    ShmemPtrT htShmAddr = {0};
    // 调用存储引擎接口创建hash索引空间
    ret = IdxCreate(seRunCtx, indexCfg, &htShmAddr);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 维护Hash索引空间共享内存的入口信息，当前关于Hash索引的元数据仅维护htShmAddr
    index->idxLabelBase.shmAddr.segId = htShmAddr.segId;
    index->idxLabelBase.shmAddr.offset = htShmAddr.offset;
    return GMERR_OK;
}

Status QryCheckDropOrTruncateKvIndex(const DmKvIndexLabelT *index)
{
    if (index->idxLabelBase.indexType != HASH_INDEX &&
        index->idxLabelBase.indexType != CHAINED_HASH_INDEX) {  // Hash索引
        // LCOV_EXCL_START
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_RESTRICT_VIOLATION,
            "Index type is not supported when drop or truncate index. Index source label name:%s.",
            index->idxLabelBase.srcLabelName);
        // LCOV_EXCL_STOP
        return GMERR_RESTRICT_VIOLATION;
    }
    return GMERR_OK;
}

Status QryCreateKvTable(SeRunCtxHdT seRunCtx, DmKvLabelT *label)
{
    DB_POINTER(label);
    // 判断该KV标签是否可以创建成功
    Status ret = CataBasicCheckLabelForCreate(&label->metaCommon, DbGetInstanceByMemCtx(label->memCtx));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "This KV table: %s is already exist.", label->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_CREATE_KV_BEGIN);
    // QryCreateGlobalKvTable内申请的index在DmDestroyKvLabel处释放,InitLabel内部只释放heap和RemoveKvTableLatch
    ret = InitLabel(seRunCtx, label);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmKvIndexLabelT *index = label->index;
    ret = InitIndex(
        seRunCtx, index, label->metaCommon.metaId, label->metaCommon.tablespaceId, label->metaCommon.isUseRsm);
    if (ret != GMERR_OK) {
        RollbackCreateKvTable(seRunCtx, label);
        DB_LOG_ERROR(ret, "create the index of KV table: %s .", label->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_CREATE_KV_INIT_INDEX);
    QryRsmSetLabelIsValid(label->rsmInfo, true);
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_CREATE_KV_END);
    // 调用Catalog接口维护该标签相关的元数据
    // 当前Heap空间只需保存一个共享内存的入口信息，该信息存储在相应table的成员heapShmAddr中
    // 索引的元信息目前维护在所属的label中
    ret = CataSaveLabel(label);
    if (ret != GMERR_OK) {
        RollbackCreateKvTable(seRunCtx, label);
        return ret;
    }
    return GMERR_OK;
}

Status QryCheckDropKvTable(DmKvLabelT *label)
{
    DB_POINTER(label);
    DmKvIndexLabelT *index = label->index;
    if (index != NULL) {
        Status ret = QryCheckDropOrTruncateKvIndex(index);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status QryDropKvTable(SeRunCtxHdT seRunCtx, DmKvLabelT *label)
{
    DB_POINTER2(seRunCtx, label);
    Status ret = GMERR_OK;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx((DbMemCtxT *)seRunCtx->sessionMemCtx);

    ret = QryCheckDropKvTable(label);
    if (ret != GMERR_OK) {
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_DROP_KV_BEGIN);
    QryRsmSetLabelIsValid(label->rsmInfo, false);

    // 删除该标签对应的Heap空间 -- 在签被Open之后则无法删除
    // 在索引被Open后，相关的标签也会被Open
    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = label->heapShmAddr,
        .isPersistent = label->metaCommon.isPersistent,
        .isUseRsm = label->metaCommon.isUseRsm,
        .instanceId = DbGetInstanceId(dbInstance)};
    ret = HeapLabelDrop(seRunCtx, &heapCntrAcsInfo, QryRsmGetRsmUndo(label->rsmInfo));
    if (ret != GMERR_OK) {
        return ret;
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_DROP_KV_DROP_HEAP);
    QryRsmFreeRsmInfo(label->rsmInfo);
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_DROP_KV_FREE_RSM_INFO);
    // 删除该点标签对应的索引空间
    DmKvIndexLabelT *index = label->index;
    if (index != NULL) {
        ret = IdxDrop(seRunCtx, (uint8_t)index->idxLabelBase.indexType, index->idxLabelBase.shmAddr);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_DROP_KV_DROP_INDEX);
    // 在catalog中删除该表的元数据，元数据删成功后不允许再返回错误
    ret = CataRemoveLabelById(dbInstance, label->metaCommon.metaId);
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_DROP_KV_END);
    return ret;
}

Status QryTruncateKvTable(QryStmtT *stmt, DmKvLabelT *kvTable)
{
    DB_POINTER2(stmt, kvTable);
    Status ret = GMERR_OK;
    SeRunCtxHdT seRunCtx = stmt->session->seInstance;
    DmKvIndexLabelT *index = kvTable->index;
    if (index != NULL) {
        ret = QryCheckDropOrTruncateKvIndex(index);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_KV_BEGIN);
    QryRsmSetTableIsTruncate(kvTable->rsmInfo, true);
    // 注意：此处之后不允许返回错误
    // 删除该标签对应的Heap空间 -- 在标签被Open之后则无法执行truncate操作
    // 在索引被Open后，相关的标签也会被Open
    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = kvTable->heapShmAddr,
        .isPersistent = kvTable->metaCommon.isPersistent,
        .isUseRsm = kvTable->metaCommon.isUseRsm,
        .instanceId = DbGetProcGlobalId()};
    (void)HeapLabelTruncate(seRunCtx, &heapCntrAcsInfo, QryRsmGetRsmUndo(kvTable->rsmInfo));
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_KV_TRUNC_CONTAINER);
    // 删除该标签对应的索引空间
    if (index != NULL) {
        IdxTruncate(seRunCtx, (uint8_t)index->idxLabelBase.indexType, index->idxLabelBase.shmAddr);
    }
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_KV_TRUNC_INDEX);
    QryRsmSetTableIsTruncate(kvTable->rsmInfo, false);
    SHM_CRASHPOINT_ONE(SHM_CRASH_DDL_TRUNCATE_KV_END);

#ifdef FEATURE_REPLICATION
    // 失败无法处理
    ret = QryReplicateTruncateKvTable(stmt->session->logBuf, kvTable);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "replicate truncate kv table: %s.", kvTable->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }
#endif

    return GMERR_OK;
}

Status QryChangeKvTableTrxInfoWithCCMode(DmKvLabelT *label)
{
    DmTrxInfoT nspTrxInfo = {0};
    Status ret =
        CataGetNspTrxInfoById(label->metaCommon.namespaceId, &nspTrxInfo, DbGetInstanceByMemCtx(label->memCtx));
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "get nsp trx info for kv table: %s, namespaceId: %" PRIu32 ".", label->metaCommon.metaName,
            label->metaCommon.namespaceId);
        // LCOV_EXCL_STOP
        return ret;
    }
    ret = QryChangeLabelTrxInfoWithCCMode(
        nspTrxInfo.trxType, nspTrxInfo.isolationLevel, label->ccType, &label->trxType, &label->isolationLevel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "change kv trx info: %s.", label->metaCommon.metaName);  // LCOV_EXCL_LINE
        return ret;
    }
#ifdef FEATURE_GQL
    label->skipRowLockPessimisticRR = false;
#endif
    return ret;
}

Status QryCheckCreateKvTable(DmKvLabelT *label)
{
    DB_POINTER(label);
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(label->memCtx);
    Status ret = QryRsmCheckLabelNsp(dbInstance, DM_BASE_LABEL(label));
    if (ret != GMERR_OK) {
        return ret;
    }

    if ((label->trxType == OPTIMISTIC_TRX && label->isolationLevel == REPEATABLE_READ) ||
        (label->trxType == PESSIMISTIC_TRX && label->isolationLevel == SERIALIZABLE)) {
        // 乐观+rr，悲观+串行化表不触发缩容
        label->needDefragmentation = false;
    }
    return ret;
}

Status QryCreateGlobalKvTable(SeRunCtxHdT seRunCtx, uint32_t nspId, uint32_t tspId)
{
    DmKvLabelT *label = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(DbGetInstanceByMemCtx((DbMemCtxT *)seRunCtx->sessionMemCtx));
    Status ret = DmCreateEmptyKvLabel(cataCacheMgr->dynMemCtx, &label);
    if (ret != GMERR_OK) {
        return ret;
    }
    label->metaCommon.dbId = DEFAULT_DATABASE_ID;
    label->metaCommon.namespaceId = nspId;
    label->metaCommon.tablespaceId = tspId;
    label->metaCommon.rsmTablespaceId = DB_INVALID_TABLE_SPACE_ID;
    if ((ret = CataGenerateUuid(DbGetInstanceByMemCtx(label->memCtx), &label->metaCommon.metaId)) != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate uuid when create global KV table.");  // LCOV_EXCL_LINE
        goto EXIT;
    }
    uint32_t metaNameLen = DM_STR_LEN(GLOBAL_KV_TABLE_NAME);
    label->metaCommon.metaName = (char *)DbDynMemCtxAlloc(label->memCtx, metaNameLen);
    if (label->metaCommon.metaName == NULL) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_OUT_OF_MEMORY, "metaname is null.");  // LCOV_EXCL_LINE
        goto EXIT;
    }
    errno_t err = strcpy_s(label->metaCommon.metaName, metaNameLen, GLOBAL_KV_TABLE_NAME);
    if (err != EOK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR_AND_SET_LASTERR(
            GMERR_INTERNAL_ERROR, "copy string: '%s' when create global KV table.", GLOBAL_KV_TABLE_NAME);
        // LCOV_EXCL_STOP
        DmDestroyKvLabel(label);
        return GMERR_INTERNAL_ERROR;
    }
    // 全局kv表默认使用CONCURRENCY_CONTROL_NORMAL，nsp隔离级别一致
    label->ccType = CONCURRENCY_CONTROL_NORMAL;
    ret = QryChangeKvTableTrxInfoWithCCMode(label);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "change TrxInfo when parse kv label: %s cctype:  %" PRId32 ".", label->metaCommon.metaName,
            (int32_t)label->ccType);
        // LCOV_EXCL_STOP
        goto EXIT;
    }
    ret = DmCreateKvIndex(label->memCtx, label, &label->index);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = QryCheckCreateKvTable(label);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(
            ret, "check kv table when create global kv table: %s.", label->metaCommon.metaName);  // LCOV_EXCL_LINE
        goto EXIT;
    }
    ret = QryCreateKvTable(seRunCtx, label);
EXIT:
    DmDestroyKvLabel(label);
    return ret;
}

Status QryExecuteCreateKvTable(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryCreateKvTableDescT *desc = (QryCreateKvTableDescT *)stmt->context->entry;
    Status ret = QryCheckCreateKvTable(desc->label);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "check kv table when create kv table: %s.", desc->label->metaCommon.metaName);
        // LCOV_EXCL_STOP
        return ret;
    }

    ret = QryCreateTspForTable(stmt, DM_BASE_LABEL(desc->label), &desc->tspConf, &desc->tspName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryCreateKvTable(stmt->session->seInstance, desc->label);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryRsmAddKvLabelRecoveryTask(desc->label);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "add rsm label recovery task, label name=%s", desc->label->metaCommon.metaName);
        // LCOV_EXCL_STOP
        goto ERROR;
    }

#ifdef FEATURE_REPLICATION
    ret = FixBufPutUint32(stmt->session->rsp, desc->label->metaCommon.metaId);
    if (ret != GMERR_OK) {
        goto ERROR;
    }

    ret = QryReplicateCreateKvTable(stmt->session->logBuf, desc->label);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "create kv table in replication, label name=%s", desc->label->metaCommon.metaName);
        // LCOV_EXCL_STOP
        goto ERROR;
    }
#endif
    return GMERR_OK;

ERROR:
    RollbackCreateKvTable(stmt->session->seInstance, desc->label);
    return ret;
}

Status QryExecuteDropKvTable(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    CataKeyT cataKey;
    DmKvLabelT *label = NULL;
    QryDropKvTableDescT *desc = (QryDropKvTableDescT *)stmt->context->entry;
    CataSetKeyForLabel(&cataKey, stmt->session->dbId, desc->namespaceId, desc->tableName.str);
    // 从Catalog中查询对应名称的点标签
    Status ret = CataGetLabelByName(&cataKey, &label, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "get KV table from catalog when drop KV table, name: %s.", cataKey.labelName);
        // LCOV_EXCL_STOP
        return ret;
    }

    ret = QryAcqLatchForKvLabel(stmt, label, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "acqLatch when drop KV table, name: %s.", cataKey.labelName);  // LCOV_EXCL_LINE
        goto EXIT;
    }

    ret = QryDropKvTable(stmt->session->seInstance, label);
    QryIncCheckChangedObjectCnt();
    QryIncCheckDeletedSubsCnt();
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop KV table, name: %s.", cataKey.labelName);  // LCOV_EXCL_LINE
        goto EXIT;
    }

#ifdef FEATURE_REPLICATION
    ret = QryReplicateDropKvTable(stmt->session->logBuf, label);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "drop kv table in replication, label name=%s", label->metaCommon.metaName);  // LCOV_EXCL_LINE
        goto EXIT;
    }
#endif

EXIT:
    (void)CataReleaseLabel(label);
    return ret;
}

Status QryExecuteTruncateKvTable(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryTruncateKvTableDescT *desc = (QryTruncateKvTableDescT *)stmt->context->entry;
    DmKvLabelT *kvTable = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, stmt->session->dbId, desc->namespaceId, desc->tableName.str);

    Status ret = CataGetLabelByName(&cataKey, &kvTable, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryAcqLatchForKvLabel(stmt, kvTable, false);
    if (ret != GMERR_OK) {
        (void)CataReleaseLabel(kvTable);
        return ret;
    }

    ret = QryTruncateKvTable(stmt, kvTable);
    (void)CataReleaseLabel(kvTable);
    return ret;
}

static Status QryGetKvTableById(uint32_t labelId, DmBuffer *buffer, uint64_t pid, DbInstanceHdT dbInstance)
{
    DB_POINTER(buffer);
    Status ret;
    // 从Catalog中查询相应点标签的元数据
    DmKvLabelT *label = NULL;
    ret = CataGetLabelById(labelId, &label, dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 序列化KvLabel得到buffer
    CataClientRefT *node = NULL;
    CataCreateClientRefCtxT ctx = {.pid = pid, .metaType = CATA_KV, .dbInstance = dbInstance};
    ret = CataCreateClientRefOnList(&ctx, &label->pidRefList, (DmMetaCommonT *)label, &node);
    if (ret != GMERR_OK) {
        (void)CataReleaseLabel(label);
        DB_LOG_ERROR(ret, "Create kv label client ref");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = GetMetaBuffer(node, label->metaCommon.metaShmPtr, label->metaCommon.metaId, buffer);

    // 释放该元数据
    (void)CataReleaseLabel(label);
    return ret;
}

static Status QryGetKvTableByName(const CataKeyT *cataKey, DmBuffer *buffer, uint64_t pid, DbInstanceHdT dbInstance)
{
    DB_POINTER2(cataKey, buffer);
    Status ret;
    // 从Catalog中查询相应标签的元数据
    DmKvLabelT *label = NULL;
    ret = CataGetLabelByName(cataKey, &label, dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 序列化KvLabel得到buffer
    CataClientRefT *node = NULL;
    CataCreateClientRefCtxT ctx = {.pid = pid, .metaType = CATA_KV, .dbInstance = dbInstance};
    ret = CataCreateClientRefOnList(&ctx, &label->pidRefList, (DmMetaCommonT *)label, &node);
    if (ret != GMERR_OK) {
        (void)CataReleaseLabel(label);
        return ret;
    }
    ret = GetMetaBuffer(node, label->metaCommon.metaShmPtr, label->metaCommon.metaId, buffer);
    // 释放该元数据
    (void)CataReleaseLabel(label);
    return ret;
}

Status QryExecuteGetKvTable(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret;
    QryGetKvTableDescT *desc = (QryGetKvTableDescT *)stmt->context->entry;
    TextT kvTable;
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)stmt->memCtx;

    if (desc->labelId != 0) {
        ret = QryGetKvTableById(
            desc->labelId, &buffer, stmt->session->conn->pid, DbGetInstanceByMemCtx(stmt->session->memCtx));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get kv table by id=%" PRIu32 ".", desc->labelId);  // LCOV_EXCL_LINE
            return ret;
        }
    } else {
        CataKeyT cataKey;
        CataSetKeyForLabel(&cataKey, stmt->session->dbId, desc->namespaceId, desc->labelName.str);
        ret = QryGetKvTableByName(
            &cataKey, &buffer, stmt->session->conn->pid, DbGetInstanceByMemCtx(stmt->session->memCtx));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_ON_DEMAND(ret, "get kv table by name: %s.", desc->labelName.str);  // LCOV_EXCL_LINE
            return ret;
        }
    }
    kvTable.str = (char *)buffer.buf;
    kvTable.len = buffer.len;
    ret = FixBufPutText(stmt->session->rsp, &kvTable);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "put kv table when get kv table, tableName: %s.", desc->labelName.str);
        // LCOV_EXCL_STOP
    }
    return ret;
}

Status QryExecuteCreateKvTableForDbStart(
    SessionT *session, SeRunCtxHdT seRunCtx, DbMemCtxT *memCtx, QryCreateKvTableDescT *desc)
{
    DB_POINTER2(memCtx, desc);
    Status ret = QryCheckCreateKvTable(desc->label);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(ret, "check kv table when create kv table for db start: %s.", desc->label->metaCommon.metaName);
        // LCOV_EXCL_STOP
        return ret;
    }
    ret = QryCreateTspForTableForDbStart(memCtx, DM_BASE_LABEL(desc->label), &desc->tspConf, &desc->tspName);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = QryCreateKvTable(seRunCtx, desc->label);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryRsmAddKvLabelRecoveryTask(desc->label);
    if (ret != GMERR_OK) {
        RollbackCreateKvTable(session->seInstance, desc->label);
        return ret;
    }

    return GMERR_OK;
}

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
bool QryWarmRebootVertexLabelFilter(const void *metaLabel)
{
    const DmVertexLabelT *vertexLabel = *(const DmVertexLabelT **)(uintptr_t)metaLabel;
    VertexLabelCommonInfoT *commonInfo = (VertexLabelCommonInfoT *)DbShmPtrToAddr(vertexLabel->commonInfoShmPtr);
#ifdef FEATURE_RSMEM
    return commonInfo != NULL && vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_NORMAL &&
           !DmIsYangVertexLabel(vertexLabel) && commonInfo->edgeLabelNum == 0 && commonInfo->autoIncrPropNum == 0 &&
           commonInfo->resColInfo == NULL && !DmVertexLabelIsDatalogLabel(vertexLabel);
#else
    return commonInfo != NULL && vertexLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_NORMAL &&
           !DmIsYangVertexLabel(vertexLabel) && commonInfo->edgeLabelNum == 0 && commonInfo->autoIncrPropNum == 0 &&
           commonInfo->resColInfo == NULL && !DmVertexLabelIsDatalogLabel(vertexLabel) &&
           vertexLabel->metaVertexLabel->warmRebootStoreDisk;
#endif
}

static bool WarmRebootKvTableFilter(const void *metaLabel)
{
#ifdef FEATURE_RSMEM
    return true;
#else
    const DmKvLabelT *kv = *(const DmKvLabelT **)(uintptr_t)metaLabel;
    return kv->warmRebootStoreDisk;
#endif
}

static void QryReleaseWarmRebootTable(CataMetaTypeE type, DmMetaCommonT *metaCommon)
{
    if (type == CATA_VL) {
        (void)CataReleaseVertexLabel((DmVertexLabelT *)(void *)metaCommon);
    } else if (type == CATA_KV) {
        (void)CataReleaseLabel((DmKvLabelT *)(void *)metaCommon);
    }
}

static Status QrySetWarmRebootOriLabel(QryStmtT *stmt, DmVertexLabelT *vertexLabel, uint32_t nspId)
{
    Status ret = GMERR_OK;
    DmVertexLabelT *originLabel = NULL;
    if (vertexLabel->metaVertexLabel->originLabelId != 0) {
        ret = CataGetVertexLabelById(
            DbGetInstanceByMemCtx(stmt->session->memCtx), vertexLabel->metaVertexLabel->originLabelId, &originLabel);
        if (ret != GMERR_OK && ret != GMERR_UNDEFINED_TABLE) {
            // LCOV_EXCL_START
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "get origin label, Id: %u.", vertexLabel->metaVertexLabel->originLabelId);
            // LCOV_EXCL_STOP
            return ret;
        }
    }
    if ((originLabel != NULL) && (originLabel->metaCommon.namespaceId == nspId) &&
        (originLabel->metaCommon.metaName != NULL)) {
        ret = FixBufPutString(stmt->session->rsp, originLabel->metaCommon.metaName);
    } else {
        ret = FixBufPutString(stmt->session->rsp, vertexLabel->metaCommon.metaName);
    }
    if (originLabel != NULL) {
        (void)CataReleaseVertexLabel(originLabel);
    }
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "put origin label name to rsp, Id: %u.", vertexLabel->metaVertexLabel->originLabelId);
        // LCOV_EXCL_STOP
    }
    return ret;
}

static Status QryGetOriLabelName(QryStmtT *stmt, CataMetaTypeE type, DmMetaCommonT *metaCommon, uint32_t nspId)
{
    Status ret = GMERR_OK;
    if (type == CATA_VL) {
        ret = QrySetWarmRebootOriLabel(stmt, (DmVertexLabelT *)(void *)metaCommon, nspId);
    } else {
        ret = FixBufPutString(stmt->session->rsp, metaCommon->metaName);
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get nsp name, nspId: %u.", metaCommon->namespaceId);  // LCOV_EXCL_LINE
    }
    return ret;
}

static inline Status QryGetWarmRebootTablesCheck(QryStmtT *stmt, CataMetaTypeE type, uint32_t *count)
{
    DB_POINTER2(stmt, count);
    if (type != CATA_KV && type != CATA_VL) {
        // warmReboot暂不支持其他label
        // LCOV_EXCL_START
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_FEATURE_NOT_SUPPORTED, "warm reboot only support vertex or kv label.");
        // LCOV_EXCL_STOP
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static Status QryGetWarmRebootTables(QryStmtT *stmt, CataMetaTypeE type, uint32_t *count)
{
    Status ret = QryGetWarmRebootTablesCheck(stmt, type, count);
    if (ret != GMERR_OK) {
        return ret;
    }
    DbOamapIteratorT iter = 0;
    DmMetaCommonT *metaCommon = NULL;
    MetaFetchFilterFucT warmRebootTableFilter =
        type == CATA_VL ? QryWarmRebootVertexLabelFilter : WarmRebootKvTableFilter;
    uint32_t nspId = 0;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(stmt->session->memCtx);
    if ((ret = CataGetNamespaceIdByName(dbInstance, PUBLIC_NAMESPACE_NAME, &nspId)) != GMERR_OK) {
        return ret;
    }
    while (CataFetchMetaLabel(&iter, type, (void **)&metaCommon, warmRebootTableFilter, dbInstance) == GMERR_OK) {
#ifdef FEATURE_RSMEM
        // 使用Gmc接口导出时，只导出对应nsp下的表
        if (metaCommon->namespaceId != stmt->session->namespaceId) {
            continue;
        }
#endif
        char *nspName = NULL;
        if ((ret = CataGetNamespaceNameById(stmt->memCtx, &nspName, metaCommon->namespaceId)) != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "get nsp name, nspId: %u.", metaCommon->namespaceId);  // LCOV_EXCL_LINE
            QryReleaseWarmRebootTable(type, metaCommon);
            return ret;
        }
        if ((ret = QryGetOriLabelName(stmt, type, metaCommon, nspId)) != GMERR_OK) {
            QryReleaseWarmRebootTable(type, metaCommon);
            DbDynMemCtxFree(stmt->memCtx, nspName);
            return ret;
        }
        ret = FixBufPutString(stmt->session->rsp, metaCommon->metaName);
        QryReleaseWarmRebootTable(type, metaCommon);
        if (ret != GMERR_OK) {
            // LCOV_EXCL_START
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "put vertex or kv label name, tableName: %s.", metaCommon->metaName);
            // LCOV_EXCL_STOP
            DbDynMemCtxFree(stmt->memCtx, nspName);
            return ret;
        }
        ret = FixBufPutString(stmt->session->rsp, nspName);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "put nsp name, nspName: %s.", nspName);  // LCOV_EXCL_LINE
            DbDynMemCtxFree(stmt->memCtx, nspName);
            return ret;
        }
        DbDynMemCtxFree(stmt->memCtx, nspName);
        (*count)++;
    }
    return ret;
}

Status QryExecuteGetWarmRebootTables(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    uint32_t count = 0;
    uint32_t offset = 0;
    Status ret = FixBufReserveDataOffset(stmt->session->rsp, sizeof(uint64_t), &offset);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "reserve data offset when get warm reboot vertex tables.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = QryGetWarmRebootTables(stmt, CATA_VL, &count);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get warm reboot tables, type: Vertex Label.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = FixBufPutReservedData(stmt->session->rsp, offset + sizeof(uint32_t), &count, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "reserve data offset when get warm reboot kv tables.");  // LCOV_EXCL_LINE
        return ret;
    }
    count = 0;
    ret = QryGetWarmRebootTables(stmt, CATA_KV, &count);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get warm reboot tables, type: Kv Label.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = FixBufPutReservedData(stmt->session->rsp, offset, &count, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(
            ret, "reserve data offset of count when get warm reboot kv tables.");  // LCOV_EXCL_LINE
        return ret;
    }
    return GMERR_OK;
}
#endif

#ifdef __cplusplus
}
#endif
