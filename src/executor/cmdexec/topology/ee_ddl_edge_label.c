/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation of executor DDL edge label
 * Author: linhuabin
 * Create: 2023-01-03
 */

#include "ee_ddl_edge_label.h"
#include "ee_topo_label.h"
#include "se_space.h"
#include "se_table_space_pub.h"
#include "dm_data_topo_seri.h"
#include "db_label_latch_mgr.h"
#include "ee_concurrency_control.h"
#include "dm_meta_basic.h"
#include "dm_meta_topo_label.h"
#include "ee_systbl.h"
#include "ee_ddl_kv.h"
#include "ee_dcl_ctrl.h"
#include "ee_ddl_tablespace.h"
#include "ee_ddl_vertex_label.h"
#include "ee_cmd.h"
#include "ee_feature_import.h"
#include "se_persist.h"
#include "ee_log.h"

#ifdef __cplusplus
extern "C" {
#endif

static void RemoveEdgeLabelLatch(DmEdgeLabelT *edgeLabel)
{
    RemoveLabelLatchWhenCreateFailed(edgeLabel->edgeLabelLatchId, edgeLabel->edgeLabelLatchVersionId, NULL);
    edgeLabel->labelLatchShmAddr = DB_INVALID_SHMPTR;
}

static Status QryCreateEdgeLabelInitCfg(DmEdgeLabelT *edgeLabel, EdgeTopoStorageCfgT *edgeTopoCfg)
{
    uint32_t addrLen;
    Status ret = SeGetHeapTupleAddrLenByInstanceId(DbGetProcGlobalId(), &addrLen);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    edgeTopoCfg->seInstanceId = DbGetProcGlobalId();
    edgeTopoCfg->rowSize = (uint16_t)(addrLen * DmGetSeriEdgeAddrNum(edgeLabel));

    edgeTopoCfg->labelId = edgeLabel->metaCommon.metaId;
    edgeTopoCfg->tableSpaceId =
        edgeLabel->metaCommon.isUseRsm ? edgeLabel->metaCommon.rsmTablespaceId : edgeLabel->metaCommon.tablespaceId;
    edgeTopoCfg->isPersistent = edgeLabel->metaCommon.isPersistent;
    if (edgeTopoCfg->isPersistent) {
        ret = SeGetSpaceIdByType(SPACE_TYPE_USER_DEFAULT, DbGetProcGlobalId(), &edgeTopoCfg->tableSpaceIndex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get space id by type when init edgelabel info.");
        }
    } else {
        ret = CataGetTspMgrIdxById(
            edgeTopoCfg->tableSpaceId, &edgeTopoCfg->tableSpaceIndex, DbGetInstanceByMemCtx(edgeLabel->memCtx));
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "get tablespaceIndex from catalog, tablespaceId=%" PRIu32 ", edgeLabel Id=%" PRIu32 ".",
                edgeTopoCfg->tableSpaceId, edgeLabel->metaCommon.metaId);
        }
    }
    return ret;
}

Status QryCreateEdgeLabel(DmEdgeLabelT *edgeLabel)
{
    DB_POINTER(edgeLabel);
    Status ret;
    // 给当前边标签分配一个唯一的labelId
    if (edgeLabel->metaCommon.metaId == 0) {
        ret = CataGenerateUuid(DbGetInstanceByMemCtx(edgeLabel->memCtx), &edgeLabel->metaCommon.metaId);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    // 为当前边标签创建一个EdgeTopo存储空间
    EdgeTopoStorageCfgT edgeTopoCfg;
    ret = QryCreateEdgeLabelInitCfg(edgeLabel, &edgeTopoCfg);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 初始化fixedHeap管理页Ptr
    ShmemPtrT topoShmAddr = edgeLabel->topoShmAddr;
    edgeLabel->topoAddrCache = NULL;
    if (SeGetPersistMode() == PERSIST_OFF ||
        (topoShmAddr.segId == 0 && topoShmAddr.offset == 0)) {  // 通过这个判断是否是重启
        ret = EdgeTopoLabelCreate(&edgeTopoCfg, &topoShmAddr, &edgeLabel->topoAddrCache);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "create edge label, tablespaceId=%" PRIu32 ", edgeLabel Id=%" PRIu32 ".",
                edgeTopoCfg.tableSpaceId, edgeLabel->metaCommon.metaId);
            return ret;
        }
    } else {
        ret = EdgeTopoInitMemfield(topoShmAddr, &edgeLabel->topoAddrCache);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "init memory field");
            return ret;
        }
    }

    // 保存入口addr，当前存储层仅要求Catalog记录共享内存入口addr
    edgeLabel->topoShmAddr.segId = topoShmAddr.segId;
    edgeLabel->topoShmAddr.offset = topoShmAddr.offset;
    edgeLabel->trxId = 0;
    edgeLabel->trxIdLastModify = 0;
    edgeLabel->trxCommitTime = 0;

    ret = InitLabelLatch(
        &edgeLabel->edgeLabelLatchId, &edgeLabel->edgeLabelLatchVersionId, &edgeLabel->labelLatchShmAddr, NULL);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        EdgeTopoLabelDrop(topoShmAddr, edgeLabel->metaCommon.isPersistent);
        return ret;
    }
    return GMERR_OK;
}

Status QryCreateSingleEdgeLabel(QryStmtT *stmt, DmEdgeLabelT *edgeLabel)
{
    Status ret = QryCreateEdgeLabel(edgeLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    if (stmt != NULL) {
        QryCreateEdgeLabelDescT *desc = (QryCreateEdgeLabelDescT *)stmt->context->entry;
        // 如果是重启，为持久化EdgeLabel重新生成内存态数据
        ret = QryRebuildMemDataForPersistEdgeLabel(edgeLabel, desc->isReboot);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "rebuild memory data for edgeLabel:%s", edgeLabel->metaCommon.metaName);
            return ret;
        }
        // 重启时重建系统表中读取出来的边表时，不用插入系统表；只有需要持久化的边表才需要插入
        if (!desc->isReboot && edgeLabel->metaCommon.isPersistent) {
            ret = SysTableInsertOneEdgeLabel(stmt->session, edgeLabel);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                RemoveEdgeLabelLatch(edgeLabel);
                EdgeTopoLabelDrop(edgeLabel->topoShmAddr, edgeLabel->metaCommon.isPersistent);
                DB_LOG_ERROR(ret, "to insert edgeLabel to sysTable, label name=%s", edgeLabel->metaCommon.metaName);
                return ret;
            }
        }
    }
    // 调用Catalog维护元数据
    ret = CataSaveEdgeLabel(edgeLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        RemoveEdgeLabelLatch(edgeLabel);
        EdgeTopoLabelDrop(edgeLabel->topoShmAddr, edgeLabel->metaCommon.isPersistent);
    }
    return ret;
}

Status QryDropEdgeLabelByName(DmEdgeLabelT *edgeLabel)
{
    DB_POINTER(edgeLabel);

    CataKeyT cataKey = {0};
    CataSetKeyForLabel(
        &cataKey, edgeLabel->metaCommon.dbId, edgeLabel->metaCommon.namespaceId, edgeLabel->metaCommon.metaName);
    // 校验并删除对应的Catalog数据
    Status ret = CataRemoveEdgeLabelByName(DbGetInstanceByMemCtx(edgeLabel->memCtx), &cataKey);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 删除EdgeLabel对应的空间
    EdgeTopoLabelDrop(edgeLabel->topoShmAddr, edgeLabel->metaCommon.isPersistent);
    return GMERR_OK;
}

// vertex label的memCtx addr一并序列化到了buf中，在反序列化时需要重新设置memCtx
Status QryGetEdgeLabelBufferByName(const CataKeyT *cataKey, DmBuffer *buffer, uint64_t pid)
{
    DB_POINTER2(cataKey, buffer);
    Status ret;
    // 从Catalog中查询相应边标签的元数据
    DmEdgeLabelT *edgeLabel = NULL;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(buffer->memCtx);
    ret = CataGetEdgeLabelByName(cataKey, &edgeLabel, dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 序列化edgeLabel得到buffer
#if defined(EXPERIMENTAL_NERGC)
    if (DbIsTcp()) {
        buffer->len = DmGetEdgeLabelSeriBufLength(edgeLabel);
        buffer->buf = (uint8_t *)DbDynMemCtxAlloc(buffer->memCtx, buffer->len);
        if (buffer->buf == NULL) {
            CataReleaseEdgeLabel(edgeLabel);
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc memory for serializing edgeLabel.");
            return GMERR_OUT_OF_MEMORY;
        }
        ret = DmSerializeEdgeLabel(edgeLabel, buffer);
        if (ret != GMERR_OK) {
            CataReleaseEdgeLabel(edgeLabel);
            DbDynMemCtxFree(buffer->memCtx, buffer->buf);
            DB_LOG_ERROR(ret, "serialize edgeLabel!");
            return ret;
        }
    } else {
#endif
        CataClientRefT *node = NULL;
        CataCreateClientRefCtxT ctx = {
            .pid = pid, .metaType = CATA_EL, .dbInstance = DbGetInstanceByMemCtx(buffer->memCtx)};
        ret = CataCreateClientRefOnList(&ctx, &edgeLabel->pidRefList, (DmMetaCommonT *)edgeLabel, &node);
        if (ret != GMERR_OK) {
            (void)CataReleaseEdgeLabel(edgeLabel);
            DB_LOG_ERROR(ret, "Create edge label client ref.");
            return ret;
        }
        ret = GetMetaBuffer(node, edgeLabel->metaCommon.metaShmPtr, edgeLabel->metaCommon.metaId, buffer);
#if defined(EXPERIMENTAL_NERGC)
    }
#endif

    // 释放该元数据
    (void)CataReleaseEdgeLabel(edgeLabel);
    return ret;
}

Status QryGetEdgeLabelBufferById(uint32_t edgeLabelId, DmBuffer *buffer, uint64_t pid)
{
    DB_POINTER(buffer);
    Status ret;
    // 从Catalog中查询相应边标签的元数据
    DmEdgeLabelT *retEdgeLabel = NULL;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(buffer->memCtx);
    ret = CataGetEdgeLabelById(edgeLabelId, &retEdgeLabel, dbInstance);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 序列化edgeLabel得到buffer
#if defined(EXPERIMENTAL_NERGC)
    if (DbIsTcp()) {
        buffer->len = DmGetEdgeLabelSeriBufLength(retEdgeLabel);
        buffer->buf = (uint8_t *)DbDynMemCtxAlloc(buffer->memCtx, buffer->len);
        if (buffer->buf == NULL) {
            CataReleaseEdgeLabel(retEdgeLabel);
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc memory for serializing edgeLabel.");
            return GMERR_OUT_OF_MEMORY;
        }
        ret = DmSerializeEdgeLabel(retEdgeLabel, buffer);
        if (ret != GMERR_OK) {
            CataReleaseEdgeLabel(retEdgeLabel);
            DbDynMemCtxFree(buffer->memCtx, buffer->buf);
            DB_LOG_ERROR(ret, "serialize edgeLabel!");
            return ret;
        }
    } else {
#endif
        CataClientRefT *node = NULL;
        CataCreateClientRefCtxT ctx = {
            .pid = pid, .metaType = CATA_EL, .dbInstance = DbGetInstanceByMemCtx(buffer->memCtx)};
        ret = CataCreateClientRefOnList(&ctx, &retEdgeLabel->pidRefList, (DmMetaCommonT *)retEdgeLabel, &node);
        if (ret != GMERR_OK) {
            (void)CataReleaseEdgeLabel(retEdgeLabel);
            DB_LOG_ERROR(ret, "Create edge label client ref.");
            return ret;
        }
        ret = GetMetaBuffer(node, retEdgeLabel->metaCommon.metaShmPtr, retEdgeLabel->metaCommon.metaId, buffer);
#if defined(EXPERIMENTAL_NERGC)
    }
#endif

    // 释放该元数据
    (void)CataReleaseEdgeLabel(retEdgeLabel);
    return ret;
}

static Status QryResetFixRowAfterCreateEdge(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret = GMERR_OK;
    uint32_t count = DbGaListGetCount(&stmt->context->labels);
    for (uint32_t i = 0; i < count; i++) {
        QryLabelT *label = NULL;
        ret = QryGetVertexLabelByIdx(stmt->context, i, &label);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "get vertex label by idx.");
            return ret;
        }
        if (label->def.vertexLabel->metaVertexLabel->labelLevel != VERTEX_LEVEL_SIMPLE) {
            // 不是简单表，不用调整fixRow size
            continue;
        }
        ret = QryAcqLatchForVertexLabel(stmt, label->def.vertexLabel, false);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        uint32_t fixRowSize = DmGetFixVertexLabelLen(label->def.vertexLabel);
        if (SECUREC_UNLIKELY(fixRowSize > DB_MAX_UINT16)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "get vertex label fix len, labelName: %s.",
                label->def.vertexLabel->metaCommon.metaName);
            return GMERR_DATA_EXCEPTION;
        }
        HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = label->def.vertexLabel->commonInfo->heapInfo.heapShmAddr,
            .isPersistent = label->def.vertexLabel->metaCommon.isPersistent,
            .isUseRsm = label->def.vertexLabel->metaCommon.isUseRsm,
            .instanceId = DbGetProcGlobalId()};
        ret = HeapLabelResetFixRow(&heapCntrAcsInfo, (uint16_t)fixRowSize);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        // 此处是同一个事务会乱序申请相关顶点表的事务表锁和表Latch
        // 事务锁是可重入，因此此处只释放表latch即可
        // 如果是失败场景，表latch统一由外层在事务提交后释放
        QryReleaseLastLabelLatch(stmt->session);
    }
    return ret;
}

Status EdgeLabelCreateTableSpace(QryStmtT *stmt, QryCreateEdgeLabelDescT *desc, uint32_t index, DmEdgeLabelT *edgeLabel)
{
    TextT *tspName = (TextT *)DbGaListGet(&desc->tableSpaceNames, index);
    if (SECUREC_UNLIKELY(tspName == NULL)) {
        DB_LOG_ERROR_AND_SET_LASTERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Null value not allowed.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return QryCreateTspForTable(stmt, DM_BASE_LABEL(edgeLabel), &desc->tspConf, tspName);
}

// 这里原子性和一致性存在问题
Status QryExecuteCreateEdgeLabel(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret = GMERR_OK;
    QryCreateEdgeLabelDescT *desc = (QryCreateEdgeLabelDescT *)stmt->context->entry;
    uint32_t count = DbGaListGetCount(&desc->edgeLabels);
    for (uint32_t i = 0; i < count; i++) {
        ret = QryExecuteCommitTrans(stmt);  // 为了适配持久化事务回滚，需要单表开启事务
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "commit transaction when create edgeLabel.");
            return ret;
        }
        ret = QryExecuteBeginTrans(stmt);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "begin transaction when create edgeLabel.");
            return ret;
        }
        DmEdgeLabelT *edgeLabel = (DmEdgeLabelT *)DbGaListGet(&desc->edgeLabels, i);
        if (SECUREC_UNLIKELY(edgeLabel == NULL)) {
            DB_LOG_ERROR_AND_SET_LASTERR(GMERR_DATA_EXCEPTION, "Get null edgeLabel from desc.");
            return GMERR_DATA_EXCEPTION;
        }
        ret = EdgeLabelCreateTableSpace(stmt, desc, i, edgeLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        ret = QryCreateSingleEdgeLabel(stmt, edgeLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    ret = QryResetFixRowAfterCreateEdge(stmt);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return ret;
}

Status QryExecuteGetEdgeLabel(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    Status ret;
    QryGetEdgeLabelDescT *desc = (QryGetEdgeLabelDescT *)stmt->context->entry;
    TextT edgeLabelBuf;
    DmBuffer buffer;
    buffer.memCtx = (DbMemCtxT *)stmt->memCtx;
    if (desc->labelId != 0) {
        ret = QryGetEdgeLabelBufferById(desc->labelId, &buffer, stmt->session->conn->pid);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        CataKeyT cataKey;
        CataSetKeyForLabel(&cataKey, stmt->session->dbId, desc->namespaceId, desc->labelName.str);
        ret = QryGetEdgeLabelBufferByName(&cataKey, &buffer, stmt->session->conn->pid);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    edgeLabelBuf.str = (char *)buffer.buf;
    edgeLabelBuf.len = buffer.len;
    ret = FixBufPutText(stmt->session->rsp, &edgeLabelBuf);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "put edge label when execute get edge label.");
    }
    return ret;
}

Status QryExecuteDropSingleEdgeLabel(QryStmtT *stmt, DmEdgeLabelT *edgeLabel)
{
    Status ret = QryAcqLatchForEdgeLabel(stmt, edgeLabel, true);
    if (ret != GMERR_OK) {
        return ret;
    }

    return QryDropEdgeLabelByName(edgeLabel);
}

static Status QryDropEdgeLabelCheckVertexLabel(QryStmtT *stmt, uint32_t vertexLabelId)
{
    Status ret = GMERR_OK;
    DmVertexLabelT *vertexLabel = NULL;
    ret = CataGetVertexLabelById(DbGetInstanceByMemCtx(stmt->session->memCtx), vertexLabelId, &vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryAcqLatchForVertexLabel(stmt, vertexLabel, false);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = vertexLabel->commonInfo->heapInfo.heapShmAddr,
        .instanceId = DbGetProcGlobalId(),
        .isPersistent = vertexLabel->metaCommon.isPersistent,
        .isUseRsm = vertexLabel->metaCommon.isUseRsm,
    };
    HeapPerfStatT heapPerfStat = {};
    HeapCfgStatT heapCfgStat = {};
    // 不支持聚簇容器，不用考虑
    ret = HeapLabelGetPerfStat(&heapCntrAcsInfo, &heapPerfStat, &heapCfgStat);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get heap statistics, vertexLabel=%s.", vertexLabel->metaCommon.metaName);
        goto EXIT;
    }

    if (heapPerfStat.phyItemNum > 0) {
        ret = GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "put drop edge label, vertexLabel=%s, phyItemNum=%" PRIu64 ".",
            vertexLabel->metaCommon.metaName, heapPerfStat.phyItemNum);
        goto EXIT;
    }

EXIT:
    CataReleaseVertexLabel(vertexLabel);
    return ret;
}

Status QryExecuteDropEdgeLabel(QryStmtT *stmt)
{
    DB_POINTER(stmt);
    QryDropEdgeLabelDescT *desc = (QryDropEdgeLabelDescT *)stmt->context->entry;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, stmt->session->dbId, desc->namespaceId, desc->labelName.str);
    DmEdgeLabelT *edgeLabel = NULL;
    Status ret = CataGetEdgeLabelByName(&cataKey, &edgeLabel, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = QryDropEdgeLabelCheckVertexLabel(stmt, edgeLabel->sourceVertexLabelId);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = QryDropEdgeLabelCheckVertexLabel(stmt, edgeLabel->destVertexLabelId);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    if (edgeLabel->metaCommon.isPersistent) {
        ret = SysTableDeleteOneEdgeLabel(stmt->session, edgeLabel);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }
    ret = QryExecuteDropSingleEdgeLabel(stmt, edgeLabel);

EXIT:
    (void)CataReleaseEdgeLabel(edgeLabel);
    return ret;
}

Status QryExecuteFindAssocEdgeLabel(
    QryStmtT *stmt, QryDropAssocParamT parameters, QryDropVertexLabelDescT *desc, uint32_t edgeLabelId)
{
    DmEdgeLabelT *edgeLabel = NULL;
    Status ret = CataGetEdgeLabelById(edgeLabelId, &edgeLabel, DbGetInstanceByMemCtx(stmt->session->memCtx));
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t *labelId = (uint32_t *)DbDynMemCtxAlloc(stmt->memCtx, sizeof(uint32_t));
    if (labelId == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "execute find associated vertexLabel.");
        goto EXIT;
    }
    *labelId = edgeLabelId;
    ret = DbOamapInsert(parameters.edgeLabelMap, *labelId, labelId, NULL, NULL);
    if (ret == GMERR_DUPLICATE_OBJECT) {
        ret = GMERR_OK;
        goto EXIT;
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "add new item to set in execute find associated edgeLabel.");
        goto EXIT;
    }

    parameters.vertexLabelName = edgeLabel->destVertexName;
    ret = QryExecuteFindAssocVertexLabel(stmt, parameters, desc);

EXIT:
    (void)CataReleaseEdgeLabel(edgeLabel);
    return ret;
}

Status QryExecuteDropAssocEdgeLabel(QryStmtT *stmt, const DbOamapT *edgeLabelMap, uint32_t nspId)
{
    Status ret = GMERR_OK;

    DbOamapIteratorT i = 0;
    void *key = NULL;
    void *value = NULL;
    while (DbOamapFetch(edgeLabelMap, &i, &key, &value) == GMERR_OK) {
        DmEdgeLabelT *edgeLabel = NULL;
        ret = CataGetEdgeLabelById(*(uint32_t *)key, &edgeLabel, DbGetInstanceByMemCtx(stmt->session->memCtx));
        if (ret == GMERR_UNDEFINED_TABLE) {
            continue;
        }
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get edgeLabel, Id=%" PRIu32 ".", *(uint32_t *)key);
            return ret;
        }
        ret = QryExecuteDropSingleEdgeLabel(stmt, edgeLabel);
        (void)CataReleaseEdgeLabel(edgeLabel);
        if (ret != GMERR_OK) {
            return ret;
        }

        // 级联drop表，是先删除边表，再按次序删除顶点表，与其他事务的正常访问顺序相反会死锁
        // 因此此处提前释放表latch与事务表锁
        // 如果是失败场景，表latch统一由外层在事务提交后释放
        QryReleaseAndUpgradeLastLabelLatch(stmt->session);
        if (SeTransGetTrxType(stmt->session->seInstance) != OPTIMISTIC_TRX) {
            SeLockMgrLockReleaseLastNewLabelXLock(stmt->session->seInstance);
        }
    }

    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
