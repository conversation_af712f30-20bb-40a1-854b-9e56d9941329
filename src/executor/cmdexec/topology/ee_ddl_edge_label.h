/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: header file of DDL executor
 * Author: zhulixia
 * Create: 2020-08-27
 */

#ifndef EE_DDL_EDGE_LABEL_H
#define EE_DDL_EDGE_LABEL_H

#include "ee_stmt.h"
#include "ee_ddl_desc.h"

#ifdef __cplusplus
extern "C" {
#endif

SO_EXPORT_FOR_TS Status QryExecuteCreateEdgeLabel(QryStmtT *stmt);
Status QryExecuteGetEdgeLabel(QryStmtT *stmt);
Status QryExecuteDropEdgeLabel(QryStmtT *stmt);
Status QryExecuteDropSingleEdgeLabel(QryStmtT *stmt, DmEdgeLabelT *edgeLabel);
Status QryCreateSingleEdgeLabel(QryStmtT *stmt, DmEdgeLabelT *edgeLabel);
Status QryDropEdgeLabelByName(DmEdgeLabelT *edgeLabel);
Status QryGetEdgeLabelBufferById(uint32_t edgeLabelId, DmBuffer *buffer, uint64_t pid);
Status QryGetEdgeLabelBufferByName(const CataKeyT *cataKey, DmBuffer *buffer, uint64_t pid);
Status QryExecuteFindAssocEdgeLabel(
    QryStmtT *stmt, QryDropAssocParamT parameters, QryDropVertexLabelDescT *desc, uint32_t edgeLabelId);
Status QryExecuteDropAssocEdgeLabel(QryStmtT *stmt, const DbOamapT *edgeLabelMap, uint32_t nspId);

#ifdef __cplusplus
}
#endif
#endif  // EE_DDL_EDGE_LABEL_H
