
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Implementation of subtree filter
 * Author: guo<PERSON>wu
 * Create: 2024-03-06
 */

#include "dm_yang_interface.h"
#include "gmc_yang_types.h"
#include "ee_yang_edit_common.h"
#include "ee_subtree_path.h"
#include "ee_subtree_common.h"
#include "ee_subtree_resultset.h"
#include "ee_non_presence_op.h"
#include "ee_subtree_raw.h"

#ifdef __cplusplus
extern "C" {
#endif

static Status QryAddDefault2DmVertex(QryTrimHandlerT *handler, QryTrimIteratorT *iter);
static Status QryTrimDefault4DmVertex(QryTrimHandlerT *handler, QryTrimIteratorT *iter);
static Status QryTrimDmVertexEmptyNode(QryTrimHandlerT *handler, QryTrimIteratorT *iter, bool *isEmpty);

void QryClearDmNode(DmNodeT *node)
{
    DmClearNode(node);
}

void QryTraceRawVertex(QryStmtT *stmt, QryRawVertexT *rawVertex)
{
    if (rawVertex->vertex != NULL) {
        YANG_TRACE_MASK_LOG(YANG_TRACE_SUBTREE,
            "RawVertex(%s) : isReused %" PRIu8 ", isModified %" PRIu8 ", heapBufSize %" PRIu32 "",
            rawVertex->vertex->vertexDesc->labelName, rawVertex->isReused, rawVertex->isModified,
            rawVertex->heapBuf.bufSize);
        DmTraceVertex(stmt->memCtx, rawVertex->vertex);
    } else {
        YANG_TRACE_MASK_LOG(YANG_TRACE_SUBTREE, "RawVertex not found in heap.");
    }
}

static inline QryLabelT *QryGetFilterContextLabel(QryFilterContextT *ctx)
{
    // label 和 edgeLabel不可能同时为NULL
    return (ctx->label == NULL) ? ctx->edgeLabel->destQryLabel : ctx->label;
}

static Status QryDmvertexNodeIsVisible(QryStmtT *stmt, DmVertexT *vertex, DmSchemaT *schema, bool *isVisible)
{
    DB_POINTER4(stmt, vertex, schema, isVisible);

    *isVisible = false;
    if (!DmSchemaHasValidWhenClause(schema)) {
        *isVisible = true;
        return GMERR_OK;
    }

    return QryDmVertexNpaVisibleByIndex(vertex, schema->yangInfo->npaIndex, isVisible);
}

static Status QryIsDefaultCaseVisible(QryStmtT *stmt, DmVertexT *vertex, DmSchemaT *schema, bool *isVisible)
{
    if (!DmIsChoiceSchema(schema)) {
        return GMERR_INTERNAL_ERROR;
    }

    Status ret = QryDmvertexNodeIsVisible(stmt, vertex, schema, isVisible);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (*isVisible == false) {
        return GMERR_OK;
    }

    DmSchemaT *defaultCase = NULL;
    for (uint32_t i = 0; i < schema->nodeNum; i++) {
        DmSchemaT *currSchema = schema->nodes[i].schema;
        if (DmIsDefaultSchema(currSchema)) {
            defaultCase = currSchema;
            break;
        }
    }

    if (defaultCase == NULL) {
        *isVisible = false;
        return GMERR_OK;
    }

    return QryDmvertexNodeIsVisible(stmt, vertex, defaultCase, isVisible);
}

Status QryCreateDmVertexByDefault(QryFilterContextT *ctx, QryRawVertexT *rawVertex)
{
    DB_POINTER3(ctx, ctx->stmt, rawVertex);

    QryStmtT *stmt = ctx->stmt;
    QryLabelT *qryLabel = QryGetFilterContextLabel(ctx);
    DmVertexLabelT *vertexLabel = qryLabel->def.vertexLabel;
    if (DmIsPresenceVertexLabel(vertexLabel) || DmIsListVertexLabel(vertexLabel) ||
        DmIsLeafListVertexLabel(vertexLabel)) {
        return GMERR_OK;
    }

    DmSchemaT *schema = vertexLabel->metaVertexLabel->schema;
    // 对无默认值子树的NP类container 的vertex， 则不创建dmvertex对象
    if (!DmHasYangDefaultSchema(schema)) {
        return GMERR_OK;
    }

    DmVertexT *emptyVertex = NULL;
    Status ret = DmCreateEmptyVertexWithMemCtx((DbMemCtxT *)stmt->memCtx, vertexLabel, &emptyVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    rawVertex->vertex = emptyVertex;
    rawVertex->isReused = false;
    YANG_TRACE_MASK_LOG(
        YANG_TRACE_SUBTREE, "After Build a %s empty RawVertex: ", qryLabel->def.vertexLabel->metaCommon.metaName);
    QryTraceRawVertex(stmt, rawVertex);

    bool isVisible = false;
    ret = QryDmvertexNodeIsVisible(stmt, emptyVertex, schema, &isVisible);
    if (SECUREC_UNLIKELY((ret != GMERR_OK) || (isVisible == false))) {
        goto END_ERR;
    }

    return GMERR_OK;

END_ERR:
    DmDestroyVertex(emptyVertex);
    QryInitRawVertex(rawVertex);
    return ret;
}

static Status QryPropertyDefaultValueIsVisible(DmVertexT *vertex, DmSchemaT *schema, uint32_t index, bool *isVisible)
{
    DB_POINTER3(vertex, schema, isVisible);

    *isVisible = false;
    DmPropertySchemaT *propSchema = NULL;
    Status ret = DmSchemaGetPropeById(schema, index, &propSchema);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "default value get property schema.");
        return ret;
    }

    if (!DmPropeSchemaHasValidWhenClause(propSchema)) {
        *isVisible = true;
        return GMERR_OK;
    }

    return QryDmVertexNpaVisibleByIndex(vertex, propSchema->npaIndex, isVisible);
}

static Status QryAddDefault2DmRecord(DmVertexT *vertex, DmRecordT *record, DmSchemaT *schema, bool *isModified)
{
    DB_POINTER4(vertex, record, schema, isModified);

    for (uint32_t i = 0; i < record->recordDesc->propeNum; ++i) {
        DmPropertyInfoT *curPrope = &(record->recordDesc->propeInfos[i]);
        if (!curPrope->isValid || curPrope->isSysPrope) {
            continue;
        }

        if ((record->propeIsSetValue[i] == DM_PROPERTY_IS_NOT_NULL) || (curPrope->defaultValueNum == 0)) {
            continue;
        }

        bool isVisible = false;
        Status ret = QryPropertyDefaultValueIsVisible(vertex, schema, i, &isVisible);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "get %s property %" PRIu32 " visible.", vertex->vertexDesc->labelName, i);
            return ret;
        }

        if (isVisible) {
            ret = SetDefaultValueByPropId(schema, i, record);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                DB_LOG_ERROR(ret, "set %s property %" PRIu32 " default value.", vertex->vertexDesc->labelName, i);
                return ret;
            }
            *isModified = true;
        }
    }

    return GMERR_OK;
}

static Status QryDmNodeIsUserChoice(QryTrimHandlerT *handler, QryTrimIteratorT *iter, bool *isUserChoice)
{
    DB_POINTER3(iter, iter->schema, iter->dmNode);

    *isUserChoice = false;
    DmNodeT *node = iter->dmNode;
    DmSchemaT *schema = iter->schema;
    if (!DmNodeIsCreated(node)) {
        return GMERR_OK;
    }

    uint32_t childNum = iter->childNum;
    DmNodeT **children = iter->childNodes;
    for (uint32_t i = 0; i < childNum; i++) {
        DmNodeT *currNode = children[i];
        DmSchemaT *currSchema = schema->nodes[i].schema;
        if (!DmNodeIsCreated(currNode)) {
            continue;
        }

        bool isEmpty = true;
        QryTrimHandlerT choiceHandler = *handler;
        choiceHandler.filterMode = DM_DEFAULT_FILTER_EXPLICIT;
        QryTrimIteratorT currIter = {.uniqNodeId = currSchema->yangInfo->uniqueNodeId,
            .schema = currSchema,
            .dmNode = currNode,
            .dmRecord = currNode->currRecord,
            .childNodes = currNode->nodes,
            .childNum = currNode->nodeDesc->nodeNumPerElement};
        Status ret = QryTrimDmVertexEmptyNode(&choiceHandler, &currIter, &isEmpty);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }

        if (choiceHandler.rawVertex->isModified) {
            handler->rawVertex->isModified = true;
        }

        if (!isEmpty) {
            *isUserChoice = true;
            break;
        }
    }

    return GMERR_OK;
}

static Status QryCreateChoiceAndDefaultCase(DmNodeT *currNode, DmSchemaT *currSchema)
{
    DB_POINTER2(currNode, currSchema);

    // 创建choice节点
    Status ret = DmNodeInit(currNode);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    // 创建default case节点
    DmSchemaT *dftCase = NULL;
    for (uint32_t i = 0; i < currSchema->nodeNum; i++) {
        dftCase = currSchema->nodes[i].schema;
        if (!DmIsDefaultSchema(dftCase)) {
            continue;
        }
        DmNodeT *caseNode = NULL;
        ret = DmNodeGetChildNodeByIndex(currNode, i, &caseNode);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        // 找到default case node，创建该node
        ret = DmNodeInit(caseNode);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        break;
    }

    if (dftCase == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Default case, Name=%s", currNode->nodeDesc->name);
        return GMERR_INTERNAL_ERROR;
    }

    return GMERR_OK;
}

Status QryAddDefault2DmNodeRecursively4Choice(QryTrimHandlerT *handler, QryTrimIteratorT *iter)
{
    DB_POINTER3(iter, iter->schema, iter->dmNode);
    DB_POINTER2(handler, handler->stmt);

    bool isUser = false;
    DmNodeT *currNode = iter->dmNode;
    DmSchemaT *currSchema = iter->schema;
    Status ret = QryDmNodeIsUserChoice(handler, iter, &isUser);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (isUser) {
        return QryAddDefault2DmVertex(handler, iter);
    }

    if (DmNodeIsCreated(currNode)) {
        DmClearNode(currNode);
        handler->rawVertex->isModified = true;
    }

    if (!DmHasYangDefaultSchema(currSchema)) {
        // 没有default case
        return GMERR_OK;
    }

    bool isVisible = false;
    ret = QryIsDefaultCaseVisible(handler->stmt, handler->rawVertex->vertex, currSchema, &isVisible);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    if (isVisible == false) {
        // choice 和 default case均不可见
        return GMERR_OK;
    }

    // 创建choice节点和default case节点
    ret = QryCreateChoiceAndDefaultCase(currNode, currSchema);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    handler->rawVertex->isModified = true;

    return QryAddDefault2DmVertex(handler, iter);
}

Status QryAddDefault2DmNodeRecursively4Case(QryTrimHandlerT *handler, QryTrimIteratorT *iter)
{
    DB_POINTER3(iter, iter->schema, iter->dmNode);

    DmNodeT *node = iter->dmNode;
    if (!node->isCreated) {
        return GMERR_OK;
    }

    return QryAddDefault2DmVertex(handler, iter);
}

Status QryAddDefault2DmNodeRecursively4Container(QryTrimHandlerT *handler, QryTrimIteratorT *iter)
{
    DB_POINTER3(handler, handler->stmt, handler->rawVertex->vertex);
    DB_POINTER3(iter, iter->schema, iter->dmNode);

    Status ret = GMERR_OK;
    DmSchemaT *currSchema = iter->schema;
    DmNodeT *currNode = iter->dmNode;
    if (DmIsPresenceSchema(currSchema)) {
        if (!DmNodeIsCreated(currNode)) {
            // P 节点不存在
            return GMERR_OK;
        }
        // P 节点存在且有默认值子树数据
        return QryAddDefault2DmVertex(handler, iter);
    }

    if (!DmNodeIsCreated(currNode)) {
        // 不存在且可见，则创建
        bool isVisible = false;
        QryStmtT *stmt = handler->stmt;
        DmVertexT *vertex = handler->rawVertex->vertex;
        ret = QryDmvertexNodeIsVisible(stmt, vertex, currSchema, &isVisible);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        if (!isVisible) {
            return GMERR_OK;
        }
        ret = DmNodeInit(currNode);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        handler->rawVertex->isModified = true;
    }

    return QryAddDefault2DmVertex(handler, iter);
}

Status QryAddDefault2DmNodeRecursively(QryTrimHandlerT *handler, QryTrimIteratorT *iter)
{
    DB_POINTER2(iter, iter->schema);

    Status ret = GMERR_OK;
    DmSchemaT *schema = iter->schema;
    uint32_t childNum = iter->childNum;
    for (uint32_t i = 0; i < childNum; i++) {
        DmNodeT *currNode = iter->childNodes[i];
        DmSchemaT *currSchema = schema->nodes[i].schema;
        if (!DmNodeIsCreated(currNode) && (DmIsPresenceSchema(currSchema) || !DmHasYangDefaultSchema(currSchema))) {
            // 不存在的P节点和无默认和的NP节点
            continue;
        }
        // 为NULL，需要创建句柄
        if (currNode == NULL) {
            ret = DmCreateEmptyNode(iter->memCtx, iter->childNodeDescs[i], &iter->childNodes[i]);
            if (ret != GMERR_OK) {
                return ret;
            }
            currNode = iter->childNodes[i];
        }

        QryTrimIteratorT currIter = {.uniqNodeId = currSchema->yangInfo->uniqueNodeId,
            .schema = currSchema,
            .dmNode = currNode,
            .dmRecord = currNode->currRecord,
            .childNodes = currNode->nodes,
            .childNum = currNode->nodeDesc->nodeNumPerElement,
            .childNodeDescs = currNode->nodeDesc->childNodeDescs,
            .memCtx = currNode->memCtx};
        if (DmIsChoiceSchema(currSchema)) {
            ret = QryAddDefault2DmNodeRecursively4Choice(handler, &currIter);
        } else if (DmIsCaseSchema(currSchema)) {
            ret = QryAddDefault2DmNodeRecursively4Case(handler, &currIter);
        } else {
            ret = QryAddDefault2DmNodeRecursively4Container(handler, &currIter);
        }

        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status QryAddDefault2DmVertex(QryTrimHandlerT *handler, QryTrimIteratorT *iter)
{
    DB_POINTER2(iter, iter->schema);
    DB_POINTER2(handler, handler->rawVertex->vertex);

    DmSchemaT *schema = iter->schema;
    DmVertexT *vertex = handler->rawVertex->vertex;
    DmRecordT *record = iter->dmRecord;
    if (DmIsLeafListSchema(schema)) {
        // leaf-list的默认值属性标识不在record上，需要设置在字段上。
        return DmSetPropeDefaultFlag4LeafList(vertex, vertex, &(handler->rawVertex->isModified));
    }
    if (record->recordDesc->hasDefaultValue) {
        bool isModified = false;
        Status ret = QryAddDefault2DmRecord(vertex, record, schema, &isModified);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        if (isModified) {
            handler->rawVertex->isModified = true;
        }
    }

    return QryAddDefault2DmNodeRecursively(handler, iter);
}

Status QryDmNodeTrimDefaultValueRecursively(QryTrimHandlerT *handler, QryTrimIteratorT *iter)
{
    DB_POINTER2(iter, iter->schema);

    DmSchemaT *schema = iter->schema;
    uint32_t childNum = iter->childNum;
    for (uint32_t i = 0; i < childNum; i++) {
        DmNodeT *currNode = iter->childNodes[i];
        if (!DmNodeIsCreated(currNode)) {
            continue;
        }

        DmSchemaT *currSchema = schema->nodes[i].schema;
        // trim模式不包含默认值，未创建的node不用处理
        QryTrimIteratorT currIter = {.uniqNodeId = currSchema->yangInfo->uniqueNodeId,
            .schema = currSchema,
            .dmNode = currNode,
            .dmRecord = currNode->currRecord,
            .childNodes = currNode->nodes,
            .childNum = currNode->nodeDesc->nodeNumPerElement};
        Status ret = QryTrimDefault4DmVertex(handler, &currIter);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status QryTrimDefault4DmVertex(QryTrimHandlerT *handler, QryTrimIteratorT *iter)
{
    DB_POINTER3(iter, iter->schema, iter->dmRecord);

    DmSchemaT *schema = iter->schema;
    DmRecordT *record = iter->dmRecord;
    if (record->recordDesc->hasDefaultValue) {
        bool isModified = false;
        Status ret = DmPruneDefaultValuesFromRecord(schema, record, &isModified);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
        if (isModified) {
            handler->rawVertex->isModified = true;
        }
    }

    return QryDmNodeTrimDefaultValueRecursively(handler, iter);
}

static Status QryLeafListDefaultIsVisibleInNPA(DmVertexT *parent, const QryLabelT *qryLabel, bool *isEmpty)
{
    DB_POINTER2(parent, qryLabel);

    *isEmpty = true;
    DmVertexLabelT *leafListLabel = qryLabel->def.vertexLabel;
    DmSchemaT *schema = leafListLabel->metaVertexLabel->schema;
    if (!DmHasYangDefaultSchema(schema)) {
        return GMERR_OK;
    }

    if (!DmSchemaHasValidWhenClause(schema)) {
        *isEmpty = false;
        return GMERR_OK;
    }

    bool isNewDefaultVisible = false;
    uint8_t npaIndexStart = schema->yangInfo->npaIndex;
    for (uint8_t i = 0; i < schema->properties[DM_YANG_LEAFLIST_KEYPROP_SUBSCRIPT].defaultValueNum; i++) {
        Status ret = QryDmVertexNpaVisibleByIndex(parent, npaIndexStart + i, &isNewDefaultVisible);
        if (ret != GMERR_OK) {
            return ret;
        }

        if (isNewDefaultVisible) {
            *isEmpty = false;
            break;
        }
    }

    return GMERR_OK;
}

static Status QryLabelDefaultValueIsVisible4LeafList(
    DmVertexT *parent, const QryLabelT *qryLabel, uint32_t defaultMode, bool *isEmpty)
{
    DB_POINTER2(qryLabel, isEmpty);

    DmVertexLabelT *vertexLabel = qryLabel->def.vertexLabel;
    if (!DmIsLeafListVertexLabel(vertexLabel)) {
        return GMERR_INTERNAL_ERROR;
    }
    if ((defaultMode != DM_DEFAULT_FILTER_REPORT_ALL) && (defaultMode != DM_DEFAULT_FILTER_REPORT_ALL_TAGGED)) {
        *isEmpty = true;
        return GMERR_OK;
    }

    return QryLeafListDefaultIsVisibleInNPA(parent, qryLabel, isEmpty);
}

static Status QryGetVisibleDmVertex4LeafList(QryStmtT *stmt, QryEdgeLabelT *qryEdgeLabel, uint32_t defaultMode,
    DmVertexT *parent, DmVertexT *currVertex, QryRawVertexT *rawVertex)
{
    DB_POINTER4(stmt, qryEdgeLabel, currVertex, rawVertex);

    qryEdgeLabel->type = SOURCE_VERTEX_NEXT_EDGE_ADDR;
    bool isReused = rawVertex->isReused;
    DmVertexLabelT *vertexLabel = qryEdgeLabel->destQryLabel->def.vertexLabel;
    while (currVertex != NULL) {
        bool isVisible = false;
        Status ret = QryIsLeafListFromSeVisible(parent, currVertex, defaultMode, vertexLabel, &isVisible);
        if (ret != GMERR_OK) {
            return ret;
        }

        if (isVisible) {
            break;
        }

        // 使用nextEdge找下一个vertex
        DmVertexT *nextVertex = NULL;
        ret = QryFetchRelatedVertex(stmt, currVertex, qryEdgeLabel, isReused, &nextVertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get next leafList, Name=%s", currVertex->vertexDesc->labelName);
            return ret;
        }
        currVertex = nextVertex;
    }

    rawVertex->vertex = currVertex;
    rawVertex->isReused = isReused;
    rawVertex->heapBuf = qryEdgeLabel->destQryLabel->heapTupleBuf;

    YANG_TRACE_MASK_LOG(YANG_TRACE_SUBTREE,
        "Try to get a visible leaf-list %s RawVertex from heap: ", vertexLabel->metaCommon.metaName);
    QryTraceRawVertex(stmt, rawVertex);

    return GMERR_OK;
}

static Status QryDmVertexChildIsVisible4LeafList(
    QryStmtT *stmt, DmVertexT *parent, QryEdgeLabelT *qryEdgeLabel, uint32_t defaultMode, bool *isEmpty)
{
    DB_POINTER4(stmt, parent, qryEdgeLabel, isEmpty);

    DmVertexT *currVertex = NULL;
    qryEdgeLabel->type = DEST_VERTEX_ADDR;
    Status ret = QryFetchRelatedVertex(stmt, parent, qryEdgeLabel, true, &currVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "get vertex %s related dest by edge %s", parent->vertexDesc->labelName,
            qryEdgeLabel->label->metaCommon.metaName);
        return ret;
    }

    if (currVertex == NULL) {
        // 没有实例，查看默认值
        return QryLabelDefaultValueIsVisible4LeafList(parent, qryEdgeLabel->destQryLabel, defaultMode, isEmpty);
    }

    QryRawVertexT rawVertex = {0};
    ret = QryGetVisibleDmVertex4LeafList(stmt, qryEdgeLabel, defaultMode, parent, currVertex, &rawVertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get next leafList, Name=%s", currVertex->vertexDesc->labelName);
        return ret;
    }

    if (rawVertex.vertex != NULL) {
        *isEmpty = false;
    }

    return GMERR_OK;
}

static Status QryDmVertexChildIsVisible4List(const DmVertexT *vertex, uint32_t edgeLabelId, bool *isEmpty)
{
    uint64_t edgeAddr = 0;
    Status ret = DmVertexGetFirstEdgeTopoAddrById(vertex, edgeLabelId, &edgeAddr);
    if (ret != GMERR_OK) {
        return ret;
    }

    *isEmpty = (edgeAddr == 0);
    return GMERR_OK;
}

static Status QryDmVertexChildIsVisible(
    QryStmtT *stmt, DmVertexT *parent, QryEdgeLabelT *qryEdgeLabel, uint32_t defaultMode, bool *isEmpty)
{
    DB_POINTER5(stmt, parent, qryEdgeLabel, qryEdgeLabel->label, isEmpty);

    QryLabelT *dstLabel = qryEdgeLabel->destQryLabel;
    if (DmIsLeafListVertexLabel(dstLabel->def.vertexLabel)) {
        return QryDmVertexChildIsVisible4LeafList(stmt, parent, qryEdgeLabel, defaultMode, isEmpty);
    }

    return QryDmVertexChildIsVisible4List(parent, qryEdgeLabel->label->metaCommon.metaId, isEmpty);
}

static Status QryDmVertexGetOutEdgeData(const QryTrimHandlerT *handler, QryTrimIteratorT *iter, bool *isEmpty)
{
    DB_POINTER5(handler, handler->stmt, handler->label, handler->rawVertex->vertex, iter);

    *isEmpty = true;
    QryStmtT *stmt = handler->stmt;
    DmVertexT *parent = handler->rawVertex->vertex;
    uint16_t uniqNodeId = iter->uniqNodeId;
    uint32_t filterMode = handler->filterMode;
    DmVertexLabelT *vertexLabel = handler->label->desc->vertexLabel;
    for (uint32_t i = 0; i < vertexLabel->commonInfo->edgeLabelNum; i++) {
        uint32_t curEdgeId = vertexLabel->commonInfo->relatedEdgeLabels[i].edgeLabelId;
        QryEdgeLabelT *qryEdgeLabel = NULL;
        Status ret = QryGetQryEdgeLabelById(stmt, curEdgeId, &qryEdgeLabel);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "get vertex %s qryEdge label by id %" PRIu32, parent->vertexDesc->labelName, curEdgeId);
            return ret;
        }
        if (qryEdgeLabel->label->sourceVertexLabelId != vertexLabel->metaCommon.metaId ||
            qryEdgeLabel->label->sourceUniqueNodeId != uniqNodeId) {
            continue;
        }

        bool isVertexEmpty = true;
        ret = QryDmVertexChildIsVisible(stmt, parent, qryEdgeLabel, filterMode, &isVertexEmpty);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "get visible for vertex %s by mode %" PRIu32, parent->vertexDesc->labelName, filterMode);
            return ret;
        }

        if (!isVertexEmpty) {
            *isEmpty = false;
            return GMERR_OK;
        }
    }

    return GMERR_OK;
}

Status QryIsRecordAndEdgeEmpty(const QryTrimHandlerT *handler, QryTrimIteratorT *iter, bool *isEmpty)
{
    DB_POINTER3(iter, iter->schema, iter->dmRecord);

    *isEmpty = true;
    DmSchemaT *schema = iter->schema;
    const char *name =
        iter->dmNode == NULL ? handler->rawVertex->vertex->vertexDesc->labelName : iter->dmNode->nodeDesc->name;
    YANG_TRACE_MASK_LOG(YANG_TRACE_SUBTREE,
        "Start to check empty node %s by mode %" PRIu32 "(0:explicit, 1:trim, 2:report-all-tagged, 3:report-all)", name,
        handler->filterMode);
    if (DmIsPresenceSchema(schema) || DmIsListSchema(schema)) {
        *isEmpty = false;
        goto CHK_OK;
    }

    // 判断所有字段是否空
    DmRecordT *record = iter->dmRecord;
    bool withDefault = (handler->filterMode == DM_DEFAULT_FILTER_REPORT_ALL ||
                        handler->filterMode == DM_DEFAULT_FILTER_REPORT_ALL_TAGGED);
    bool isNotEmpty = DmRecordHasNormalPrope(schema, record, withDefault);
    if (isNotEmpty) {
        *isEmpty = false;
        goto CHK_OK;
    }

    // 判断是否有出边数据，需根据查询模式判断：report-all 包括leaf-list的默认值；trim删除默认值；explicit用户值
    Status ret = QryDmVertexGetOutEdgeData(handler, iter, isEmpty);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "check out edge data");
        goto CHK_ERR;
    }

CHK_OK:
    YANG_TRACE_MASK_LOG(YANG_TRACE_SUBTREE, "Check empty node %s end: isEmpty %" PRId8 ",", name, *isEmpty);
    return GMERR_OK;

CHK_ERR:
    return ret;
}

static Status QryTrimDmVertexEmptyNode(QryTrimHandlerT *handler, QryTrimIteratorT *iter, bool *isEmpty)
{
    DB_POINTER2(iter, iter->schema);

    *isEmpty = true;
    bool isCurEmpty = true;
    DmSchemaT *schema = iter->schema;
    DmNodeT **children = iter->childNodes;
    uint32_t childNum = iter->childNum;
    for (uint32_t i = 0; i < childNum; i++) {
        DmNodeT *currNode = children[i];
        DmSchemaT *currSchema = schema->nodes[i].schema;
        if (!DmNodeIsCreated(currNode)) {
            continue;
        }
        bool isNodeEmpty = true;
        QryTrimIteratorT curIter = {.uniqNodeId = currSchema->yangInfo->uniqueNodeId,
            .schema = currSchema,
            .dmNode = currNode,
            .dmRecord = currNode->currRecord,
            .childNodes = currNode->nodes,
            .childNum = currNode->nodeDesc->nodeNumPerElement};
        Status ret = QryTrimDmVertexEmptyNode(handler, &curIter, &isNodeEmpty);
        if (ret != GMERR_OK) {
            return ret;
        }

        if (!isNodeEmpty) {
            isCurEmpty = false;
            continue;
        }

        if (handler->rmGraphFunc != NULL && (DmIsNonPresenceSchema(currSchema) || DmIsChoiceCaseSchema(currSchema))) {
            handler->rawVertex->isModified = true;
            ret = handler->rmGraphFunc(handler->stmt, handler->rawVertex->vertex, handler->rawVertex->addr,
                curIter.schema->yangInfo->uniqueNodeId);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(
                    ret, "delete yang node graph, vLabel=%s.", handler->rawVertex->vertex->vertexDesc->labelName);
                return ret;
            }
        }

        if (handler->rmFunc != NULL && (DmIsNonPresenceSchema(currSchema) || DmIsChoiceCaseSchema(currSchema))) {
            handler->rawVertex->isModified = true;
            handler->rmFunc(currNode);
        }
    }

    if (!isCurEmpty) {
        *isEmpty = false;
        return GMERR_OK;
    }

    return (handler->chkFunc != NULL) ? handler->chkFunc(handler, iter, isEmpty) : GMERR_OK;
}

static Status QryFetchRawVertexByPk(
    QryStmtT *stmt, QryLabelT *qryLabel, DmVertexT *filter, bool isReuse, QryRawVertexT *rawVertex)
{
    DB_POINTER3(stmt, qryLabel, filter);

    DmVertexT *srcVertex = NULL;
    Status ret = QryFetchVertexByPk(stmt, qryLabel, filter, isReuse, &srcVertex);
    if (SECUREC_UNLIKELY((ret != GMERR_OK) || (srcVertex == NULL))) {
        return ret;
    }

    rawVertex->vertex = srcVertex;
    rawVertex->isReused = isReuse;
    return GMERR_OK;
}

static Status QryFetchRawVertexByUniqueKey(
    QryStmtT *stmt, QryLabelT *qryLabel, DmVertexT *filter, uint32_t indexId, bool isReuse, QryRawVertexT *rawVertex)
{
    DB_POINTER3(stmt, qryLabel, filter);

    DmVertexT *srcVertex = NULL;
    Status ret = QryFetchVertexByUniqueKey(stmt, qryLabel, filter, indexId, isReuse, &srcVertex);
    if (SECUREC_UNLIKELY((ret != GMERR_OK) || (srcVertex == NULL))) {
        return ret;
    }

    rawVertex->vertex = srcVertex;
    rawVertex->isReused = isReuse;
    return GMERR_OK;
}

static Status QryFetchRawVertexByScan(
    QryStmtT *stmt, QryLabelT *qryLabel, DmVertexT *filter, bool isReuse, QryRawVertexT *rawVertex)
{
    DB_POINTER3(stmt, qryLabel, filter);

    uint64_t addr = 0;
    DmVertexT *srcVertex = NULL;
    Status ret = QryFetchVertexByScan(stmt, qryLabel, filter, isReuse, &srcVertex, &addr);
    if (SECUREC_UNLIKELY((ret != GMERR_OK) || (srcVertex == NULL))) {
        return ret;
    }

    rawVertex->vertex = srcVertex;
    rawVertex->addr = addr;
    rawVertex->isReused = isReuse;
    return GMERR_OK;
}

static Status QryFetchRelatedRawVertex(
    QryStmtT *stmt, const DmVertexT *currVertex, QryEdgeLabelT *qryEdgeLabel, bool isReuse, QryRawVertexT *rawVertex)
{
    DB_POINTER3(stmt, currVertex, qryEdgeLabel);

    DmVertexT *srcVertex = NULL;
    Status ret = QryFetchRelatedVertex(stmt, currVertex, qryEdgeLabel, isReuse, &srcVertex);
    if (SECUREC_UNLIKELY((ret != GMERR_OK) || (srcVertex == NULL))) {
        return ret;
    }

    rawVertex->vertex = srcVertex;
    rawVertex->isReused = isReuse;
    if (qryEdgeLabel->type == SOURCE_VERTEX_ADDR || qryEdgeLabel->type == DEST_VERTEX_NEXT_EDGE_ADDR ||
        qryEdgeLabel->type == DEST_VERTEX_PREV_EDGE_ADDR) {
        rawVertex->heapBuf = qryEdgeLabel->sourceQryLabel->heapTupleBuf;
    } else {
        rawVertex->heapBuf = qryEdgeLabel->destQryLabel->heapTupleBuf;
    }

    return GMERR_OK;
}

static Status QryFetchRawVertexByAddr(
    QryStmtT *stmt, QryLabelT *qryLabel, bool isReuse, uint64_t vertexAddr, QryRawVertexT *rawVertex)
{
    DB_POINTER2(stmt, qryLabel);

    DmVertexT *srcVertex = NULL;
    Status ret = QryFetchVertexByAddr(stmt, qryLabel, isReuse, vertexAddr, &srcVertex);
    if (SECUREC_UNLIKELY((ret != GMERR_OK) || (srcVertex == NULL))) {
        return ret;
    }

    rawVertex->vertex = srcVertex;
    rawVertex->isReused = isReuse;
    rawVertex->heapBuf = qryLabel->heapTupleBuf;
    return GMERR_OK;
}

static Status QryGetRawVertexFromStorage(QryFilterContextT *ctx, QryRawVertexT *rawVertex)
{
    Status ret = GMERR_OK;
    QryInitRawVertex(rawVertex);
    switch (ctx->mode) {
        case FETCH_BY_FILTER_PK:
            ret = QryFetchRawVertexByPk(ctx->stmt, ctx->label, ctx->filter, ctx->isReuse, rawVertex);
            break;
        case FETCH_BY_FILTER_UNIQUEKEY:
            ret =
                QryFetchRawVertexByUniqueKey(ctx->stmt, ctx->label, ctx->filter, ctx->indexId, ctx->isReuse, rawVertex);
            break;
        case FETCH_BY_FILTER_SCAN:
            ret = QryFetchRawVertexByScan(ctx->stmt, ctx->label, ctx->filter, ctx->isReuse, rawVertex);
            break;
        case FETCH_BY_FILTER_EDGE:
            DB_ASSERT(ctx->isReuse == true);
            ret = QryFetchRelatedRawVertex(ctx->stmt, ctx->filter, ctx->edgeLabel, ctx->isReuse, rawVertex);
            break;
        case FETCH_BY_FILTER_PATH:
            DB_ASSERT(ctx->isReuse == true);
            ret = QryFetchRawVertexByAddr(ctx->stmt, ctx->label, ctx->isReuse, ctx->path->rowId, rawVertex);
            break;
        case FETCH_BY_FILTER_MAX:
        default:
            ret = GMERR_INVALID_PARAMETER_VALUE;
            DB_LOG_ERROR(ret, "get data by mode %" PRIu32, (uint32_t)(ctx->mode));
            break;
    }
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get raw data by mode %" PRIu32, (uint32_t)(ctx->mode));
        return ret;
    }

    QryLabelT *qryLabel = QryGetFilterContextLabel(ctx);
    YANG_TRACE_MASK_LOG(
        YANG_TRACE_SUBTREE, "Subtree try a %s RawVertex from heap: ", qryLabel->def.vertexLabel->metaCommon.metaName);
    QryTraceRawVertex(ctx->stmt, rawVertex);

    return GMERR_OK;
}

static Status QryGetRawDmVertexExplicit(QryFilterContextT *ctx, QryRawVertexT *rawVertex)
{
    DB_POINTER3(ctx, ctx->stmt, rawVertex);

    QryLabelT *qryLabel = QryGetFilterContextLabel(ctx);
    const char *labelName = qryLabel->def.vertexLabel->metaCommon.metaName;
    Status ret = QryGetRawVertexFromStorage(ctx, rawVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get trim vertex for label %s.", labelName);
        return ret;
    }

    if (rawVertex->vertex == NULL) {
        return GMERR_OK;
    }

    DmVertexT *srcVertex = rawVertex->vertex;
    if (!DmIsLeafListVertexDesc(srcVertex->vertexDesc)) {
        return GMERR_OK;
    }

    // 若是leaf-list，则遍历直到获取一个可见的对象
    return QryGetVisibleDmVertex4LeafList(
        ctx->stmt, ctx->edgeLabel, ctx->defaultMode, ctx->parent, srcVertex, rawVertex);
}

static Status QryGetRawDmVertexTrim(QryFilterContextT *ctx, QryRawVertexT *rawVertex)
{
    DB_POINTER3(ctx, ctx->stmt, rawVertex);

    QryLabelT *qryLabel = QryGetFilterContextLabel(ctx);
    const char *labelName = qryLabel->def.vertexLabel->metaCommon.metaName;
    Status ret = QryGetRawDmVertexExplicit(ctx, rawVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get trim vertex for label %s.", labelName);
        return ret;
    }

    if (rawVertex->vertex == NULL) {
        return GMERR_OK;
    }

    // Trim list、container等的默认值
    DmVertexT *srcVertex = rawVertex->vertex;
    QryStmtT *stmt = ctx->stmt;
    DmSchemaT *schema = qryLabel->def.vertexLabel->metaVertexLabel->schema;
    QryTrimHandlerT trimHandler = {.stmt = stmt,
        .label = qryLabel,
        .rawVertex = rawVertex,
        .filterMode = ctx->defaultMode,
        .rmFunc = NULL,
        .rmGraphFunc = NULL,
        .chkFunc = NULL};
    // trim模式不包含默认值，未创建的node不用处理
    QryTrimIteratorT iter = {.uniqNodeId = schema->yangInfo->uniqueNodeId,
        .schema = schema,
        .dmNode = NULL,
        .dmRecord = srcVertex->record,
        .childNodes = srcVertex->nodes,
        .childNum = srcVertex->vertexDesc->nodeNum};
    ret = QryTrimDefault4DmVertex(&trimHandler, &iter);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "trim data for label %s vertex.", labelName);
        return ret;
    }

    YANG_TRACE_MASK_LOG(YANG_TRACE_SUBTREE,
        "After a %s RawVertex trim default values: ", qryLabel->def.vertexLabel->metaCommon.metaName);
    QryTraceRawVertex(ctx->stmt, rawVertex);

    return GMERR_OK;
}

static Status QryPrepareDmVertexReportAll(QryFilterContextT *ctx, QryRawVertexT *rawVertex)
{
    DB_POINTER3(ctx, ctx->stmt, rawVertex);

    QryLabelT *qryLabel = QryGetFilterContextLabel(ctx);
    const char *labelName = qryLabel->def.vertexLabel->metaCommon.metaName;
    Status ret = QryGetRawVertexFromStorage(ctx, rawVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get report-all vertex for label %s.", labelName);
        return ret;
    }

    if (rawVertex->vertex == NULL) {
        ret = QryCreateDmVertexByDefault(ctx, rawVertex);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR_AND_SET_LASTERR(ret, "get raw default data for label %s.", labelName);
            return ret;
        }
    }

    if (rawVertex->vertex == NULL) {
        return GMERR_OK;
    }

    DmVertexT *sourceVertex = rawVertex->vertex;
    if (!DmIsLeafListVertexDesc(sourceVertex->vertexDesc)) {
        return GMERR_OK;
    }

    // 若是leaf-list，则遍历直到获取一个可见的默认值的对象
    return QryGetVisibleDmVertex4LeafList(
        ctx->stmt, ctx->edgeLabel, ctx->defaultMode, ctx->parent, sourceVertex, rawVertex);
}

static Status QryGetRawDmVertexReportAll(QryFilterContextT *ctx, QryRawVertexT *rawVertex)
{
    DB_POINTER3(ctx, ctx->stmt, rawVertex);

    QryLabelT *qryLabel = QryGetFilterContextLabel(ctx);
    const char *labelName = qryLabel->def.vertexLabel->metaCommon.metaName;
    Status ret = QryPrepareDmVertexReportAll(ctx, rawVertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "get report-all vertex for label %s.", labelName);
        return ret;
    }

    if (rawVertex->vertex == NULL) {
        return GMERR_OK;
    }

    // 填充默认值数据
    QryStmtT *stmt = ctx->stmt;
    DmVertexT *srcVertex = rawVertex->vertex;
    DmSchemaT *schema = qryLabel->def.vertexLabel->metaVertexLabel->schema;
    QryTrimHandlerT handler = {.stmt = stmt,
        .label = qryLabel,
        .rawVertex = rawVertex,
        .filterMode = ctx->defaultMode,
        .rmFunc = NULL,
        .rmGraphFunc = NULL,
        .chkFunc = QryIsRecordAndEdgeEmpty};
    // ReportAll包含默认值，未创建的node需要进行创建
    QryTrimIteratorT iter = {.uniqNodeId = schema->yangInfo->uniqueNodeId,
        .schema = schema,
        .dmNode = NULL,
        .dmRecord = srcVertex->record,
        .childNodes = srcVertex->nodes,
        .childNum = srcVertex->vertexDesc->nodeNum,
        .childNodeDescs = srcVertex->vertexDesc->nodeDescs,
        .memCtx = srcVertex->memCtx};
    ret = QryAddDefault2DmVertex(&handler, &iter);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "add default data for label %s vertex.", labelName);
        return ret;
    }

    YANG_TRACE_MASK_LOG(YANG_TRACE_SUBTREE,
        "After a %s RawVertex add default values: ", qryLabel->def.vertexLabel->metaCommon.metaName);
    QryTraceRawVertex(ctx->stmt, rawVertex);

    return GMERR_OK;
}

Status QryTrimDmVertexNode(QryFilterContextT *ctx, QryRawVertexT *rawVertex, bool *isEmpty)
{
    DB_POINTER3(ctx, ctx->stmt, rawVertex);
    // 删除空的NP、choice、case数据节点（无子节点，出边等）
    QryStmtT *stmt = ctx->stmt;
    QryLabelT *qryLabel = QryGetFilterContextLabel(ctx);
    bool vertexEmpty = false;
    DmVertexT *srcVertex = rawVertex->vertex;
    DmSchemaT *schema = qryLabel->def.vertexLabel->metaVertexLabel->schema;
    QryTrimHandlerT handler = {.stmt = stmt,
        .label = qryLabel,
        .rawVertex = rawVertex,
        .filterMode = ctx->defaultMode,
        .rmFunc = QryClearDmNode,
        .rmGraphFunc = NULL,
        .chkFunc = QryIsRecordAndEdgeEmpty};
    // trim模式不包含默认值，未创建的node不用处理
    QryTrimIteratorT iter = {.uniqNodeId = schema->yangInfo->uniqueNodeId,
        .schema = schema,
        .dmNode = NULL,
        .dmRecord = srcVertex->record,
        .childNodes = srcVertex->nodes,
        .childNum = srcVertex->vertexDesc->nodeNum};
    Status ret = QryTrimDmVertexEmptyNode(&handler, &iter, &vertexEmpty);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR_AND_SET_LASTERR(ret, "delete empty NP node");
        return ret;
    }

    *isEmpty = vertexEmpty;
    if (vertexEmpty) {
        QryInitRawVertex(rawVertex);
    }

    YANG_TRACE_MASK_LOG(
        YANG_TRACE_SUBTREE, "After a %s RawVertex trim empty nodes: ", qryLabel->def.vertexLabel->metaCommon.metaName);
    QryTraceRawVertex(stmt, rawVertex);
    return GMERR_OK;
}

Status QryTrimDmVertexGraph(QryStmtT *stmt, QryLabelT *qryLabel, QryRawVertexT *rawVertex, bool *isEmpty)
{
    DB_POINTER3(stmt, qryLabel, rawVertex);
    // 删除空的NP、choice、case数据节点及其子树，包括出边数据（leaf-list的默认值可能在存储）
    if (stmt->qryCursor == NULL) {
        DB_LOG_WARN(GMERR_INVALID_PARAMETER_VALUE, "delete vertex graph");
        return GMERR_OK;
    }

    bool vertexEmpty = false;
    DmVertexT *srcVertex = rawVertex->vertex;
    DmSchemaT *schema = qryLabel->def.vertexLabel->metaVertexLabel->schema;
    QryTrimHandlerT handler = {.stmt = stmt,
        .label = qryLabel,
        .rawVertex = rawVertex,
        .filterMode = DM_DEFAULT_FILTER_EXPLICIT,
        .rmFunc = NULL,
        .rmGraphFunc = QryDeleteVertexNodeGraph,
        .chkFunc = QryIsRecordAndEdgeEmpty};
    // trim模式不包含默认值，未创建的node不用处理
    QryTrimIteratorT iter = {.uniqNodeId = schema->yangInfo->uniqueNodeId,
        .schema = schema,
        .dmNode = NULL,
        .dmRecord = srcVertex->record,
        .childNodes = srcVertex->nodes,
        .childNum = srcVertex->vertexDesc->nodeNum};
    Status ret = QryTrimDmVertexEmptyNode(&handler, &iter, &vertexEmpty);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "delete empty NP node");
        return ret;
    }

    *isEmpty = vertexEmpty;
    if (vertexEmpty) {
        QryInitRawVertex(rawVertex);
    }

    YANG_TRACE_MASK_LOG(
        YANG_TRACE_SUBTREE, "After a %s RawVertex trim empty graph: ", qryLabel->def.vertexLabel->metaCommon.metaName);
    QryTraceRawVertex(stmt, rawVertex);
    return GMERR_OK;
}

Status QryGetRawDmVertexData(QryFilterContextT *ctx, QryRawVertexT *rawVertex)
{
    DB_POINTER3(ctx, ctx->stmt, rawVertex);

    Status ret = GMERR_OK;
    // 如果从Label中没有扫描到点，根据默认值查询模式判断是否需要创建空的vertex，提取默认值数据
    switch (ctx->defaultMode) {
        case DM_DEFAULT_FILTER_EXPLICIT:
            ret = QryGetRawDmVertexExplicit(ctx, rawVertex);
            break;
        case DM_DEFAULT_FILTER_TRIM:
            ret = QryGetRawDmVertexTrim(ctx, rawVertex);
            break;
        case DM_DEFAULT_FILTER_REPORT_ALL:
        case DM_DEFAULT_FILTER_REPORT_ALL_TAGGED:
            ret = QryGetRawDmVertexReportAll(ctx, rawVertex);
            break;
        default:
            DB_LOG_ERROR_AND_SET_LASTERR(
                GMERR_INVALID_PARAMETER_VALUE, "subtree filter type: %" PRIu32, ctx->defaultMode);
            break;
    }

    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get raw data for filter mode %" PRIu32, ctx->defaultMode);
        return ret;
    }

    if (rawVertex->vertex == NULL) {
        return GMERR_OK;
    }
    rawVertex->vertex->isDeltaVertex = true;

    if (DmIsLeafListVertexDesc(rawVertex->vertex->vertexDesc)) {
        return GMERR_OK;
    }

    // 删除空的NP、choice、case数据节点（无子节点，出边等）
    bool isEmpty = false;
    ret = QryTrimDmVertexNode(ctx, rawVertex, &isEmpty);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "trim empty nodes for vertex");
        return ret;
    }

    return GMERR_OK;
}

Status QryGetChildVertexByPath(
    QryStmtT *stmt, DmSubtreeT *parent, DmPathTreeT *path, QryEdgeLabelT *qryEdgeLabel, QryRawVertexT *child)
{
    DB_POINTER3(stmt, qryEdgeLabel, path);

    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    QryFilterContextT subCtx = {.mode = FETCH_BY_FILTER_PATH,
        .defaultMode = currFilter->defaultMode,
        .configFilter = currFilter->configFilter,
        .isReuse = true,
        .stmt = stmt,
        .parent = parent->origVertex,
        .filter = NULL,
        .path = path,
        .label = qryEdgeLabel->destQryLabel,
        .edgeLabel = qryEdgeLabel};
    Status ret = QryGetRawDmVertexData(&subCtx, child);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(
            ret, "get fetch vertex %s by path(addr)", qryEdgeLabel->destQryLabel->def.vertexLabel->metaCommon.metaName);
        return ret;
    }

    return GMERR_OK;
}

Status QryGetChildVertexByEdge(QryStmtT *stmt, DmSubtreeT *parent, QryEdgeLabelT *qryEdgeLabel, QryRawVertexT *child)
{
    DB_POINTER3(stmt, qryEdgeLabel, parent);

    qryEdgeLabel->type = DEST_VERTEX_ADDR;
    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    QryFilterContextT subCtx = {.mode = FETCH_BY_FILTER_EDGE,
        .defaultMode = currFilter->defaultMode,
        .configFilter = currFilter->configFilter,
        .isReuse = true,
        .stmt = stmt,
        .parent = parent->origVertex,
        .filter = parent->origVertex,
        .path = NULL,
        .label = NULL,
        .edgeLabel = qryEdgeLabel};
    Status ret = QryGetRawDmVertexData(&subCtx, child);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(
            ret, "get fetch vertex %s by edge addr", qryEdgeLabel->destQryLabel->def.vertexLabel->metaCommon.metaName);
        return ret;
    }

    return GMERR_OK;
}

Status QryGetChildVertexByUniqueKey(QryStmtT *stmt, DmSubtreeT *parent, DmSubtreeT *filter, uint32_t indexId,
    QryEdgeLabelT *qryEdgeLabel, QryRawVertexT *child)
{
    DB_POINTER3(stmt, qryEdgeLabel, filter);

    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    QryFilterContextT subCtx = {.mode = FETCH_BY_FILTER_UNIQUEKEY,
        .indexId = indexId,
        .defaultMode = currFilter->defaultMode,
        .configFilter = currFilter->configFilter,
        .isReuse = !currFilter->isLocationFilter,
        .stmt = stmt,
        .parent = parent->origVertex,
        .filter = filter->vertex,
        .path = NULL,
        .label = qryEdgeLabel->destQryLabel,
        .edgeLabel = qryEdgeLabel};
    Status ret = QryGetRawDmVertexData(&subCtx, child);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "get fetch vertex by %s primary key",
            qryEdgeLabel->destQryLabel->def.vertexLabel->metaCommon.metaName);
        return ret;
    }

    return GMERR_OK;
}

Status QryGetNextVertexByCur(
    QryStmtT *stmt, QryEdgeLabelT *qryEdgeLabel, DmVertexT *parent, DmVertexT *curr, QryRawVertexT *next)
{
    DB_POINTER5(stmt, qryEdgeLabel, parent, curr, next);

    qryEdgeLabel->type = SOURCE_VERTEX_NEXT_EDGE_ADDR;
    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    QryFilterContextT subCtx = {.mode = FETCH_BY_FILTER_EDGE,
        .defaultMode = currFilter->defaultMode,
        .configFilter = currFilter->configFilter,
        .isReuse = true,
        .stmt = stmt,
        .parent = parent,
        .filter = curr,
        .path = NULL,
        .label = NULL,
        .edgeLabel = qryEdgeLabel};
    Status ret = QryGetRawDmVertexData(&subCtx, next);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "get next vertex %s", qryEdgeLabel->destQryLabel->def.vertexLabel->metaCommon.metaName);
        return ret;
    }

    return GMERR_OK;
}

Status QryGetPrevVertexByCur(
    QryStmtT *stmt, QryEdgeLabelT *qryEdgeLabel, DmVertexT *parent, DmVertexT *curr, QryRawVertexT *prev)
{
    DB_POINTER3(stmt, qryEdgeLabel, curr);

    qryEdgeLabel->type = SOURCE_VERTEX_PREV_EDGE_ADDR;
    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    QryFilterContextT subCtx = {.mode = FETCH_BY_FILTER_EDGE,
        .defaultMode = currFilter->defaultMode,
        .configFilter = currFilter->configFilter,
        .isReuse = true,
        .stmt = stmt,
        .parent = parent,
        .filter = curr,
        .path = NULL,
        .label = NULL,
        .edgeLabel = qryEdgeLabel};
    Status ret = QryGetRawDmVertexData(&subCtx, prev);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "get prev vertex %s", qryEdgeLabel->destQryLabel->def.vertexLabel->metaCommon.metaName);
        return ret;
    }

    return GMERR_OK;
}

Status QryGetRootVertexByFilter(QryStmtT *stmt, DmSubtreeT *filter, QryLabelT *qryLabel, QryRawVertexT *child)
{
    DB_POINTER3(stmt, qryLabel, filter);

    DmVertexT *filterVertex = filter->vertex;
    QryFetchModeE fetchMode = FETCH_BY_FILTER_SCAN;
    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    QryFilterContextT subCtx = {.mode = fetchMode,
        .defaultMode = currFilter->defaultMode,
        .configFilter = currFilter->configFilter,
        .isReuse = true,
        .stmt = stmt,
        .parent = NULL,
        .filter = filterVertex,
        .path = NULL,
        .label = qryLabel,
        .edgeLabel = NULL};
    Status ret = QryGetRawDmVertexData(&subCtx, child);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "get fetch root vertex by filter %s", qryLabel->def.vertexLabel->metaCommon.metaName);
        return ret;
    }

    return GMERR_OK;
}

Status QryGetChildVertexByFilter(
    QryStmtT *stmt, DmSubtreeT *parent, DmSubtreeT *filter, QryEdgeLabelT *qryEdgeLabel, QryRawVertexT *child)
{
    DB_POINTER4(stmt, parent, qryEdgeLabel, filter);

    DmVertexT *filterVertex = filter->vertex;
    QryFetchModeE fetchMode = FETCH_BY_FILTER_MAX;
    if (filterVertex->vertexDesc->hasPkIdx == true && DmVertexPkPropeIsAllSet(filterVertex)) {
        fetchMode = FETCH_BY_FILTER_PK;
    } else {
        qryEdgeLabel->type = DEST_VERTEX_ADDR;
        fetchMode = FETCH_BY_FILTER_EDGE;
    }

    QrySubtreeFilterItemT *currFilter = ((QrySubtreeFilterDescT *)stmt->context->entry)->currFilter;
    QryFilterContextT subCtx = {.mode = fetchMode,
        .defaultMode = currFilter->defaultMode,
        .configFilter = currFilter->configFilter,
        .isReuse = !(fetchMode == FETCH_BY_FILTER_PK && currFilter->isLocationFilter),
        .stmt = stmt,
        .parent = parent->origVertex,
        .filter = (fetchMode == FETCH_BY_FILTER_PK) ? filter->vertex : parent->origVertex,
        .path = NULL,
        .label = qryEdgeLabel->destQryLabel,
        .edgeLabel = qryEdgeLabel};
    Status ret = QryGetRawDmVertexData(&subCtx, child);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(
            ret, "get fetch child vertex %s.", qryEdgeLabel->destQryLabel->def.vertexLabel->metaCommon.metaName);
        return ret;
    }

    return GMERR_OK;
}

Status QryGetVertexByChildFilter(
    QryFilterContextT *ctx, DmSubtreeT *parent, DmSubtreeT *filter, QryRawVertexT *rawVertex, bool *isByUniqueKey)
{
    Status ret = DmSetChildVertexPid(parent->origVertex, filter->vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set child vertex pid by filter %s", filter->vertex->vertexDesc->labelName);
        return ret;
    }

    uint32_t indexId;
    *isByUniqueKey = DmIsUniqueKeyFilter(filter, ctx->edgeLabel->destQryLabel->def.vertexLabel, true, &indexId);
    if (ctx->path != NULL && !ctx->path->isDefault) {
        ret = QryGetChildVertexByPath(ctx->stmt, parent, ctx->path, ctx->edgeLabel, rawVertex);
    } else if (*isByUniqueKey) {
        ret = QryGetChildVertexByUniqueKey(ctx->stmt, parent, filter, indexId, ctx->edgeLabel, rawVertex);
    } else {
        ret = QryGetChildVertexByEdge(ctx->stmt, parent, ctx->edgeLabel, rawVertex);
    }

    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        DB_LOG_ERROR(ret, "get child vertex.");
        return ret;
    }

    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
