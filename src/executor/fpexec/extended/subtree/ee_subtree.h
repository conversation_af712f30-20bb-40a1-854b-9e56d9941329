/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: header file of subtree filter
 * Author: linhuabin
 * Create: 2022-07-21
 */
#ifndef EE_SUBTREE_H
#define EE_SUBTREE_H

#include "dm_yang_subtree.h"
#include "ee_stmt.h"

#ifdef __cplusplus
extern "C" {
#endif

// 根据过滤树查询获取结果树
Status QryExecuteSubtreeFilter(QryStmtT *stmt);

#ifdef __cplusplus
}
#endif
#endif /* EE_SUBTREE_H */
