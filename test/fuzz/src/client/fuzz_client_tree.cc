#include "fuzz_client.h"
#include "schema.h"

static void InitTreeTable(GmcConnT *conn, GmcStmtT *stmt, GmcNodeT **root)
{
    const auto labelName = "superfieldVertex_byName1";
    Status ret;
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, g_superfieldLabelSchema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, root);
    EXPECT_EQ(GMERR_OK, ret);
}

static void InitTreeTable2(GmcConnT *conn, GmcStmtT *stmt, GmcNodeT **root)
{
    const auto labelName = "vertexLabelTest1";
    Status ret;
    GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, g_vertexLabelTest1Schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, root);
    EXPECT_EQ(GMERR_OK, ret);
}

// GmcNodeSetPropertyByName
// GmcNodeSetPropertyById
// GmcNodeSetPropertyByIdWithoutType
// GmcNodeGetPropertySizeByName
// GmcNodeGetPropertySizeById
// GmcNodeGetPropertyByName
// GmcNodeGetPropertyById
Status FuzzGmcNodeSetProperty(GmcConnT *conn, GmcStmtT *stmt)
{
    uint32_t g_count = 0;
    GmcNodeT *root = NULL;
    InitTreeTable(conn, stmt, &root);

    char *name = DT_SetGetString(&g_Element[g_count++], strlen("F0") + 1, MAX_STRING_LEN, "F0");
    char *strValue = DT_SetGetString(&g_Element[g_count++], strlen("fuzz_string") + 1, MAX_STRING_LEN, "fuzz_string");
    int32_t DataTypeEnum = *(int32_t *)DT_SetGetNumberEnum(
        &g_Element[g_count++], 1, data_type_table, sizeof(data_type_table) / sizeof(data_type_table[0]));
    uint32_t value = RANDOM_UINT32;
    GmcNodeSetPropertyByName(root, name, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    GmcExecute(stmt);
    GmcNodeSetPropertyByName(root, name, GMC_DATATYPE_STRING, strValue, strlen(strValue));
    GmcExecute(stmt);
    GmcNodeSetPropertyByName(root, name, DataTypeEnum, strValue, strlen(strValue));
    GmcExecute(stmt);

    GmcNodeSetPropertyById(root, RANDOM_UINT32, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    GmcExecute(stmt);
    GmcNodeSetPropertyByIdWithoutType(root, RANDOM_UINT32, &value, sizeof(uint32_t));
    GmcExecute(stmt);
    GmcNodeSetPropertyById(root, RANDOM_UINT32, GMC_DATATYPE_STRING, strValue, strlen(strValue));
    GmcExecute(stmt);
    GmcNodeSetPropertyByIdWithoutType(root, RANDOM_UINT32, strValue, strlen(strValue));
    GmcExecute(stmt);
    GmcNodeSetPropertyById(root, RANDOM_UINT32, DataTypeEnum, strValue, strlen(strValue));
    GmcExecute(stmt);
    GmcNodeSetPropertyByIdWithoutType(root, RANDOM_UINT32, strValue, strlen(strValue));
    GmcExecute(stmt);

    char buf[128];
    bool isNull;
    uint32_t size;
    GmcNodeGetPropertySizeByName(root, name, &size);
    GmcNodeGetPropertyByName(root, name, buf, sizeof(buf), &isNull);

    GmcNodeGetPropertySizeById(root, RANDOM_UINT32, &size);
    GmcNodeGetPropertyById(root, RANDOM_UINT32, buf, sizeof(buf), &isNull);
}

// GmcNodeSetSuperFieldByName
// GmcNodeSetSuperfieldById
// GmcNodeGetSuperfieldSizeByName
// GmcNodeGetSuperFieldByName
// GmcNodeGetSuperfieldById
Status FuzzGmcNodeSetSuperField(GmcConnT *conn, GmcStmtT *stmt)
{
    uint32_t g_count = 0;
    GmcNodeT *root = NULL;
    InitTreeTable(conn, stmt, &root);

    char *name = DT_SetGetString(&g_Element[g_count++], strlen("superfield0") + 1, MAX_STRING_LEN, "superfield0");
    uint32_t value = RANDOM_UINT32;
    uint32_t id = RANDOM_UINT32;
    GmcNodeSetSuperfieldByName(root, name, &value, sizeof(uint32_t));
    GmcExecute(stmt);

    GmcNodeSetSuperfieldById(root, id, &value, sizeof(uint32_t));
    GmcExecute(stmt);

    uint32_t size;
    GmcNodeGetSuperfieldSizeByName(root, name, &size);
    GmcNodeGetSuperfieldByName(root, name, &value, sizeof(uint32_t));
    GmcNodeGetSuperfieldById(root, id, &value, sizeof(uint32_t));
}

// GmcGetChildNode
// GmcNodeGetChild
// GmcGetNodeWithKeyBuf
// GmcNodeGetElementCount
// GmcNodeGetType
// GmcNodeGetName
Status FuzzGmcNodeGetChild(GmcConnT *conn, GmcStmtT *stmt)
{
    uint32_t g_count = 0;
    GmcNodeT *root = NULL;
    InitTreeTable(conn, stmt, &root);

    GmcNodeT *node = NULL;
    char *path1 = (DT_SetGetString(&g_Element[g_count++], strlen("superfield0") + 1, 128 + 1, "superfield0"));
    char *path2 = (DT_SetGetString(&g_Element[g_count++], strlen("node0") + 1, 128 + 1, "node0"));
    char *path3 = (DT_SetGetString(&g_Element[g_count++], strlen("node1") + 1, 128 + 1, "node1"));
    GmcGetChildNode(stmt, path1, &node);
    GmcNodeGetChild(root, path2, &node);
    GmcGetNodeWithKeyBuf(root, path3, RANDOM_UINT32, NULL, &node);

    uint32_t count;
    GmcNodeGetElementCount(node, &count);

    GmcTreeNodeTypeE type;
    GmcNodeGetType(node, &type);

    const char *name = NULL;
    GmcNodeGetName(node, &name);
}

// GmcNodeGetElementByIndex
// GmcNodeRemoveElementByIndex
// GmcNodeAppendElement
// GmcNodeSortElement
Status FuzzGmcNodeElementByIndex(GmcConnT *conn, GmcStmtT *stmt)
{
    uint32_t g_count = 0;
    GmcNodeT *root = NULL;
    InitTreeTable(conn, stmt, &root);

    GmcNodeT *c2 = NULL;
    GmcNodeGetChild(root, "F0", &c2);

    GmcNodeT *element = NULL;
    GmcNodeAppendElement(c2, &element);
    GmcNodeAppendElement(c2, &element);
    GmcNodeAppendElement(c2, &element);

    GmcNodeGetElementByIndex(root, RANDOM_UINT32, &element);
    GmcNodeRemoveElementByIndex(c2, RANDOM_UINT32);
    GmcNodeSortElement(root, DT_SetGetString(&g_Element[g_count++], strlen("F0") + 1, GMC_PROPERTY_NAME_MAX_LEN, "F0"),
        *(GmcOrderDirectionE *)DT_SetGetNumberRange(&g_Element[g_count++], 0, 0, 3));

    GmcExecute(stmt);
}

// GmcNodeAllocKey
Status FuzzGmcNodeAllocKey(GmcConnT *conn, GmcStmtT *stmt)
{
    uint32_t g_count = 0;
    GmcNodeT *root = NULL;
    InitTreeTable2(conn, stmt, &root);

    GmcNodeT *c2 = NULL;
    GmcNodeGetChild(root, "c2", &c2);

    GmcIndexKeyT *key = NULL;
    char *Str = DT_SetGetString(&g_Element[g_count++], strlen("T39_K0") + 1, MAX_STRING_LEN, "T39_K0");
    GmcNodeAllocKey(c2, Str, &key);
    GmcNodeFreeKey(key);
}

// GmcNodeSetKeyValue
Status FuzzGmcNodeSetKeyValue(GmcConnT *conn, GmcStmtT *stmt)
{
    uint32_t g_count = 0;
    GmcNodeT *root = NULL;
    InitTreeTable2(conn, stmt, &root);

    GmcNodeT *c2 = NULL;
    GmcNodeGetChild(root, "c2", &c2);

    GmcIndexKeyT *key = NULL;
    GmcNodeAllocKey(c2, "member_key", &key);
    GmcNodeSetKeyValue(key, RANDOM_UINT32, GMC_DATATYPE_UINT32, &RANDOM_UINT32, sizeof(uint32_t));

    GmcNodeT *element = NULL;
    GmcNodeGetElementByKey(c2, key, &element);
    GmcNodeRemoveElementByKey(c2, key);
    GmcNodeFreeKey(key);
}

// GmcSetVertexByJson
// GmcDumpVertexToJson
// GmcFreeJsonStr
Status FuzzGmcVertexJson(GmcConnT *conn, GmcStmtT *stmt)
{
    uint32_t g_count = 0;
    GmcNodeT *root = NULL;
    InitTreeTable(conn, stmt, &root);

    GmcSetVertexByJson(
        stmt, RANDOM_UINT32, DT_SetGetString(&g_Element[g_count++], strlen("fuzz_vertex") + 1, 128 + 1, "fuzz_vertex"));
    GmcExecute(stmt);
    char *json = NULL;
    GmcDumpVertexToJson(stmt, RANDOM_UINT32, &json);
    GmcFreeJsonStr(stmt, json);
}

class FuzzClientTree : public FuzzClientTest {
protected:
    virtual void SetUp()
    {
        DT_Enable_Leak_Check(0, 0);
        int32_t ret = CreateSyncConnAndStmt(&conn, &stmt);
        int reconnTimes = 0;
        while (ret != GMERR_OK && reconnTimes <= CLIENT_MAX_RECONN_TIMES) {
            usleep(1000);
            ret = CreateSyncConnAndStmt(&conn, &stmt);
            reconnTimes++;
        }
        ASSERT_EQ(GMERR_OK, ret);
    }
    virtual void TearDown()
    {
        DestroyConnectionAndStmt(conn, stmt);
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        FuzzSetUp();
        StartDbServerWithConfig(NULL);
        Status ret = GmcInit();
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        (void)GmcUnInit();
        ShutDownDbServer();
    }

protected:
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
};

TEST_F(FuzzClientTree, FuzzGmcNodeSetProperty){
    DT_FUZZ_START_CMD("FuzzGmcNodeSetProperty"){FuzzGmcNodeSetProperty(conn, stmt);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientTree, FuzzGmcNodeSetSuperField){
    DT_FUZZ_START_CMD("FuzzGmcNodeSetSuperField"){FuzzGmcNodeSetSuperField(conn, stmt);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientTree, FuzzGmcNodeGetChild){DT_FUZZ_START_CMD("FuzzGmcNodeGetChild"){FuzzGmcNodeGetChild(conn, stmt);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientTree, FuzzGmcNodeElementByIndex){
    DT_FUZZ_START_CMD("FuzzGmcNodeElementByIndex"){FuzzGmcNodeElementByIndex(conn, stmt);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientTree, FuzzGmcNodeAllocKey){DT_FUZZ_START_CMD("FuzzGmcNodeAllocKey"){FuzzGmcNodeAllocKey(conn, stmt);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientTree, FuzzGmcNodeSetKeyValue){
    DT_FUZZ_START_CMD("FuzzGmcNodeSetKeyValue"){FuzzGmcNodeSetKeyValue(conn, stmt);
}
DT_FUZZ_END()
}

TEST_F(FuzzClientTree, FuzzGmcVertexJson)
{
    DT_FUZZ_START_CMD("FuzzGmcVertexJson")
    {
        FuzzGmcVertexJson(conn, stmt);
    }
    DT_FUZZ_END()
}
