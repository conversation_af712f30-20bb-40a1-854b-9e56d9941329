/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: ut file for data model edge and edge topo
 * Author:
 * Create: 2020-9-20
 */

#include "gtest/gtest.h"

#include "dm_data_prop.h"
#include "common_init.h"
#include "db_dynmem_algo.h"
#include "db_mem_context.h"
#include "dm_data_topo.h"
#include "dm_data_topo_seri.h"

#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <string.h>
#include "securec.h"
#include "ut_dm_common.h"

#define EDGE_DESERI_INCORRECT_BUFFER_TEST_LOOP 100
DbMemCtxT *dyAlgoCtxForEdge = NULL;
DbMemCtxT *edgeShmemCtx = NULL;
class ut_dm_edge : public testing::Test {
protected:
    virtual void SetUp()
    {}

    virtual void TearDown()
    {}

    static void SetUpTestCase()
    {
        CommonInit();
        DbMemCtxArgsT args = {0};
        args.instanceId = GET_INSTANCE_ID;
        dyAlgoCtxForEdge =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        DbMemCtxSwitchTo(dyAlgoCtxForEdge);
        edgeShmemCtx = (DbMemCtxT *)DbInitTopShmemCtx("top shmem context!", &args);
    }

    static void TearDownTestCase()
    {
        DbDeleteDynMemCtx(dyAlgoCtxForEdge);
        DbDeleteShmemCtx((DbMemCtxT *)edgeShmemCtx);
        CommonRelease();
    }
};

VertexLabelCommonInfoT *getVertexCommonInfoForEdgrUt()
{
    VertexLabelCommonInfoT *commonInfo =
        (VertexLabelCommonInfoT *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, sizeof(VertexLabelCommonInfoT));
    (void)memset_s(commonInfo, sizeof(VertexLabelCommonInfoT), 0x00, sizeof(VertexLabelCommonInfoT));
    commonInfo->edgeLabelNum = 0;
    commonInfo->refCount = 1;
    commonInfo->labelLatchShmAddr = DB_INVALID_SHMPTR;
    return commonInfo;
}

/*
 * 创建一个完整 filter，返回其指针
 */
DmIndexKeyT *getFilterForTest()
{
    DmPropertySchemaT *property = AllocProperties(dyAlgoCtxForEdge, 3);
    property[0].isValid = true;
    property[0].dataType = DB_DATATYPE_UINT16;
    property[0].nameLen = DM_STR_LEN("c1");
    property[0].name = (char *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, property[0].nameLen);
    strcpy(property[0].name, "c1");
    property[0].isFixed = true;
    property[0].size = 2;
    property[0].propeId = 0;

    property[1].isValid = true;
    property[1].dataType = DB_DATATYPE_UINT64;
    property[1].nameLen = DM_STR_LEN("c1");
    property[1].name = (char *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, property[1].nameLen);
    strcpy(property[1].name, "c2");
    property[1].isFixed = true;
    property[1].size = 8;
    property[1].propeId = 1;

    DmSchemaT *schema = (DmSchemaT *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, sizeof(DmSchemaT));
    memset_s(schema, sizeof(DmSchemaT), 0, sizeof(DmSchemaT));
    schema->isFlat = true;
    schema->propeNum = 2;
    schema->properties = property;

    DmVlIndexLabelT *index = (DmVlIndexLabelT *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, sizeof(DmVlIndexLabelT));
    memset_s(index, sizeof(DmVlIndexLabelT), 0, sizeof(DmVlIndexLabelT));
    index->indexNameLen = DM_STR_LEN("index1");
    index->indexName = (char *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, index->indexNameLen);
    strcpy(index->indexName, "index1");
    index->idxLabelBase.indexId = 1;
    index->idxLabelBase.dbId = 1;
    index->idxLabelBase.indexConstraint = PRIMARY;
    index->idxLabelBase.indexType = HASH_INDEX;
    index->idxLabelBase.srcLabelId = 1;
    index->properties = property;
    index->propIds = AllocPropIds(dyAlgoCtxForEdge, 1);
    index->propIds[0] = 0;

    index->propeNum = 1;
    index->nullInfoBytes = DmGetNullInfoBytes(index->propeNum);
    index->maxKeyLen = DmGetMaxKeyBufLen4QE(index);

    DmVertexLabelT *vertexLabel = NULL;
    DmUtCreateEmptyVL(dyAlgoCtxForEdge, &vertexLabel);
    vertexLabel->metaCommon.dbId = 1;
    vertexLabel->metaCommon.metaId = 1;
    vertexLabel->metaCommon.metaName = (char *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, strlen("zhang") + 1);
    strcpy(vertexLabel->metaCommon.metaName, "zhang");
    vertexLabel->metaVertexLabel->pkIndex = index;
    vertexLabel->metaVertexLabel->secIndexNum = 0;
    vertexLabel->metaVertexLabel->secIndexes = NULL;
    vertexLabel->metaVertexLabel->schema = schema;
    vertexLabel->commonInfo = getVertexCommonInfoForEdgrUt();

    DmValueT *propertyValues = (DmValueT *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, sizeof(DmValueT));
    propertyValues[0].type = DB_DATATYPE_UINT16;
    propertyValues[0].value.ushortValue = 300;

    DmVertexLabelT *copyVertexLabel = NULL;
    EXPECT_EQ(GMERR_OK, CopyVertexLabel(edgeShmemCtx, vertexLabel, &copyVertexLabel));

    DmIndexKeyT *filter = NULL;
    DmCreateIndexKeyByNameWithMemCtx(dyAlgoCtxForEdge, copyVertexLabel, "index1", propertyValues, 1, &filter);
    DestroyShmVertexLabel(edgeShmemCtx, copyVertexLabel);
    return filter;
}

DmEdgeLabelT *GetEdgeLabelForTest()
{
    DmEdgeLabelT *edgeLabel = (DmEdgeLabelT *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, sizeof(DmEdgeLabelT));
    memset_s(edgeLabel, sizeof(DmEdgeLabelT), 0, sizeof(DmEdgeLabelT));
    edgeLabel->metaCommon.dbId = 0;
    edgeLabel->destVertexLabelId = 2;
    edgeLabel->metaCommon.metaId = 0;
    edgeLabel->isLightEdge = true;
    edgeLabel->sourceVertexLabelId = 1;
    return edgeLabel;
}

// 创建空的edge
TEST_F(ut_dm_edge, create_edge_correct_with_memCtx)
{
    DmEdgeT *edge = NULL;
    DmEdgeLabelT *edgeLabel = GetEdgeLabelForTest();
    ASSERT_EQ(GMERR_OK, DmCreateEmptyEdgeWithMemCtx(dyAlgoCtxForEdge, edgeLabel, &edge));
    DmDestroyEdge(edge);
}

// 设置和获取edge中的srcKey
TEST_F(ut_dm_edge, set_and_get_srcKey_correct)
{
    DmEdgeT *edge = NULL;
    DmEdgeLabelT *edgeLabel = GetEdgeLabelForTest();
    ASSERT_EQ(GMERR_OK, DmCreateEmptyEdgeWithMemCtx(dyAlgoCtxForEdge, edgeLabel, &edge));  // 先创建edge
    DmIndexKeyT *srcKey = getFilterForTest();
    ASSERT_EQ(GMERR_OK, DmSetSrcVertexKey(edge, srcKey));  // set srcKey

    DmIndexKeyT *filterOut;
    ASSERT_EQ(GMERR_OK, DmGetSrcVertexKey(edge, &filterOut));

    uint32_t srcKeySize;
    uint8_t *srcKeyBuf = NULL;
    DmIndexKeyGetKeyBuf(srcKey, &srcKeyBuf, &srcKeySize);
    uint32_t outKeySize;
    uint8_t *outKeyBuf = NULL;
    DmIndexKeyGetKeyBuf((DmIndexKeyT *)filterOut, &outKeyBuf, &outKeySize);
    ASSERT_EQ(srcKeySize, outKeySize);
    DmDestroyEdge(edge);
}

// 设置和获取edge中的destKey
TEST_F(ut_dm_edge, set_and_get_destKey_correct)
{
    DmEdgeT *edge = NULL;
    DmEdgeLabelT *edgeLabel = GetEdgeLabelForTest();
    ASSERT_EQ(GMERR_OK, DmCreateEmptyEdgeWithMemCtx(dyAlgoCtxForEdge, edgeLabel, &edge));
    DmIndexKeyT *destKey = getFilterForTest();
    ASSERT_EQ(GMERR_OK, DmSetDestVertexKey(edge, destKey));  // 先set destKey 属性

    DmIndexKeyT *filterOut;
    ASSERT_EQ(GMERR_OK, DmGetDestVertexKey(edge, &filterOut));
    uint32_t srcKeySize;
    uint8_t *srcKeyBuf = NULL;
    DmIndexKeyGetKeyBuf(destKey, &srcKeyBuf, &srcKeySize);
    uint32_t outKeySize;
    uint8_t *outKeyBuf = NULL;
    DmIndexKeyGetKeyBuf((DmIndexKeyT *)filterOut, &outKeyBuf, &outKeySize);
    ASSERT_EQ(srcKeySize, outKeySize);
    DmDestroyEdge(edge);
}

// 获取空的edge srcKey
TEST_F(ut_dm_edge, get_srcKey_error_with_null_edge_srcKey)
{
    DmEdgeT *edge = NULL;
    DmEdgeLabelT *edgeLabel = GetEdgeLabelForTest();
    ASSERT_EQ(GMERR_OK, DmCreateEmptyEdgeWithMemCtx(dyAlgoCtxForEdge, edgeLabel, &edge));
    DmIndexKeyT *filterOut = NULL;
    ASSERT_EQ(GMERR_DATA_EXCEPTION, DmGetSrcVertexKey(edge, &filterOut));
}

// 获取空的edge destKey
TEST_F(ut_dm_edge, get_destKey_error_with_null_edge_destKey)
{
    DmEdgeT *edge = NULL;
    DmEdgeLabelT *edgeLabel = GetEdgeLabelForTest();
    ASSERT_EQ(GMERR_OK, DmCreateEmptyEdgeWithMemCtx(dyAlgoCtxForEdge, edgeLabel, &edge));  // 创建 edge
    DmIndexKeyT *destKey = NULL;
    ASSERT_EQ(GMERR_DATA_EXCEPTION, DmGetDestVertexKey(edge, &destKey));
}

// 序列化edge
TEST_F(ut_dm_edge, serialize_edge_correct)
{
    DmIndexKeyT *srcKey = getFilterForTest();
    DmIndexKeyT *destKey = getFilterForTest();
    DmEdgeT *edge = NULL;
    DmEdgeLabelT *edgeLabel = GetEdgeLabelForTest();
    ASSERT_EQ(GMERR_OK, DmCreateEmptyEdgeWithMemCtx(dyAlgoCtxForEdge, edgeLabel, &edge));  // 创建 edge
    ASSERT_EQ(GMERR_OK, DmSetSrcVertexKey(edge, srcKey));                                  // set srcKey
    ASSERT_EQ(GMERR_OK, DmSetDestVertexKey(edge, destKey));                                // set destKey
    // 序列化edge
    uint8_t *buf = NULL;
    uint32_t length = 0;
    ASSERT_EQ(GMERR_OK, DmSerializeEdge(edge, &buf, &length));
    uint32_t length1 = DmEdgeGetSeriBufLength(edge);
    ASSERT_EQ(length1, length);
    DmDestroyEdge(edge);
}

// 序列化edge到已经申请好的buf
TEST_F(ut_dm_edge, serialize_edge_to_invoker_buf_correct)
{
    DmIndexKeyT *srcKey = getFilterForTest();
    DmIndexKeyT *destKey = getFilterForTest();
    DmEdgeT *edge = NULL;
    DmEdgeLabelT *edgeLabel = GetEdgeLabelForTest();
    ASSERT_EQ(GMERR_OK, DmCreateEmptyEdgeWithMemCtx(dyAlgoCtxForEdge, edgeLabel, &edge));  // 创建 edge
    ASSERT_EQ(GMERR_OK, DmSetSrcVertexKey(edge, srcKey));                                  // set srcKey
    ASSERT_EQ(GMERR_OK, DmSetDestVertexKey(edge, destKey));                                // set destKey
                                                                                           // 序列化edge
    uint32_t length = DmEdgeGetSeriBufLength(edge);  // 求得 应申请的 buf大小
    uint8_t *buf = (uint8_t *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, sizeof(length));
    ASSERT_EQ(GMERR_OK, DmSerializeEdge2InvokerBuf(edge, length, buf));
    DmDestroyEdge(edge);
}

// 反序列化edge
TEST_F(ut_dm_edge, deserialize_edge_correct)
{
    DmIndexKeyT *srcKey = getFilterForTest();
    DmIndexKeyT *destKey = getFilterForTest();
    DmEdgeT *edge = NULL;
    DmEdgeLabelT *edgeLabel = (DmEdgeLabelT *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, sizeof(DmEdgeLabelT));
    memset_s(edgeLabel, sizeof(DmEdgeLabelT), 0, sizeof(DmEdgeLabelT));
    ASSERT_EQ(GMERR_OK, DmCreateEmptyEdgeWithMemCtx(dyAlgoCtxForEdge, edgeLabel, &edge));  // 创建 edge
    ASSERT_EQ(GMERR_OK, DmSetSrcVertexKey(edge, srcKey));                                  // set srcKey
    ASSERT_EQ(GMERR_OK, DmSetDestVertexKey(edge, destKey));                                // set destKey

    uint8_t *buf = NULL;
    uint32_t length = 0;
    ASSERT_EQ(GMERR_OK, DmSerializeEdge(edge, &buf, &length));
    DmEdgeT *edgeOut = NULL;
    ASSERT_EQ(GMERR_OK, DmDeSerializeEdgeWithMemCtx(dyAlgoCtxForEdge, buf, length, edgeLabel, &edgeOut));

    DmIndexKeyT *srcOut = NULL;
    ASSERT_EQ(GMERR_OK, DmGetSrcVertexKey(edgeOut, &srcOut));
    DmIndexKeyT *destOut = NULL;
    ASSERT_EQ(GMERR_OK, DmGetDestVertexKey(edgeOut, &destOut));

    uint32_t srcKeySize;
    uint8_t *srcKeyBuf;
    DmIndexKeyGetKeyBuf(srcKey, &srcKeyBuf, &srcKeySize);
    uint32_t outKeySize;
    uint8_t *outKeyBuf;
    DmIndexKeyGetKeyBuf((DmIndexKeyT *)srcOut, &outKeyBuf, &outKeySize);
    ASSERT_EQ(srcKeySize, outKeySize);
    ASSERT_EQ(0, memcmp(srcKeyBuf, outKeyBuf, srcKeySize));
    uint32_t destKeySize;
    uint8_t *destKeyBuf;
    DmIndexKeyGetKeyBuf(destKey, &destKeyBuf, &destKeySize);
    DmIndexKeyGetKeyBuf((DmIndexKeyT *)destOut, &outKeyBuf, &outKeySize);
    ASSERT_EQ(destKeySize, outKeySize);
    ASSERT_EQ(0, memcmp(destKeyBuf, outKeyBuf, destKeySize));

    DmDestroyEdge(edge);
    DmDestroyEdge(edgeOut);
}

// 从SerializeEdge2InvokerBuf反序列化成功
TEST_F(ut_dm_edge, deserialize_edge_correct2)
{
    DmIndexKeyT *srcKey = getFilterForTest();
    DmIndexKeyT *destKey = getFilterForTest();
    DmEdgeT *edge = NULL;
    DmEdgeLabelT *edgeLabel = (DmEdgeLabelT *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, sizeof(DmEdgeLabelT));
    memset_s(edgeLabel, sizeof(DmEdgeLabelT), 0, sizeof(DmEdgeLabelT));
    ASSERT_EQ(GMERR_OK, DmCreateEmptyEdgeWithMemCtx(dyAlgoCtxForEdge, edgeLabel, &edge));  // 创建 edge
    ASSERT_EQ(GMERR_OK, DmSetSrcVertexKey(edge, srcKey));                                  // set srcKey
    ASSERT_EQ(GMERR_OK, DmSetDestVertexKey(edge, destKey));                                // set destKey

    uint32_t length = DmEdgeGetSeriBufLength(edge);                        // 求得 应申请的 buf大小
    uint8_t *buf = (uint8_t *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, length);  // InvokerBuf 序列化
    ASSERT_EQ(GMERR_OK, DmSerializeEdge2InvokerBuf(edge, length, buf));

    DmEdgeT *edgeOut = NULL;
    ASSERT_EQ(GMERR_OK, DmDeSerializeEdgeWithMemCtx(dyAlgoCtxForEdge, buf, length, edgeLabel, &edgeOut));

    DmIndexKeyT *srcOut = NULL;
    ASSERT_EQ(GMERR_OK, DmGetSrcVertexKey(edgeOut, &srcOut));
    DmIndexKeyT *destOut = NULL;
    ASSERT_EQ(GMERR_OK, DmGetDestVertexKey(edgeOut, &destOut));

    uint32_t srcKeySize;
    uint8_t *srcKeyBuf;
    DmIndexKeyGetKeyBuf(srcKey, &srcKeyBuf, &srcKeySize);
    uint32_t outKeySize;
    uint8_t *outKeyBuf;
    DmIndexKeyGetKeyBuf((DmIndexKeyT *)srcOut, &outKeyBuf, &outKeySize);
    ASSERT_EQ(srcKeySize, outKeySize);
    ASSERT_EQ(0, memcmp(srcKeyBuf, outKeyBuf, srcKeySize));
    uint32_t destKeySize;
    uint8_t *destKeyBuf;
    DmIndexKeyGetKeyBuf(destKey, &destKeyBuf, &destKeySize);
    DmIndexKeyGetKeyBuf((DmIndexKeyT *)destOut, &outKeyBuf, &outKeySize);
    ASSERT_EQ(destKeySize, outKeySize);
    ASSERT_EQ(0, memcmp(destKeyBuf, outKeyBuf, destKeySize));

    DmDestroyEdge(edge);
    DmDestroyEdge(edgeOut);
}

// 校验edge buf反序列化逻辑正确
TEST_F(ut_dm_edge, deserialize_edge_incorrect_buffer)
{
    DmEdgeLabelT *edgeLabel = (DmEdgeLabelT *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, sizeof(DmEdgeLabelT));
    memset_s(edgeLabel, sizeof(DmEdgeLabelT), 0, sizeof(DmEdgeLabelT));
    DmEdgeT *edge = NULL;
    uint32_t testCount = 0;
    uint32_t len;
    uint8_t *buf;
    uint8_t *bufCursor;

    while (testCount < EDGE_DESERI_INCORRECT_BUFFER_TEST_LOOP) {
        len = (uint8_t)random();
        buf = (uint8_t *)DbDynMemCtxAlloc(dyAlgoCtxForEdge, len);
        ASSERT_NE((void *)NULL, buf);
        bufCursor = buf;
        for (uint32_t i = 0; i < len; i++) {
            *bufCursor = (uint8_t)random();
            bufCursor++;
        }
        (void)DmDeSerializeEdgeWithMemCtx(dyAlgoCtxForEdge, buf, len, edgeLabel, &edge);  // Doesn't care what ret is
        DbDynMemCtxFree(dyAlgoCtxForEdge, buf);
        DmDestroyEdge(edge);
        edge = NULL;
        buf = NULL;
        testCount++;
    }
    DbDynMemCtxFree(dyAlgoCtxForEdge, edgeLabel);
}

// 删除edge
TEST_F(ut_dm_edge, DestroyEdge)
{

    DmIndexKeyT *srcKey = getFilterForTest();
    DmIndexKeyT *destKey = getFilterForTest();
    DmEdgeT *edge = NULL;

    DbMemCtxT *beginMemCtx = dyAlgoCtxForEdge;
    uint32_t beginUsedMemSize = beginMemCtx->totalAllocSize;

    DmEdgeLabelT *edgeLabel = (DmEdgeLabelT *)DbDynMemCtxAlloc(beginMemCtx, sizeof(DmEdgeLabelT));
    memset_s(edgeLabel, sizeof(DmEdgeLabelT), 0, sizeof(DmEdgeLabelT));
    edgeLabel->memCtx = beginMemCtx;
    ASSERT_EQ(GMERR_OK, DmCreateEmptyEdgeWithMemCtx(beginMemCtx, edgeLabel, &edge));  // 创建 edge
    ASSERT_EQ(GMERR_OK, DmSetSrcVertexKey(edge, srcKey));                             // set srcKey
    ASSERT_EQ(GMERR_OK, DmSetDestVertexKey(edge, destKey));                           // set destKey
    DmDestroyEdge(edge);
    DmEdgeLabelDestroy(edgeLabel);

    DbMemCtxT *endUsedMemCtx = beginMemCtx;
    uint32_t endUsedMemSize = endUsedMemCtx->totalAllocSize;
    ASSERT_EQ((uint32_t)0, endUsedMemSize - beginUsedMemSize);
}
