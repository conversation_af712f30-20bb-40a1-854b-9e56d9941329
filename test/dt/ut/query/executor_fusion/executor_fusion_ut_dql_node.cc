/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: datalog ut
 * Author: GMDBv5 EE Team
 * Create: 2022-08-15
 */

#include "executor_fusion_ut_dql_base.h"

// 用例描述: 创建删除org表结果请求
// 预置条件: 内存上下文memCtx分配成功
// 输入数据: 无
// 执行步骤: 略
// 预期结果: 1. 返回GMERR_OK
//          2. DropTableResultStmtT非空
TEST_F(UtExecutorFusionDQL, NewDropTableResultStmt)
{
    DropTableResultStmtT *resultStmt;
    Status ret = NewDropTableResultStmt(memCtx, &resultStmt);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(T_DROP_TABLE_STMT, resultStmt->node.tag);

    DbDynMemCtxFree(memCtx, resultStmt);
}

// 用例描述: TempTable相关构造函数ut测试
// 预置条件: 内存上下文memCtx分配成功
// 输入数据: 无
// 执行步骤: 略
// 预期结果: 1. 返回GMERR_OK
//          2. Stmt字段设置正确
TEST_F(UtExecutorFusionDQL, NewTempTableStmt)
{
    DeltaTableT *deltaTable;
    CreateTableCmdT cmd;
    cmd.vertexLabel = g_labelA;
    Status ret = NewTempTable(&cmd, memCtx, &deltaTable);
    EXPECT_EQ(GMERR_OK, ret);

    DropTempTableStmtT *reqStmt;
    ret = NewDropTempTableStmt(memCtx, deltaTable, &reqStmt);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(T_DROP_TEMP_TABLE_STMT, reqStmt->node.tag);

    DropTempTableResultStmtT *resStmt;
    ret = NewDropTempTableResultStmt(memCtx, &resStmt);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(T_DROP_TEMP_TABLE_STMT, resStmt->node.tag);

    DbDynMemCtxFree(memCtx, reqStmt);
    DbDynMemCtxFree(memCtx, resStmt);
}
