/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: TS background drop table worker test
 * Author: zhangtao
 * Create: 2024-01-11
 */

#include "query_ut_base.h"
#include "ee_background_schedule.h"

static DbMemCtxT *g_ts_memCtx = NULL;

class TsUtBgDropTask : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        BaseInit();
        DbMemCtxArgsT args = {0};
        g_ts_memCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "dynamic memctx", &args);
        EXPECT_NE(nullptr, g_ts_memCtx);
        // 单独启动LCM后台线程
        EXPECT_EQ(GMERR_OK, TsLcmBgWorkerInit(g_ts_memCtx, NULL));
    }
    static void TearDownTestCase()
    {
        DbDeleteDynMemCtx(g_ts_memCtx);
        // TS后台线程关闭由线程框架管理，此处无需手动关闭
        BaseUninit();
    }
};

// 测试：模拟EE发送10次Drop Table请求给后台线程，且每次请求间Sleep 100毫秒
// 预期：10次Drop请求全部按序执行成功，Sleep的目的是模拟常规大多数非频繁Drop Table的场景
TEST_F(TsUtBgDropTask, LcmDropTableWithSleep)
{
    Status ret = GMERR_OK;
    uint32_t maxVertexLabelId = 10;
    for (uint32_t labelId = 1; labelId <= maxVertexLabelId; ++labelId) {
        ret = TsLcmBgDropTblAddNotify(labelId, NULL);
        EXPECT_EQ(ret, GMERR_OK);
        DbSleep(10);
    }
    // 需等待后台线程执行完毕再结束当前函数，否则可能后台线程正在做任务就被迫终止了
    DbSleep(1000);
}

// 测试：模拟EE频繁的发送10次Drop Table请求给后台线程，各请求间无Sleep
// 预期：10次Drop请求均被后台线程成功处理，仅当上一次Drop请求完成后，后续的Drop请求才会被处理，否则线程处于阻塞状态
TEST_F(TsUtBgDropTask, LcmDropTableWithoutSleep)
{
    Status ret = GMERR_OK;
    uint32_t maxVertexLabelId = 10;
    for (uint32_t labelId = 1; labelId <= maxVertexLabelId; ++labelId) {
        ret = TsLcmBgDropTblAddNotify(labelId, NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }
    // 需等待后台线程执行完毕再结束当前函数，否则可能后台线程正在做任务就被迫终止了
    DbSleep(1000);
}
