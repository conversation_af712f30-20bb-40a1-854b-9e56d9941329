project(GMDB_PERSISTENCE_UT)
ADD_DEFINITIONS(-DFEATURE_PERSISTENCE)
ADD_DEFINITIONS(-DFEATURE_EDGELABEL)


# 生成可执行文件
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR})
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/backup BACKUP_UT_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/btree BTREE_UT_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/reliability UT_RELIABILITY_FRAMEWORK)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/crash UT_CRASH_FRAMEWORK)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/bufferpool BUFFERPOOL_UT_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/space_reliability SPACE_UT_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/undo UNDO_UT_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/heap HEAP_UT_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/fixed_heap FIXED_HEAP_UT_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/durable_memdata_space DM_SPACE_UT_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/disk_err DISK_ERROR_UT_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/instance INSTANCE_UT_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/base_module BASE_MODULE_UT_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/../query/common/ QUERY_COMMON)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/../datamodel/common/ DATAMODEL_COMMON)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/../storage/common/ STORAGE_COMMON)
list(REMOVE_ITEM UT_CRASH_FRAMEWORK "${CMAKE_CURRENT_SOURCE_DIR}/crash/cstore_crash_ut.cc")

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/reliability)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/btree)
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

set(PERSIST_BUFFERPOOL_UT_LIST
    persistence_common.cc
    space_common.cc
    ctrlfile_ut.cc
    heapLob_ut.cc
    heapLob_ut_common.cc
    #undo_recovery_ut.cc
    # ctrlFile_redo_ut.cc
    recovery_ut.cc
    heap_util.cc
    heap_optis_trx_ut.cc
    heap_optis_fix_ut.cc
    heap_reliability_ut.cc
    heap_fix_reliability_ut.cc
    lfs_reliability_ut.cc
    space_concurrency_ut.cc
    systable_ut.cc
    space_user_defined_ut.cc
    ctrl_expand_ut.cc
    dir_switch_ut.cc
    persistence_executor_ut.cc
)

set(PERSIST_DURABLE_MEMDATA_UT_LIST
    persistence_common.cc
)

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/../common_test_include COMMON_TEST_INCLUDE_LIST)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../common_test_include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../query)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../query/common)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../datamodel/common)

if (DURABLE_MEMDATA)
    # durable_memdata
    add_executable(ut_persistence ${PERSIST_DURABLE_MEMDATA_UT_LIST} ${FIXED_HEAP_UT_LIST} ${DM_SPACE_UT_LIST})
else()
    # bufferpool
    add_executable(ut_persistence ${PERSIST_BUFFERPOOL_UT_LIST} ${COMMON_TEST_INCLUDE_LIST}
        ${UT_RELIABILITY_FRAMEWORK} ${BTREE_UT_LIST} ${BUFFERPOOL_UT_LIST} ${SPACE_UT_LIST} ${UNDO_UT_LIST} ${HEAP_UT_LIST} ${BASE_MODULE_UT_LIST}
        ${UT_CRASH_FRAMEWORK} ${INSTANCE_UT_LIST} 
        ${BACKUP_UT_LIST}
        ${DISK_ERROR_UT_LIST}
        ${QUERY_COMMON} 
        ${DATAMODEL_COMMON}  # 系统表依赖
        ${STORAGE_COMMON} 
    )
endif()

if (SINGLE_SO)
    target_link_libraries(ut_persistence securec stub gmdb)
else()
    target_link_libraries(ut_persistence securec stub gmcommon gmmemdata gmbufferpool gmfastpath gmpersistence gmtrm)
endif()

