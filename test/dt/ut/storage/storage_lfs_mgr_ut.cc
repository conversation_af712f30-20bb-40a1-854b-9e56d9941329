
#include <limits.h>
#include <stdlib.h>
#include "gtest/gtest.h"
#include "adpt_types.h"
#include "adpt_spinlock.h"
#include "se_capacity_def_inner.h"
#include "se_lfsmgr.h"
#include "gmc_errno.h"
#include "stub.h"
#include "db_common_init.h"
#include "common_init.h"
#include "db_internal_error.h"
#include "storage_ut_common.h"
#ifdef __cplusplus
extern "C" {
#endif

LfsMgrT g_lfsMgr;
PageMgrT g_pageMgr;
ShmemPtrT g_dataMemMgrShm;

#define G_PAGE_NUM 1024
uint8_t *g_pageAddr[G_PAGE_NUM] = {NULL};

extern "C" {
typedef struct FsmHead {
    uint32_t fsmCnt;  // in persistent, only fsm page0 need to record this
    uint32_t fsmFileId;
    uint32_t fsmPageIdx;
    uint32_t pageSize;
    uint32_t maxSlotPerFsmPage;
    uint16_t usedSlotCnt;
    uint16_t reserve;
} FsmHeadT;

typedef struct FsmPageHead {
    PageHeadT pageHead;
    FsmHeadT fsmHead;
    FsmListNodeT fsmListNode[];
} FsmPageHeadT;
StatusInter LfsGetNewFsmPage(MdMgrT *md, LfsMgrT *mgr, FsmPageHeadT **page, DbInstanceHdT dbInstance);

StatusInter LfsGetFsmPage(PageMgrT *pageMgr, const LfsMgrT *mgr, uint32_t fsmPageIdx, uint8_t **page, bool isWrite);

StatusInter LfsGetFsmPageByCache(LfsMgrT *mgr, uint32_t blockId, uint8_t **page);

StatusInter SeFreePage(PageMgrT *pageMgr, FreePageParamT *freePageParam);

StatusInter LfsGetNewUpperPage(LfsOpInfoT *lfsOpInfo, PageAddrT *pageAddr, DbInstanceHdT dbInstance);

StatusInter LfsGetOwnerListId(const LfsMgrT *mgr, uint32_t availSize, int32_t *listId);

StatusInter LfsGetAllocListId(const LfsMgrT *mgr, uint32_t requireSize, int32_t *listId);

StatusInter LfsGetAndCacheUpperPage(LfsOpInfoT *lfsOpInfo, PageAddrInfoT *addrInfo);

void LfsMgrClearFsmCache(LfsMgrT *mgr);
}
class UtStorageLabelSpace : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        int32_t ret = CommonInit();
        if (ret != DB_SUCCESS) {
            printf("ret = %d\n", ret);
            ASSERT_EQ(0, 1);
        }
    };

    static void TearDownTestCase()
    {
        CommonRelease();
    };
};

StatusInter MockLfsGetNewFsmPage(MdMgrT *md, LfsMgrT *mgr, FsmPageHeadT **page, DbInstanceHdT dbInstance)
{
    uint32_t fsmPageIdx = mgr->fsmPageCnt;
    if (fsmPageIdx < G_PAGE_NUM) {
        if (g_pageAddr[fsmPageIdx] == NULL) {
            g_pageAddr[fsmPageIdx] = (uint8_t *)malloc(32 * 1024);
            *page = (FsmPageHeadT *)g_pageAddr[fsmPageIdx];
            return STATUS_OK_INTER;
        }
    }
    *page = NULL;
    return NO_DATA_NULL_POINTER;
}

StatusInter MockLfsGetFsmPage(MdMgrT *mdMgr, const LfsMgrT *mgr, uint32_t fsmPageIdx, uint8_t **page, bool isWrite)
{
    if (fsmPageIdx < G_PAGE_NUM) {
        if (g_pageAddr[fsmPageIdx] == NULL) {
            return NO_DATA_NULL_POINTER;
        }
        *page = g_pageAddr[fsmPageIdx];
        return STATUS_OK_INTER;
    }
    *page = NULL;
    return NO_DATA_NULL_POINTER;
}

StatusInter MockLfsGetFsmPageByCache(MdMgrT *md, LfsMgrT *mgr, uint32_t fsmPageIdx, FsmPageHeadT **page, bool isWrite)
{
    return MockLfsGetFsmPage(md, mgr, fsmPageIdx, (uint8_t **)page, isWrite);
}

StatusInter MockMdFreePage(MdMgrT *mdMgr, FreePageParamT *freePageParam)
{
    FsmPageHeadT *page = NULL;
    StatusInter ret = MdGetPage(mdMgr, freePageParam->addr, (uint8_t **)&page, ENTER_PAGE_NORMAL, false);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    if (page->fsmHead.fsmPageIdx < G_PAGE_NUM) {
        if (g_pageAddr[page->fsmHead.fsmPageIdx] != NULL) {
            free(g_pageAddr[page->fsmHead.fsmPageIdx]);
            g_pageAddr[page->fsmHead.fsmPageIdx] = NULL;
        }
    }

    return STATUS_OK_INTER;
}

StatusInter MockLfsGetNewMemPage(LfsOpInfoT *lfsOpInfo, PageAddrT *pageAddr)
{
    pageAddr->pageId = 0;
    return STATUS_OK_INTER;
}

void MockLfsMgrClearFsmCache(MdMgrT *md, LfsMgrT *mgr)
{
    for (uint32_t blockId = 0; blockId < G_PAGE_NUM; blockId++) {
        if (blockId < G_PAGE_NUM) {
            if (g_pageAddr[blockId] != NULL) {
                free(g_pageAddr[blockId]);
                g_pageAddr[blockId] = NULL;
            }
        }
    }
    return;
}

extern StatusInter MockLfsGetAndCacheUpperPage(LfsOpInfoT *lfsOpInfo, PageAddrInfoT *addrInfo)
{
    return STATUS_OK_INTER;
}

#ifdef __cplusplus
}
#endif

static void InitLfsMgr(HpPageTypeE pageType)
{
    LfsCfgT cfg = {.fsmFileId = 0,
        .dataFileId = 0,
        .shmArrayMemCtxId = 3070u,
        .labelId = 1024,
        .pageSize = 32 * 1024,
        .pageType = pageType,
        .needLock = true,
        .isUseRsm = false,
        .reserveSize = 1024,
        .availSize = 31 * 1024,
        .tableSpaceIndex = 0};

    int32_t stub_id1 = setStubC((void *)LfsGetNewFsmPage, (void *)MockLfsGetNewFsmPage);
    ASSERT_TRUE(stub_id1 > 0);
    int32_t stub_id2 = setStubC((void *)LfsGetFsmPage, (void *)MockLfsGetFsmPage);
    ASSERT_TRUE(stub_id2 > 0);
    int32_t stub_id3 = setStubC((void *)LfsGetNewUpperPage, (void *)MockLfsGetNewMemPage);
    ASSERT_TRUE(stub_id3 > 0);
    int32_t stub_id4 = setStubC((void *)LfsGetFsmPageByCache, (void *)MockLfsGetFsmPageByCache);
    ASSERT_TRUE(stub_id4 > 0);
    int32_t stub_id5 = setStubC((void *)LfsGetAndCacheUpperPage, (void *)MockLfsGetAndCacheUpperPage);
    ASSERT_TRUE(stub_id5 > 0);
    int32_t stub_id6 = setStubC((void *)MdFreePage, (void *)MockMdFreePage);
    ASSERT_TRUE(stub_id6 > 0);
    int32_t stub_id7 = setStubC((void *)LfsMgrClearFsmCache, (void *)MockLfsMgrClearFsmCache);
    ASSERT_TRUE(stub_id7 > 0);
    LfsMgrInit(&g_lfsMgr, &cfg);
}

static void DestroyFsm()
{
    int32_t ret = LfsMgrDestroy((MdMgrT *)(void *)&g_pageMgr, &g_lfsMgr, NULL);
    EXPECT_EQ(0, ret);
}

TEST_F(UtStorageLabelSpace, initLfsMgr)
{
    InitLfsMgr(HEAP_VAR_LEN_ROW_PAGE);
    DestroyFsm();
}

static StatusInter LfsGetAvailBlockWrapper(
    LfsMgrT *mgr, uint32_t requireSize, bool *isNewBlock, PageAddrInfoT *addrInfo)
{
    StatusInter ret;
    LfsOpInfoT lfsOpInfo = {.mgr = mgr,
        .md = (MdMgrT *)(void *)&g_pageMgr,
        .rsmUndoRec = NULL,
        .useCachePage = true,
        .needReleasePage = false};
    lfsOpInfo.useCachePage = false;
    ret = LfsGetAvailBlock(&lfsOpInfo, requireSize, isNewBlock, addrInfo, NULL);
    return ret;
}

static void SearchFsm()
{
    bool isNewBlock;
    PageAddrInfoT addrInfo;
    int32_t ret = LfsGetAvailBlockWrapper(&g_lfsMgr, 1024, &isNewBlock, &addrInfo);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(0, (int32_t)addrInfo.pageAddr.blockId);
    EXPECT_EQ(true, (int32_t)isNewBlock);

    ret = LfsGetAvailBlockWrapper(&g_lfsMgr, 1024 * 1024, &isNewBlock, &addrInfo);
    EXPECT_EQ(ret, INT_ERR_LFS_INVALID_REQ_SIZE);
}
TEST_F(UtStorageLabelSpace, searchFsm)
{
    InitLfsMgr(HEAP_VAR_LEN_ROW_PAGE);
    SearchFsm();
    DestroyFsm();
}

static void UpdateFsm()
{
    FsmDataPageHeadT *page = (FsmDataPageHeadT *)malloc(32 * 1024);
    page->slotIdx = 0;
    LfsOpInfoT lfsOpInfo = {.mgr = &g_lfsMgr,
        .md = (MdMgrT *)(void *)&g_pageMgr,
        .rsmUndoRec = NULL,
        .useCachePage = false,
        .needReleasePage = true};
    int32_t ret = LfsSetBlockFreeSpace(&lfsOpInfo, (uint8_t *)page, 30 * 1024, FSM_INVALID_MAX_ROW_SIZE, false);
    EXPECT_EQ(0, ret);

    page->slotIdx = 1;
    ret = LfsSetBlockFreeSpace(&lfsOpInfo, (uint8_t *)page, 30 * 1024, FSM_INVALID_MAX_ROW_SIZE, false);
    EXPECT_EQ(0, ret);
}

TEST_F(UtStorageLabelSpace, updateFsm)
{
    InitLfsMgr(HEAP_VAR_LEN_ROW_PAGE);
    SearchFsm();
    UpdateFsm();
    DestroyFsm();
}

TEST_F(UtStorageLabelSpace, destroyLfsMgr)
{
    InitLfsMgr(HEAP_VAR_LEN_ROW_PAGE);
    SearchFsm();
    UpdateFsm();
    DestroyFsm();
}

TEST_F(UtStorageLabelSpace, fullStepForFixType)
{
    InitLfsMgr(HEAP_FIX_LEN_ROW_PAGE);

    bool isNewBlock;
    PageAddrInfoT addrInfo;
    int32_t ret = LfsGetAvailBlockWrapper(&g_lfsMgr, 1024, &isNewBlock, &addrInfo);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(0, (int32_t)addrInfo.pageAddr.blockId);
    EXPECT_EQ(true, (int32_t)isNewBlock);

    ret = LfsGetAvailBlockWrapper(&g_lfsMgr, 1024 * 1024, &isNewBlock, &addrInfo);
    EXPECT_EQ(ret, INT_ERR_LFS_INVALID_REQ_SIZE);

    uint32_t blockCnt = LfsGetRealBlockCnt(&g_lfsMgr);
    EXPECT_EQ(1u, blockCnt);

    UpdateFsm();

    ret = LfsMgrDestroy((MdMgrT *)(void *)&g_pageMgr, &g_lfsMgr, NULL);
    EXPECT_EQ(0, ret);
}

#define LFS_INVALID_LIST_ID 0x3F
// 测试场景：针对FSM8级链表升级为16级链表，测试升级后的边界值,LfsGetOwnerListId
// 场景1：设置边界值为最小，预计落在第一级
// 场景2：设置边界值为最大，预计落在最后一级
// 场景3：设置边界值为17，位于[15,20]区间，预计落在13级
TEST_F(UtStorageLabelSpace, fsmUpdateLinkListFromEightToSixteen)
{
    InitLfsMgr(HEAP_VAR_LEN_ROW_PAGE);

    int32_t listId;
    int32_t ret;
    for (int32_t i = 0; i < FSM_VAR_LIST_COUNT - 1; i++) {

        ret = LfsGetAllocListId(&g_lfsMgr, g_lfsMgr.listRange[i], &listId);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(i, listId);

        if (i != 0) {
            ret = LfsGetAllocListId(&g_lfsMgr, g_lfsMgr.listRange[i] - 1, &listId);
            EXPECT_EQ(ret, GMERR_OK);
            EXPECT_EQ(i, listId);
        }

        ret = LfsGetAllocListId(&g_lfsMgr, g_lfsMgr.listRange[i] + 1, &listId);
        if (i == FSM_VAR_LIST_COUNT - 2) {
            EXPECT_EQ(ret, INT_ERR_LFS_INVALID_REQ_SIZE);
        } else {
            EXPECT_EQ(ret, GMERR_OK);
            EXPECT_EQ(i + 1, listId);
        }

        ret = LfsGetOwnerListId(&g_lfsMgr, g_lfsMgr.listRange[i], &listId);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(i, listId);

        if (i != 0) {
            ret = LfsGetOwnerListId(&g_lfsMgr, g_lfsMgr.listRange[i] - 1, &listId);
            EXPECT_EQ(ret, GMERR_OK);
            EXPECT_EQ(i - 1, listId);
        }

        ret = LfsGetOwnerListId(&g_lfsMgr, g_lfsMgr.listRange[i] + 1, &listId);
        if (i == FSM_VAR_LIST_COUNT - 2) {
            EXPECT_EQ(ret, INT_ERR_LFS_INVALID_REQ_SIZE);
        } else {
            EXPECT_EQ(ret, GMERR_OK);
            EXPECT_EQ(i, listId);
        }
    }

    ret = LfsMgrDestroy((MdMgrT *)(void *)&g_pageMgr, &g_lfsMgr, NULL);
    EXPECT_EQ(0, ret);
}
