/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: ut for art container
 * Author: qipengcheng
 * Create: 2021-03-16
 */

#include "adpt_types.h"
#include "adpt_spinlock.h"
#include "se_instance.h"
#include "db_table_space.h"
#include "se_define.h"
#include "se_memdata.h"
#include "db_shm_array.h"
#include "gmc_errno.h"
#include "db_list.h"
#include "stub.h"
#include "db_mem_context.h"
#include "adpt_mem_segment_euler.h"
#include "db_dynmem_algo.h"
#include "db_common_init.h"
#include "se_instance.h"
#include "se_art_mem.h"
#include "common_init.h"
#include "db_top_shmem_ctx.h"
#include "db_shm_array.h"
#include "storage_ut_common.h"
#include "se_page_mgr.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

using namespace std;

typedef struct Node {
    uint32_t id;
    ArtNodePtrT prev;
    ArtNodePtrT next;
} NodeT;

typedef struct TestNode {
    char obj;
    uint32_t size;
} TestNodeT;

typedef struct ArtNodeDesc {
    uint32_t blockId;
    uint16_t offset;
    uint16_t nodeSize;
    bool isFreeNode;
} ArtNodeDescT;

DbMemCtxT *g_ArtMemMemCtx = nullptr;

class UtArtMem : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        Status ret = CommonInit();
        EXPECT_EQ(GMERR_OK, ret);

        // memData
        DbMemCtxT *topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
        DB_POINTER(topShmMemCtx);

        SeConfigT artMemConfig = {0};
        artMemConfig.deviceSize = 4 * DB_KIBI;
        artMemConfig.pageSize = 16;
        artMemConfig.instanceId = 1;
        artMemConfig.maxSeMem = 500 * DB_KIBI;
        artMemConfig.maxTrxNum = MAX_TRX_NUM;

        SeInstanceHdT artMemPtr = nullptr;
        ret = SeCreateInstance(NULL, topShmMemCtx, &artMemConfig, &artMemPtr);
        EXPECT_EQ(GMERR_OK, ret);
        SeInstanceT *cmpPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
        EXPECT_EQ(artMemPtr, cmpPtr);

        // memCtx
        DbMemCtxArgsT artMemArgs = {0};
        g_ArtMemMemCtx =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &artMemArgs);
        DB_POINTER(g_ArtMemMemCtx);
        DbMemCtxSwitchTo((DbMemCtxT *)g_ArtMemMemCtx);
    };

    static void TearDownTestCase()
    {
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        DbClearTopShmCtx(GET_INSTANCE_ID);
        CommonRelease();
    };
};

void ArtMgrInit(ArtMemMgrRunCtxT *memRunCtx, ArtMemMgrT *artMemMgr, uint32_t *nodesSize)
{
    ArtCfgT artCfg;
    artCfg.mode = ART_FREE_NODE_DETECT;
    artCfg.spaceId = 0;
    artCfg.instanceId = GET_INSTANCE_ID;
    artCfg.isVertexUseRsm = false;
    Status ret = ArtMemInit(&artCfg, artMemMgr, nodesSize);
    EXPECT_EQ(GMERR_OK, ret);
    (void)memset_s(memRunCtx, sizeof(ArtMemMgrRunCtxT), 0, sizeof(ArtMemMgrRunCtxT));
    memRunCtx->mdMgr = (MdMgrT *)SeGetPageMgr(GET_INSTANCE_ID);
    DB_POINTER(memRunCtx->mdMgr);
    memRunCtx->artMemMgr = artMemMgr;
    memRunCtx->memctx = g_ArtMemMemCtx;
}

// mark last nodeDesc in nodeDescList freeNode
static Status ArtMarkLastNodeDescFreeNode(const DbShmArrayT *nodeDescList, bool *isFreeNode)
{
    DB_POINTER(isFreeNode);
    if (*isFreeNode) {
        DbArrayAddrT arrAddrItem = {0};
        ArtNodeDescT *nodeDesc =
            (ArtNodeDescT *)DbShmArrayGetItemById(nodeDescList, nodeDescList->curPos - 1, &arrAddrItem);
        if (nodeDesc == nullptr) {
            DB_LOG_ERROR(INT_ERR_ART_MEM_GET_SHMARRAY_ITEM_ERROR, "the description of the Node is nullptr");
            return GMERR_INTERNAL_ERROR;
        }
        nodeDesc->isFreeNode = true;
        *isFreeNode = false;
    }
    return GMERR_OK;
}

// add node info into nodeDescList
static Status ArtInsertNodeDesc(
    ArtMemMgrRunCtxT *memMgrCtx, DbShmArrayT *nodeDescList, uint16_t nodeSize, uint32_t blockId, uint16_t nodeOffset)
{
    uint8_t *pageBeginAddr = nullptr;
    Status ret = ArtMemGetPageByBlockId(memMgrCtx, blockId, &pageBeginAddr);
    if (ret != GMERR_OK) {
        return ret;
    }
    PageInfoT *pageInfo = ArtMemPageInfoAddr(pageBeginAddr);
    // insert nodeDesc into given list
    ArtNodeDescT *nodeDesc = nullptr;
    uint32_t itemId = 0u;
    DbArrayAddrT arrAddrItem = {0};
    ret = DbShmArrayGetItem(nodeDescList, &itemId, (void **)&nodeDesc, &arrAddrItem);
    if (ret != GMERR_OK) {
        return ret;
    }
    nodeDesc->nodeSize = nodeSize;
    nodeDesc->blockId = blockId;
    nodeDesc->offset = nodeOffset + (uint16_t)pageInfo->beginOffset;
    nodeDesc->isFreeNode = false;
    return GMERR_OK;
}

static Status FreeNodeDescList(Status ret, DbMemCtxT *memCtx, DbShmArrayT *nodeDescList)
{
    DB_POINTER2(memCtx, nodeDescList);
    DbDynMemCtxFree(memCtx, nodeDescList);
    return ret;
}

static Status ArtMemGetPageInfoForDebug(
    DbMemCtxT *memCtx, ArtMemMgrT *artMemMgr, uint32_t blockId, DbShmArrayT **nodeDescList)
{
    DB_POINTER(artMemMgr);
    EXPECT_EQ(ART_FREE_NODE_DETECT, artMemMgr->mode);
    *nodeDescList = (DbShmArrayT *)DbDynMemCtxAlloc(memCtx, sizeof(DbShmArrayT));
    DB_POINTER(*nodeDescList);
    Status ret =
        DbShmArrayInit(*nodeDescList, ART_SHMARRAY_CAPACITY, sizeof(ArtNodeDescT), artMemMgr->artShmArrayId, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t *pageBeginAddr = nullptr;
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    ArtMemMgrRunCtxT memMgrCtx = {.memctx = memCtx, .artMemMgr = artMemMgr, .mdMgr = (MdMgrT *)seIns->mdMgr};
    ret = ArtMemGetPageByBlockId(&memMgrCtx, blockId, &pageBeginAddr);
    EXPECT_EQ(GMERR_OK, ret);
    PageInfoT *pageInfo = ArtMemPageInfoAddr(pageBeginAddr);
    uint32_t *artNodeAddrMap = ArtGetNodeAddrMap(artMemMgr, pageBeginAddr);
    uint32_t *artFNodeAddrMap = ArtGetFNodeAddrMap(artMemMgr, pageBeginAddr);
    uint16_t endOffset = (uint16_t)(pageInfo->offset - pageInfo->beginOffset);
    uint16_t offset = 0;
    while (offset != endOffset) {
        // bit 1 mapping to a node addr
        if (ArtMemIsBitMapSet(artMemMgr, artNodeAddrMap, offset)) {
            // a freeNode?
            bool isFreeNode = ArtMemIsBitMapSet(artMemMgr, artFNodeAddrMap, offset);
            uint16_t nodeSize = ArtMemGetNodeSize(artMemMgr, artNodeAddrMap, offset, endOffset);
            ret = ArtInsertNodeDesc(&memMgrCtx, *nodeDescList, nodeSize, blockId, offset);
            if (ret != GMERR_OK) {
                return FreeNodeDescList(ret, memCtx, *nodeDescList);
            }
            ret = ArtMarkLastNodeDescFreeNode(*nodeDescList, &isFreeNode);
            if (ret != GMERR_OK) {
                return FreeNodeDescList(ret, memCtx, *nodeDescList);
            }
            offset += nodeSize;
        } else {
            offset += ART_PAGE_DIVIDE_LENGTH;
        }
    }
    return GMERR_OK;
}

// free nodeDescList that stores node Picture of page
static void ArtMemFreeInfo(DbMemCtxT *memCtx, const ArtMemMgrT *artMemMgr, DbShmArrayT *nodeDescList)
{
    DbShmArrayDestroy(nodeDescList, (DbInstanceHdT)DbGetInstanceByMemCtx(memCtx));
    DbDynMemCtxFree(memCtx, nodeDescList);
}

uint16_t NodeFixedSize(uint16_t nodeSize, uint16_t pageDivideLength)
{
    EXPECT_NE(0, pageDivideLength);
    // fixedNodeSize can't exceed size of free node
    uint16_t tmpNodeSize = nodeSize;
    if (tmpNodeSize % pageDivideLength != 0) {
        tmpNodeSize = tmpNodeSize < sizeof(FreeNodeT) ? sizeof(FreeNodeT) : tmpNodeSize;
        // get min num >= (nodeSize / 8), return num * 8
        return (tmpNodeSize & ~(pageDivideLength - 1u)) + pageDivideLength;
    }
    return tmpNodeSize;
}

static void SetNode(TestNodeT *node, uint32_t size)
{
    DB_POINTER(node);
    node->obj = 'c';
    node->size = size;
}

static void CheckArtNodeDesc(ArtNodeDescT *nodeDesc, uint32_t nodeSize, ArtNodePtrT node, bool isFreeNode)
{
    DB_POINTER(nodeDesc);
    ASSERT_EQ(nodeSize, nodeDesc->nodeSize);
    ASSERT_EQ(node.offset, nodeDesc->offset);
    ASSERT_EQ(node.blockId, nodeDesc->blockId);
    ASSERT_EQ(isFreeNode, nodeDesc->isFreeNode);
}

// must in mode ART_FREE_NODE_DETECT
TEST_F(UtArtMem, pageSnapshot)
{
    ArtMemMgrT artMemMgr;
    ArtMemMgrRunCtxT memRunCtx;
    uint32_t nodesSize[ART_NODE_TYPE_NUM] = {0};
    for (uint32_t i = 0; i < ART_NODE_TYPE_NUM; i++) {
        nodesSize[i] = rand() % ART_NODE_TYPE_NUM + 16;
    }
    ArtMgrInit(&memRunCtx, &artMemMgr, nodesSize);

    uint32_t randomNodeSize = 0;
    uint8_t *addr[ART_NODE_TYPE_NUM];
    ArtNodePtrT nodePtr[ART_NODE_TYPE_NUM];
    ArtAllocNodeParamT allocParam = {0};
    for (uint32_t i = 0; i < ART_NODE_TYPE_NUM; i++) {
        randomNodeSize = (uint32_t)nodesSize[i];
        ArtNodePtrT newNodePtr;
        uint8_t *memPageAddr = nullptr;
        // alloc node
        allocParam.tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX;
        allocParam.nodeType = i;
        allocParam.nodeSize = randomNodeSize;
        Status ret = ArtMemAllocNode(&memRunCtx, &allocParam, &memPageAddr, &newNodePtr);
        EXPECT_EQ(GMERR_OK, ret);
        // set node
        SetNode((TestNodeT *)memPageAddr, randomNodeSize);
        addr[i] = memPageAddr;
        // logic info
        nodePtr[i] = newNodePtr;
    }

    DbShmArrayT *nodeDescList = nullptr;
    uint32_t pageCount = artMemMgr.pageAllocatedCount;
    uint32_t nodeCounter = 0;
    DbArrayAddrT arrAddr;

    // get snapshot by page, update node infos
    for (uint32_t blockId = 0; blockId < pageCount; blockId++) {
        Status ret = ArtMemGetPageInfoForDebug(g_ArtMemMemCtx, &artMemMgr, blockId, &nodeDescList);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t i = 0; i < nodeDescList->curPos; i++) {
            ArtNodeDescT *nodeDesc = (ArtNodeDescT *)DbShmArrayGetItemById(nodeDescList, i, &arrAddr);
            CheckArtNodeDesc(
                nodeDesc, NodeFixedSize(((TestNodeT *)addr[nodeCounter])->size, 8), nodePtr[nodeCounter], false);
            nodeCounter++;
        }
        ArtMemFreeInfo(g_ArtMemMemCtx, &artMemMgr, nodeDescList);
    }
    EXPECT_EQ(ART_NODE_TYPE_NUM, nodeCounter);
}

// alloc 1 node and free, then alloc node with same size
TEST_F(UtArtMem, allocAndFreeNode0)
{
    uint8_t *addr = nullptr;
    ArtMemMgrT artMemMgr;
    ArtMemMgrRunCtxT memRunCtx;
    uint32_t nodesSize[ART_NODE_TYPE_NUM] = {
        sizeof(TestNodeT), sizeof(TestNodeT), sizeof(TestNodeT), sizeof(TestNodeT)};
    ArtMgrInit(&memRunCtx, &artMemMgr, nodesSize);

    ArtNodePtrT seNode1;
    ArtAllocNodeParamT allocParam = {0};
    allocParam.tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX;
    allocParam.nodeType = 0;
    allocParam.nodeSize = sizeof(TestNodeT);
    Status ret = ArtMemAllocNode(&memRunCtx, &allocParam, &addr, &seNode1);
    EXPECT_EQ(GMERR_OK, ret);
    DB_POINTER(addr);
    EXPECT_FALSE(ArtMemNodeIsEqual(seNode1, ART_MEM_NULL_NODE_PTR));

    // set node var
    SetNode((TestNodeT *)addr, 1);

    // freeNode will be created
    ret = ArtMemFreeNode(&memRunCtx, 0, seNode1);
    EXPECT_EQ(GMERR_OK, ret);

    // new Node should locate on addr of freeNode
    seNode1 = ART_MEM_NULL_NODE_PTR;
    uint8_t *newAddr = nullptr;
    ArtMemAllocNode(&memRunCtx, &allocParam, &newAddr, &seNode1);

    EXPECT_EQ(addr, newAddr);

    // set node var
    SetNode((TestNodeT *)addr, 1);

    // get node var
    ArtMemGetNodeAddrByBlockId(&memRunCtx, seNode1, &addr);
    TestNodeT *targetNode = (TestNodeT *)addr;
    EXPECT_EQ('c', targetNode->obj);
    EXPECT_EQ(1u, targetNode->size);

    DbShmArrayT *nodeDescList = nullptr;
    ret = ArtMemGetPageInfoForDebug(g_ArtMemMemCtx, &artMemMgr, 0, &nodeDescList);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, nodeDescList->used);

    DbArrayAddrT arrAddr;
    ArtNodeDescT *nodeDesc = (ArtNodeDescT *)DbShmArrayGetItemById(nodeDescList, 0, &arrAddr);
    CheckArtNodeDesc(nodeDesc, NodeFixedSize(sizeof(TestNodeT), 8), seNode1, false);
    ArtMemFreeInfo(g_ArtMemMemCtx, &artMemMgr, nodeDescList);
}

/* alloc node and free, alloc another node and free
 * then alloc 2 nodes, these node's addr and logic addr must be the same as previous 2 nodes'
 */
TEST_F(UtArtMem, allocAndFreeNode1)
{
    ArtMemMgrT artMemMgr;
    ArtMemMgrRunCtxT memRunCtx;
    uint32_t nodesSize[ART_NODE_TYPE_NUM] = {
        sizeof(TestNodeT), sizeof(TestNodeT) * 8, sizeof(TestNodeT), sizeof(TestNodeT)};
    ArtMgrInit(&memRunCtx, &artMemMgr, nodesSize);

    uint8_t *addr[4];
    ArtNodePtrT nodePtr[4];
    ArtAllocNodeParamT allocParam = {0};
    allocParam.tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX;
    allocParam.nodeType = 0;
    allocParam.nodeSize = sizeof(TestNodeT);
    Status ret = ArtMemAllocNode(&memRunCtx, &allocParam, &addr[0], &nodePtr[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ArtMemFreeNode(&memRunCtx, 0, nodePtr[0]);
    EXPECT_EQ(GMERR_OK, ret);
    allocParam.nodeType = 1;
    allocParam.nodeSize = sizeof(TestNodeT) * 8;
    ret = ArtMemAllocNode(&memRunCtx, &allocParam, &addr[1], &nodePtr[1]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ArtMemFreeNode(&memRunCtx, 1, nodePtr[1]);
    EXPECT_EQ(GMERR_OK, ret);
    allocParam.nodeType = 1;
    allocParam.nodeSize = sizeof(TestNodeT) * 8;
    ret = ArtMemAllocNode(&memRunCtx, &allocParam, &addr[2], &nodePtr[2]);
    EXPECT_EQ(GMERR_OK, ret);
    allocParam.nodeType = 0;
    allocParam.nodeSize = sizeof(TestNodeT);
    ret = ArtMemAllocNode(&memRunCtx, &allocParam, &addr[3], &nodePtr[3]);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(addr[1], addr[2]);
    EXPECT_EQ(addr[0], addr[3]);
    EXPECT_TRUE(ArtMemNodeIsEqual(nodePtr[1], nodePtr[2]));
    EXPECT_TRUE(ArtMemNodeIsEqual(nodePtr[0], nodePtr[3]));

    // validate in node pic
    DbShmArrayT *nodeDescList = nullptr;
    ret = ArtMemGetPageInfoForDebug(g_ArtMemMemCtx, &artMemMgr, 0, &nodeDescList);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, nodeDescList->used);

    DbArrayAddrT arrAddr;
    ArtNodeDescT *nodeDesc1 = (ArtNodeDescT *)DbShmArrayGetItemById(nodeDescList, 0, &arrAddr);
    CheckArtNodeDesc(nodeDesc1, NodeFixedSize(sizeof(TestNodeT), 8), nodePtr[0], false);
    ArtNodeDescT *nodeDesc2 = (ArtNodeDescT *)DbShmArrayGetItemById(nodeDescList, 1, &arrAddr);
    CheckArtNodeDesc(nodeDesc2, NodeFixedSize(sizeof(TestNodeT) * 8, 8), nodePtr[1], false);
    ArtMemFreeInfo(g_ArtMemMemCtx, &artMemMgr, nodeDescList);
}

/*
 * alloc 4 nodes with same size, and free first 3 nodes
 * then there will be 4 nodes in Snapshot, and first nodeDesc should be free
 */
TEST_F(UtArtMem, freeNode1)
{
    ArtMemMgrT artMemMgr;
    ArtMemMgrRunCtxT memRunCtx;
    uint32_t nodesSize[ART_NODE_TYPE_NUM] = {9, sizeof(TestNodeT) * 8, sizeof(TestNodeT), sizeof(TestNodeT)};
    ArtMgrInit(&memRunCtx, &artMemMgr, nodesSize);

    uint8_t *addr = nullptr;
    ArtNodePtrT seNode[4];
    Status ret;
    ArtAllocNodeParamT allocParam = {0};
    allocParam.tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX;
    allocParam.nodeType = 0;
    allocParam.nodeSize = 9;
    for (uint32_t i = 0; i < 4; i++) {
        ret = ArtMemAllocNode(&memRunCtx, &allocParam, &addr, &seNode[i]);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(ArtMemNodeIsEqual(seNode[i], ART_MEM_NULL_NODE_PTR));
    }

    for (uint32_t i = 0; i < 3; i++) {
        ret = ArtMemFreeNode(&memRunCtx, 0, seNode[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    FreeListT *freeListHeader = &artMemMgr.freeList[0];
    EXPECT_EQ(24, freeListHeader->nodeSize);
    EXPECT_EQ(3u, freeListHeader->nodeCount);

    DbShmArrayT *nodeDescList = nullptr;
    ret = ArtMemGetPageInfoForDebug(g_ArtMemMemCtx, &artMemMgr, 0, &nodeDescList);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(4u, nodeDescList->curPos);

    DbArrayAddrT arrAddr;
    for (uint32_t i = 0; i < 3; i++) {
        ArtNodeDescT *nodeDesc = (ArtNodeDescT *)DbShmArrayGetItemById(nodeDescList, i, &arrAddr);
        CheckArtNodeDesc(nodeDesc, 24, seNode[i], true);
    }
    ArtNodeDescT *nodeDesc = (ArtNodeDescT *)DbShmArrayGetItemById(nodeDescList, 3, &arrAddr);
    CheckArtNodeDesc(nodeDesc, 24, seNode[3], false);
    ArtMemFreeInfo(g_ArtMemMemCtx, &artMemMgr, nodeDescList);
}

/*
 * alloc 4 nodes with diff size, and free node0, node1, node3
 * then there will be 4 nodes in Snapshot, and first nodeDesc should be free
 */
TEST_F(UtArtMem, freeNode2)
{
    ArtMemMgrT artMemMgr;
    ArtMemMgrRunCtxT memRunCtx;
    uint32_t nodesSize[ART_NODE_TYPE_NUM] = {3, 17, 25, sizeof(TestNodeT)};
    ArtMgrInit(&memRunCtx, &artMemMgr, nodesSize);

    uint8_t *addr = nullptr;
    ArtNodePtrT seNode[4];
    ArtAllocNodeParamT allocParam = {0};
    allocParam.tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX;
    allocParam.nodeType = 0;
    allocParam.nodeSize = 3;
    Status ret = ArtMemAllocNode(&memRunCtx, &allocParam, &addr, &seNode[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ArtMemAllocNode(&memRunCtx, &allocParam, &addr, &seNode[1]);
    EXPECT_EQ(GMERR_OK, ret);
    allocParam.nodeType = 1;
    allocParam.nodeSize = 17;
    ret = ArtMemAllocNode(&memRunCtx, &allocParam, &addr, &seNode[2]);
    EXPECT_EQ(GMERR_OK, ret);
    allocParam.nodeType = 2;
    allocParam.nodeSize = 25;
    ret = ArtMemAllocNode(&memRunCtx, &allocParam, &addr, &seNode[3]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ArtMemFreeNode(&memRunCtx, 2, seNode[3]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ArtMemFreeNode(&memRunCtx, 0, seNode[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = ArtMemFreeNode(&memRunCtx, 0, seNode[1]);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < ART_NODE_TYPE_NUM; i++) {
        FreeListT *freeListHeader = &artMemMgr.freeList[i];
        if (i == 0) {
            EXPECT_EQ(2u, freeListHeader->nodeCount);
            EXPECT_EQ(24, freeListHeader->nodeSize);
        } else if (i == 2) {
            EXPECT_EQ(1u, freeListHeader->nodeCount);
            EXPECT_EQ(32, freeListHeader->nodeSize);
        }
    }

    DbShmArrayT *nodeDescList = nullptr;
    ret = ArtMemGetPageInfoForDebug(g_ArtMemMemCtx, &artMemMgr, 0, &nodeDescList);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(4u, nodeDescList->used);

    DbArrayAddrT arrAddr;
    for (uint32_t i = 0; i < 4; i++) {
        ArtNodeDescT *nodeDesc = (ArtNodeDescT *)DbShmArrayGetItemById(nodeDescList, i, &arrAddr);
        DB_POINTER(nodeDesc);
        EXPECT_EQ(nodeDesc->isFreeNode, i != 2);
    }
    ArtMemFreeInfo(g_ArtMemMemCtx, &artMemMgr, nodeDescList);
}

Status IterateSnapshot(ArtMemMgrT *artMemMgr, uint32_t *calFreeNodeCount, uint32_t *calNormalNodeCount)
{
    uint32_t pageCount = artMemMgr->pageAllocatedCount;
    DbArrayAddrT arrAddr;

    *calFreeNodeCount = 0;
    *calNormalNodeCount = 0;

    // iterate all used page
    for (uint32_t i = 0; i < pageCount; i++) {
        DbShmArrayT *nodeDescList = nullptr;
        uint32_t blockId = i;
        Status ret = ArtMemGetPageInfoForDebug(g_ArtMemMemCtx, artMemMgr, blockId, &nodeDescList);
        EXPECT_EQ(GMERR_OK, ret);

        for (uint32_t j = 0; j < nodeDescList->curPos; j++) {
            ArtNodeDescT *nodeDesc = (ArtNodeDescT *)DbShmArrayGetItemById(nodeDescList, j, &arrAddr);
            if (nodeDesc == nullptr) {
                DB_LOG_ERROR(INT_ERR_ART_MEM_GET_SHMARRAY_ITEM_ERROR, "the description of the Node is nullptr");
                return GMERR_INTERNAL_ERROR;
            }
            if (nodeDesc->isFreeNode) {
                DB_LOG_ERROR_UNFOLD(0, "xxdebug freed [%u,%u]", nodeDesc->blockId, nodeDesc->offset);
                (*calFreeNodeCount)++;
            } else {
                DB_LOG_ERROR_UNFOLD(0, "xxdebug normal [%u,%u]", nodeDesc->blockId, nodeDesc->offset);
                (*calNormalNodeCount)++;
            }
        }
        ArtMemFreeInfo(g_ArtMemMemCtx, artMemMgr, nodeDescList);
    }
    return GMERR_OK;
}

typedef struct NodeDesc {
    ArtNodePtrT nodePtr;
    uint32_t size;
    bool used;
} NodeDescT;

static Status AllocNodes(ArtMemMgrRunCtxT *memMgrCtx, uint32_t allocNodeNum, NodeDescT *nodeDescArray)
{
    uint8_t *memPageAddr = nullptr;
    Status ret = GMERR_OK;
    ArtAllocNodeParamT allocParam = {0};
    allocParam.tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX;
    for (uint32_t i = 0; i < allocNodeNum; i++) {
        uint32_t size = i % ART_NODE_TYPE_NUM + 12;
        uint32_t nodeType = i % ART_NODE_TYPE_NUM;
        allocParam.nodeType = nodeType;
        allocParam.nodeSize = size;
        ret = ArtMemAllocNode(memMgrCtx, &allocParam, &memPageAddr, &(nodeDescArray[i].nodePtr));
        EXPECT_EQ(GMERR_OK, ret);

        nodeDescArray[i].size = size;
        nodeDescArray[i].used = true;
        DB_LOG_ERROR_UNFOLD(0, "xxdebug alloc node[%u], [%u,%u]", nodeDescArray[i].nodePtr.pageId,
            nodeDescArray[i].nodePtr.blockId, nodeDescArray[i].nodePtr.offset);
        ret = memset_s(memPageAddr, size, 0xff, size);  // assume all of allocated space is used
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

/*
 * free num Nodes in given node array
 */
static Status FreeNodes(ArtMemMgrRunCtxT *memMgrCtx, uint32_t num, NodeDescT *nodeDescArray)
{
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < num; i++) {
        uint32_t nodeType = i % ART_NODE_TYPE_NUM;
        if (nodeDescArray[i].used) {
            DB_LOG_ERROR_UNFOLD(0, "xxdebug free node[%u], [%u, %u]", nodeDescArray[i].nodePtr.pageId,
                nodeDescArray[i].nodePtr.blockId, nodeDescArray[i].nodePtr.offset);
            ret = ArtMemFreeNode(memMgrCtx, nodeType, nodeDescArray[i].nodePtr);
            EXPECT_EQ(GMERR_OK, ret);
            nodeDescArray[i].used = false;
        }
    }
    return ret;
}

TEST_F(UtArtMem, complexNodeAllocAndFree)
{
    ArtMemMgrT artMemMgr;
    ArtMemMgrRunCtxT memRunCtx;
    uint32_t nodesSize[ART_NODE_TYPE_NUM] = {0};
    for (uint32_t i = 0; i < ART_NODE_TYPE_NUM; i++) {
        nodesSize[i] = i + 12;
    }
    ArtMgrInit(&memRunCtx, &artMemMgr, nodesSize);
    uint32_t validArrayLength = 0;
    uint32_t allocNodeNum = 20;
    uint32_t freeNodeNum = allocNodeNum / 2;
    uint32_t calNormalNodeCount = 0;  // not freeNode
    uint32_t calFreeNodeCount = 0;

    NodeDescT *nodeArray = (NodeDescT *)DbDynMemCtxAlloc(g_ArtMemMemCtx, allocNodeNum * sizeof(NodeDescT));

    // alloc nodes
    Status ret = AllocNodes(&memRunCtx, allocNodeNum, nodeArray);
    EXPECT_EQ(GMERR_OK, ret);

    ret = IterateSnapshot(&artMemMgr, &calFreeNodeCount, &calNormalNodeCount);
    EXPECT_EQ(calNormalNodeCount, allocNodeNum);
    EXPECT_EQ(0u, calFreeNodeCount);

    // free nodes
    ret = FreeNodes(&memRunCtx, freeNodeNum, nodeArray);
    EXPECT_EQ(GMERR_OK, ret);
    ret = IterateSnapshot(&artMemMgr, &calFreeNodeCount, &calNormalNodeCount);
    EXPECT_EQ(calNormalNodeCount, allocNodeNum - freeNodeNum);
    EXPECT_EQ(calFreeNodeCount, freeNodeNum);
    validArrayLength += freeNodeNum;

    // alloc nodes
    ret = AllocNodes(&memRunCtx, allocNodeNum, nodeArray);
    EXPECT_EQ(GMERR_OK, ret);
    validArrayLength += allocNodeNum;

    ret = IterateSnapshot(&artMemMgr, &calFreeNodeCount, &calNormalNodeCount);
    EXPECT_EQ(calNormalNodeCount, allocNodeNum * 2 - freeNodeNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = FreeNodes(&memRunCtx, freeNodeNum, nodeArray);
    EXPECT_EQ(GMERR_OK, ret);
    DbDynMemCtxFree(g_ArtMemMemCtx, nodeArray);
}

TEST_F(UtArtMem, freeArtMemMgr)
{
    ArtMemMgrT artMemMgr;
    ArtMemMgrRunCtxT memRunCtx;
    uint32_t nodesSize[ART_NODE_TYPE_NUM] = {
        sizeof(TestNodeT), sizeof(TestNodeT), sizeof(TestNodeT), sizeof(TestNodeT)};
    ArtMgrInit(&memRunCtx, &artMemMgr, nodesSize);

    ArtNodePtrT seNode1 = ART_MEM_NULL_NODE_PTR;
    uint8_t *addr = nullptr;
    ArtAllocNodeParamT allocParam = {0};
    allocParam.tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX;
    allocParam.nodeType = 0;
    allocParam.nodeSize = sizeof(TestNodeT);
    Status ret = ArtMemAllocNode(&memRunCtx, &allocParam, &addr, &seNode1);
    EXPECT_EQ(GMERR_OK, ret);
    ArtMemDestroy(&artMemMgr, NULL);
}
