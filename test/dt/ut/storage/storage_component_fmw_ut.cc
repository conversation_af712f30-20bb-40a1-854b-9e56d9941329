/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: ut for component fmw
 * Author: z<PERSON>yingjie
 * Create: 2024-07-24
 */

#include "gtest/gtest.h"
#include "db_internal_error.h"
#include "se_fixed_heap.h"
#include "se_fixed_heap_inner.h"
#include "se_resource_column_inner.h"
#include "se_rsm_block_am.h"
#include "se_rsm_tablespace_am.h"
#include <cstdlib>

class UtComponentFmwAm : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

extern "C" {
extern FixedHeapAmT g_fixedHeapAm;
}

TEST_F(UtComponentFmwAm, FixedHeapInterfaceFunc)
{
    FixedHeapAmT backup = g_fixedHeapAm;
    FixedHeapAmT tmp = {0};
    FixedHeapSetAmFunc(&tmp);

    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapDrop({0, 0}, false));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapTruncate({0, 0}, false));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapOpen(NULL, NULL));
    FixedHeapClose(NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapInsert(NULL, NULL, 0, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapBatchInsert(NULL, 0, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapUpdate(NULL, 0, NULL, 0));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapDelete(NULL, 0, false));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapFetch(NULL, 0, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapFetchNextTupleAddr(NULL, NULL, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapOpenCursor(NULL, 0, NULL));
    FixedHeapCloseCursor(NULL, NULL);
    ASSERT_EQ(STATUS_OK_INTER, FixedHeapMemContextCreate(NULL, NULL));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, FixedHeapMarkDeleteRollBack(NULL, 0, 0));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, FixedHeapUnlinkRollPtr(NULL, 0, 0, NULL));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, FixedHeapUpdateRollBack(NULL, 0, 0, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapGetStat({0, 0}, false, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FetchOldestVisibleRowBuf(NULL, 0, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapAllocRowBuf(NULL, NULL));
    FixedHeapFreeRowBuf(NULL, NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapGetAddrMode(NULL, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapGetRowDataSize(NULL, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapSetAllocRow(NULL, false));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapGetLabelId(NULL, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapGetTrxId(NULL, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapInitMemfield({0, 0}, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapResetMemFields(NULL, {0, 0}, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, FixedHeapRestoreMemFields(NULL, {0, 0}, NULL));

    FixedHeapSetAmFunc(&backup);
}

extern "C" {
extern ResColPoolAmT g_gmdbResColPoolAm;
}

TEST_F(UtComponentFmwAm, ResColInterfaceFunc)
{
    ResColPoolAmT backup = g_gmdbResColPoolAm;
    ResColPoolAmT tmp = {0};
    ResColSetAmFunc(&tmp);

    ASSERT_EQ(STATUS_OK_INTER, ResColMgrCreate(NULL, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColCreateResPool(NULL, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColDestroyResPoolCheck({0, 0}));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColDestroyResPool({0, 0}));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColBindExtendedPool({0, 0}, {0, 0}));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColExtendedPoolIsExisted({0, 0}, NULL));

    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColUnbindExtendedPool({0, 0}));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColBindResPoolToLabel({0, 0}, 0));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColUnbindResPoolToLabel({0, 0}, 0));

    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColPoolOpen(NULL, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColPoolClose(NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColPoolAllocResId(NULL, 0, NULL, 0));

    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColPoolReleaseResId(NULL, NULL, false, 0));
    ASSERT_EQ(DB_INVALID_UINT32, ResColPoolGetPoolId(NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColPoolReleaseAllRes({0, 0}));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColGetPoolStat(NULL, 0, NULL));

    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColGetPoolAndTableStat(NULL, 0, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColGetPoolBitmapStat(NULL, NULL, 0, NULL));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColGetStartIndexStatByResPoolShmPtr(NULL, {0, 0}, 0, 0));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColGetResPoolStatByResId(NULL, 0));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColGetResPoolStatByShmPtr(NULL, {0, 0}));
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ResColGetExternResPoolStatByShmPtr(NULL, {0, 0}));

    ResColSetAmFunc(&backup);
}

#ifndef FEATURE_RSMEM
TEST_F(UtComponentFmwAm, RsmBlockInterfaceFunc)
{
    int *para1 = (int *)malloc(8);
    int *para2 = (int *)malloc(8);
    int *para3 = (int *)malloc(8);
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockCreate((SeInstanceHdT)para1, (DbMemCtxT *)para2));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockRecovery((SeInstanceHdT)para1));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockDestroy((SeInstanceHdT)para1, (DbMemCtxT *)para2));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockUpdReserveDevice((RsmBlkMgrHdlT)para1, 0));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockAllocDevice((RsmBlkMgrHdlT)para1, 0, 0, (uint32_t *)para2));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockUnreserveDevice((RsmBlkMgrHdlT)para1, 0));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockReturnAllDevice((RsmBlkMgrHdlT)para1, 0));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockReturnLastDevice((RsmBlkMgrHdlT)para1, 0, 0));

    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockAllocEntry((RsmBlkMgrHdlT)para1, 0, 0, (AllocEntryInfoT *)para2));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockFreeEntry((RsmBlkMgrHdlT)para1, {0, 0}));

    ASSERT_EQ(
        FEATURE_NOT_SUPPORTED_INNER, RsmBlockCompressTsp((RsmBlkMgrHdlT)para1, 0, 0, 0, (CompressResultT *)para2));
    ASSERT_EQ(
        FEATURE_NOT_SUPPORTED_INNER, RsmBlockCompressTspCheck((RsmBlkMgrHdlT)para1, 0, (CompressCheckInfoT *)para2));

    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockGetEntry((RsmBlkMgrHdlT)para1, {0, 0}, (uint8_t **)para2));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockGetMaxDevCnt((RsmBlkMgrHdlT)para1, (uint32_t *)para2));
    ASSERT_EQ(
        FEATURE_NOT_SUPPORTED_INNER, RsmBlockGetRsmSubBlockCntInRsmBlock((RsmBlkMgrHdlT)para1, (uint32_t *)para2));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockGetFreeDeviceCnt((RsmBlkMgrHdlT)para1, (uint32_t *)para2));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockGetCurRsmBlockCnt((RsmBlkMgrHdlT)para1, (uint32_t *)para2));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockGetDevCntInRsmBlock((RsmBlkMgrHdlT)para1, (uint32_t *)para2));

    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmBlockCheckAddr((RsmBlkMgrHdlT)para1, {0, 0}));
    ASSERT_EQ(NULL, RsmBlockGetRsmUndoRec((RsmBlkMgrHdlT)para1));

    RsmBlockSetIsRecovery((RsmBlkMgrHdlT)para1, false);
    RsmBlockUndoAllocBlock((RsmBlkMgrHdlT)para1, 0, 0);
    RsmBlockUndoAllocDev((RsmBlkMgrHdlT)para1, {0, 0}, 0, 0, 0);

    RsmBlockUndoFreeDev((RsmBlkMgrHdlT)para1, (const RsmUndoBlkMgrFreeDevRec *)para2);
    RsmBlockUndoReuseDev((RsmBlkMgrHdlT)para1, {0, 0}, 0);

    RsmBlockUndoAllocEntry((RsmBlkMgrHdlT)para1, {0, 0}, 0, 0, 0);
    RsmBlockUndoFreeEntry((RsmBlkMgrHdlT)para1, {0, 0}, 0, 0);
    RsmBlockUndoMemcpyEntry((RsmBlkMgrHdlT)para1, (const RsmUndoBlkMgrMemcpyEntryRec *)para2);

    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER,
        RsmBlockGetStat((RsmBlkMgrHdlT)para1, 0, (DbMemCtxT *)para2, (RsmBlockInfoT **)para3));
    ASSERT_EQ(false, RsmBlockGetNextUnusedEntry((RsmBlkMgrHdlT)para1, (FreeEntryInfoT *)para2));
    RsmBlockRebuildRsmSpace((RsmBlkMgrHdlT)para1, (RsmBlockRebuildRsmSpaceFunc)para2, NULL, (Handle)para3);
    free(para1);
    free(para2);
    free(para3);
}

TEST_F(UtComponentFmwAm, RsmTableSpaceInterfaceFunc)
{
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, MdRsmSpaceMgrInit(NULL, NULL));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmSpaceMgrRecovery(NULL));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, RsmSpaceMgrRecoveryTspInfo(NULL, NULL, 0));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, MdRsmAllocChunk(NULL, NULL));
    MdRsmFreeChunk(NULL, 0, {0, 0});
    MdRsmInitChunk(NULL, NULL, NULL);
    MdRsmResetChunk(NULL, NULL, NULL, 0);
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, MdRsmCompressTableSpaceCursor(NULL));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, MdRsmCompressTableSpaceCheck(NULL, NULL, NULL));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, MdRsmCompressTableSpace(NULL, 0, 0, 0, NULL));
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, MdRsmSpaceDropTableSpace(NULL, 0));

    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, MdRsmSpaceCreateTableSpace(NULL, NULL, NULL, NULL));
    SeGetSingleRsmTspInfo(NULL, NULL, NULL);
    SeGetHighestUsedRatioRsmTspAndTotalInfo(NULL, NULL, NULL, NULL);
    ASSERT_EQ(FEATURE_NOT_SUPPORTED_INNER, MdRsmSpaceFindRecoveryInfoById(NULL, 0, NULL, NULL));
    MdRsmSetIsRecovery(NULL, false);
    MdRsmUndoInitChunk(NULL, 0, {0, 0});

    MdRsmUndoResetChunk(NULL, 0, {0, 0});
    RsmSpaceCheckAndFreeUnusedEntries();
}
#endif
