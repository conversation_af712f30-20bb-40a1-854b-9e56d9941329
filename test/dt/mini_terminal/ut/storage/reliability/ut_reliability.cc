/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: ut file for persistence reply
 * Author: lideshi
 * Create: 2024-11-22
 */

#include "ut_reliability.h"
#include <algorithm>
#include <vector>
#include <sys/mman.h>
#include "se_database.h"

using namespace std;
using namespace testing::ext;

// 用于检测Redo原子范围内，所有get/leave页是否成对匹配。
// 此引用计数只在已开启原子范围内计数，在RedoLogEndMock内检查引用计数是否为0
static map<PageIdCompare, uint32_t> g_pgRefMapInRedoAtomic;

// 用于检测写页是否已经结束，第一次GetPage(写)时，开启引用计数，当引用计数为0时，写操作结束
static map<PageIdCompare, uint32_t> g_pgRefMapInWritting;

// 第一次GetPage时，拷贝页内存到bufTrace, 写页结束时，更新bufTrace。
// 开启写页时会和bufTrace比较，用例结束时也会比较，如果不一致，说明页在非写状态下被修改。
static map<PageIdCompare, uint8_t *> g_pgBufTrace;

// 桩函数map，string 为函数名，int为桩Id
map<string, int> g_stubMap;

static uint8_t *GetPageBuf(BufpoolMgrT *pageMgr)
{
    return (uint8_t *)pageMgr->bufPool->pageBuf;
}
// 打印page1和page2内容不同的字节偏移
static void PrintInconsistPage(uint8_t *page1, uint8_t *page2, uint32_t pageSize)
{
    PageIdT pageAddr = ((PageHeadT *)page1)->permanentAddr;
    uint8_t *snapshot = page1;
    uint8_t *current = page2;
    while (snapshot - page1 < pageSize) {
        if (*snapshot != *current) {
            printf("Err: SnapShot[%u,%u] content [%u] differ from current content [%u] at offset %u.\n",
                pageAddr.deviceId, pageAddr.blockId, *snapshot, *current, (uint32_t)(snapshot - page1));
        }
        snapshot++;
        current++;
    }
}

static uint8_t *GetPageBuf(BufpoolMgrT *pageMgr, PageIdT pageId)
{
    BufDescT *desc = NULL;
    BufFindDesc(pageMgr, pageId, &desc);
    DB_ASSERT(desc != NULL);
    return (uint8_t *)(desc->page);
}
/*
** 用于检测Redo原子范围内，所有get/leave页是否成对匹配
*/
void IncrPageRefIfRedoAtomic(PageIdCompare pageAddr)
{
    RedoRunCtxT *ctx = SeGetCurRedoCtx();
    if (ctx != nullptr && ctx->status == ATOMIC_OP_START) {
        if (g_pgRefMapInRedoAtomic.count(pageAddr) == 0) {
            g_pgRefMapInRedoAtomic[pageAddr] = 1;
        } else {
            g_pgRefMapInRedoAtomic[pageAddr]++;
        }
    }
}

void DecPageRefIfRedoAtomic(PageIdCompare pageAddr)
{
    // 检查页在Redo原子操作范围内Get/Leave是否成对
    RedoRunCtxT *ctx = SeGetCurRedoCtx();
    if (ctx != nullptr && ctx->status == ATOMIC_OP_START) {
        if (g_pgRefMapInRedoAtomic.count(pageAddr) == 0) {
            ASSERT_TRUE(false);  // 在原子操作内，没有GetPage就LeavePage或者多LeavePage, 不符合规范
        } else {
            g_pgRefMapInRedoAtomic[pageAddr]--;
        }
    }
}

void ComparePageBuf(BufpoolMgrT *mgr, PageIdCompare pageAddr, uint8_t *page, bool isWrite)
{
    if (g_pgBufTrace.count(pageAddr) == 0) {  // 第一次GetPage, 拷贝页内存
        uint8_t *buf = new uint8_t[mgr->pageSize];
        g_pgBufTrace[pageAddr] = buf;
        memcpy_s(g_pgBufTrace[pageAddr], mgr->pageSize, page, mgr->pageSize);
    }

    if (g_pgRefMapInWritting.count(pageAddr) == 0) {  // 第一次GetPage， 初始化
        g_pgRefMapInWritting[pageAddr] = 0;
    }

    if (g_pgRefMapInWritting[pageAddr] > 0) {  // 已经在写页流程中，引用计数+1
        g_pgRefMapInWritting[pageAddr]++;
        return;
    }
    if (isWrite) {  // 开启写页流程
        g_pgRefMapInWritting[pageAddr] = 1;
        if (memcmp(g_pgBufTrace[pageAddr], page, mgr->pageSize) != 0) {
            PrintInconsistPage(g_pgBufTrace[pageAddr], page, mgr->pageSize);
            DB_ASSERT(false);
        }
    }
}

void UpdatePageBuf(BufpoolMgrT *mgr, PageIdCompare pageAddr)
{
    if (g_pgRefMapInWritting[pageAddr] == 1) {  // 写页准备结束
        uint8_t *page = GetPageBuf(mgr, pageAddr.id);
        ASSERT_NE(g_pgBufTrace[pageAddr], nullptr);
        memcpy_s(g_pgBufTrace[pageAddr], mgr->pageSize, page, mgr->pageSize);
    }

    if (g_pgRefMapInWritting[pageAddr] > 0) {
        g_pgRefMapInWritting[pageAddr]--;
    }
}

StatusInter BufpoolGetPageInnerRelyMock(
    BufpoolMgrT *pageMgr, BufGetArgsT bufGetParas, void *recyArg, uint8_t **page, bool *isNew)
{
    ClearStub(g_stubMap["GetPage"]);
    StatusInter ret = BufpoolGetPageInner(pageMgr, bufGetParas, recyArg, page, isNew);
    IncrPageRefIfRedoAtomic(bufGetParas.pageId);
    ComparePageBuf(pageMgr, bufGetParas.pageId, *page, bufGetParas.isWrite);
    g_stubMap["GetPage"] = SetStubC((void *)BufpoolGetPageInner, (void *)BufpoolGetPageInnerRelyMock);
    return ret;
}

void BufpoolLeavePageInnerRelyMock(BufpoolMgrT *pageMgr, PageIdT pageId, bool isChanged, bool isFromAllocPage)
{
    DecPageRefIfRedoAtomic(pageId);
    UpdatePageBuf(pageMgr, pageId);
    ClearStub(g_stubMap["LeavePage"]);
    BufpoolLeavePageInner(pageMgr, pageId, isChanged, isFromAllocPage);
    g_stubMap["LeavePage"] = SetStubC((void *)BufpoolLeavePageInner, (void *)BufpoolLeavePageInnerRelyMock);
}

void CheckPageRefInRedoEnd()
{
    for (auto it : g_pgRefMapInRedoAtomic) {
        if (it.second != 0) {
            ASSERT_TRUE(false);  // 在Redo原子操作范围内Get/Leave不匹配
        }
    }
    g_pgRefMapInRedoAtomic.clear();
}

StatusInter RedoModifyChangedPageRelyMock(RedoRunCtxT *redoCtx)
{
    ClearStub(g_stubMap["ModifyPage"]);
    CheckPageRefInRedoEnd();
    for (uint32_t i = 0; i < redoCtx->changedPages.count; i++) {
        PageIdCompare pageId = redoCtx->changedPages.array[i]->pageId;
        ((PageHeadT *)g_pgBufTrace[pageId])->lsn = DbAtomicGet64(&redoCtx->redoMgr->redoBuf.redoPubBuf->curLsn);
    }
    StatusInter ret = RedoModifyChangedPage(redoCtx);
    g_stubMap["ModifyPage"] = SetStubC((void *)RedoModifyChangedPage, (void *)RedoModifyChangedPageRelyMock);
    return ret;
}

void UtReliability::DoStub()
{
    g_stubMap["ModifyPage"] = ::SetStubC((void *)RedoModifyChangedPage, (void *)RedoModifyChangedPageRelyMock);
    g_stubMap["GetPage"] = ::SetStubC((void *)BufpoolGetPageInner, (void *)BufpoolGetPageInnerRelyMock);
    g_stubMap["LeavePage"] = ::SetStubC((void *)BufpoolLeavePageInner, (void *)BufpoolLeavePageInnerRelyMock);
}

void UtReliability::PrepareRelyTest()
{
    DoStub();
    Status ret = CkptTrigger(seIns, CKPT_MODE_FULL, true, 2000);
    EXPECT_EQ(ret, GMERR_OK);
}

void UtReliability::RelyTestLastStep()
{
    SeSetCurRedoCtx((RedoRunCtxT *)seRunCtx->redoCtx);
    BufpoolMgrT *bufPoolMgr = (BufpoolMgrT *)(PageMgrT *)seIns->pageMgr;
    UtReliabilityErrorType ret = RELY_OK;
    /*
    ** 检测get/leave page 是否匹配，因为LeavePage函数代码内部已有检测是否多leave，所以只需要在此处检测是否有多get
    ** 遍历bufpool 的bufDesc, 判断其引用计数是否为0
    */
    ret = CheckGetLeaveMatch(bufPoolMgr);
    EXPECT_EQ(RELY_OK, ret);
    /*
    ** 检测RedoLogBegin/RedoLogEnd是否匹配，RedoLogBegin已经做了嵌套检测，RedoLogEnd也做了状态检测，所以只需要在此处检测是否最
    ** 后只调用了RedoLogBegin, 而没有调用RedoLogEnd的情况
    */
    ret = CheckRedoLogBeginEndMatch();
    EXPECT_EQ(RELY_OK, ret);

    /*
    ** 检测页的修改是否在get(isWritting = true)/leave范围内
    ** 写页结束到下一次写页开始期间检测页是否修改是在GetPage中检测的
    ** 这里检测写页结束到用例执行完毕是否有修改
    */
    ret = CheckPageModify(bufPoolMgr);
    EXPECT_EQ(RELY_OK, ret);

    for (auto &it : g_stubMap) {
        ClearStub(it.second);
    }

    ret = CheckRedo();  // 此处会卸载seIns
    EXPECT_EQ(RELY_OK, ret);
}
UtReliabilityErrorType UtReliability::CheckPageModify(BufpoolMgrT *mgr)
{
    for (auto it : g_pgBufTrace) {
        uint8_t *page = GetPageBuf(mgr, it.first.id);
        if (memcmp(page, it.second, mgr->pageSize) != 0) {
            printf("modify page, but not get page\n");
            PrintInconsistPage(page, it.second, mgr->pageSize);
            return DATA_ERROR;
        }
    }
    return RELY_OK;
}

UtReliabilityErrorType UtReliability::CheckRedoLogBeginEndMatch()
{
    RedoRunCtxT *redoCtx = (RedoRunCtxT *)seRunCtx->redoCtx;
    if (redoCtx->status == ATOMIC_OP_START) {
        RedoLogEnd(redoCtx, true);
        return REDO_LOG_BEGIN_END_NOT_MATCH;
    }
    return RELY_OK;
}

UtReliabilityErrorType UtReliability::CheckGetLeaveMatch(BufpoolMgrT *bufPoolMgr)
{
    BufDescT *descs = bufPoolMgr->bufDescs;
    for (uint32_t i = 0; i < bufPoolMgr->bufPool->hwm; i++) {
        if (descs[i].refCount != 0) {
            cout << "PageId [" << descs[i].pageId.deviceId << ", " << descs[i].pageId.blockId
                 << "] get/leave page "
                    "not match"
                 << endl;
            return GET_LEAVE_NOT_MATCH;
        }
    }
    return RELY_OK;
}

UtReliabilityErrorType UtReliability::CheckRedo()
{
    RedoPointT lrpPoint = {0, 1, 0};
    StatusInter ret = RedoLogFlush(seIns->redoMgr, &lrpPoint);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    DestroySeIns(false);
    ConstructSeInsAndSeRun(dbCfg, true, true);

    BufpoolMgrT *mgr = (BufpoolMgrT *)seIns->pageMgr;
    for (uint32_t i = 0; i < mgr->bufPool->hwm; i++) {
        PageHeadT *nowPage = (PageHeadT *)mgr->bufDescs[i].page;
        uint32_t compSize = mgr->pageSize;
        PageIdCompare pageId(mgr->bufDescs[i].pageId);

        if (g_pgBufTrace.count(pageId) == 0) {  // pageBufTrace中没有这个页，那就从磁盘中加载
            uint8_t *pageBuf = new uint8_t[compSize];
            ReadPage(pageId.id, pageBuf);
            g_pgBufTrace[pageId] = pageBuf;
        }

        PageHeadT *pageBufTrace = (PageHeadT *)g_pgBufTrace[pageId];

        pageBufTrace->crcCheckSum = nowPage->crcCheckSum;  // pageBufTrace 的页没刷盘，所以没计算CRC
        if (seIns->seConfig.isEncrypted) {
            compSize -= (DB_ENCRYPT_MAC_LENGTH + DB_ENCRYPT_IV_LENGTH + DB_ENCRYPT_RESERVED_LENGTH);
        }
        if (pageBufTrace->lsn > nowPage->lsn) {  // 有get写页，但是没记redo日志，recovery时不会更新lsn
            pageBufTrace->lsn = nowPage->lsn;
        }

        if (memcmp(g_pgBufTrace[pageId], mgr->bufDescs[i].page, compSize) != 0) {
            printf("replay error, [%u, %u]\n", pageId.id.deviceId, pageId.id.blockId);
            PrintInconsistPage(g_pgBufTrace[pageId], (uint8_t *)mgr->bufDescs[i].page, mgr->pageSize);
            return REDO_CHECK_ERR;
        }
    }
    return RELY_OK;
}

void UtReliability::CleanSource()
{
    for (auto it : g_pgBufTrace) {
        delete[] it.second;
    }
    g_pgRefMapInRedoAtomic.clear();
    g_pgRefMapInWritting.clear();
    g_pgBufTrace.clear();
    g_stubMap.clear();
}
void UtReliability::SetCfg()
{}
