/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef EXECUTOR_TEST_COMMON_H
#define EXECUTOR_TEST_COMMON_H

#include "gtest/gtest.h"
#include "common_init.h"
#include "ee_mini_base.h"
#include "ee_session_interface.h"
#include "srv_mini_service.h"
#include "se_define.h"

class UtExecutorTestCommon : public testing::Test {
public:
    virtual void SetUp()
    {}

    virtual void TearDown()
    {}

    static void SetUpTestCase();

    static void TearDownTestCase();

private:
    static SeInstanceT *seIns;
};

using namespace std;
using namespace testing::ext;

#endif
