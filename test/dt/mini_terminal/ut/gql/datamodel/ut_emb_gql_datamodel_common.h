/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Unit test header for GQL data model module
 * Author: GQL Team
 * Create: 2024-12-10
 */

#ifndef UT_EMB_GQL_DATAMODEL_COMMON_H
#define UT_EMB_GQL_DATAMODEL_COMMON_H

#include "gtest/gtest.h"
#include "stub.h"
#include "db_mem_context.h"
#include "ut_emb_gql_common.h"

const std::string CREATE_GRAPH_GQL = R"(
CREATE GRAPH MYGRAPH {
    (person:Person {name STRING, gender STRING, namePinyin STRING,
        nickname STRING, phoneNumbers STRING, emails STRING, familyAddress STRING, organization STRING,
        occupations STRING, position STRING, isUser BOOL, birthdate STRING, rowidNumber STRING, aliases STRING,
        updatedAt STRING, createdAt STRING DEFAULT CURRENT_TIMESTAMP}),
    (event:Event {title STRING, timeStart STRING, timeEnd STRING, participants STRING,
        participantEmails STRING, eventLocation STRING, description STRING,
        updatedAt STRING, createdAt STRING DEFAULT CURRENT_TIMESTAMP}),
    (person) -[:直系亲属]-> (person),
    (person) -[:旁系亲属]-> (person),
    (person) -[:姻亲]-> (person),
    (person) -[:工作]-> (person),
    (person) -[:朋友]-> (person),
    (person) -[:教育]-> (person),
    (person) -[:参与]-> (event),
    (event) -[:参与者]-> (person)
};)";

const std::string DROP_GRAPH_GQL = R"(
DROP GRAPH MYGRAPH;
)";

class UtEmbGqlDataModelCommon : public testing::Test {
public:
    static void SetUpTestCase()
    {
        Status ret = BaseInit();
        ASSERT_EQ(ret, GMERR_OK);
    }

    static void TearDownTestCase()
    {
        BaseUninit();
    }

    virtual void SetUp()
    {
        ClearAllStub();
        DbMemCtxArgsT args = {0};
        memCtx =
            DbCreateDynMemCtx(DbSrvGetSysDynCtx(UtGetDbInstance()->instanceId), true, "dynamic memory context", &args);
        ASSERT_NE(nullptr, memCtx);
        oldMemCtx = DbMemCtxSwitchTo(memCtx);

        EXPECT_EQ(
            GMERR_OK, QrySessionAlloc(SESSION_TYPE_NO_CONN, SESSION_NO_DYN_MEM_LIMIT, UtGetDbInstance(), &session));
    }

    virtual void TearDown()
    {
        QrySessionRelease(session);
        DbMemCtxSwitchBack(oldMemCtx, memCtx);
        DbDeleteDynMemCtx(memCtx);
        ClearAllStub();
    }

    void *UtGqlAllocWithZero(size_t size);

public:
    DbMemCtxT *memCtx;
    DbMemCtxT *oldMemCtx;
    Session *session;
};

#endif /* UT_EMB_GQL_DATAMODEL_COMMON_H */
