/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: system test for mini kv batch
 * Author:
 * Create: 2024-03-25
 */

#include <cstring>
#include <string>
#include <vector>

#include "gtest/gtest.h"

#include "db_inter_process_lock.h"
#include "ee_mini_base.h"
#include "gme_api.h"
#include "gme_kv.h"
#include "ee_mini_cursor.h"
#include "se_buffer_pool.h"
#include "se_define.h"
#include "se_redo.h"
#include "se_trx_inner.h"
#include "se_trx_mgr.h"
#include "stub.h"
#include "test_mini_util.h"

using namespace testing::ext;
namespace {
typedef int32_t (*KvInsertFunc)(GmeConnT *, const char *, const GmeKvItemT *, const GmeKvItemT *);
}

static char g_cfgPath[128] = R"(
{
    "dataFilePath": "./data/gmdb/datafile"
})";

class CcehIndexDmlSt : public testing::Test {
public:
    GmeConnT *conn = nullptr;
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {}

    virtual void SetUp()
    {
        InitStub();
        system("rm -rf ./data/gmdb*");
        DbTestMakeDirectory("./data/gmdb", PERM_USRRWX);
        int ret = GmeOpen(g_cfgPath, 0x01, &conn);
        ASSERT_EQ(GMERR_OK, ret);
    }
    virtual void TearDown()
    {
        ClearAllStub();
        int ret = GmeClose(conn);
        EXPECT_EQ(GMERR_OK, ret);
        conn = nullptr;
        system("rm -rf ./data/gmdb*");
    }

    void InvalidArgsTest(KvInsertFunc func);
    void RestrictionTest(KvInsertFunc func);
    void InsertMemoryLeakTest(KvInsertFunc func);
    void CrossConnectionInsertTest(KvInsertFunc func, int updateRet);
};

void CcehIndexDmlSt::RestrictionTest(KvInsertFunc func)
{
    char cfgJson[] = R"({"max_record_count":100,"mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t key = 123;
    uint16_t val = 0;
    GmeKvItemT firstKey = {&key, sizeof(uint8_t)};
    GmeKvItemT firstValue = {&val, sizeof(uint16_t)};

    /**
     * @tc.steps: step1. insert a data with null conn
     * @tc.expected: step1. GMERR_UNEXPECTED_NULL_VALUE
     */
    ret = func(nullptr, kvTableName, &firstKey, &firstValue);
    ASSERT_EQ(GMERR_UNEXPECTED_NULL_VALUE, ret);

    char *invalidTable = static_cast<char *>(calloc(MAX_TABLE_NAME_LEN + 1, sizeof(char)));

    /**
     * @tc.steps: step2. insert a data with unExists table.
     * @tc.expected: step2. GMERR_UNDEFINED_TABLE
     */
    ret = sprintf_s(invalidTable, MAX_TABLE_NAME_LEN + 1, "teacher");
    ASSERT_GE(ret, 0);
    ret = func(conn, invalidTable, &firstKey, &firstValue);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);

    /**
     * @tc.steps: step3. insert a data with empty table name.
     * @tc.expected: step3. GMERR_INVALID_NAME
     */
    ret = sprintf_s(invalidTable, MAX_TABLE_NAME_LEN + 1, "\0");
    ASSERT_GE(ret, 0);
    ret = func(conn, invalidTable, &firstKey, &firstValue);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);

    /**
     * @tc.steps: step4. insert a data with overLength table name.
     * @tc.expected: step4. GMERR_NAME_TOO_LONG
     */
    memset_s(invalidTable, MAX_TABLE_NAME_LEN + 1, 'a', MAX_TABLE_NAME_LEN);
    ret = func(conn, invalidTable, &firstKey, &firstValue);
    ASSERT_EQ(GMERR_NAME_TOO_LONG, ret);

    /**
     * @tc.steps: step5. insert a data with table name same as sysTable name.
     * @tc.expected: step5. GMERR_RESTRICT_VIOLATION
     */
    ret = func(conn, "GM_SYS", &firstKey, &firstValue);
    ASSERT_EQ(GMERR_RESTRICT_VIOLATION, ret);

    free(invalidTable);

    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

void CcehIndexDmlSt::InvalidArgsTest(KvInsertFunc func)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    char *invalidKey = static_cast<char *>(calloc(MAX_KEY_LEN + 1, sizeof(char)));
    (void)memset_s(invalidKey, MAX_KEY_LEN + 1, '\0', MAX_KEY_LEN + 1);
    char *invalidValue = static_cast<char *>(calloc(ST_MAX_VALUE_LEN_32M + 1, sizeof(char)));
    (void)memset_s(invalidValue, ST_MAX_VALUE_LEN_32M + 1, '\0', ST_MAX_VALUE_LEN_32M + 1);

    GmeKvItemT invalidKeyItem = {.data = invalidKey, .dataLen = MAX_KEY_LEN + 1};
    GmeKvItemT invalidValueItem = {.data = invalidValue, .dataLen = ST_MAX_VALUE_LEN_32M + 1};
    GmeKvItemT emptyItem = {.data = invalidKey, .dataLen = 1};
    GmeKvItemT nullItem = {.data = nullptr, .dataLen = 1};
    GmeKvItemT zeroLengthItem = {.data = invalidKey, .dataLen = 0};

    /**
     * @tc.steps: step1. insert a data with null key
     * @tc.expected: step1. GMERR_NULL_VALUE_NOT_ALLOWED
     */
    ret = func(conn, kvTableName, nullptr, &emptyItem);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    /**
     * @tc.steps: step2. insert a data with overLength key
     * @tc.expected: step2. GMERR_PROGRAM_LIMIT_EXCEEDED
     */
    ret = func(conn, kvTableName, &invalidKeyItem, &emptyItem);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    /**
     * @tc.steps: step3. insert a data with overLength value
     * @tc.expected: step3. GMERR_PROGRAM_LIMIT_EXCEEDED
     */
    ret = func(conn, kvTableName, &emptyItem, &invalidValueItem);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    /**
     * @tc.steps: step4. insert a data with empty key and value
     * @tc.expected: step4. GMERR_OK
     */
    ret = func(conn, kvTableName, &emptyItem, &emptyItem);
    ASSERT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step5. get data inserted at step 4
     * @tc.expected: step5. GMERR_OK
     */
    ret = memcmp(emptyItem.data, invalidValue, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step6. insert a data with key which data is null
     * @tc.expected: step6. GMERR_NULL_VALUE_NOT_ALLOWED
     */
    ret = func(conn, kvTableName, &nullItem, &emptyItem);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    /**
     * @tc.steps: step7. insert a data with key which dataLen is 0
     * @tc.expected: step7. GMERR_NULL_VALUE_NOT_ALLOWED
     */
    ret = func(conn, kvTableName, &zeroLengthItem, &emptyItem);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    free(invalidKey);
    free(invalidValue);

    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

static int g_tableCursorStub = 0;
static uint64_t g_reservedMemorySize = 0;
Status MiniCreateKvTableCursorMock(MiniCursorCreateParamT *createParam, MiniLabelCursorT **labelCursor)
{
#ifndef NDEBUG
    uint64_t memoryUsage = createParam->memCtx->totalAllocSize;
#endif
    ClearStub(g_tableCursorStub);
    Status ret = MiniCreateKvTableCursor(createParam, labelCursor);
    EXPECT_EQ(ret, GMERR_OK);
#ifndef NDEBUG
    uint64_t curMemoryUsage = createParam->memCtx->totalAllocSize;
    g_reservedMemorySize = curMemoryUsage - memoryUsage;
#endif
    return ret;
}

void CcehIndexDmlSt::InsertMemoryLeakTest(KvInsertFunc func)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t key = 0;
    uint16_t val = 0;
    GmeKvItemT firstKey = {&key, sizeof(uint16_t)};
    GmeKvItemT firstValue = {&val, sizeof(uint16_t)};
    uint64_t memoryUsage = GetMemUsage(conn->miniSession->memCtx->ctxId);

    g_tableCursorStub = SetStubC(
        reinterpret_cast<void *>(MiniCreateKvTableCursor), reinterpret_cast<void *>(MiniCreateKvTableCursorMock));
    EXPECT_GT(g_tableCursorStub, 0);
    /**
     * @tc.steps: step1. insert different data for 100 times, expect no memory usage change
     * @tc.expected: step1. GMERR_OK
     */
    uint16_t count = 100;
    for (uint32_t i = 0; i < count; ++i) {
        ret = func(conn, kvTableName, &firstKey, &firstValue);
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t memoryCurUsage = GetMemUsage(conn->miniSession->memCtx->ctxId);
        EXPECT_EQ(memoryUsage, memoryCurUsage - g_reservedMemorySize);
        key++;
    }

    ClearStub(g_tableCursorStub);
    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

void CcehIndexDmlSt::CrossConnectionInsertTest(KvInsertFunc func, int updateRet)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t key = 123;
    uint16_t val = 0;
    GmeKvItemT firstKey = {&key, sizeof(uint8_t)};
    GmeKvItemT firstValue = {&val, sizeof(uint16_t)};
    /**
     * @tc.steps: step1. Insert a data.
     * @tc.expected: step1. GMERR_OK
     */
    ret = func(conn, kvTableName, &firstKey, &firstValue);
    ASSERT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step2. Create a connection
     * @tc.expected: step2. GMERR_OK
     */
    GmeConnT *conn2 = nullptr;
    ret = GmeOpen(g_cfgPath, GME_OPEN_CREATE, &conn2);
    ASSERT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step2. Create a existed table with conn2
     * @tc.expected: step2. GMERR_DUPLICATE_TABLE
     */
    ret = GmeKvCreateTable(conn2, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);

    /**
     * @tc.steps: step3. update data
     * @tc.expected: step3. GMERR_OK
     */
    ret = func(conn2, kvTableName, &firstKey, &firstValue);
    ASSERT_EQ(updateRet, ret);

    /**
     * @tc.steps: step4. drop this table with conn2
     * @tc.expected: step4. GMERR_OK
     */
    ret = GmeKvDropTable(conn2, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step5. create this table again with conn2
     * @tc.expected: step5. GMERR_OK
     */
    ret = GmeKvCreateTable(conn2, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step6. insert a data to new table with conn1
     * @tc.expected: step6. GMERR_OK
     */
    ret = func(conn, kvTableName, &firstKey, &firstValue);
    ASSERT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step7. update data inserted at step 6 to new table with conn2
     * @tc.expected: step7. GMERR_OK
     */
    ret = func(conn2, kvTableName, &firstKey, &firstValue);
    ASSERT_EQ(updateRet, ret);

    /**
     * @tc.steps: step8. clean up
     * @tc.expected: step8. GMERR_OK
     */
    ret = GmeClose(conn2);
    ASSERT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

HWTEST_F(CcehIndexDmlSt, GmeKvSetTest001, TestSize.Level0)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t count = 100;

    for (int i = 0; i < count; ++i) {
        GmeKvItemT item = {&i, sizeof(i)};
        EXPECT_EQ(GMERR_OK, GmeKvSet(conn, kvTableName, &item, &item));
    }

    for (int i = 0; i < count; ++i) {
        GmeKvItemT key = {&i, sizeof(i)};
        GmeKvItemT value;
        EXPECT_EQ(GMERR_OK, GmeKvGet(conn, kvTableName, &key, &value));
        EXPECT_EQ(i, *(static_cast<int *>(value.data)));
        EXPECT_EQ(sizeof(i), value.dataLen);
        GmeKvFreeItem(&value);
    }
    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

HWTEST_F(CcehIndexDmlSt, GmeKvSetTest002, TestSize.Level0)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t count = 100;

    for (int i = 0; i < count; ++i) {
        GmeKvItemT item = {&i, sizeof(i)};
        EXPECT_EQ(GMERR_OK, GmeKvSet(conn, kvTableName, &item, &item));
    }

    for (int i = 0; i < count; ++i) {
        GmeKvItemT keyItem = {&i, sizeof(i)};
        int valueInt = 2 * i;
        GmeKvItemT valueItem = {&valueInt, sizeof(valueInt)};
        EXPECT_EQ(GMERR_OK, GmeKvSet(conn, kvTableName, &keyItem, &valueItem));
    }

    for (int i = 0; i < count; ++i) {
        GmeKvItemT key = {&i, sizeof(i)};
        GmeKvItemT value;
        EXPECT_EQ(GMERR_OK, GmeKvGet(conn, kvTableName, &key, &value));
        EXPECT_EQ(2 * i, *(static_cast<int *>(value.data)));
        EXPECT_EQ(sizeof(i), value.dataLen);
        GmeKvFreeItem(&value);
    }
    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

HWTEST_F(CcehIndexDmlSt, GmeKvSetTest003, TestSize.Level0)
{
    RestrictionTest(GmeKvSet);
}

HWTEST_F(CcehIndexDmlSt, GmeKvSetTest004, TestSize.Level0)
{
    InvalidArgsTest(GmeKvSet);
}

HWTEST_F(CcehIndexDmlSt, GmeKvSetTest005, TestSize.Level0)
{
    InsertMemoryLeakTest(GmeKvSet);
}

HWTEST_F(CcehIndexDmlSt, GmeKvSetTest006, TestSize.Level0)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t key = 0;
    uint16_t val = 0;
    GmeKvItemT firstKey = {&key, sizeof(uint16_t)};
    GmeKvItemT firstValue = {&val, sizeof(uint16_t)};

    /**
     * @tc.steps: step1. insert a data
     * @tc.expected: step1. GMERR_OK
     */
    ret = GmeKvSet(conn, kvTableName, &firstKey, &firstValue);
    ASSERT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step2. get this data
     * @tc.expected: step2. GMERR_OK
     */
    ret = GmeKvGet(conn, kvTableName, &firstKey, &firstValue);
    (void)GmeKvFreeItem(&firstValue);
    ASSERT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step3. get this data for 100 times, expecting no memory usage change.
     * @tc.expected: step3. GMERR_OK
     */
    uint64_t memoryUsage = GetMemUsage(conn->miniSession->memCtx->ctxId);
    uint16_t count = 100;
    for (uint32_t i = 0; i < count; ++i) {
        ret = GmeKvGet(conn, kvTableName, &firstKey, &firstValue);
        ASSERT_EQ(GMERR_OK, ret);
        uint64_t memoryCurUsage = GetMemUsage(conn->miniSession->memCtx->ctxId);
        EXPECT_EQ(memoryUsage, memoryCurUsage);
        GmeKvFreeItem(&firstValue);
    }
    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

HWTEST_F(CcehIndexDmlSt, GmeKvSetTest007, TestSize.Level1)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t key = 0;
    GmeKvItemT firstKey = {&key, sizeof(uint16_t)};
    uint64_t memoryUsage = GetMemUsage(conn->miniSession->memCtx->ctxId);

    g_tableCursorStub = SetStubC(
        reinterpret_cast<void *>(MiniCreateKvTableCursor), reinterpret_cast<void *>(MiniCreateKvTableCursorMock));
    EXPECT_GT(g_tableCursorStub, 0);
    /**
     * @tc.steps: step1. remove unExist data for 100 times, expecting no memory usage change.
     * @tc.expected: step1. GMERR_OK
     */
    uint16_t count = 100;
    for (uint32_t i = 0; i < count; ++i) {
        ASSERT_EQ(GMERR_OK, GmeKvRemove(conn, kvTableName, &firstKey));
        uint64_t memoryCurUsage = GetMemUsage(conn->miniSession->memCtx->ctxId);
        EXPECT_EQ(memoryUsage, memoryCurUsage - g_reservedMemorySize);
        key++;
    }

    ClearStub(g_tableCursorStub);
    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

HWTEST_F(CcehIndexDmlSt, GmeKvSetTest008, TestSize.Level0)
{
    CrossConnectionInsertTest(GmeKvSet, GMERR_OK);
}

HWTEST_F(CcehIndexDmlSt, GmeKvAddTest001, TestSize.Level0)
{
    RestrictionTest(GmeKvAdd);
}

HWTEST_F(CcehIndexDmlSt, GmeKvAddTest002, TestSize.Level1)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t key = 123;
    uint16_t val = 456;
    GmeKvItemT insertKey = {&key, sizeof(uint8_t)};
    GmeKvItemT insertValue = {&val, sizeof(uint16_t)};

    /**
     * @tc.steps: step1. Insert a data.
     * @tc.expected: step1. GMERR_OK
     */
    ret = GmeKvAdd(conn, kvTableName, &insertKey, &insertValue);
    EXPECT_EQ(GMERR_OK, ret);
    GmeKvItemT getValue = {0};
    EXPECT_EQ(GmeKvGet(conn, kvTableName, &insertKey, &getValue), GMERR_OK);
    EXPECT_EQ(insertValue.dataLen, getValue.dataLen);
    EXPECT_EQ(memcmp(insertValue.data, getValue.data, insertValue.dataLen), 0);
    GmeKvFreeItem(&getValue);

    /**
     * @tc.steps: step2. Insert the same key as step1.
     * @tc.expected: step2. return GMERR_UNIQUE_VIOLATION
     */
    EXPECT_EQ(GmeKvAdd(conn, kvTableName, &insertKey, &insertValue), GMERR_UNIQUE_VIOLATION);

    /**
     * @tc.steps: step3. remove insertKdy, Insert the same key as step1 again.
     * @tc.expected: step3. return GMERR_OK
     */
    EXPECT_EQ(GmeKvRemove(conn, kvTableName, &insertKey), GMERR_OK);
    EXPECT_EQ(GmeKvAdd(conn, kvTableName, &insertKey, &insertValue), GMERR_OK);

    /**
     * @tc.steps: step4. GmeKvSet with val = 78.
     * @tc.expected: step4. return GMERR_OK
     */
    val = 78;
    EXPECT_EQ(GmeKvSet(conn, kvTableName, &insertKey, &insertValue), GMERR_OK);

    /**
     * @tc.steps: step5. get the insertKey.
     * @tc.expected: step5. return GMERR_OK, value = 78
     */
    EXPECT_EQ(GmeKvGet(conn, kvTableName, &insertKey, &getValue), GMERR_OK);
    EXPECT_EQ(insertValue.dataLen, getValue.dataLen);
    EXPECT_EQ(*(static_cast<uint16_t *>(getValue.data)), val);
    GmeKvFreeItem(&getValue);

    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

HWTEST_F(CcehIndexDmlSt, GmeKvAddTest003, TestSize.Level0)
{
    InvalidArgsTest(GmeKvAdd);
}

HWTEST_F(CcehIndexDmlSt, GmeKvAddTest004, TestSize.Level0)
{
    InsertMemoryLeakTest(GmeKvAdd);
}

HWTEST_F(CcehIndexDmlSt, GmeKvAddTest005, TestSize.Level0)
{
    CrossConnectionInsertTest(GmeKvAdd, GMERR_UNIQUE_VIOLATION);
}

HWTEST_F(CcehIndexDmlSt, GmeKvGetTest001, TestSize.Level0)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t key = 123;
    uint16_t val = 456;
    GmeKvItemT insertKey = {&key, sizeof(uint8_t)};
    GmeKvItemT insertValue = {&val, sizeof(uint16_t)};

    ret = GmeKvSet(conn, kvTableName, &insertKey, &insertValue);
    EXPECT_EQ(GMERR_OK, ret);

    GmeKvItemT getValue = {0};
    EXPECT_EQ(GMERR_OK, GmeKvGet(conn, kvTableName, &insertKey, &getValue));
    EXPECT_EQ(insertValue.dataLen, getValue.dataLen);
    EXPECT_EQ(memcmp(insertValue.data, getValue.data, insertValue.dataLen), 0);
    GmeKvFreeItem(&getValue);

    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

HWTEST_F(CcehIndexDmlSt, GmeKvGetTest002, TestSize.Level1)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t key = 123;
    uint16_t val = 456;
    GmeKvItemT firstKey = {&key, sizeof(uint8_t)};
    GmeKvItemT firstValue = {&val, sizeof(uint16_t)};
    EXPECT_EQ(GMERR_OK, GmeKvSet(conn, kvTableName, &firstKey, &firstValue));

    /**
     * @tc.steps: step1. get a data with null conn
     * @tc.expected: step1. GMERR_UNEXPECTED_NULL_VALUE
     */
    ret = GmeKvGet(nullptr, kvTableName, &firstKey, &firstValue);
    ASSERT_EQ(GMERR_UNEXPECTED_NULL_VALUE, ret);

    char *invalidTable = static_cast<char *>(calloc(MAX_TABLE_NAME_LEN + 1, sizeof(char)));

    /**
     * @tc.steps: step2. get a data with unExists table.
     * @tc.expected: step2. GMERR_UNDEFINED_TABLE
     */
    ret = sprintf_s(invalidTable, MAX_TABLE_NAME_LEN + 1, "teacher");
    ASSERT_GE(ret, 0);
    ret = GmeKvGet(conn, invalidTable, &firstKey, &firstValue);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);

    /**
     * @tc.steps: step3. get a data with empty table name.
     * @tc.expected: step3. GMERR_INVALID_NAME
     */
    ret = sprintf_s(invalidTable, MAX_TABLE_NAME_LEN + 1, "\0");
    ASSERT_GE(ret, 0);
    ret = GmeKvGet(conn, invalidTable, &firstKey, &firstValue);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);

    /**
     * @tc.steps: step4. get a data with overLength table name.
     * @tc.expected: step4. GMERR_NAME_TOO_LONG
     */
    memset_s(invalidTable, MAX_TABLE_NAME_LEN + 1, 'a', MAX_TABLE_NAME_LEN);
    ret = GmeKvGet(conn, invalidTable, &firstKey, &firstValue);
    ASSERT_EQ(GMERR_NAME_TOO_LONG, ret);

    /**
     * @tc.steps: step5. get a data with table name same as sysTable name.
     * @tc.expected: step5. GMERR_RESTRICT_VIOLATION
     */
    ret = GmeKvGet(conn, "GM_SYS", &firstKey, &firstValue);
    ASSERT_EQ(GMERR_RESTRICT_VIOLATION, ret);

    free(invalidTable);

    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

HWTEST_F(CcehIndexDmlSt, GmeKvGetTest003, TestSize.Level0)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    char *invalidKey = static_cast<char *>(calloc(MAX_KEY_LEN + 1, sizeof(char)));
    (void)memset_s(invalidKey, MAX_KEY_LEN + 1, '\0', MAX_KEY_LEN + 1);

    GmeKvItemT invalidKeyItem = {.data = invalidKey, .dataLen = MAX_KEY_LEN + 1};
    GmeKvItemT emptyItem = {.data = invalidKey, .dataLen = 1};
    GmeKvItemT nullItem = {.data = nullptr, .dataLen = 1};
    GmeKvItemT zeroLengthItem = {.data = invalidKey, .dataLen = 0};

    /**
     * @tc.steps: step1. get a data with null key
     * @tc.expected: step1. GMERR_NULL_VALUE_NOT_ALLOWED
     */
    ret = GmeKvGet(conn, kvTableName, nullptr, &emptyItem);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    /**
     * @tc.steps: step2. get a data with overLength key
     * @tc.expected: step2. GMERR_PROGRAM_LIMIT_EXCEEDED
     */
    ret = GmeKvGet(conn, kvTableName, &invalidKeyItem, &emptyItem);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    /**
     * @tc.steps: step3. get a data with empty key but not insert
     * @tc.expected: step3. GMERR_NO_DATA
     */
    ret = GmeKvGet(conn, kvTableName, &emptyItem, &emptyItem);
    ASSERT_EQ(GMERR_NO_DATA, ret);

    /**
     * @tc.steps: step4. get a data with key which dataLen is null
     * @tc.expected: step4. GMERR_NULL_VALUE_NOT_ALLOWED
     */
    ret = GmeKvGet(conn, kvTableName, &nullItem, &nullItem);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    /**
     * @tc.steps: step5. get a data with key which dataLen is 0
     * @tc.expected: step5. GMERR_NULL_VALUE_NOT_ALLOWED
     */
    ret = GmeKvGet(conn, kvTableName, &zeroLengthItem, &zeroLengthItem);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    /**
     * @tc.steps: step6. get a data with null value
     * @tc.expected: step6. GMERR_NULL_VALUE_NOT_ALLOWED
     */
    ret = GmeKvGet(conn, kvTableName, &emptyItem, nullptr);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    free(invalidKey);

    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

HWTEST_F(CcehIndexDmlSt, GmeKvRemoveTest001, TestSize.Level0)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t count = 100;

    for (int i = 0; i < count; ++i) {
        GmeKvItemT item = {&i, sizeof(i)};
        EXPECT_EQ(GMERR_OK, GmeKvSet(conn, kvTableName, &item, &item));
    }

    for (int i = 0; i < count; ++i) {
        GmeKvItemT keyItem = {&i, sizeof(i)};
        EXPECT_EQ(GMERR_OK, GmeKvRemove(conn, kvTableName, &keyItem));
    }

    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

HWTEST_F(CcehIndexDmlSt, GmeKvRemoveTest002, TestSize.Level0)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    int i = 0;
    GmeKvItemT keyItem = {&i, sizeof(i)};
    EXPECT_EQ(GMERR_OK, GmeKvSet(conn, kvTableName, &keyItem, &keyItem));

    ret = GmeKvRemove(nullptr, kvTableName, &keyItem);
    ASSERT_EQ(GMERR_UNEXPECTED_NULL_VALUE, ret);

    char *invalidTable = static_cast<char *>(calloc(MAX_TABLE_NAME_LEN + 1, sizeof(char)));

    /**
     * @tc.steps: step2. remove a data with unExists table.
     * @tc.expected: step2. GMERR_UNDEFINED_TABLE
     */
    ret = sprintf_s(invalidTable, MAX_TABLE_NAME_LEN + 1, "teacher");
    ASSERT_GE(ret, 0);
    ret = GmeKvRemove(conn, invalidTable, &keyItem);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);

    /**
     * @tc.steps: step3. remove a data with empty table name.
     * @tc.expected: step3. GMERR_INVALID_NAME
     */
    ret = sprintf_s(invalidTable, MAX_TABLE_NAME_LEN + 1, "\0");
    ASSERT_GE(ret, 0);
    ret = GmeKvRemove(conn, invalidTable, &keyItem);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);

    /**
     * @tc.steps: step4. remove a data with overLength table name.
     * @tc.expected: step4. GMERR_NAME_TOO_LONG
     */
    memset_s(invalidTable, MAX_TABLE_NAME_LEN + 1, 'a', MAX_TABLE_NAME_LEN);
    ret = GmeKvRemove(conn, invalidTable, &keyItem);
    ASSERT_EQ(GMERR_NAME_TOO_LONG, ret);

    /**
     * @tc.steps: step5. remove a data with table name same as sysTable name.
     * @tc.expected: step5. GMERR_RESTRICT_VIOLATION
     */
    ret = GmeKvRemove(conn, "GM_SYS", &keyItem);
    ASSERT_EQ(GMERR_RESTRICT_VIOLATION, ret);

    free(invalidTable);

    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

HWTEST_F(CcehIndexDmlSt, GmeKvRemoveTest003, TestSize.Level1)
{
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";

    char kvTableName[] = "student";
    int32_t ret = GmeKvCreateTable(conn, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    int i = 0;
    GmeKvItemT keyItem = {&i, sizeof(i)};
    EXPECT_EQ(GMERR_OK, GmeKvSet(conn, kvTableName, &keyItem, &keyItem));

    char *invalidKey = static_cast<char *>(calloc(MAX_KEY_LEN + 1, sizeof(char)));
    (void)memset_s(invalidKey, MAX_KEY_LEN + 1, '\0', MAX_KEY_LEN + 1);

    GmeKvItemT invalidKeyItem = {.data = invalidKey, .dataLen = MAX_KEY_LEN + 1};
    GmeKvItemT emptyItem = {.data = invalidKey, .dataLen = 1};
    GmeKvItemT nullItem = {.data = nullptr, .dataLen = 1};
    GmeKvItemT zeroLengthItem = {.data = invalidKey, .dataLen = 0};

    /**
     * @tc.steps: step1. remove a data with null key
     * @tc.expected: step1. GMERR_NULL_VALUE_NOT_ALLOWED
     */
    ret = GmeKvRemove(conn, kvTableName, nullptr);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    /**
     * @tc.steps: step2. remove a data with overLength key
     * @tc.expected: step2. GMERR_PROGRAM_LIMIT_EXCEEDED
     */
    ret = GmeKvRemove(conn, kvTableName, &invalidKeyItem);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    /**
     * @tc.steps: step3. remove a data with empty key
     * @tc.expected: step3. GMERR_OK
     */
    ret = GmeKvRemove(conn, kvTableName, &emptyItem);
    ASSERT_EQ(GMERR_OK, ret);

    /**
     * @tc.steps: step4. remove a data with key which dataLen is null
     * @tc.expected: step4. GMERR_NULL_VALUE_NOT_ALLOWED
     */
    ret = GmeKvRemove(conn, kvTableName, &nullItem);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    /**
     * @tc.steps: step5. remove a data with key which dataLen is 0
     * @tc.expected: step5. GMERR_NULL_VALUE_NOT_ALLOWED
     */
    ret = GmeKvRemove(conn, kvTableName, &zeroLengthItem);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    free(invalidKey);

    EXPECT_EQ(GMERR_OK, GmeKvDropTable(conn, kvTableName));
}

static void StCheckDataWithKey(
    GmeConnT *conn, const char *kvTableName, const GmeKvItemT *key, const GmeKvItemT *expectValue)
{
    GmeKvItemT retValue = {nullptr, 0};
    ASSERT_EQ(GmeKvGet(conn, kvTableName, key, &retValue), GMERR_OK);
    if (expectValue == nullptr || expectValue->data == nullptr) {
        ASSERT_EQ(retValue.data, nullptr);
    } else {
        ASSERT_EQ(retValue.dataLen, expectValue->dataLen);
        ASSERT_EQ(memcmp(retValue.data, expectValue->data, retValue.dataLen), 0);
    }

    ASSERT_EQ(GmeKvFreeItem(&retValue), GMERR_OK);
}

/**
 * @tc.name: kvOperationSetBoundaryKeyValue
 * @tc.desc: test to set key/value with boundary size
 * @tc.type: FUNC
 * @tc.author: zhujinlin
 */
HWTEST_F(CcehIndexDmlSt, kvOperationSetBoundaryKeyValue, TestSize.Level4)
{
    /**
     * @tc.steps: step1. Preset table used to insert data.
     * @tc.expected: step1. Execute successfully
     */
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";
    char kvTableName[] = "setkeyvalue";
    EXPECT_EQ(GmeKvCreateTable(conn, kvTableName, cfgJson), GMERR_OK);
    /**
     * @tc.steps: step2. Insert data with biggest key size.
     * @tc.expected: step2. Execute successfully
     */
    uint32_t utMaxKvKeyLen = 1024;
    const int utMaxKvValueLen = 32 * 1024 * 1024;
    std::string biggestKeyStr(utMaxKvKeyLen, '1');
    GmeKvItemT insertBiggestKey = {static_cast<void *>(const_cast<char *>(biggestKeyStr.c_str())), utMaxKvKeyLen};
    std::string valueStr(utMaxKvValueLen - 1, '1');  // value size approaching biggest but not
    GmeKvItemT insertValue = {static_cast<void *>(const_cast<char *>(valueStr.c_str())), utMaxKvValueLen - 1};
    EXPECT_EQ(GmeKvSet(conn, kvTableName, &insertBiggestKey, &insertValue), GMERR_OK);
    StCheckDataWithKey(conn, kvTableName, &insertBiggestKey, &insertValue);
    /**
     * @tc.steps: step3. Insert data with biggest value size.
     * @tc.expected: step3. Execute successfully
     */
    std::string biggestValueStr(utMaxKvValueLen, '2');
    GmeKvItemT insertBiggestValue = {static_cast<void *>(const_cast<char *>(biggestValueStr.c_str())), utMaxKvValueLen};
    std::string keyStr(utMaxKvKeyLen - 1, '2');  // key size approaching biggest but not
    GmeKvItemT insertKey = {static_cast<void *>(const_cast<char *>(keyStr.c_str())), utMaxKvKeyLen};
    EXPECT_EQ(GmeKvSet(conn, kvTableName, &insertKey, &insertBiggestValue), GMERR_OK);
    StCheckDataWithKey(conn, kvTableName, &insertKey, &insertBiggestValue);
    /**
     * @tc.steps: step4. Insert data with biggest key&value size.
     * @tc.expected: step4. Execute successfully
     */
    EXPECT_EQ(GmeKvSet(conn, kvTableName, &insertBiggestKey, &insertBiggestValue), GMERR_OK);
    StCheckDataWithKey(conn, kvTableName, &insertBiggestKey, &insertBiggestValue);
}

/**
 * @tc.name: kvOperationAddBoundaryKeyValue
 * @tc.desc: test to set key/value with boundary size
 * @tc.type: FUNC
 * @tc.author: zhujinlin
 */
HWTEST_F(CcehIndexDmlSt, kvOperationAddBoundaryKeyValue, TestSize.Level4)
{
    /**
     * @tc.steps: step1. Preset table used to insert data.
     * @tc.expected: step1. Execute successfully
     */
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";
    char kvTableName[] = "addkeyvalue";
    EXPECT_EQ(GmeKvCreateTable(conn, kvTableName, cfgJson), GMERR_OK);
    /**
     * @tc.steps: step2. Insert data with biggest key size.
     * @tc.expected: step2. Execute successfully
     */
    const int utMaxKvKeyLen = 1024;
    const int utMaxKvValueLen = 32 * 1024 * 1024;
    std::string biggestKeyStr(utMaxKvKeyLen, '1');
    GmeKvItemT insertBiggestKey = {static_cast<void *>(const_cast<char *>(biggestKeyStr.c_str())), utMaxKvKeyLen};
    std::string valueStr(utMaxKvValueLen - 1, '1');  // value size approaching biggest but not
    GmeKvItemT insertValue = {static_cast<void *>(const_cast<char *>(valueStr.c_str())), utMaxKvValueLen - 1};
    EXPECT_EQ(GmeKvAdd(conn, kvTableName, &insertBiggestKey, &insertValue), GMERR_OK);
    StCheckDataWithKey(conn, kvTableName, &insertBiggestKey, &insertValue);
    EXPECT_EQ(GmeKvRemove(conn, kvTableName, &insertBiggestKey), GMERR_OK);
    /**
     * @tc.steps: step3. Insert data with biggest value size.
     * @tc.expected: step3. Execute successfully
     */
    std::string biggestValueStr(utMaxKvValueLen, '2');
    GmeKvItemT insertBiggestValue = {static_cast<void *>(const_cast<char *>(biggestValueStr.c_str())), utMaxKvValueLen};
    std::string keyStr(utMaxKvKeyLen - 1, '2');  // key size approaching biggest but not
    GmeKvItemT insertKey = {static_cast<void *>(const_cast<char *>(keyStr.c_str())), utMaxKvKeyLen - 1};
    EXPECT_EQ(GmeKvAdd(conn, kvTableName, &insertKey, &insertBiggestValue), GMERR_OK);
    StCheckDataWithKey(conn, kvTableName, &insertKey, &insertBiggestValue);
    EXPECT_EQ(GmeKvRemove(conn, kvTableName, &insertKey), GMERR_OK);
    /**
     * @tc.steps: step4. Insert data with biggest key&value size.
     * @tc.expected: step4. Execute successfully
     */
    EXPECT_EQ(GmeKvAdd(conn, kvTableName, &insertBiggestKey, &insertBiggestValue), GMERR_OK);
    StCheckDataWithKey(conn, kvTableName, &insertBiggestKey, &insertBiggestValue);
    EXPECT_EQ(GmeKvRemove(conn, kvTableName, &insertBiggestKey), GMERR_OK);
}

/**
 * @tc.name: kvInsert1GBWithDropTable
 * @tc.desc: create a table and insert data which are 1GB in all, and then drop table
 * @tc.type: FUNC
 * @tc.author: chentingrong
 */
HWTEST_F(CcehIndexDmlSt, kvInsert1GBWithDropTable, TestSize.Level4)
{
    /**
     * @tc.steps: step1. Preset table used to insert data.
     * @tc.expected: step1. Execute successfully
     */
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";
    char kvTableName[] = "add1GBdata1";
    ASSERT_EQ(GmeKvCreateTable(conn, kvTableName, cfgJson), GMERR_OK);
    uint32_t dataNum = 32;  // 1GB data in all

    /**
     * @tc.steps: step2. Insert 1GB data.
     * @tc.expected: step2. Execute successfully
     */
    for (uint32_t i = 0; i < dataNum; ++i) {
        uint32_t valueLen = 32 * 1024 * 1024;
        std::string keyStr(std::to_string(i));
        GmeKvItemT key = {static_cast<void *>(const_cast<char *>(keyStr.c_str())), (uint32_t)keyStr.size()};
        std::string valueStr(valueLen, '1');  // value size approaching biggest but not
        GmeKvItemT insertValue = {static_cast<void *>(const_cast<char *>(valueStr.c_str())), valueLen};
        ASSERT_EQ(GmeKvAdd(conn, kvTableName, &key, &insertValue), GMERR_OK) << ", test index:" << i;
        StCheckDataWithKey(conn, kvTableName, &key, &insertValue);
    }

    /**
     * @tc.steps: step3. drop table
     * @tc.expected: step3. Execute successfully
     */
    ASSERT_EQ(GmeKvDropTable(conn, kvTableName), GMERR_OK);
}

/**
 * @tc.name: kvInsert1GBWithDeleteData
 * @tc.desc: create a table and insert data which are 1GB in all, and then delete data
 * @tc.type: FUNC
 * @tc.author: chentingrong
 */
HWTEST_F(CcehIndexDmlSt, kvInsert1GBWithDeleteData, TestSize.Level4)
{
    /**
     * @tc.steps: step1. Preset table used to insert data.
     * @tc.expected: step1. Execute successfully
     */
    char cfgJson[] = R"({"max_record_count":100, "mode":"kv", "indextype":"hash"})";
    char kvTableName[] = "add1GBdata2";
    ASSERT_EQ(GmeKvCreateTable(conn, kvTableName, cfgJson), GMERR_OK);
    uint32_t dataNum = 32;  // 1GB data in all

    /**
     * @tc.steps: step2. Insert 1GB data.
     * @tc.expected: step2. Execute successfully
     */
    std::vector<GmeKvItemT> keyArr;
    for (uint32_t i = 0; i < dataNum; ++i) {
        uint32_t valueLen = 32 * 1024 * 1024;
        std::string keyStr(std::to_string(i));
        GmeKvItemT key = {static_cast<void *>(const_cast<char *>(keyStr.c_str())), (uint32_t)keyStr.size()};
        std::string valueStr(valueLen, '1');  // value size approaching biggest but not
        GmeKvItemT insertValue = {static_cast<void *>(const_cast<char *>(valueStr.c_str())), valueLen};
        ASSERT_EQ(GmeKvAdd(conn, kvTableName, &key, &insertValue), GMERR_OK) << ", test index:" << i;
        StCheckDataWithKey(conn, kvTableName, &key, &insertValue);
        keyArr.push_back(key);
    }

    /**
     * @tc.steps: step3. delete all data
     * @tc.expected: step3. Execute successfully
     */
    for (uint32_t i = 0; i < keyArr.size(); ++i) {
        ASSERT_EQ(GmeKvRemove(conn, kvTableName, &keyArr[i]), GMERR_OK) << ", test index:" << i;
    }

    /**
     * @tc.steps: step4. drop table
     * @tc.expected: step4. Execute successfully
     */
    ASSERT_EQ(GmeKvDropTable(conn, kvTableName), GMERR_OK);
}

static EmbeddedTrxControlT *GetEmbeddedTrxMgr(MemUtilsT *memUtils, TrxMgrT *trxMgr)
{
    return (EmbeddedTrxControlT *)DbWrappedShmPtrToAddr(memUtils, trxMgr->embeddedTrxMgrShm);
}

static Status SeReleaseBuffPoolPagesMockForSameTable(SeRunCtxHdT seRunCtx, bool isReadOnly)
{
    SeInstanceT *seIns = static_cast<SeInstanceT *>(seRunCtx->seIns);
    TrxMgrT *trxMgr = static_cast<TrxMgrT *>(DbShmPtrToAddr(seIns->trxMgrShm));
    if (SECUREC_UNLIKELY(trxMgr == nullptr)) {
        SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "Unsucc to convert shmPtr to pointer.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    EmbeddedTrxControlT *miniTrxMgr = GetEmbeddedTrxMgr(&seIns->memUtils, trxMgr);
    if (miniTrxMgr == nullptr) {
        return GMERR_OK;
    }
    uint32_t pageReserve = REDO_ATOMIC_MAX_PAGE_COUNT;
    if (isReadOnly) {
        pageReserve = 8;  // 读操作预留8个页
    }
    // only one thread can be here
    EXPECT_EQ(miniTrxMgr->curPageUsage, pageReserve);
    Status ret = DbInterProcLockLock(&seIns->seLockFn, &(miniTrxMgr->lock));
    if (ret != GMERR_OK) {
        return ret;
    }
    miniTrxMgr->curPageUsage -= pageReserve;
    DbInterProcLockUnlock(&seIns->seLockFn, &(miniTrxMgr->lock));
    return GMERR_OK;
}

typedef struct SetArg {
    GmeConnT *conn;
    int thrIndex;
} SetArgT;

static void *ThrDoKvSetForSameTable(void *args)
{
    int ret = 0;
    SetArgT *setArg = static_cast<SetArgT *>(args);
    int32_t key = setArg->thrIndex;
    int32_t value = 2 * setArg->thrIndex;
    GmeKvItemT keyItem = {&key, sizeof(key)};
    GmeKvItemT valueItem = {&value, sizeof(value)};
    ret = GmeKvSet(setArg->conn, "student", &keyItem, &valueItem);
    EXPECT_EQ(GMERR_OK, ret);
    return nullptr;
}

/**
 * @tc.name: TrxConcurrencyControlTest001
 * @tc.desc: Concurrent set kv to the same table, same label lock, therefore no concurrence
 * @tc.type: FUNC
 * @tc.author: sunhan
 */
HWTEST_F(CcehIndexDmlSt, TrxConcurrencyControlTest001, TestSize.Level2)
{
    char kvTableName[] = "student";
    EXPECT_EQ(GmeKvCreateTable(conn, kvTableName, nullptr), GMERR_OK);
    const int thrNum = 10;
    SetArgT setArg[thrNum];
    pthread_t thrSetKv[thrNum];
    for (int i = 0; i < thrNum; i++) {
        setArg[i].thrIndex = i;
        EXPECT_EQ(GMERR_OK, GmeOpen(g_cfgPath, GME_OPEN_CREATE, &setArg[i].conn));
    }
    int stub = SetStubC(reinterpret_cast<void *>(SeReleaseBuffPoolPages),
        reinterpret_cast<void *>(SeReleaseBuffPoolPagesMockForSameTable));
    EXPECT_GT(stub, 0);
    for (int i = 0; i < thrNum; i++) {
        ASSERT_EQ(pthread_create(&thrSetKv[i], nullptr, ThrDoKvSetForSameTable, (void *)&setArg[i]), 0);
    }
    for (int i = 0; i < thrNum; i++) {
        pthread_join(thrSetKv[i], nullptr);
    }
    ClearStub(stub);
    for (int i = 0; i < thrNum; i++) {
        EXPECT_EQ(GMERR_OK, GmeClose(setArg[i].conn));
    }
}

uint32_t GetThreadCount(SeInstanceT *seIns)
{
    DB_POINTER(seIns);
    return seIns->bufPoolMgr->bufPool->capacity / REDO_ATOMIC_MAX_PAGE_COUNT;
}

static pthread_mutex_t g_mutexLock;
static int g_maxCurrentCnt = 0;
static int g_currentCnt = 0;
static bool g_testAns = true;
static Status SeReleaseBuffPoolPagesMockForDiffTable(SeRunCtxHdT seRunCtx, bool isReadOnly)
{
    pthread_mutex_lock(&g_mutexLock);
    g_currentCnt++;
    if (g_currentCnt > g_maxCurrentCnt) {
        g_testAns = false;
    }
    pthread_mutex_unlock(&g_mutexLock);
    sleep(1);
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    TrxMgrT *trxMgr = (TrxMgrT *)DbShmPtrToAddr(seIns->trxMgrShm);
    if (SECUREC_UNLIKELY(trxMgr == nullptr)) {
        SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "Unsucc to convert shmPtr to pointer.");
        return DbGetExternalErrno(MEMORY_OPERATE_FAILED_INTER);
    }
    EmbeddedTrxControlT *miniTrxMgr = GetEmbeddedTrxMgr(&seIns->memUtils, trxMgr);
    if (miniTrxMgr == nullptr) {
        return GMERR_OK;
    }
    EXPECT_GE(miniTrxMgr->curPageUsage, REDO_ATOMIC_MAX_PAGE_COUNT);
    Status ret = DbInterProcLockLock(&seIns->seLockFn, &(miniTrxMgr->lock));
    if (ret != GMERR_OK) {
        return ret;
    }

    miniTrxMgr->curPageUsage -= REDO_ATOMIC_MAX_PAGE_COUNT;
    DbInterProcLockUnlock(&seIns->seLockFn, &(miniTrxMgr->lock));
    pthread_mutex_lock(&g_mutexLock);
    g_currentCnt--;
    pthread_mutex_unlock(&g_mutexLock);
    return GMERR_OK;
}

static void *ThrDoKvSetForDiffTables(void *args)
{
    int ret = 0;
    int thrIndex = ((SetArgT *)args)->thrIndex;
    string tableName = "table" + std::to_string(thrIndex);
    int32_t key = thrIndex;
    int32_t value = 2 * thrIndex;
    GmeKvItemT keyItem = {&key, sizeof(key)};
    GmeKvItemT valueItem = {&value, sizeof(value)};
    ret = GmeKvSet(((SetArgT *)args)->conn, tableName.c_str(), &keyItem, &valueItem);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmeClose(((SetArgT *)args)->conn);
    EXPECT_EQ(GMERR_OK, ret);
    return 0;
}

/**
 * @tc.name: TrxConcurrencyControlTest002
 * @tc.desc: Concurrent set kv to the diff tables, it will be jamed after bfp can not be reserved
 * @tc.type: FUNC
 * @tc.author: sunhan
 */
HWTEST_F(CcehIndexDmlSt, TrxConcurrencyControlTest002, TestSize.Level4)
{
    ASSERT_EQ(pthread_mutex_init(&g_mutexLock, nullptr), 0);
    int thrNum = 10;
    pthread_t thrKvSet[thrNum];
    string tableName[thrNum];
    uint32_t concurrentCnt = GetThreadCount((SeInstanceT *)conn->miniSession->seRunCtx->seIns);
    g_maxCurrentCnt = concurrentCnt;
    EXPECT_GT(thrNum, concurrentCnt);
    SetArgT setArg[thrNum];
    for (int i = 0; i < thrNum; i++) {
        tableName[i] = "table" + std::to_string(i);
        ASSERT_EQ(GmeKvCreateTable(conn, tableName[i].c_str(), nullptr), GMERR_OK);
        setArg[i].thrIndex = i;
    }
    for (int i = 0; i < thrNum; i++) {
        EXPECT_EQ(GMERR_OK, GmeOpen(g_cfgPath, GME_OPEN_CREATE, &setArg[i].conn));
    }
    int stub = SetStubC((void *)SeReleaseBuffPoolPages, (void *)SeReleaseBuffPoolPagesMockForDiffTable);
    EXPECT_GT(stub, 0);
    for (int i = 0; i < thrNum; i++) {
        setArg[i].thrIndex = i;
        ASSERT_EQ(pthread_create(&thrKvSet[i], nullptr, ThrDoKvSetForDiffTables, (void *)&setArg[i]), 0);
    }

    for (int i = 0; i < thrNum; i++) {
        pthread_join(thrKvSet[i], nullptr);
    }
    EXPECT_TRUE(g_testAns);
    ClearStub(stub);

    ASSERT_EQ(pthread_mutex_destroy(&g_mutexLock), 0);
}
