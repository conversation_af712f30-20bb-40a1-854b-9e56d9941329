/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 * File Name: db_file_tool.cpp
 * Description: implement of file tool
 * Author:
 * Create: 2023-12-15
 */

#include <dirent.h>
#include <cstdio>
#include <cstring>
#include <sys/stat.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "db_file_tool.h"

int DbFileTool::GrdRemoveDirAw(const char *dir)
{
    if (dir == nullptr) {
        GTEST_LOG_(WARNING) << "dir is null.";
        return -1;
    }
    if (access(dir, F_OK) != 0) {
        return 0;
    }
    struct stat dirStat {};
    if (stat(dir, &dirStat) < 0) {
        GTEST_LOG_(WARNING) << "get directory stat error.";
        return -1;
    }

    char dirName[PATH_MAX];
    DIR *dirPtr = nullptr;
    struct dirent *dr = nullptr;
    if (S_ISREG(dirStat.st_mode)) {  // normal file
        remove(dir);
    } else if (S_ISDIR(dirStat.st_mode)) {
        dirPtr = opendir(dir);
        while ((dr = readdir(dirPtr)) != nullptr) {
            // ignore . and ..
            if ((strcmp(".", dr->d_name) == 0) || (strcmp("..", dr->d_name) == 0)) {
                continue;
            }
            sprintf_s(dirName, PATH_MAX, "%s/%s", dir, dr->d_name);
            GrdRemoveDirAw(dirName);
        }
        closedir(dirPtr);
        rmdir(dir);  // remove empty dir
    } else {
        GTEST_LOG_(WARNING) << "unknown file type!";
    }
    return 0;
}

int DbFileTool::GrdMakeDirAw(const char *dir)
{
    return DbMakeDirectory(dir, PERM_USRRWX);
}

struct stat DbFileTool::GrdGetFileInfo(const char *fileName)
{
    struct stat statbuf;
    memset_s(&statbuf, sizeof(struct stat), 0, sizeof(struct stat));
    if (fileName == nullptr) {
        return statbuf;
    }

    char realPath[PATH_MAX] = {0};
    Status ret = DbGetRealPath(fileName, realPath, PATH_MAX);
    if (ret != GMERR_OK) {
        return statbuf;
    }

    stat(realPath, &statbuf);
    return statbuf;
}
