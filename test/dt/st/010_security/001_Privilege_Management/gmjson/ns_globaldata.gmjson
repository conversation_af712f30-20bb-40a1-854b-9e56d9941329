{"version": "2.0", "type": "record", "name": "ns_globaldata", "config": {"check_validity": false}, "max_record_count": 64, "fields": [{"name": "vrId", "type": "uint32"}, {"name": "flowType", "type": "uint8"}, {"name": "mplsAware", "type": "uint32"}, {"name": "time", "type": "uint32"}, {"name": "intervalTime", "type": "uint32"}, {"name": "version", "type": "uint32"}, {"name": "indexSwitch", "type": "uint32"}, {"name": "tcpFlag", "type": "uint32"}], "keys": [{"name": "nsGlobal", "index": {"type": "primary"}, "node": "ns_globaldata", "fields": ["vrId", "flowType"], "constraints": {"unique": true}}]}