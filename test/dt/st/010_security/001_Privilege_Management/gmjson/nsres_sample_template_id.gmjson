{"version": "2.0", "type": "record", "name": "nsres_sample_template_id", "config": {"check_validity": false}, "max_record_count": 131072, "fields": [{"name": "vrid", "type": "uint32"}, {"name": "ifIndex", "type": "uint32"}, {"name": "direct", "type": "uint8"}, {"name": "resvKey0", "type": "uint8"}, {"name": "resvKey1", "type": "uint16"}, {"name": "enableMap", "type": "uint64"}, {"name": "sampleValue", "type": "uint16"}, {"name": "ipv6SampleValue", "type": "uint16"}, {"name": "sampleResv0", "type": "uint16"}, {"name": "sampleResv1", "type": "uint16"}, {"name": "index", "type": "uint32"}, {"name": "mod", "type": "uint32"}, {"name": "TB", "type": "uint16"}, {"name": "TP", "type": "uint16"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nsres_sample_template_id", "fields": ["vrid", "ifIndex", "direct", "resvKey0", "resvKey1"], "constraints": {"unique": true}}, {"name": "sample_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "nsres_sample_template_id", "fields": ["vrid", "enableMap", "sampleValue", "ipv6SampleValue", "sampleResv0", "sampleResv1"], "constraints": {"unique": false}}]}