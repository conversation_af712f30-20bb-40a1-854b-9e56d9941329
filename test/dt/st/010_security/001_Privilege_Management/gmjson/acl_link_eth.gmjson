{"comment": "二层ACL表", "version": "2.0", "type": "record", "name": "acl_link_eth", "config": {"check_validity": true}, "max_record_count": 128000, "fields": [{"name": "vrid", "type": "uint32", "comment": "虚拟路由器的索引"}, {"name": "acl_index", "type": "uint32", "comment": "ACL的索引"}, {"name": "acl_group_id", "type": "uint32", "comment": "ACLGroup的索引"}, {"name": "vrvrf_index", "type": "uint32", "nullable": true, "comment": "平台VrVrf的索引"}, {"name": "acl_priority", "type": "uint32", "comment": "ACL的优先级"}, {"name": "cond_mask", "type": "uint32", "comment": "条件掩码"}, {"name": "time_range_id", "type": "uint32", "nullable": true, "comment": "Time Range的索引"}, {"name": "time_range_status", "type": "uint8", "nullable": true, "comment": "Time Range的激活状态"}, {"name": "acl_action_type", "type": "uint8", "comment": "ACL动作类型"}, {"name": "frame_type", "type": "uint16", "nullable": true, "comment": "桢类型"}, {"name": "frame_type_mask", "type": "uint16", "nullable": true, "comment": "桢类型掩码"}, {"name": "src_mac", "type": "fixed", "nullable": true, "size": 6, "comment": "源MAC地址"}, {"name": "src_mac_mask", "type": "fixed", "nullable": true, "size": 6, "comment": "源MAC地址掩码"}, {"name": "dest_mac", "type": "fixed", "nullable": true, "size": 6, "comment": "目的MAC地址"}, {"name": "dest_mac_mask", "type": "fixed", "nullable": true, "size": 6, "comment": "目的MAC地址掩码"}, {"name": "dot1p", "type": "uint8", "nullable": true, "comment": "dot1p优先级"}, {"name": "ce_dotp", "type": "uint8", "nullable": true, "comment": "ce_dotp优先级"}, {"name": "vlan_id", "type": "uint16", "nullable": true, "comment": "vlan的索引"}, {"name": "vlan_mask", "type": "uint16", "nullable": true, "comment": "vlan的掩码"}, {"name": "ce_vlan_id", "type": "uint16", "nullable": true, "comment": "ce_vlan的索引"}, {"name": "ce_vlan_mask", "type": "uint16", "nullable": true, "comment": "ce_vlan的掩码"}, {"name": "double_tag", "type": "uint8", "nullable": true, "comment": "双标签"}, {"name": "encap_type", "type": "uint8", "nullable": true, "comment": "封装类型"}, {"name": "app_source_id", "type": "uint32", "comment": "生产者源标识，对应VRP8 hSrcPid字段"}, {"name": "app_serial_id", "type": "uint32", "comment": "生产者源标识"}, {"name": "app_obj_id", "type": "uint64", "comment": "用于记录的生命周期管理，即使KEY和DATA相同，但删除后再添加时这个ID也会不同，具体使用场景不明确，暂时保留不用"}, {"name": "app_version", "type": "uint32", "comment": "记录版本号，用于跟踪同一条记录的变化情形，具体使用场景不明确，暂时保留不用"}], "keys": [{"name": "acl_link_eth_pk", "index": {"type": "primary"}, "node": "acl_link_eth", "fields": ["vrid", "acl_index", "acl_group_id"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "acl_link_eth_pri_pk", "index": {"type": "local"}, "node": "acl_link_eth", "fields": ["vrid", "acl_group_id", "acl_priority"], "comment": "根据vrid、acl_group_id和acl_priority索引"}]}