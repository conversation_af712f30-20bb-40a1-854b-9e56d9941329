/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: st file for Dropping EDGE defined by PATH DSL
 * Author: gongsai
 * Create: 2022-9-19
 */
#include <functional>
#include <stdio.h>
#include "gmc_gql.h"
#include "client_option.h"
#include "client_common_st.h"
#include "fes_common.h"

using namespace std;

const uint32_t WAIT_TIME = 1000;  // ms
const uint32_t STR_MAX_LEN = 200;
const uint32_t MAX_TEST_TIME = 100;
#define DSL_EDGE_TEMPLATE_STR_LEN 1024
#define EDGE_LABEL_LIMIT_NUM 10000

class PathDropEdge : public testing::Test {
protected:
    void InitClient()
    {
        EXPECT_EQ(GMERR_OK, GmcInit());
    }

    static void SetUpTestCase()
    {
        // FES PATH需要硬件加速提供的hash
        StartDbServer((char *)"gmserver_gql.ini");
        ImportAllowList();
        st_clt_init();
    }

    static void TearDownTestCase()
    {
        st_clt_uninit();
        ShutDownDbServer();
    }

    virtual void SetUp()
    {
        InitClient();
        CreateSyncConnectionAndStmt(&conn, &stmt);
    }

    virtual void TearDown()
    {
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
    }

    GmcConnT *conn;
    GmcStmtT *stmt;
};

// 创建超过10000个边标签(元数据存储上限)
TEST_F(PathDropEdge, drop_edge_001)
{
    const char *createVertexLabel = R"(
        CREATE VERTEXLABEL v1 (
            uiVsIndex UINT32,
            uiLdpMode UINT32,
            uiSuffixFlag UINT32,
            hSrcPid UINT32,
            uiVerNo UINT32
            PRIMARY INDEX pk(uiVsIndex) HASH_CAP 100
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createVertexLabel));
    const char *createVertexLabel2 = R"(
        CREATE VERTEXLABEL v2 (
            uiVsIndex UINT32,
            uiLdpMode UINT32,
            uiSuffixFlag UINT32,
            hSrcPid UINT32,
            uiVerNo UINT32
            PRIMARY INDEX pk(uiVsIndex) HASH_CAP 100
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createVertexLabel2));
    // 创建超过10000个边标签
    const char *templateDsl = R"(
        CREATE EDGE e%d FROM v1 TO v2
        WHERE v1.uiVsIndex == v2.uiVsIndex;
    )";
    char dsl[DSL_EDGE_TEMPLATE_STR_LEN];
    memset_s(dsl, DSL_EDGE_TEMPLATE_STR_LEN, '\0', DSL_EDGE_TEMPLATE_STR_LEN);
    for (uint32_t i = 1; i <= EDGE_LABEL_LIMIT_NUM; ++i) {
        sprintf_s(dsl, DSL_EDGE_TEMPLATE_STR_LEN, templateDsl, i);
        EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dsl));
    }
    // 第10001个边标签
    sprintf_s(dsl, DSL_EDGE_TEMPLATE_STR_LEN, templateDsl, DSL_EDGE_TEMPLATE_STR_LEN + 1);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, GmcExecGql(stmt, dsl));
    // drop掉成功的10000个边标签
    const char *templateDsl2 = R"(
        DROP EDGE e%d;
    )";
    memset_s(dsl, DSL_EDGE_TEMPLATE_STR_LEN, '\0', DSL_EDGE_TEMPLATE_STR_LEN);
    for (uint32_t i = 1; i <= EDGE_LABEL_LIMIT_NUM; ++i) {
        sprintf_s(dsl, DSL_EDGE_TEMPLATE_STR_LEN, templateDsl2, i);
        EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dsl));
    }
    // drop掉创建的点标签
    const char *dropCreateVertexLabel = R"(
        DROP VERTEXLABEL v1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropCreateVertexLabel));
    const char *dropCreateVertexLabel1 = R"(
        DROP VERTEXLABEL v2;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropCreateVertexLabel1));
}

// 重复删除
TEST_F(PathDropEdge, drop_edge_002_01)
{
    const char *createVertexLabels = R"(
        CREATE VERTEXLABEL v1 (
            uiVsIndex UINT32,
            uiLdpMode UINT32,
            uiSuffixFlag UINT32,
            hSrcPid UINT32,
            uiVerNo UINT32
            PRIMARY INDEX pk(uiVsIndex) HASH_CAP 100
        );
        CREATE VERTEXLABEL v2 (
            uiVsIndex UINT32,
            uiLdpMode UINT32,
            uiSuffixFlag UINT32,
            hSrcPid UINT32,
            uiVerNo UINT32
            PRIMARY INDEX pk(uiVsIndex) HASH_CAP 100
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createVertexLabels));
    const char *createEdge = R"(
        CREATE EDGE e FROM v1 TO v2
        WHERE v1.uiVsIndex == v2.uiVsIndex;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createEdge));
    const char *dropEdge = "DROP EDGE e;";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdge));

    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcExecGql(stmt, dropEdge));

    const char *dropVertexLabels = R"(
       DROP VERTEXLABEL v1;
       DROP VERTEXLABEL v2;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropVertexLabels));
}

// 删除完后，再次创建同名的边
TEST_F(PathDropEdge, drop_edge_002_02)
{
    const char *createVertexLabels = R"(
        CREATE VERTEXLABEL v1 (
            uiVsIndex UINT32,
            uiLdpMode UINT32,
            uiSuffixFlag UINT32,
            hSrcPid UINT32,
            uiVerNo UINT32
            PRIMARY INDEX pk(uiVsIndex) HASH_CAP 100
        );
        CREATE VERTEXLABEL v2 (
            uiVsIndex UINT32,
            uiLdpMode UINT32,
            uiSuffixFlag UINT32,
            hSrcPid UINT32,
            uiVerNo UINT32
            PRIMARY INDEX pk(uiVsIndex) HASH_CAP 100
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createVertexLabels));
    const char *createEdge = R"(
        CREATE EDGE e FROM v1 TO v2
        WHERE v1.uiVsIndex == v2.uiVsIndex;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createEdge));
    const char *dropEdge = "DROP EDGE e;";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdge));

    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createEdge));

    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdge));

    const char *dropVertexLabels = R"(
       DROP VERTEXLABEL v1;
       DROP VERTEXLABEL v2;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropVertexLabels));
}

// 边的name不超过31
TEST_F(PathDropEdge, drop_edge_002_03)
{
    const char *createVertexLabels = R"(
        CREATE VERTEXLABEL v1 (
            uiVsIndex UINT32,
            uiLdpMode UINT32,
            uiSuffixFlag UINT32,
            hSrcPid UINT32,
            uiVerNo UINT32
            PRIMARY INDEX pk(uiVsIndex) HASH_CAP 100
        );
        CREATE VERTEXLABEL v2 (
            uiVsIndex UINT32,
            uiLdpMode UINT32,
            uiSuffixFlag UINT32,
            hSrcPid UINT32,
            uiVerNo UINT32
            PRIMARY INDEX pk(uiVsIndex) HASH_CAP 100
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createVertexLabels));

    const char *createEdge1 = R"(
        CREATE EDGE e111111111111111111111111111111 FROM v1 TO v2
        WHERE v1.uiVsIndex == v2.uiVsIndex;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createEdge1));
    const char *dropEdge2 = R"(
        DROP EDGE e1111111111111111111111111111111;
    )";
    EXPECT_EQ(GMERR_NAME_TOO_LONG, GmcExecGql(stmt, dropEdge2));

    const char *dropEdge1 = R"(
        DROP EDGE e111111111111111111111111111111;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdge1));
    const char *dropVertexLabels = R"(
       DROP VERTEXLABEL v1;
       DROP VERTEXLABEL v2;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropVertexLabels));
}

// 删除不存在的边
TEST_F(PathDropEdge, drop_edge_002_04)
{
    const char *dropEdge3 = R"(
        DROP EDGE notExistedEdge;
    )";
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcExecGql(stmt, dropEdge3));
}

// 关键字全大写/全小写
TEST_F(PathDropEdge, drop_edge_002_05)
{
    const char *createVertexLabels = R"(
        CREATE VERTEXLABEL v1 (
            uiVsIndex UINT32,
            uiLdpMode UINT32,
            uiSuffixFlag UINT32,
            hSrcPid UINT32,
            uiVerNo UINT32
            PRIMARY INDEX pk(uiVsIndex) HASH_CAP 100
        );
        CREATE VERTEXLABEL v2 (
            uiVsIndex UINT32,
            uiLdpMode UINT32,
            uiSuffixFlag UINT32,
            hSrcPid UINT32,
            uiVerNo UINT32
            PRIMARY INDEX pk(uiVsIndex) HASH_CAP 100
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createVertexLabels));

    const char *createEdge4 = R"(
        CREATE EDGE e4 FROM v1 TO v2
        WHERE v1.uiVsIndex == v2.uiVsIndex;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createEdge4));
    const char *dropEdge4 = R"(
        DrOP EDGE e4;
    )";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecGql(stmt, dropEdge4));
    const char *dropEdge5 = R"(
        DROP EdGE e4;
    )";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecGql(stmt, dropEdge5));

    const char *dropEdge6 = R"(
        DROP edge e4;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdge6));
    const char *dropVertexLabels = R"(
       DROP VERTEXLABEL v1;
       DROP VERTEXLABEL v2;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropVertexLabels));
}

// 关键字丢失
TEST_F(PathDropEdge, drop_edge_002_06)
{
    const char *createVertexLabels = R"(
        CREATE VERTEXLABEL v1 (
            uiVsIndex UINT32,
            uiLdpMode UINT32,
            uiSuffixFlag UINT32,
            hSrcPid UINT32,
            uiVerNo UINT32
            PRIMARY INDEX pk(uiVsIndex) HASH_CAP 100
        );
        CREATE VERTEXLABEL v2 (
            uiVsIndex UINT32,
            uiLdpMode UINT32,
            uiSuffixFlag UINT32,
            hSrcPid UINT32,
            uiVerNo UINT32
            PRIMARY INDEX pk(uiVsIndex) HASH_CAP 100
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createVertexLabels));

    const char *createEdge7 = R"(
        CREATE EDGE e7 FROM v1 TO v2
        WHERE v1.uiVsIndex == v2.uiVsIndex;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createEdge7));
    const char *dropEdge8 = R"(
        DROP e7;
    )";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecGql(stmt, dropEdge8));
    const char *dropEdge9 = R"(
        EDGE e7;
    )";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecGql(stmt, dropEdge9));

    const char *dropEdge11 = R"(
       drop edge e7;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdge11));
    const char *dropVertexLabels = R"(
       DROP VERTEXLABEL v1;
       DROP VERTEXLABEL v2;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropVertexLabels));
}

// 边名丢失
TEST_F(PathDropEdge, drop_edge_002_07)
{
    const char *dropEdge10 = R"(
       DROP EDGE ;
    )";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecGql(stmt, dropEdge10));
}

// 关键字错误
TEST_F(PathDropEdge, drop_edge_002_08)
{
    const char *dropEdge7 = R"(
        DROP XX e7;
    )";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecGql(stmt, dropEdge7));
}

// 删除设置过oneway的一般边
TEST_F(PathDropEdge, drop_edge_003_01)
{
    const char *createVertexLabel = R"(
        CREATE VERTEXLABEL v18 (
            uiVsIndex UINT32
        );
        CREATE VERTEXLABEL v19 (
            uiVsIndex UINT32,
            uiLdpMode UINT32
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createVertexLabel));
    // 删除设置过oneway的一般边
    const char *createEdgeLabel = R"(
        CREATE EDGE e1819
        FROM v18 TO v19
        WHERE v18.uiVsIndex == v19.uiVsIndex
        AND v18.uiVsIndex IN 4
        ONEWAY
        ;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createEdgeLabel));

    const char *dropEdge = R"(
        DROP EDGE e1819;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdge));
    const char *dropVertexLabel = R"(
        DROP VERTEXLABEL v18;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropVertexLabel));
    const char *dropVertexLabel1 = R"(
        DROP VERTEXLABEL v19;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropVertexLabel1));
}

// 删除设置过oneway的空边

TEST_F(PathDropEdge, drop_edge_003)
{
    const char *createVertexLabel = R"(
        CREATE VERTEXLABEL v18 (
            uiVsIndex UINT32
        );
        CREATE VERTEXLABEL v19 (
            uiVsIndex UINT32,
            uiLdpMode UINT32
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createVertexLabel));

    const char *createEdgeLabel1 = R"(
        CREATE EDGE e18_NULL
        FROM v18 TO NULL
        WHERE v18.uiVsIndex IN 4
        ONEWAY
        ;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createEdgeLabel1));

    const char *dropEdge1 = R"(
        DROP EDGE e18_NULL;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdge1));
    const char *dropVertexLabel = R"(
        DROP VERTEXLABEL v18;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropVertexLabel));
    const char *dropVertexLabel1 = R"(
        DROP VERTEXLABEL v19;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropVertexLabel1));
}
