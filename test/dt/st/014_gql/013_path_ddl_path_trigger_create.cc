/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st file for create path of fes project
 * Author:
 * Create:
 */

#include <string>
#include "gmc_gql.h"
#include "client_option.h"
#include "client_common_st.h"
#include "fes_common.h"

const uint32_t WAIT_TIME = 1000;  // ms

static void InitLabels(GmcStmtT *stmt)
{
    const char *createVertexLabel = R"(
        CREATE VERTEXLABEL T1 (
            c1 uint8,
            c2 uint8
            PRIMARY INDEX pk(c1)
            HAC_HASH INDEX index1(c1) UNIQUE,
            HAC_HASH INDEX index2(c2) UNIQUE
        );
        CREATE VERTEXLABEL T2 (
            c1 uint8,
            c2 uint8
            PRIMARY INDEX pk(c1)
            HAC_HASH INDEX index1(c1) UNIQUE,
            HAC_HASH INDEX index2(c2) UNIQUE
        );
        CREATE VERTEXLABEL T3 (
            c1 uint8,
            c2 uint8
            PRIMARY INDEX pk(c1)
            HAC_HASH INDEX index1(c1) UNIQUE,
            HAC_HASH INDEX index2(c2) UNIQUE
        );
        CREATE VERTEXLABEL T4 (
            c1 uint8,
            c2 uint8
            PRIMARY INDEX pk(c1)
            HAC_HASH INDEX index1(c1) UNIQUE,
            HAC_HASH INDEX index2(c2) UNIQUE
        );
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createVertexLabel));

    const char *createEdgeLabel = R"(
        CREATE EDGE eT12
            FROM T1 TO T2
            WHERE T1.c2 == T2.c2
            ONEWAY;
        CREATE EDGE eT13
            FROM T1 TO T3
            WHERE T1.c1 == T3.c1;
        CREATE EDGE eT14
            FROM T1 TO T4
            WHERE T1.c1 == T4.c1;
        CREATE EDGE eT32
            FROM T3 TO T2
            WHERE T3.c1 == T2.c1;
        CREATE EDGE eT34 
            FROM T3 TO T4
            WHERE T3.c1 == T4.c1;
        CREATE EDGE eT10
            FROM T1 TO NULL
            WHERE T1.c1 IN 1,2;
        CREATE EDGE eT20
            FROM T2 TO NULL
            WHERE T2.c1 IN 1;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createEdgeLabel));

    const char *createPaths = R"(
        CREATE PATH pathT1(
            MATCH 
            (T1:T1)-[:eT12]->(T2:T2)
            RETURN
            (
                REPLACE T1.c1 100, T2.c2 200
            )
        );
        CREATE PATH pathT2(
            MATCH 
            (T1:T1)-[:eT12]->(T2:T2),
            (T1:T1)-[:eT13|:eT10]->((T3:T3)|(NULL))
            RETURN
            (
                REPLACE T1.c1 100, T2.c1 200
                DELETE T1.c1 300, T2.c1 400
            )
            WITH MODE 3
        );
        CREATE PATH pathT3 (
            MATCH
            (T1:T1)-[:eT12]->(T21:T2),
            (T1:T1)-[:eT13]->(T3:T3)-[:eT32|:eT34]->((T22:T2)-[:eT20]->(NULL) | (T4:T4))
            RETURN
            (
                DELETE T1.c1 300, T2.c2 400
            )
            WITH MODE 2
        );
    )";

    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createPaths));
}

static void RemoveLabels(GmcStmtT *stmt)
{
    const char *dropPath = R"(
        DROP PATH pathT1;
        DROP PATH pathT2;
        DROP PATH pathT3;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropPath));

    const char *dropEdge = R"(
        DROP EDGE eT12;
        DROP EDGE eT13;
        DROP EDGE eT14;
        DROP EDGE eT32;
        DROP EDGE eT34;
        DROP EDGE eT10;
        DROP EDGE eT20;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropEdge));

    const char *dropVertexLabel = R"(
        DROP VERTEXLABEL T1;
        DROP VERTEXLABEL T2;
        DROP VERTEXLABEL T3;
        DROP VERTEXLABEL T4;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropVertexLabel));
}

class PathCreateTrigger : public testing::Test {
protected:
    void InitClient()
    {
        EXPECT_EQ(GMERR_OK, GmcInit());
    }

    static void SetUpTestCase()
    {
        // FES PATH需要硬件加速提供的hash
        StartDbServer((char *)"gmserver_gql.ini");
        ImportAllowList();
        st_clt_init();
    }

    static void TearDownTestCase()
    {
        st_clt_uninit();
        ShutDownDbServer();
    }

    virtual void SetUp()
    {
        InitClient();
        CreateSyncConnectionAndStmt(&conn, &stmt);

        InitLabels(stmt);
    }

    virtual void TearDown()
    {
        RemoveLabels(stmt);

        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
    }

    GmcConnT *conn;
    GmcStmtT *stmt;
};

// create one trigger return GMERR_OK
TEST_F(PathCreateTrigger, pathtrigger01_01)
{
    const char *createTriggerRule = R"(CREATE TRIGGER trigger01
        ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;)";

    EXPECT_EQ(GMERR_OK, GmcExecGql(PathCreateTrigger::stmt, createTriggerRule));

    const char *dropTriggers = R"(
        DROP TRIGGER trigger01;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
}

// 触发器name长度超限31字节
TEST_F(PathCreateTrigger, pathtrigger01_02)
{
    std::string createTriggerRule2 = R"(CREATE TRIGGER trigger1111111111111111111111111
        ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;)";
    EXPECT_EQ(GMERR_NAME_TOO_LONG, GmcExecGql(PathCreateTrigger::stmt, createTriggerRule2.c_str()));

    const char *createTriggerRule = R"(CREATE TRIGGER trigger111111111111111111111111
        ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;)";

    EXPECT_EQ(GMERR_OK, GmcExecGql(PathCreateTrigger::stmt, createTriggerRule));

    const char *dropTriggers = R"(
        DROP TRIGGER trigger111111111111111111111111;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
}

// trigger的行为中的单个path不存在
TEST_F(PathCreateTrigger, pathtrigger01_03)
{
    const char *createTriggerRule2 = R"(CREATE TRIGGER trigger01
        ON REPLACE T1 WHEN TRUE DO SEARCH notExist01;)";
    EXPECT_EQ(GMERR_UNDEFINED_OBJECT, GmcExecGql(PathCreateTrigger::stmt, createTriggerRule2));
}

// define the REPLACE and DELETE at the same time
TEST_F(PathCreateTrigger, pathtrigger02_01)
{
    const char *createTrigger01 = R"(
        CREATE TRIGGER trigger02 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;
        CREATE TRIGGER trigger03 ON DELETE T1 WHEN TRUE DO SEARCH pathT2;
    )";
    int ret = GmcExecGql(stmt, createTrigger01);
    EXPECT_EQ(GMERR_OK, ret);

    const char *dropTriggers = R"(
        DROP TRIGGER trigger02;
        DROP TRIGGER trigger03;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
}

// trigger的行为搜索多个path
TEST_F(PathCreateTrigger, pathtrigger02)
{
    const char *createTrigger02 = R"(
        CREATE TRIGGER trigger02 ON DELETE T1 WHEN TRUE DO SEARCH pathT3,pathT2;
    )";
    Status ret = GmcExecGql(stmt, createTrigger02);
    EXPECT_EQ(GMERR_OK, ret);
    const char *dropTriggers = R"(
        DROP TRIGGER trigger02;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
}

// trigger的事件不支持多表
TEST_F(PathCreateTrigger, pathtrigger03)
{
    const char *createTrigger01 = R"(CREATE TRIGGER trigger03_001 ON REPLACE T1,T2 WHEN TRUE DO SEARCH pathT1;)";

    // 创建触发器
    int ret = GmcExecGql(stmt, createTrigger01);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
}

// 关键字缺失
TEST_F(PathCreateTrigger, pathtrigger04)
{
    const char *createTrigger01 = R"(
        TRIGGER trigger03_001 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger01);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger02 = R"(
        CREATE TRIGGER trigger03_001 ON T1 WHEN TRUE DO SEARCH pathT1;
    )";
    ret = GmcExecGql(stmt, createTrigger02);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger03 = R"(
        CREATE TRIGGER trigger03_001 ON REPLACE T1 WHEN TRUE DO pathT1;
    )";
    ret = GmcExecGql(stmt, createTrigger03);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
}

// 关键字错误
TEST_F(PathCreateTrigger, pathtrigger05)
{
    const char *createTrigger01 = R"(
        CREATE TRIGER trigger03_001 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger01);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger02 = R"(
        CREATE TRIGGER trigger03_001 ON REPLACE T1 WHEN TRUE DO SEARC pathT1;
    )";
    ret = GmcExecGql(stmt, createTrigger02);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
}

// 关键字位置不对
TEST_F(PathCreateTrigger, pathtrigger06)
{
    const char *createTrigger01 = R"(
        CREATE TRIGGER trigger03_001 WHEN TRUE DO SEARCH pathT1 ON REPLACE T1;
    )";
    int ret = GmcExecGql(stmt, createTrigger01);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger02 = R"(
        CREATE TRIGGER trigger03_001 ON REPLACE T1 DO SEARCH pathT1 WHEN TRUE;
    )";
    ret = GmcExecGql(stmt, createTrigger02);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
}

// 触发器name缺失
TEST_F(PathCreateTrigger, pathtrigger07)
{
    const char *createTrigger01 = R"(
        CREATE TRIGGER ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger01);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
}

// 触发器name命名规范
TEST_F(PathCreateTrigger, pathtrigger08)
{
    // 数字开头
    const char *createTrigger01 = R"(
        CREATE TRIGGER 0trigger08 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger01);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    // 特殊符号
    const char *createTrigger02 = R"(
        CREATE TRIGGER tri$gger08 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;
    )";
    ret = GmcExecGql(stmt, createTrigger02);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    // 下划线开头
    const char *createTrigger03 = R"(
        CREATE TRIGGER _trigger08 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;
    )";
    ret = GmcExecGql(stmt, createTrigger03);
    EXPECT_EQ(GMERR_OK, ret);

    const char *dropTriggers = R"(
        DROP TRIGGER _trigger08;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
}

// trigger的事件中的表缺失
TEST_F(PathCreateTrigger, pathtrigger09)
{
    const char *createTrigger03 = R"(
        CREATE TRIGGER trigger09 ON REPLACE WHEN TRUE DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger03);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
}

// trigger的事件中的表的命名规范
TEST_F(PathCreateTrigger, pathtrigger10)
{
    // 数字开头
    const char *createTrigger03 = R"(
        CREATE TRIGGER trigger10 ON REPLACE 0Table WHEN TRUE DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger03);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    // 特殊符号
    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger10 ON REPLACE T@able WHEN TRUE DO SEARCH pathT1;
    )";
    ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
}

// trigger的行为中的path缺失
TEST_F(PathCreateTrigger, pathtrigger11)
{
    const char *createTrigger03 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN TRUE DO SEARCH;
    )";
    int ret = GmcExecGql(stmt, createTrigger03);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
}

// trigger的行为中的path的命名规范
TEST_F(PathCreateTrigger, pathtrigger12)
{
    // 数字开头
    const char *createTrigger03 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN TRUE DO SEARCH 0pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger03);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    // 特殊符号
    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1#;
    )";
    ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
}

// trigger的行为中的多个path之间不用‘,’隔开
TEST_F(PathCreateTrigger, pathtrigger13)
{
    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1 pathT2 pathT3;
    )";
    int ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
}

// 批量trigger创建
TEST_F(PathCreateTrigger, pathtrigger14)
{
    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;
        CREATE TRIGGER trigger11 ON DELETE T2 WHEN TRUE DO SEARCH pathT2;
        CREATE TRIGGER trigger12 ON REPLACE T2 WHEN TRUE DO SEARCH pathT2;
    )";
    int ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_OK, ret);

    const char *dropTriggers = R"(
        DROP TRIGGER trigger09;
        DROP TRIGGER trigger11;
        DROP TRIGGER trigger12;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
}

// 同名trigger创建
TEST_F(PathCreateTrigger, pathtrigger15)
{
    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, ret);

    const char *dropTriggers = R"(
        DROP TRIGGER trigger09;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
}

// 同dml+table的trigger创建
TEST_F(PathCreateTrigger, pathtrigger16)
{
    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_OK, ret);

    const char *createTrigger05 = R"(
        CREATE TRIGGER trigger10 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;
    )";
    ret = GmcExecGql(stmt, createTrigger05);
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, ret);

    const char *dropTriggers = R"(
        DROP TRIGGER trigger09;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
}

// trigger的事件中的点不存在
TEST_F(PathCreateTrigger, pathtrigger18)
{
    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON REPLACE noExistTable WHEN TRUE DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

// trigger的条件全大写/全小写
TEST_F(PathCreateTrigger, pathtrigger19)
{
    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN TRuE DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger05 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN true DO SEARCH pathT1;
        CREATE TRIGGER trigger10 ON REPLACE T2 WHEN TRUE DO SEARCH pathT1;
    )";
    ret = GmcExecGql(stmt, createTrigger05);
    EXPECT_EQ(GMERR_OK, ret);

    const char *dropTriggers = R"(
        DROP TRIGGER trigger09;
        DROP TRIGGER trigger10;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
}

// trigger的条件缺失
TEST_F(PathCreateTrigger, pathtrigger20)
{
    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
}

// trigger的行为中的多个path中的某一个不存在
TEST_F(PathCreateTrigger, pathtrigger21)
{
    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1, notExistPath;
    )";
    int ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_UNDEFINED_OBJECT, ret);
}

// trigger的行为只搜索一个path
TEST_F(PathCreateTrigger, pathtrigger22)
{
    const char *createTrigger05 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN true DO SEARCH pathT1;
        CREATE TRIGGER trigger11 ON REPLACE T2 WHEN TRUE DO SEARCH pathT1;
        CREATE TRIGGER trigger12 ON DELETE T2 WHEN TRUE DO SEARCH pathT2;
    )";
    int ret = GmcExecGql(stmt, createTrigger05);
    EXPECT_EQ(GMERR_OK, ret);

    const char *dropTriggers = R"(
        DROP TRIGGER trigger09;
        DROP TRIGGER trigger11;
        DROP TRIGGER trigger12;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));
}

// trigger的事件中的点不在行为中的path
TEST_F(PathCreateTrigger, pathtrigger23)
{
    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T3 WHEN TRUE DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_INVALID_OBJECT_DEFINITION, ret);
}

// trigger定义后,其事件中的点和行为中的path不能提前删除
TEST_F(PathCreateTrigger, pathtrigger24)
{
    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN TRUE DO SEARCH pathT1;
    )";
    int ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_OK, ret);

    const char *dropTables = R"(
        DROP VERTEXLABEL T1;
    )";
    ret = GmcExecGql(stmt, dropTables);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);

    const char *dropPaths = R"(
        DROP PATH pathT1;
    )";
    ret = GmcExecGql(stmt, dropPaths);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);

    const char *dropTriggers = R"(
        DROP TRIGGER trigger09;
    )";
    ret = GmcExecGql(stmt, dropTriggers);
    EXPECT_EQ(GMERR_OK, ret);
}

// event为REPLACE, 校验path的下发事件是否匹配
TEST_F(PathCreateTrigger, pathtrigger25)
{
    // 没有下发字段
    const char *createPaths = R"(
        CREATE PATH pathT25(
            MATCH
            (T1:T1)-[:eT12]->(T2:T2)
        );
    )";

    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createPaths));

    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN TRUE DO SEARCH pathT25;
    )";
    int ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_INVALID_OBJECT_DEFINITION, ret);
    // 没有REPLACE字段
    const char *createTrigger05 = R"(
        CREATE TRIGGER trigger09 ON REPLACE T1 WHEN TRUE DO SEARCH pathT3;
    )";
    ret = GmcExecGql(stmt, createTrigger05);
    EXPECT_EQ(GMERR_INVALID_OBJECT_DEFINITION, ret);

    const char *dropPath = R"(
        DROP PATH pathT25;
    )";
    ret = GmcExecGql(stmt, dropPath);
    EXPECT_EQ(GMERR_OK, ret);
}

// event为DELETE, 校验path的下发事件是否匹配
TEST_F(PathCreateTrigger, pathtrigger26)
{
    // 没有下发字段
    const char *createPaths = R"(
        CREATE PATH pathT25(
            MATCH
            (T1:T1)-[:eT12]->(T2:T2)
        );
    )";

    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createPaths));

    const char *createTrigger04 = R"(
        CREATE TRIGGER trigger09 ON DELETE T1 WHEN TRUE DO SEARCH pathT25;
    )";
    int ret = GmcExecGql(stmt, createTrigger04);
    EXPECT_EQ(GMERR_INVALID_OBJECT_DEFINITION, ret);
    // 没有DELETE字段
    const char *createTrigger05 = R"(
        CREATE TRIGGER trigger09 ON DELETE T1 WHEN TRUE DO SEARCH pathT1;
    )";
    ret = GmcExecGql(stmt, createTrigger05);
    EXPECT_EQ(GMERR_INVALID_OBJECT_DEFINITION, ret);

    const char *dropPath = R"(
        DROP PATH pathT25;
    )";
    ret = GmcExecGql(stmt, dropPath);
    EXPECT_EQ(GMERR_OK, ret);
}
