{"type": "record", "name": "member_key_label", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F7", "type": "uint8", "nullable": false}, {"name": "F14", "type": "string", "size": 100}, {"name": "F15", "type": "fixed", "nullable": false, "size": 16}, {"name": "F20", "type": "uint32", "nullable": false}, {"name": "F21", "type": "uint32", "nullable": false}, {"name": "T1", "type": "record", "vector": true, "size": 3, "fields": [{"name": "V0", "type": "int64"}, {"name": "V1", "type": "uint64"}, {"name": "V2", "type": "int32"}, {"name": "V3", "type": "uint32"}, {"name": "T1_1", "type": "record", "array": true, "fields": [{"name": "U0", "type": "int64"}, {"name": "U1", "type": "uint64"}, {"name": "T1_1_1", "type": "record", "array": true, "fields": [{"name": "X0", "type": "int64"}, {"name": "X1", "type": "uint64"}]}]}]}, {"name": "T2", "type": "record", "fixed_array": true, "fields": [{"name": "W0", "type": "int64"}, {"name": "W1", "type": "uint64"}]}], "keys": [{"node": "member_key_label", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "member_key_label", "name": "lpm6_key", "fields": ["F20", "F21", "F15", "F7"], "index": {"type": "lpm6_tree_bitmap"}, "constraints": {"unique": true}}, {"node": "T1", "name": "member_key", "index": {"type": "none"}, "fields": ["V0", "V2", "V1"], "constraints": {"unique": true}}, {"node": "T1_1", "name": "T1_1_member_key", "index": {"type": "none"}, "fields": ["U0", "U1"], "constraints": {"unique": false}}, {"node": "T1_1_1", "name": "T1_1_1_member_key", "index": {"type": "none"}, "fields": ["X0", "X1"], "constraints": {"unique": false}}, {"node": "T2", "name": "T2_member_key", "index": {"type": "none"}, "fields": ["W0"], "constraints": {"unique": true}}]}