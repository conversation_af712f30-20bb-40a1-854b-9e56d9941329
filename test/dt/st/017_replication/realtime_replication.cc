/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: realtime_replication.cc
 * Description: 测试实时复制
 * Create: 2025-03-28
 */

#include <sys/epoll.h>

#include "gtest/gtest.h"
#include "gmc_test.h"
#include "InitClt.h"
#include "test_ha.h"
#include "client_common_st.h"

static const char *g_serverLocator1 = "usocket:/run/verona/unix_emserver1";
static const char *g_serverLocator2 = "usocket:/run/verona/unix_emserver2";
static const char *g_url = "tcp:host=127.0.0.1,port=5757";

class RealTimeReplication : public testing::Test {
protected:
    virtual void SetUp()
    {
        ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, g_serverLocator1, "017_replication", "conn1", &conn1));
        ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, g_serverLocator2, "017_replication", "conn2", &conn2));
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn1, &stmt1));
        ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn2, &stmt2));
    }

    virtual void TearDown()
    {
        DestroyConnectionAndStmt(conn2, stmt2);
        DestroyConnectionAndStmt(conn1, stmt1);
    }

    static void SetUpTestCase()
    {
        GmcDetachAllShmSeg();
        system("rm -f gmserver1");
        system("rm -f gmserver2");
        system("ln -s ../../../../output/euler/aarch64/bin/gmserver gmserver1; chmod 777 gmserver1");
        system("ln -s ../../../../output/euler/aarch64/bin/gmserver gmserver2; chmod 777 gmserver2");
        system("kill -9 `pidof gmserver1`");
        system("kill -9 `pidof gmserver2`");
        system("ipcrm -a");
        system("rm -rf /data/gmdb*");
        system("rm -rf /dev/shm/_run_verona_unix_emserver*");
        system("kill -9 `pidof gmserver`");
        system("./gmserver1 -b -p gmserver1.ini\n");
        system("./gmserver2 -b -p gmserver2.ini\n");
        sleep(2);

        ASSERT_EQ(GMERR_OK, GmcSetDbRole(g_serverLocator1, GMC_DB_ROLE_MASTER, g_url));
        ASSERT_EQ(GMERR_OK, GmcSetDbRole(g_serverLocator2, GMC_DB_ROLE_SLAVE, g_url));
        ASSERT_EQ(GMERR_OK, GmcSlaveOnline(g_serverLocator1, g_url));
        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator2, GMC_DB_STATUS_ACCESSIBLE, 10));      // 等待备可访问
        ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));  // 等待主可同步备份

        GmcInit();
    }

    static void TearDownTestCase()
    {
        GmcUnInit();
        system("kill -9 `pidof gmserver1`");
        system("kill -9 `pidof gmserver2`");
    }

protected:
    GmcConnT *conn1 = NULL, *conn2 = NULL;
    GmcStmtT *stmt1 = NULL, *stmt2 = NULL;
};

void VertexLabelInsertDataWithoutIncRol(int32_t count, const char *labelName, GmcConnT *conn, GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcTransStart(conn, NULL));
    for (int32_t i = 0; i < count; i++) {
        const int32_t f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt));
    }
    ASSERT_EQ(GMERR_OK, GmcTransCommit(conn));
}

void VertexLabelQueryIncRol(int32_t count, const char *labelName, uint32_t incExpect[], GmcStmtT *stmt)
{
    for (int32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "XT60_K0"));
        uint32_t f0 = incExpect[i];
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
        EXPECT_EQ(false, eof);
        uint32_t f1 = 0;
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F1", &f1, sizeof(f1), &isNull));
        EXPECT_EQ(isNull, false);
    }
}

void VertexLabelInsertDataIncRolNotPri(int32_t count, const char *labelName, GmcConnT *conn, GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcTransStart(conn, NULL));
    for (int32_t i = 0; i < count; i++) {
        const int32_t f0 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt));
    }
    ASSERT_EQ(GMERR_OK, GmcTransCommit(conn));
}

void VertexLabelQueryIncRolNotPri(
    int32_t count, const char *labelName, uint32_t incKey[], uint32_t incExpect[], GmcStmtT *stmt)
{
    for (int32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "XT60_K0"));
        uint32_t f0 = incKey[i];
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt));
        bool eof = false;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
        EXPECT_EQ(false, eof);
        uint32_t f1 = 0;
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F1", &f1, sizeof(f1), &isNull));
        EXPECT_EQ(isNull, false);
        EXPECT_EQ(incExpect[i], f1);
    }
}

// 创建同步的VertexLabel表
TEST_F(RealTimeReplication, CreateVertexLabel01)
{
    const char *labelName = "XT60";
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    uint32_t labelType = 0;

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));
    ASSERT_EQ(GMERR_OK, GmcGetLabelTypeByName(stmt2, labelName, &labelType));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcGetLabelTypeByName(stmt2, labelName, &labelType));
}

// 创建不同步的VertexLabel表
TEST_F(RealTimeReplication, CreateVertexLabel02)
{
    const char *labelName = "XT60";
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    uint32_t labelType = 0;
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcGetLabelTypeByName(stmt2, labelName, &labelType));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 非public的namespace建表
TEST_F(RealTimeReplication, CreateVertexLabel03)
{
    const char *labelName = "XT60";
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    uint32_t labelType = 0;

    const char *nameSpace = "replication";
    const char *userName = "userXXX";

    ASSERT_EQ(GMERR_OK, GmcCreateNamespace(stmt1, nameSpace, userName));
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt1, nameSpace));
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));

    // 预期表在备机的public namespace下查不到
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcGetLabelTypeByName(stmt2, labelName, &labelType));
    // 预期表在备机的replication namespace查到
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt2, nameSpace));
    ASSERT_EQ(GMERR_OK, GmcGetLabelTypeByName(stmt2, labelName, &labelType));

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
    ASSERT_EQ(GMERR_OK, GmcDropNamespace(stmt1, nameSpace));
}

// 没有主键的表不允许复制
TEST_F(RealTimeReplication, CreateVertexLabel04)
{
    const char *configJson1 = R"({"max_record_count":1000, "replication":1})";
    const char *configJson2 = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}]
            }])";

    ASSERT_EQ(GMERR_INVALID_TABLE_DEFINITION, GmcCreateVertexLabel(stmt1, labelJson, configJson1));
    ASSERT_EQ(GMERR_INVALID_TABLE_DEFINITION, GmcCreateVertexLabel(stmt1, labelJson, configJson2));
}

// 创建指定ID的表
TEST_F(RealTimeReplication, CreateVertexLabel05)
{
    const char *labelName = "XT60";
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "id":100,
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, "V$CATA_VERTEX_LABEL_INFO", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcSetFilter(stmt2, "VERTEX_LABEL_NAME = \'XT60\'"));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    bool eof = false;
    ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
    ASSERT_EQ(false, eof);
    uint32_t id = 0;
    bool isNull;
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "VERTEX_LABEL_ID", &id, sizeof(id), &isNull));
    ASSERT_EQ(id, 100);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 创建同步的KV表
TEST_F(RealTimeReplication, CreateKvTable01)
{
    const char *table_name = "KV60";
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    uint32_t labelType = 0;
    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, table_name, configJson));
    ASSERT_EQ(GMERR_OK, GmcGetLabelTypeByName(stmt2, table_name, &labelType));
    ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt1, table_name));
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcGetLabelTypeByName(stmt2, table_name, &labelType));
}

// 创建不同步的KV表
TEST_F(RealTimeReplication, CreateKvTable02)
{
    const char *table_name = "KV60";
    const char *configJson = R"({"max_record_count":1000})";
    uint32_t labelType = 0;
    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, table_name, configJson));
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcGetLabelTypeByName(stmt2, table_name, &labelType));
    ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt1, table_name));
}

// 非public的namespace建表
TEST_F(RealTimeReplication, CreateKvTable03)
{
    const char *table_name = "KV60";
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    uint32_t labelType = 0;

    const char *nameSpace = "replication";
    const char *userName = "userXXX";

    ASSERT_EQ(GMERR_OK, GmcCreateNamespace(stmt1, nameSpace, userName));
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt1, nameSpace));
    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, table_name, configJson));

    // 预期表在备机的public namespace下查不到
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcGetLabelTypeByName(stmt2, table_name, &labelType));
    // 预期表在备机的replication namespace查到
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt2, nameSpace));
    ASSERT_EQ(GMERR_OK, GmcGetLabelTypeByName(stmt2, table_name, &labelType));

    ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt1, table_name));
    ASSERT_EQ(GMERR_OK, GmcDropNamespace(stmt1, nameSpace));
}

// 创建删除NameSpace
TEST_F(RealTimeReplication, CreateNameSpace01)
{
    const char *nameSpace = "replication";
    const char *userName = "userXXX";
    ASSERT_EQ(GMERR_OK, GmcCreateNamespace(stmt1, nameSpace, userName));
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt2, nameSpace));
    ASSERT_EQ(GMERR_OK, GmcDropNamespace(stmt1, nameSpace));
    ASSERT_EQ(GMERR_UNDEFINED_OBJECT, GmcUseNamespace(stmt2, nameSpace));
}

// 插入单条记录
TEST_F(RealTimeReplication, InsertVertex01)
{
    const char *labelName = "XT61";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    const int32_t f0 = 0, f1 = 1, f2 = 2, f3 = 3;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    bool eof = false;
    ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
    ASSERT_EQ(false, eof);
    bool isNull = false;
    int32_t f = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F0", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f0);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F1", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f1);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F2", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f2);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F3", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f3);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 更新单条记录
TEST_F(RealTimeReplication, UpdateVertex01)
{
    const char *labelName = "XT65";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    int32_t f0 = 0, f1 = 1, f2 = 2, f3 = 3;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_UPDATE));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    f3 = 33;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));

    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    bool eof = false;
    ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
    ASSERT_EQ(false, eof);
    bool isNull = false;
    int32_t f = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F0", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f0);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F1", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f1);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F2", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f2);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F3", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f3);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 覆盖单条记录
TEST_F(RealTimeReplication, ReplaceVertex01)
{
    const char *labelName = "XT61";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    const int32_t f0 = 0, f1 = 1, f2 = 2, f3 = 3;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    bool eof = false;
    ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
    ASSERT_EQ(false, eof);
    bool isNull = false;
    int32_t f = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F0", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f0);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F1", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f1);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F2", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f2);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F3", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f3);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 大对象单条记录
TEST_F(RealTimeReplication, ReplaceVertex02)
{
    const char *labelName = "XT61BIG";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"string", "nullable":false},
                    {"name":"F2", "type":"string", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    const int32_t f0 = 0;
    const size_t f1Size = 64 * 1000;
    char f1[f1Size] = {};
    memset_s(f1, f1Size, 'a', f1Size - 1);
    f1[f1Size - 2] = 'f';

    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_STRING, f1, strlen(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_STRING, f1, strlen(f1)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    bool eof = false;
    ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
    ASSERT_EQ(false, eof);
    bool isNull = false;
    f1[f1Size - 2] = '0';
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F1", f1, sizeof(f1), &isNull));
    ASSERT_EQ(f1[f1Size - 2], 'f');
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 异步复制：覆盖单条记录
TEST_F(RealTimeReplication, ReplaceVertex03)
{
    const char *labelName = "XT61";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000, "replication":1})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    const int32_t f0 = 0, f1 = 1, f2 = 2, f3 = 3;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    bool eof = true;  // 异步复制需要多同步几次
    for (int i = 0; i < 100 && eof; i++) {
        usleep(100000 * i);
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
    }
    ASSERT_EQ(eof, false);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// Merge-Insert单条记录
TEST_F(RealTimeReplication, MergeInsertVertex01)
{
    const char *labelName = "XT666";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_MERGE));
    const int32_t f0 = 0, f1 = 1, f2 = 2, f3 = 3;
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    bool eof = false;
    ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
    ASSERT_EQ(false, eof);
    bool isNull = false;
    int32_t f = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F0", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f0);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F1", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f1);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F2", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f2);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F3", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f3);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// Merge-Update单条记录
TEST_F(RealTimeReplication, MergeUpdateVertex01)
{
    const char *labelName = "XT65";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    int32_t f0 = 0, f1 = 1, f2 = 2, f3 = 3;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    f3 = 33;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));

    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    bool eof = false;
    ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
    ASSERT_EQ(false, eof);
    bool isNull = false;
    int32_t f = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F0", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f0);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F1", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f1);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F2", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f2);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt2, "F3", &f, sizeof(f), &isNull));
    ASSERT_EQ(f, f3);
    ASSERT_EQ(false, isNull);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 删除单条记录
TEST_F(RealTimeReplication, DeleteVertex01)
{
    const char *labelName = "XT65";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    int32_t f0 = 0, f1 = 1, f2 = 2, f3 = 3;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    bool eof = false;
    ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
    ASSERT_EQ(false, eof);

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_DELETE));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt2));
    ASSERT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
    ASSERT_EQ(true, eof);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// KV表插入
TEST_F(RealTimeReplication, SetKv01)
{
    const char *table_name = "KV514";
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, table_name, configJson));
    uint32_t key = 1000, value = 2000;
    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, table_name));
    ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));

    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt2, table_name));
    uint32_t valueLen = sizeof(value);
    ASSERT_EQ(GMERR_OK, GmcKvGet(stmt2, &key, sizeof(key), &value, &valueLen));
    ASSERT_EQ(value, 2000);
    ASSERT_EQ(valueLen, sizeof(value));
    ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt1, table_name));
}

// KV表更新
TEST_F(RealTimeReplication, SetKv02)
{
    const char *table_name = "KV514";
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, table_name, configJson));
    uint32_t key = 1000, value = 2000;
    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, table_name));
    ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));
    value = 2001;
    ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));

    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt2, table_name));
    uint32_t valueLen = sizeof(value);
    ASSERT_EQ(GMERR_OK, GmcKvGet(stmt2, &key, sizeof(key), &value, &valueLen));
    ASSERT_EQ(value, 2001);
    ASSERT_EQ(valueLen, sizeof(value));
    ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt1, table_name));
}

// 异步复制：KV表插入
TEST_F(RealTimeReplication, SetKv03)
{
    const char *table_name = "KV514";
    const char *configJson = R"({"max_record_count":1000, "replication":1})";
    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, table_name, configJson));
    uint32_t key = 1000, value = 2000;
    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, table_name));
    ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));

    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt2, table_name));
    uint32_t valueLen = sizeof(value);
    Status ret = GMERR_NO_DATA;
    for (int i = 0; i < 100 && ret != GMERR_OK; i++) {
        usleep(100000 * i);
        ret = GmcKvGet(stmt2, &key, sizeof(key), &value, &valueLen);
    }
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(value, 2000);
    ASSERT_EQ(valueLen, sizeof(value));
    ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt1, table_name));
}

// KV表删除
TEST_F(RealTimeReplication, RemoveKv01)
{
    const char *table_name = "KV515";
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, table_name, configJson));
    uint32_t key = 1000, value = 2000;
    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, table_name));
    ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));
    ASSERT_EQ(GMERR_OK, GmcKvRemove(stmt1, &key, sizeof(key)));
    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt2, table_name));
    uint32_t valueLen = sizeof(value);
    ASSERT_EQ(GMERR_NO_DATA, GmcKvGet(stmt2, &key, sizeof(key), &value, &valueLen));
}

// 交互式小事务单表操作
TEST_F(RealTimeReplication, Tx01)
{
    const char *labelName = "XT611";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    const int32_t count = 10;
    for (int32_t i = 0; i < count; i++) {
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }
    ASSERT_EQ(GMERR_OK, GmcTransCommit(conn1));

    uint64_t count2 = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName, NULL, &count2));
    ASSERT_EQ(count, count2);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 交互式小事务多表操作
TEST_F(RealTimeReplication, Tx02)
{
    const char *labelName1 = "XT611-1";  // 测试名字和Json不一致
    const char *labelName2 = "XT611-2";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName1));
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName2));

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));

    const int32_t count = 10;
    for (int32_t i = 0; i < count; i++) {
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName1, GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName2, GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }
    ASSERT_EQ(GMERR_OK, GmcTransCommit(conn1));

    uint64_t count2 = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName1, NULL, &count2));
    ASSERT_EQ(count, count2);
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName2, NULL, &count2));
    ASSERT_EQ(count, count2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName1));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName2));
}

// 交互式大事务单表增删
TEST_F(RealTimeReplication, Tx03)
{
    const char *labelName = "XT6110";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));

    // 插入
    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    const int32_t count = 10000;
    for (int32_t i = 0; i < count; i++) {
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }
    ASSERT_EQ(GMERR_OK, GmcTransCommit(conn1));

    uint64_t count2 = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName, NULL, &count2));
    ASSERT_EQ(count, count2);

    // 删除
    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    for (int32_t i = 0; i < count; i++) {
        const int32_t f0 = i + 0;
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_DELETE));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }
    ASSERT_EQ(GMERR_OK, GmcTransCommit(conn1));

    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName, NULL, &count2));
    ASSERT_EQ(0, count2);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 交互式小事务覆盖删除混合操作
TEST_F(RealTimeReplication, Tx04)
{
    const char *labelName = "XT611";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));

    const int32_t count = 10;
    for (int32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_DELETE));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }
    ASSERT_EQ(GMERR_OK, GmcTransCommit(conn1));

    uint64_t count2 = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName, NULL, &count2));
    ASSERT_EQ(0, count2);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 小事务回滚，无需和备机交互
TEST_F(RealTimeReplication, Tx05)
{
    const char *labelName = "XT611";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000000, "replication":1})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    const int32_t count = 10;
    for (int32_t i = 0; i < count; i++) {
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }
    ASSERT_EQ(GMERR_OK, GmcTransRollBack(conn1));

    uint64_t count2 = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName, NULL, &count2));
    ASSERT_EQ(0, count2);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 大事务回滚，需和备机交互
TEST_F(RealTimeReplication, Tx06)
{
    const char *labelName = "XT611";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    const int32_t count = 10000;
    for (int32_t i = 0; i < count; i++) {
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }
    ASSERT_EQ(GMERR_OK, GmcTransRollBack(conn1));

    uint64_t count2 = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName, NULL, &count2));
    ASSERT_EQ(0, count2);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 大事务回滚，混合同步复制和异步复制(有些logbuf是异步，有些是同步),
TEST_F(RealTimeReplication, Tx07)
{
    const char *labelName1 = "XT611";  // 测试名字和Json不一致
    const char *configJson1 = R"({"max_record_count":1000000, "replication":2})";
    const char *labelName2 = "XT612";  // 测试名字和Json不一致
    const char *configJson2 = R"({"max_record_count":1000000, "replication":1})";

    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson1, labelName1));
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson2, labelName2));
    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));

    const int32_t count = 5000;
    for (int32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName1, GMC_OPERATION_REPLACE));
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    for (int32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName2, GMC_OPERATION_REPLACE));
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }
    ASSERT_EQ(GMERR_OK, GmcTransCommit(conn1));

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    for (int32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName2, GMC_OPERATION_REPLACE));
        const int32_t f0 = i + 10000, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    for (int32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName1, GMC_OPERATION_REPLACE));
        const int32_t f0 = i + 10000, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }
    ASSERT_EQ(GMERR_OK, GmcTransCommit(conn1));

    uint64_t count2 = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName1, NULL, &count2));
    ASSERT_LE(count, count2);
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName2, NULL, &count2));
    ASSERT_EQ(count * 2, count2);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName1));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName2));
}

// 事务并发
TEST_F(RealTimeReplication, MutiTrx)
{
    const char *labelName = "XT6110";         // 测试名字和Json不一致
    const char *labelNameTrx = "XT6110_Trx";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelNameTrx));

    // 插入
    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    const int32_t count = 10000;
    for (int32_t i = 0; i < count; i++) {
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    }

    // 备机有未提交的事务，现嵌入一次事务提交，预期redoCtx原事务可正常使用
    GmcConnT *connTrx = NULL;
    GmcStmtT *stmtTrx = NULL;
    ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, g_serverLocator1, "017_replication", "connTrx", &connTrx));
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(connTrx, &stmtTrx));
    // 开启事务2，并最终提交
    ASSERT_EQ(GMERR_OK, GmcTransStart(connTrx, NULL));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmtTrx, labelNameTrx, GMC_OPERATION_REPLACE));
    const int32_t f0 = count + 0, f1 = count + 1, f2 = count + 2, f3 = count + 3;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmtTrx, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmtTrx, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmtTrx, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmtTrx, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmtTrx));
    ASSERT_EQ(GMERR_OK, GmcTransCommit(connTrx));
    DestroyConnectionAndStmt(connTrx, stmtTrx);

    // 原事务提交，预期无影响，不影响redoCtx使用
    ASSERT_EQ(GMERR_OK, GmcTransCommit(conn1));

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelNameTrx));
}

// 批量操作
TEST_F(RealTimeReplication, Batch01)
{
    const char *labelName = "XT611";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));

    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ASSERT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    ASSERT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 200));
    ASSERT_EQ(GMERR_OK, GmcBatchPrepare(conn1, &batchOption, &batch));
    const int32_t count = 100;
    for (int32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
        const int32_t f0 = i + 0, f1 = i + 1, f2 = i + 2, f3 = i + 3;
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
        ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt1));
    }
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));

    uint64_t count2 = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexCount(stmt2, labelName, NULL, &count2));
    ASSERT_EQ(count, count2);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 备机下线用例
TEST_F(RealTimeReplication, SlaveOffline)
{
    const char *label_name = "XT60";
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    uint32_t labelType = 0;

    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));
    ASSERT_EQ(GMERR_OK, GmcGetLabelTypeByName(stmt2, label_name, &labelType));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, label_name));
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcGetLabelTypeByName(stmt2, label_name, &labelType));

    // 备下线
    ASSERT_EQ(GMERR_OK, GmcSlaveOffline(g_serverLocator1));

    // 重新拉起备机
    DestroyConnectionAndStmt(conn2, stmt2);
    conn2 = NULL;
    stmt2 = NULL;
    GmcUnInitByInstance(2);
    system("kill $(ps -ef | grep gmserver2 | grep -v grep | awk -F \" \" '{print $2}')");
    system("rm -rf /dev/shm/_run_verona_unix_emserver2");
    system("gmserver -b -p gmserver2.ini\n");
    sleep(2);
    ASSERT_EQ(GMERR_OK, GmcSetDbRole(g_serverLocator2, GMC_DB_ROLE_SLAVE, g_url));
    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator2, GMC_DB_STATUS_ACCESSIBLE, 10));  // 等待备可访问
    ASSERT_EQ(GMERR_OK, GmcSlaveOnline(g_serverLocator1, g_url));
    ASSERT_EQ(GMERR_OK, GmcWait(g_serverLocator1, GMC_DB_STATUS_BACKUP_ENABLED, 10));  // 等待主可同步备份

    // 恢复同步
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, labelJson, configJson));

    // 校验备机无连接时，后台truncate正常
    ASSERT_EQ(GMERR_OK, GmcDeleteAllFast(stmt1, label_name));

    ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, g_serverLocator2, "017_replication", "conn2", &conn2));
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn2, &stmt2));
    ASSERT_EQ(GMERR_OK, GmcGetLabelTypeByName(stmt2, label_name, &labelType));

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, label_name));
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcGetLabelTypeByName(stmt2, label_name, &labelType));
}

TEST_F(RealTimeReplication, TruncateVertexLabel)
{
    const char *labelName = "XT61";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    const int32_t f0 = 0, f1 = 1, f2 = 2, f3 = 3;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt2, &count));
    ASSERT_EQ(1, count);

    ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt1, labelName));

    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt2, &count));
    ASSERT_EQ(0, count);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

TEST_F(RealTimeReplication, TruncateBackgroundVertexLabel)
{
    const char *labelName = "XT61";  // 测试名字和Json不一致
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    const int32_t f0 = 0, f1 = 1, f2 = 2, f3 = 3;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt1, &count));
    ASSERT_EQ(1, count);

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt2, &count));
    ASSERT_EQ(1, count);

    ASSERT_EQ(GMERR_OK, GmcDeleteAllFast(stmt1, labelName));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt1, &count));
    ASSERT_EQ(0, count);

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt2, &count));
    ASSERT_EQ(0, count);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

TEST_F(RealTimeReplication, TruncateKv)
{
    const char *table_name = "KV514";
    const char *configJson = R"({"max_record_count":1000, "replication":2})";
    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, table_name, configJson));

    uint32_t key = 1000, value = 2000;
    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, table_name));
    ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));

    uint32_t valueLen = sizeof(value);
    ASSERT_EQ(GMERR_OK, GmcKvGet(stmt1, &key, sizeof(key), &value, &valueLen));
    ASSERT_EQ(value, 2000);
    ASSERT_EQ(valueLen, sizeof(value));

    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt2, table_name));
    ASSERT_EQ(GMERR_OK, GmcKvGet(stmt2, &key, sizeof(key), &value, &valueLen));
    ASSERT_EQ(value, 2000);
    ASSERT_EQ(valueLen, sizeof(value));

    ASSERT_EQ(GMERR_OK, GmcKvTruncateTable(stmt1, table_name));

    ASSERT_EQ(GMERR_NO_DATA, GmcKvGet(stmt1, &key, sizeof(key), &value, &valueLen));

    ASSERT_EQ(GMERR_NO_DATA, GmcKvGet(stmt2, &key, sizeof(key), &value, &valueLen));

    ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt1, table_name));
}

TEST_F(RealTimeReplication, ClearNamespace)
{
    const char *nameSpace = "replication";
    const char *userName = "userXXX";
    ASSERT_EQ(GMERR_OK, GmcCreateNamespace(stmt1, nameSpace, userName));
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt1, nameSpace));
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt2, nameSpace));

    const char *configJson = R"({"max_record_count":1000, "replication":2})";

    const char *labelName = "XT61";  // 测试名字和Json不一致
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    const int32_t f0 = 0, f1 = 1, f2 = 2, f3 = 3;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt2, &count));
    ASSERT_EQ(1, count);

    const char *tableName = "KV514";
    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, tableName, configJson));

    uint32_t key = 1000, value = 2000;
    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, tableName));
    ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));

    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt2, tableName));
    uint32_t valueLen = sizeof(value);
    ASSERT_EQ(GMERR_OK, GmcKvGet(stmt2, &key, sizeof(key), &value, &valueLen));
    ASSERT_EQ(value, 2000);
    ASSERT_EQ(valueLen, sizeof(value));

    ASSERT_EQ(GMERR_OK, GmcClearNamespace(stmt1, nameSpace));

    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcKvPrepareStmtByLabelName(stmt1, tableName));

    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmcKvPrepareStmtByLabelName(stmt2, tableName));

    ASSERT_EQ(GMERR_OK, GmcDropNamespace(stmt1, nameSpace));
}

static void CommonAsyncDDLCb(void *userData, Status status, const char *errMsg)
{
    ASSERT_EQ(GMERR_OK, status);
    uint32_t *received = reinterpret_cast<uint32_t *>(userData);
    (*received)++;
}

static pthread_t g_responseEpollThreadId;
static int g_responseEpollFd;

static int32_t EpollReg(int fd, GmcEpollCtlTypeE type)
{
    switch (type) {
        case GMC_EPOLL_ADD: {
            struct epoll_event event;
            event.data.fd = fd;
            event.events = EPOLLIN;
            return epoll_ctl(g_responseEpollFd, EPOLL_CTL_ADD, fd, &event);
        }
        case GMC_EPOLL_DEL:
            return epoll_ctl(g_responseEpollFd, EPOLL_CTL_DEL, fd, NULL);
        default:
            return -1;
    }
}

// 建连
static void CreateAsyncConnAndStmt(const char *serverLocator, GmcConnT **conn, GmcStmtT **stmt)
{
    GmcConnT *tmpConn = NULL;
    GmcStmtT *tmpStmt = NULL;
    GmcConnOptionsT *connOptions;
    // 设置异步连接的选项
    EXPECT_EQ(GMERR_OK, GmcConnOptionsCreate(&connOptions));
    EXPECT_EQ(GMERR_OK, GmcConnOptionsSetServerLocator(connOptions, serverLocator));
    EXPECT_EQ(GMERR_OK, GmcConnOptionsSetEpollRegFunc(connOptions, EpollReg));
    EXPECT_EQ(GMERR_OK, GmcConnect(GMC_CONN_TYPE_ASYNC, connOptions, &tmpConn));
    GmcConnOptionsDestroy(connOptions);
    EXPECT_EQ(GMERR_OK, GmcAllocStmt(tmpConn, &tmpStmt));

    *conn = tmpConn;
    *stmt = tmpStmt;
}

TEST_F(RealTimeReplication, TruncateNamespace)
{
    const char *nameSpace = "replication";
    const char *userName = "userXXX";
    ASSERT_EQ(GMERR_OK, GmcCreateNamespace(stmt1, nameSpace, userName));
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt1, nameSpace));
    ASSERT_EQ(GMERR_OK, GmcUseNamespace(stmt2, nameSpace));

    const char *configJson = R"({"max_record_count":1000, "replication":2})";

    const char *labelName = "XT61";  // 测试名字和Json不一致
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt1, labelJson, configJson, labelName));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    const int32_t f0 = 0, f1 = 1, f2 = 2, f3 = 3;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    uint64_t count = 0;
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt2, &count));
    ASSERT_EQ(1, count);

    const char *tableName = "KV514";
    ASSERT_EQ(GMERR_OK, GmcKvCreateTable(stmt1, tableName, configJson));

    uint32_t key = 1000, value = 2000;
    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, tableName));
    ASSERT_EQ(GMERR_OK, GmcKvSet(stmt1, &key, sizeof(key), &value, sizeof(value)));

    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt2, tableName));
    uint32_t valueLen = sizeof(value);
    ASSERT_EQ(GMERR_OK, GmcKvGet(stmt2, &key, sizeof(key), &value, &valueLen));
    ASSERT_EQ(value, 2000);
    ASSERT_EQ(valueLen, sizeof(value));

    CreateAndStartEpoll(&g_responseEpollThreadId, &g_responseEpollFd);
    ASSERT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(EpollReg));
    ASSERT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(EpollReg));

    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    CreateAsyncConnAndStmt(g_serverLocator1, &connAsync, &stmtAsync);

    uint32_t received = 0;
    ASSERT_EQ(GMERR_OK, GmcTruncateNamespaceAsync(stmtAsync, nameSpace, CommonAsyncDDLCb, &received));
    WAIT_WHILE(received != 1);

    DestroyConnectionAndStmt(connAsync, stmtAsync);

    GmcStopHeartbeat(NULL);

    StopAndDestroyEpoll(g_responseEpollThreadId, &g_responseEpollFd);

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt1, &count));
    ASSERT_EQ(0, count);

    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt1, tableName));
    ASSERT_EQ(GMERR_NO_DATA, GmcKvGet(stmt1, &key, sizeof(key), &value, &valueLen));

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcGetVertexRecordCount(stmt2, &count));
    ASSERT_EQ(0, count);

    ASSERT_EQ(GMERR_OK, GmcKvPrepareStmtByLabelName(stmt2, tableName));
    ASSERT_EQ(GMERR_NO_DATA, GmcKvGet(stmt2, &key, sizeof(key), &value, &valueLen));

    ASSERT_EQ(GMERR_OK, GmcKvDropTable(stmt1, tableName));
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
    ASSERT_EQ(GMERR_OK, GmcDropNamespace(stmt1, nameSpace));
}

// 主键自增，插入10条数据，自增列同步自增
TEST_F(RealTimeReplication, AutoInc01)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "auto_increment":true},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    const int32_t count = 10;
    VertexLabelInsertDataWithoutIncRol(count, labelName, conn1, stmt1);

    uint32_t incExpect[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    VertexLabelQueryIncRol(count, labelName, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 主键自增，插入10条数据，删除1条，再插入一条
TEST_F(RealTimeReplication, AutoInc02)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "auto_increment":true},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    const int32_t count = 10;
    VertexLabelInsertDataWithoutIncRol(count, labelName, conn1, stmt1);

    const uint32_t f0 = 2;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_DELETE));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    const int32_t addCount = 1;
    VertexLabelInsertDataWithoutIncRol(addCount, labelName, conn1, stmt1);

    uint32_t incExpect[] = {1, 11, 3, 4, 5, 6, 7, 8, 9, 10};
    VertexLabelQueryIncRol(count, labelName, incExpect, stmt1);
    VertexLabelQueryIncRol(count, labelName, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 主键自增，插入10条数据，更新1条(update可以找到主键)，再插入一条
TEST_F(RealTimeReplication, AutoInc03)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "auto_increment":true},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    const int32_t count = 10;
    VertexLabelInsertDataWithoutIncRol(count, labelName, conn1, stmt1);

    uint32_t f0 = 1;
    int32_t f3 = 33;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_UPDATE));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    const int32_t addCount = 1;
    VertexLabelInsertDataWithoutIncRol(addCount, labelName, conn1, stmt1);

    uint32_t incExpect[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    VertexLabelQueryIncRol(count + addCount, labelName, incExpect, stmt1);
    VertexLabelQueryIncRol(count + addCount, labelName, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 主键自增，插入10条数据，更新1条(找不到主键)，再插入一条
TEST_F(RealTimeReplication, AutoInc04)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "auto_increment":true},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    const int32_t count = 10;
    VertexLabelInsertDataWithoutIncRol(count, labelName, conn1, stmt1);

    uint32_t f0 = 20;
    int32_t f3 = 33;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_UPDATE));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    const int32_t addCount = 1;
    VertexLabelInsertDataWithoutIncRol(addCount, labelName, conn1, stmt1);

    uint32_t incExpect[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    VertexLabelQueryIncRol(count + addCount, labelName, incExpect, stmt1);
    VertexLabelQueryIncRol(count + addCount, labelName, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 主键自增，插入10条数据，回滚一次，再插入一条数据，预期可以正确自增
TEST_F(RealTimeReplication, AutoInc05)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "auto_increment":true},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    const int32_t count = 10;
    VertexLabelInsertDataWithoutIncRol(count, labelName, conn1, stmt1);

    ASSERT_EQ(GMERR_OK, GmcTransStart(conn1, NULL));
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_REPLACE));
    const int32_t f2 = 22, f3 = 33;
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));
    ASSERT_EQ(GMERR_OK, GmcTransRollBack(conn1));

    const int32_t addCount = 1;
    VertexLabelInsertDataWithoutIncRol(addCount, labelName, conn1, stmt1);

    uint32_t incExpect[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    VertexLabelQueryIncRol(count, labelName, incExpect, stmt1);
    VertexLabelQueryIncRol(count, labelName, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 主键自增，插入10条数据，清空表，再插入2条数据，预期可以重置自增列，再正确自增
TEST_F(RealTimeReplication, AutoInc06)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "auto_increment":true},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    const int32_t count = 10;
    VertexLabelInsertDataWithoutIncRol(count, labelName, conn1, stmt1);

    ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt1, labelName));

    const int32_t addCount = 2;
    VertexLabelInsertDataWithoutIncRol(addCount, labelName, conn1, stmt1);

    uint32_t incExpect[] = {1, 2};
    VertexLabelQueryIncRol(2, labelName, incExpect, stmt1);
    VertexLabelQueryIncRol(2, labelName, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 非主键自增，主机插入10条数据，预期备机可以正确自增
TEST_F(RealTimeReplication, AutoInc07)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"uint32", "auto_increment":true},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    int32_t count = 10;
    VertexLabelInsertDataIncRolNotPri(count, labelName, conn1, stmt1);

    uint32_t incKey[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    uint32_t incExpect[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    VertexLabelQueryIncRolNotPri(count, labelName, incKey, incExpect, stmt1);
    VertexLabelQueryIncRolNotPri(count, labelName, incKey, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 非主键自增，主机插入10条数据，再merge一条(能找到，相当于update)，然后再插入，预期备机可以正确自增
TEST_F(RealTimeReplication, AutoInc08)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"uint32", "auto_increment":true},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    int32_t count = 10;
    VertexLabelInsertDataIncRolNotPri(count, labelName, conn1, stmt1);

    int32_t f3 = 33, f0 = 7;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    f0 = 100, f3 = 300;
    int32_t f2 = 200;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    uint32_t incKey[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 100};
    uint32_t incExpect[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    VertexLabelQueryIncRolNotPri(count + 1, labelName, incKey, incExpect, stmt1);
    VertexLabelQueryIncRolNotPri(count + 1, labelName, incKey, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 非主键自增，主机插入10条数据，再merge一条(找不到，相当于insert)，然后再插入，预期备机可以正确自增
TEST_F(RealTimeReplication, AutoInc09)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"uint32", "auto_increment":true},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    int32_t count = 10;
    VertexLabelInsertDataIncRolNotPri(count, labelName, conn1, stmt1);

    int32_t f3 = 33, f0 = 999;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_MERGE));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    f0 = 100, f3 = 300;
    int32_t f2 = 200;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    uint32_t incKey[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 999, 100};
    uint32_t incExpect[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12};
    VertexLabelQueryIncRolNotPri(count + 1, labelName, incKey, incExpect, stmt1);
    VertexLabelQueryIncRolNotPri(count + 1, labelName, incKey, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 非主键自增，插入10条数据，更新1条(能找到)，再插入一条
TEST_F(RealTimeReplication, AutoInc10)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"uint32", "auto_increment":true},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    const int32_t count = 10;
    VertexLabelInsertDataIncRolNotPri(count, labelName, conn1, stmt1);

    int32_t f0 = 1;
    int32_t f3 = 33;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_UPDATE));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    int32_t f2 = 22;
    f0 = 21, f3 = 23;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    uint32_t incKey[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 21};
    uint32_t incExpect[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    VertexLabelQueryIncRolNotPri(count + 1, labelName, incKey, incExpect, stmt1);
    VertexLabelQueryIncRolNotPri(count + 1, labelName, incKey, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 非主键自增，插入10条数据，更新1条(找不到)，再插入一条
TEST_F(RealTimeReplication, AutoInc11)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"uint32", "auto_increment":true},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    const int32_t count = 10;
    VertexLabelInsertDataIncRolNotPri(count, labelName, conn1, stmt1);

    int32_t f0 = 98;
    int32_t f3 = 33;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_UPDATE));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    int32_t f2 = 22;
    f0 = 21, f3 = 23;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    uint32_t incKey[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 21};
    uint32_t incExpect[] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
    VertexLabelQueryIncRolNotPri(count + 1, labelName, incKey, incExpect, stmt1);
    VertexLabelQueryIncRolNotPri(count + 1, labelName, incKey, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 非主键自增，插入10条数据，清空数据表，再插入一条
TEST_F(RealTimeReplication, AutoInc12)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"uint32", "auto_increment":true},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    const int32_t count = 10;
    VertexLabelInsertDataIncRolNotPri(count, labelName, conn1, stmt1);

    ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt1, labelName));

    int32_t f0 = 21, f2 = 22, f3 = 23;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    uint32_t incKey[] = {21};
    uint32_t incExpect[] = {1};
    VertexLabelQueryIncRolNotPri(1, labelName, incKey, incExpect, stmt1);
    VertexLabelQueryIncRolNotPri(1, labelName, incKey, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}

// 非主键自增，插入10条数据，删除一条，再插入一条
TEST_F(RealTimeReplication, AutoInc13)
{
    const char *labelName = "XT60";
    const char *autoincConfigJson = R"({"max_record_count":1000,  "auto_increment":1, "replication":2})";
    const char *autoincLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"uint32", "auto_increment":true},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, autoincLabelJson, autoincConfigJson));

    const int32_t count = 10;
    VertexLabelInsertDataIncRolNotPri(count, labelName, conn1, stmt1);

    int32_t f0 = 5;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_DELETE));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "XT60_K0"));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    f0 = 21;
    int32_t f2 = 22, f3 = 23;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, labelName, GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &f2, sizeof(f2)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &f3, sizeof(f3)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt1));

    uint32_t incKey[] = {1, 2, 3, 4, 6, 7, 8, 9, 10, 21};
    uint32_t incExpect[] = {1, 2, 3, 4, 6, 7, 8, 9, 10, 11};
    VertexLabelQueryIncRolNotPri(count, labelName, incKey, incExpect, stmt1);
    VertexLabelQueryIncRolNotPri(count, labelName, incKey, incExpect, stmt2);

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, labelName));
}
