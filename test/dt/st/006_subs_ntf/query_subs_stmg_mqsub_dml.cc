/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation of subscription message
 * Author: <PERSON><PERSON>(pink folyd)
 * Create: 2023-3-6
 */

#include "query_subs_st_base.h"

using namespace std;

extern const char *serverLocator;
extern const char *userName;
extern const char *pwd;

#define WAIT_WHILE(condition) \
    do {                      \
        usleep(5000);         \
    } while (condition)

static const char *subLabelJson =
    R"([{
        "type":"record",
        "name":"subLabel1",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"subLabel1",
                    "name":"subLabel1_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

const char *labelCfgJson = R"({
    "max_record_count":300000,
    "push_age_record_batch":20,
    "isFastReadUncommitted":false,
    "defragmentation":false,
    "status_merge_sub":true
    })";

class StQueryStmgOldSubs : public StTestSuitBase {
protected:
    static void SetUpTestCase()
    {
        SetStopAutoStart(true);
        SetConfigItems(
            "\"subsChannelGlobalShareMemSizeMax=32\" \"subsChannelGlobalDynamicMemSizeMax=32\" \"userPolicyMode=0\"");
        SetDefaultModConf(false);
        StartServerByCfgItems();
    }
    static void TearDownTestCase()
    {
        StopServer();
    }
};

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnReplace_Update)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace update", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"F1"
                            },
                            {
                                "property":"F2",
                                "value":4
                            }
                        ]
                }
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 1, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 0;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 3;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = i + 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE] < 1) {
        DbSleep(10);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnReplace_Update_NoChange)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace update", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"F1"
                            },
                            {
                                "property":"F2",
                                "value":3
                            }
                        ]
                }
        })";

    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 3, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 0;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = j;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 3;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE] < total) {
        DbSleep(10);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnUpdate_NoChange)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"update", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"F1"
                            },
                            {
                                "property":"F2",
                                "value":3
                            }
                        ]
                }
        })";

    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 2, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = j + 3;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int i = 1; i < 3; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_UPDATE));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel1_K0"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_UPDATE] < 2) {
        DbSleep(1);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnUpdate_WithoutCondValue_Set)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"update", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"F1"
                            }
                        ]
                }
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 2, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = j + 2;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    printf("update without set value\n");

    for (unsigned int i = 1; i < 3; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_UPDATE));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel1_K0"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    DbSleep(10);
    /* wait untill all events are received */
    EXPECT_EQ(0u, subVerify.receivedRows);
    printf("update set value\n");

    for (unsigned int i = 1; i < 3; i++) {
        int32_t F0Value = i;
        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_UPDATE));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel1_K0"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    /* wait untill all events are received */
    WAIT_WHILE_TIMEOUT(subVerify.receivedRows != 2u, 5);
    EXPECT_EQ(2u, subVerify.receivedRows);

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnUpdate_HaveCondValue_Set)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"update", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"F1",
                                "value":3
                            }
                        ]
                }
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 2, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = 3;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    printf("update without set value\n");

    for (unsigned int i = 1; i < 3; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_UPDATE));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel1_K0"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    DbSleep(10);
    /* wait untill all events are received */
    EXPECT_EQ(0, (int32_t)subVerify.receivedRows);
    printf("update set value\n");

    for (unsigned int i = 1; i < 3; i++) {
        int32_t F0Value = i;
        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_UPDATE));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel1_K0"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    WAIT_WHILE_TIMEOUT(subVerify.receivedRows != 1u, 5);
    /* wait untill all events are received */
    EXPECT_EQ(1u, subVerify.receivedRows);

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

#define ST_QRY_SUBS_SIZE 1024
char g_qryViewCmdStmg[ST_QRY_SUBS_SIZE];
char g_qryViewResStmg[ST_QRY_SUBS_SIZE];

void QrySetViewCmdStmg(const char *viewName, const char *fieldName, const char *delimiter)
{
    memset_s(g_qryViewCmdStmg, ST_QRY_SUBS_SIZE, 0, ST_QRY_SUBS_SIZE);
    snprintf(g_qryViewCmdStmg, ST_QRY_SUBS_SIZE, "gmsysview -q %s | grep -E '%s' | awk -F '%s' '{print $2}'", viewName,
        fieldName, delimiter);
}

int32_t GetViewSubsStmg()
{
    const char *viewName = "V\\$QRY_SUBS_QUEUE";
    const char *filedName = "PID";
    const char *delimiter = ":";
    QrySetViewCmdStmg(viewName, filedName, delimiter);
    memset_s(g_qryViewResStmg, ST_QRY_SUBS_SIZE, 0, ST_QRY_SUBS_SIZE);
    int32_t resultCnt = StGetViewFieldResult(g_qryViewCmdStmg, g_qryViewResStmg, ST_QRY_SUBS_SIZE);

    return resultCnt;
}

void AbnormalSubProcStmg(GmcConnT *syncConn)
{
    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"F1"
                            },
                            {
                                "property":"F2",
                                "value":4
                            }
                        ]
                }
        })";

    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmt = NULL;
    ret = GmcAllocStmt(syncConn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}
#ifndef RTOSV2X
TEST_F(StQueryStmgOldSubs, testSubReleaseClientAbnormalExit)
{
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    pid_t pid = fork();
    if (pid == -1) {
        printf("fork failed\n");
    } else if (pid == 0) {
        AbnormalSubProcStmg(syncConn);
        exit(0);
    }
    // 等待子进程退出
    wait(NULL);
    printf("child process exit\n");
    Status ret;
    for (unsigned int i = 1; i < 3; i++) {
        // insert
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = i + 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    int32_t queueCnt = GetViewSubsStmg();
    EXPECT_EQ(queueCnt, 0);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}
#endif

typedef struct {
    uint32_t count;
    uint32_t strBaseSize;
    char *strBaseData;
} CallBackParaT;
void SubCallbackStmg(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userdata)
{
    CallBackParaT *callBackpara = (CallBackParaT *)userdata;
    sleep(2);
    callBackpara->count++;
}

void testDrtConnSubsStatViewStmg()
{
    char cmd[1024];
    char const *viewName = "V\\$DRT_CONN_SUBS_STAT";
    snprintf(cmd, 1024, "gmsysview -q %s", viewName);

    const char *matchStr[] = {
        "Node[id: 10, name: SUBS_STATIS]",
        "SUB_TRY_CNT: 10",
        "SUB_SEND_SUC_CNT: 10",
        "Node[id: 7, name: SUBS_CYCLE_STATIS]",
        "SUB_TRY_CNT: 10",
        "SUB_SEND_SUC_CNT: 10",
        "SUB_TRY_CNT: 0",
        "SUB_SEND_SUC_CNT: 0",
        "SUB_TRY_CNT: 0",
        "SUB_SEND_SUC_CNT: 0",
        "SUB_TRY_CNT: 0",
        "SUB_SEND_SUC_CNT: 0",
        "SUB_TRY_CNT: 0",
        "SUB_SEND_SUC_CNT: 0",
        "SUB_TRY_CNT: 0",
        "SUB_SEND_SUC_CNT: 0",
    };

    int32_t ret = StExecuteCommandWithMatch(cmd, matchStr, sizeof(matchStr) / sizeof(char *));
    EXPECT_EQ(GMERR_OK, ret);

    char const *viewDrtConnStatName = "V\\$DRT_CONN_STAT";
    snprintf(cmd, 1024, "gmsysview -q %s", viewDrtConnStatName);

    const char *matchDrtConnStatStr[] = {"NODE_NAME: testDrtConnSubsStatViewStmg", "NODE_CLIENT_TYPE: SUB"};
    ret = StExecuteCommandWithMatch(cmd, matchDrtConnStatStr, sizeof(matchDrtConnStatStr) / sizeof(char *));
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StQueryStmgOldSubs, testDrtConnSubsStatViewStmg)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "testDrtConnSubsStatViewStmg";
    uint32_t size = 64;
    GmcConnOptionsT *connOption;
    int32_t ret = GmcConnOptionsCreate(&connOption);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcConnOptionsSetServerLocator(connOption, serverLocator);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetConnName(connOption, subConnName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcConnOptionsSetMsgQueueSize(connOption, size);
#if (defined RTOSV2 || defined RTOSV2X)
    EXPECT_EQ(GMERR_OK, ret);
#else
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
#endif
    ret = GmcConnect(GMC_CONN_TYPE_SUB, connOption, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "testDrtConnSubsStatViewStmg";
    const char *subLabelViewJson =
        R"([{
            "type":"record",
            "name":"testDrtConnSubsStatViewStmg",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"testDrtConnSubsStatViewStmg",
                        "name":"testDrtConnSubsStatViewStmg_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelViewJson, labelCfgJson));
    GmcSubConfigT config;
    config.subsName = "testDrtConnSubsStatViewStmg";
    config.configJson = R"({
            "label_name":"testDrtConnSubsStatViewStmg",
            "comment":"testDrtConnSubsStatViewStmg subscription",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"F1"
                            }
                        ]
                }
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "testDrtConnSubsStatViewStmg", GMC_OPERATION_INSERT));
    uint32_t total = 10;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    sleep(2);
    testDrtConnSubsStatViewStmg();

    ret = GmcUnSubscribe(stmt, "testDrtConnSubsStatViewStmg");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);
}

static sem_t g_gmdbSubsSem;

void *insert_thread_stmg(void *arg)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    status_t ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));

    // keep the insert be ok
    unsigned int g_batch = (*(int *)arg) * 100;
    for (unsigned int i = 0; i < 100; i++) {
        // insert
        int32_t F0Value = g_batch + i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = F0Value + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = F0Value + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        if (i == 0) {
            sem_post(&g_gmdbSubsSem);
        }
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

TEST_F(StQueryStmgOldSubs, SubsAndDisConnect_MultThread)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    int32_t ret;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));
    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "persist":false,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"F1"
                            }
                        ]
                }
        })";

    sem_init(&g_gmdbSubsSem, 0, 0);
    for (unsigned int j = 0; j < 10; j++) {
        ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
        EXPECT_EQ(GMERR_OK, ret);
        SubVerifyT subVerify = {0};
        SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
        defer
        {
            SubsCommDestroySubVerify(&subVerify);
        };
        ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
        EXPECT_EQ(GMERR_OK, ret);

        pthread_t tid[1];
        ret = pthread_create(&tid[0], NULL, insert_thread_stmg, &j);
        EXPECT_EQ(GMERR_OK, ret);

        struct timespec timeout;
        clock_gettime(CLOCK_REALTIME, &timeout);
        timeout.tv_sec += 10;
        sem_timedwait(&g_gmdbSubsSem, &timeout);
        ret = GmcDisconnect(subChan);
        EXPECT_EQ(GMERR_OK, ret);

        pthread_join(tid[0], NULL);
        ret = GmcUnSubscribe(stmt, "subVertexLabel1");
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

#ifndef ASAN
TEST_F(StQueryStmgOldSubs, testSubPushFullSync_Split)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    GmcConnT *subConn = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subConn);
    EXPECT_EQ(GMERR_OK, ret);

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback and user data */

    constexpr uint32_t total = 1000;
    /* full sync seq scan */
    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i + 0;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1 = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 full sync",
        "events":
        [
            {
                "type":"initial_load"
            }
        ]
    }
    )";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 10, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = j + 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(syncStmt, &config, subConn, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    WAIT_WHILE(subRowStatis->typeNum[GMC_SUB_EVENT_INITIAL_LOAD] != total);
    ret = GmcDisconnect(subConn);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StQueryStmgOldSubs, testSubPushFullSync_Split_scan)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    GmcConnT *subConn = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subConn);
    EXPECT_EQ(GMERR_OK, ret);

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback and user data */

    constexpr uint32_t total = 1000;
    /* full sync seq scan */
    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i + 0;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1 = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 full sync",
        "events":
        [
            {
                "type":"initial_scan"
            }
        ],
        "constraint": {
            "operator_type": "and",
           "conditions": [
               {
                   "property": "F1",
                   "cmp_type": 6,
                   "left_value": 1,
                   "right_value": 10
               }
            ]
        }
    }
    )";

    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 10, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = j + 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(syncStmt, &config, subConn, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    WAIT_WHILE(subVerify.verifyRow.fetchRowsIndex != 10);
    (void)subRowStatis;
    ret = GmcDisconnect(subConn);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StQueryStmgOldSubs, testSubPushFullSync_Split_FixFiveSubs)
{
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);
    GmcConnT *subConn = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subConn);
    EXPECT_EQ(GMERR_OK, ret);

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback and user data */

    constexpr uint32_t total = 3000;
    /* full sync seq scan */
    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1 = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 full sync",
        "events":
        [
            {
                "type":"initial_load"
            }
        ]
    }
    )";
    GmcSubConfigT config2;
    config2.subsName = "subVertexLabel2";
    config2.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 full sync",
        "events":
        [
            {
                "type":"initial_load"
            }
        ]
    }
    )";
    GmcSubConfigT config3;
    config3.subsName = "subVertexLabel3";
    config3.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 full sync",
        "events":
        [
            {
                "type":"initial_load"
            }
        ]
    }
    )";
    GmcSubConfigT config4;
    config4.subsName = "subVertexLabel4";
    config4.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 full sync",
        "events":
        [
            {
                "type":"initial_load"
            }
        ]
    }
    )";
    GmcSubConfigT config5;
    config5.subsName = "subVertexLabel5";
    config5.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 full sync",
        "events":
        [
            {
                "type":"initial_load"
            }
        ]
    }
    )";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 10, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = j + 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(syncStmt, &config, subConn, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    SubVerifyT subVerify2 = {0};
    SubsCommInitAndMallocSubVerify(&subVerify2, config2.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify2);
    };
    SubRowStatisT *subRowStatis2 = (SubRowStatisT *)subVerify2.verifyHeader.headerStatis;
    ret = GmcSubscribe(syncStmt, &config2, subConn, SubsCallbackComm, &subVerify2);
    EXPECT_EQ(GMERR_OK, ret);
    SubVerifyT subVerify3 = {0};
    SubsCommInitAndMallocSubVerify(&subVerify3, config3.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify3);
    };
    SubRowStatisT *subRowStatis3 = (SubRowStatisT *)subVerify3.verifyHeader.headerStatis;
    ret = GmcSubscribe(syncStmt, &config3, subConn, SubsCallbackComm, &subVerify3);
    EXPECT_EQ(GMERR_OK, ret);
    SubVerifyT subVerify4 = {0};
    SubsCommInitAndMallocSubVerify(&subVerify4, config4.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify4);
    };
    SubRowStatisT *subRowStatis4 = (SubRowStatisT *)subVerify4.verifyHeader.headerStatis;
    ret = GmcSubscribe(syncStmt, &config4, subConn, SubsCallbackComm, &subVerify4);
    EXPECT_EQ(GMERR_OK, ret);
    SubVerifyT subVerify5 = {0};
    SubsCommInitAndMallocSubVerify(&subVerify5, config5.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify5);
    };
    SubRowStatisT *subRowStatis5 = (SubRowStatisT *)subVerify5.verifyHeader.headerStatis;
    ret = GmcSubscribe(syncStmt, &config5, subConn, SubsCallbackComm, &subVerify5);
    EXPECT_EQ(GMERR_OK, ret);
    WAIT_WHILE(subRowStatis->typeNum[GMC_SUB_EVENT_INITIAL_LOAD] != total ||
               subRowStatis2->typeNum[GMC_SUB_EVENT_INITIAL_LOAD] != total ||
               subRowStatis3->typeNum[GMC_SUB_EVENT_INITIAL_LOAD] != total ||
               subRowStatis4->typeNum[GMC_SUB_EVENT_INITIAL_LOAD] != total ||
               subRowStatis5->typeNum[GMC_SUB_EVENT_INITIAL_LOAD] != total);
    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(syncStmt, config2.subsName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(syncStmt, config3.subsName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(syncStmt, config4.subsName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(syncStmt, config5.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcDisconnect(subConn);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexForMaxMem_TwoSubs)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *subChan2 = NULL;
    const char *subConnName2 = "subConnName2";
    ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName2, &subChan2);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));
    const char *subLabelName2 = "subLabel2";
    const char *subLabelJson2 =
        R"([{
            "type":"record",
            "name":"subLabel2",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel2",
                        "name":"subLabel2_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson2, labelCfgJson));

    GmcSubConfigT subConfig;
    subConfig.subsName = "subVertexLabel1";
    subConfig.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"update", "msgTypes":["new object", "old object"]},
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "is_reliable":true
        })";
    SubDeadlockT subVerify = {0};
    subVerify.needRecvWait = true;
    ret = GmcSubscribe(stmt, &subConfig, subChan, SubsCallbackDeadlock, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT subConfig2;
    subConfig2.subsName = "subVertexLabel2";
    subConfig2.configJson = R"({
            "label_name":"subLabel2",
            "comment":"VertexLabel2 subscription",
            "events":
                [
                    {"type":"update", "msgTypes":["new object", "old object"]},
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "is_reliable":true
        })";
    SubDeadlockT subVerify2 = {0};
    subVerify2.needRecvWait = true;
    ret = GmcSubscribe(stmt, &subConfig2, subChan2, SubsCallbackDeadlock, &subVerify2);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int i = 1; i <= 6000; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback1------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_UPDATE));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel1_K0"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback2------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_SUB_PUSH_QUEUE_FULL, ret);
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=subsChannelTopShmCtx");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f PARENT_CTX_NAME=subsChannelTopShmCtx");
#else
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f CTX_NAME=subsChannelTopDynmemCtx");
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f PARENT_CTX_NAME=subsChannelTopDynmemCtx");
#endif
    for (unsigned int i = 1; i <= 6000; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel2", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback11------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel2", GMC_OPERATION_UPDATE));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel2_K0"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback22------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_SUB_PUSH_QUEUE_FULL, ret);
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=subsChannelTopShmCtx");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f PARENT_CTX_NAME=subsChannelTopShmCtx");
#else
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f CTX_NAME=subsChannelTopDynmemCtx");
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f PARENT_CTX_NAME=subsChannelTopDynmemCtx");
#endif

    subVerify.needRecvWait = false;
    subVerify2.needRecvWait = false;
    usleep(1000000);
    printf("subVerify.updateMsg:%d\n", subVerify.typeNum[GMC_SUB_EVENT_UPDATE]);
    printf("subVerify.insertMsg:%d\n", subVerify.typeNum[GMC_SUB_EVENT_INSERT]);
    printf("subVerify2.updateMsg:%d\n", subVerify2.typeNum[GMC_SUB_EVENT_UPDATE]);
    printf("subVerify2.insertMsg:%d\n", subVerify2.typeNum[GMC_SUB_EVENT_INSERT]);
    // 订阅通道的内存上限相同，但是由于心跳信号的报文也会占用订阅通道内存，所以接收的推送消息数量有一定的浮动
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=subsChannelTopShmCtx");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f PARENT_CTX_NAME=subsChannelTopShmCtx");
#else
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f CTX_NAME=subsChannelTopDynmemCtx");
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f PARENT_CTX_NAME=subsChannelTopDynmemCtx");
#endif

    subVerify.typeNum[GMC_SUB_EVENT_UPDATE] = 0;
    subVerify.typeNum[GMC_SUB_EVENT_INSERT] = 0;
    subVerify.needRecvWait = true;
    subVerify2.typeNum[GMC_SUB_EVENT_UPDATE] = 0;
    subVerify2.typeNum[GMC_SUB_EVENT_INSERT] = 0;
    subVerify2.needRecvWait = true;

    for (unsigned int i = 6001; i <= 12000; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel2", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback11------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel2", GMC_OPERATION_UPDATE));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel2_K0"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback22------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_SUB_PUSH_QUEUE_FULL, ret);
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=subsChannelTopShmCtx");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f PARENT_CTX_NAME=subsChannelTopShmCtx");
#else
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f CTX_NAME=subsChannelTopDynmemCtx");
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f PARENT_CTX_NAME=subsChannelTopDynmemCtx");
#endif

    for (unsigned int i = 6001; i <= 12000; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback1------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_UPDATE));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel1_K0"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback2------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_SUB_PUSH_QUEUE_FULL, ret);
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=subsChannelTopShmCtx");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f PARENT_CTX_NAME=subsChannelTopShmCtx");
#else
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f CTX_NAME=subsChannelTopDynmemCtx");
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f PARENT_CTX_NAME=subsChannelTopDynmemCtx");
#endif

    subVerify.needRecvWait = false;
    subVerify2.needRecvWait = false;
    usleep(1000000);
    printf("subVerify.updateMsg:%d\n", subVerify.typeNum[GMC_SUB_EVENT_UPDATE]);
    printf("subVerify.insertMsg:%d\n", subVerify.typeNum[GMC_SUB_EVENT_INSERT]);
    printf("subVerify2.updateMsg:%d\n", subVerify2.typeNum[GMC_SUB_EVENT_UPDATE]);
    printf("subVerify2.insertMsg:%d\n", subVerify2.typeNum[GMC_SUB_EVENT_INSERT]);
    // 订阅通道的内存上限相同，但是由于心跳信号的报文也会占用订阅通道内存，所以接收的推送消息数量有一定的浮动
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=subsChannelTopShmCtx");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f PARENT_CTX_NAME=subsChannelTopShmCtx");
#else
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f CTX_NAME=subsChannelTopDynmemCtx");
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f PARENT_CTX_NAME=subsChannelTopDynmemCtx");
#endif

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "subVertexLabel2");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDisconnect(subChan2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName2);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexForMaxMem_DisConn)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT subConfig;
    subConfig.subsName = "subVertexLabel1";
    subConfig.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"update", "msgTypes":["new object", "old object"]},
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "is_reliable":true
        })";
    SubDeadlockT subVerify = {0};
    subVerify.needRecvWait = true;
    ret = GmcSubscribe(stmt, &subConfig, subChan, SubsCallbackDeadlock, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int i = 1; i <= 30000; i++) {
        // insert
        if (i % 1000 == 0) {
            printf("%d\n", i / 1000);
        }
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback1------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_UPDATE));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel1_K0"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback2------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_SUB_PUSH_QUEUE_FULL, ret);
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=subsChannelTopShmCtx");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f PARENT_CTX_NAME=subsChannelTopShmCtx");
#else
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f CTX_NAME=subsChannelTopDynmemCtx");
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f PARENT_CTX_NAME=subsChannelTopDynmemCtx");
#endif

    subVerify.needRecvWait = false;
    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);
    printf("subVerify.updateMsg:%d\n", subVerify.typeNum[GMC_SUB_EVENT_UPDATE]);
    printf("subVerify.insertMsg:%d\n", subVerify.typeNum[GMC_SUB_EVENT_INSERT]);
    // 断开订阅连接观察
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=subsChannelTopShmCtx");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f PARENT_CTX_NAME=subsChannelTopShmCtx");
#else
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f CTX_NAME=subsChannelTopDynmemCtx");
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f PARENT_CTX_NAME=subsChannelTopDynmemCtx");
#endif

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexForMaxMem_TwoSubsDisConn)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *subChan2 = NULL;
    const char *subConnName2 = "subConnName2";
    ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName2, &subChan2);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));
    const char *subLabelName2 = "subLabel2";
    const char *subLabelJson2 =
        R"([{
            "type":"record",
            "name":"subLabel2",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel2",
                        "name":"subLabel2_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson2, labelCfgJson));

    GmcSubConfigT subConfig;
    subConfig.subsName = "subVertexLabel1";
    subConfig.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"update", "msgTypes":["new object", "old object"]},
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "is_reliable":true
        })";
    SubDeadlockT subVerify = {0};
    subVerify.needRecvWait = true;
    ret = GmcSubscribe(stmt, &subConfig, subChan, SubsCallbackDeadlock, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT subConfig2;
    subConfig2.subsName = "subVertexLabel2";
    subConfig2.configJson = R"({
            "label_name":"subLabel2",
            "comment":"VertexLabel2 subscription",
            "events":
                [
                    {"type":"update", "msgTypes":["new object", "old object"]},
                    {"type":"insert", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "is_reliable":true
        })";
    SubDeadlockT subVerify2 = {0};
    subVerify2.needRecvWait = true;
    ret = GmcSubscribe(stmt, &subConfig2, subChan2, SubsCallbackDeadlock, &subVerify2);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int i = 1; i <= 6000; i++) {
        // insert
        if (i % 1000 == 0) {
            printf("%d\n", i / 1000);
        }
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback1------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_UPDATE));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel1_K0"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback2------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        // insert
        F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel2", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback11------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);

        // update
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel2", GMC_OPERATION_UPDATE));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel2_K0"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            printf("------open callback22------%u\n", i);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_SUB_PUSH_QUEUE_FULL, ret);
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=subsChannelTopShmCtx");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f PARENT_CTX_NAME=subsChannelTopShmCtx");
#else
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f CTX_NAME=subsChannelTopDynmemCtx");
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f PARENT_CTX_NAME=subsChannelTopDynmemCtx");
#endif

    subVerify.needRecvWait = false;
    ret = GmcDisconnect(subChan);
    sleep(1);
    EXPECT_EQ(GMERR_OK, ret);
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=subsChannelTopShmCtx");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f PARENT_CTX_NAME=subsChannelTopShmCtx");
#else
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f CTX_NAME=subsChannelTopDynmemCtx");
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f PARENT_CTX_NAME=subsChannelTopDynmemCtx");
#endif

    subVerify2.needRecvWait = false;
    ret = GmcDisconnect(subChan2);
    sleep(1);
    EXPECT_EQ(GMERR_OK, ret);
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=subsChannelTopShmCtx");
    system("gmsysview -q V\\$COM_SHMEM_CTX -f PARENT_CTX_NAME=subsChannelTopShmCtx");
#else
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f CTX_NAME=subsChannelTopDynmemCtx");
    system("gmsysview -q V\\$COM_DYN_CTX -s "
           "usocket:/run/verona/unix_emserver -f PARENT_CTX_NAME=subsChannelTopDynmemCtx");
#endif

    printf("subVerify.updateMsg:%d\n", subVerify.typeNum[GMC_SUB_EVENT_UPDATE]);
    printf("subVerify.insertMsg:%d\n", subVerify.typeNum[GMC_SUB_EVENT_INSERT]);
    printf("subVerify2.updateMsg:%d\n", subVerify2.typeNum[GMC_SUB_EVENT_UPDATE]);
    printf("subVerify2.insertMsg:%d\n", subVerify2.typeNum[GMC_SUB_EVENT_INSERT]);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "subVertexLabel2");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName2);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

#endif

void testMaxSubsDataMoreThan32K_OKCallbackStmg(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    CallBackParaT *callBackpara = (CallBackParaT *)userData;

    EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
    EXPECT_EQ(1u, info->labelCount);

    char pushLabelName[MAX_TABLE_NAME_LEN] = {};
    uint32_t pushLabelNameSize = sizeof(pushLabelName);
    int ret = GmcSubGetLabelName(subStmt, 0, pushLabelName, &pushLabelNameSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strlen(pushLabelName), pushLabelNameSize);
    EXPECT_STREQ("subLabel1", pushLabelName);

    char *value = (char *)malloc(sizeof(char) * callBackpara->strBaseSize);
    EXPECT_NE((uint64_t)value, 0u);
    if (value == NULL) {
        printf("malloc error for callBackpara as size %u\n", callBackpara->strBaseSize);
        return;
    }

    for (bool eof;; callBackpara->count++) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }

        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t propSize;
        ret = GmcGetVertexPropertySizeByName(subStmt, "F0", &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(sizeof(uint32_t), propSize);

        uint32_t F0V;
        bool isNull;
        ret = GmcGetVertexPropertyByName(subStmt, "F0", &F0V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(callBackpara->count, F0V);

        // F1
        ret = GmcGetVertexPropertySizeByName(subStmt, "F1", &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(propSize, callBackpara->strBaseSize);

        ret = GmcGetVertexPropertyByName(subStmt, "F1", value, propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ(callBackpara->strBaseData, value);

        // F2
        ret = GmcGetVertexPropertySizeByName(subStmt, "F2", &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(propSize, callBackpara->strBaseSize);

        ret = GmcGetVertexPropertyByName(subStmt, "F2", value, propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ(callBackpara->strBaseData, value);

        // F3
        ret = GmcGetVertexPropertySizeByName(subStmt, "F3", &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(propSize, callBackpara->strBaseSize);

        ret = GmcGetVertexPropertyByName(subStmt, "F3", value, propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ(callBackpara->strBaseData, value);

        // F4
        ret = GmcGetVertexPropertySizeByName(subStmt, "F4", &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(propSize, callBackpara->strBaseSize);

        ret = GmcGetVertexPropertyByName(subStmt, "F4", value, propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_STREQ(callBackpara->strBaseData, value);
    }
    free(value);
};

TEST_F(StQueryStmgOldSubs, testSubPushFullSyncMult)
{
    GmcConnT *subConn = NULL;
    const char *subConnName = "subVertexLabel1";
    GmcConnOptionsT *connOption;
    int32_t ret = GmcConnOptionsCreate(&connOption);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcConnOptionsSetServerLocator(connOption, serverLocator);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcConnOptionsSetConnName(connOption, subConnName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcConnect(GMC_CONN_TYPE_SUB, connOption, &subConn);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    CreateSyncConnectionAndStmt(&syncConn, &syncStmt);

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback and user data */

    constexpr uint32_t total = 1000;
    /* full sync seq scan */
    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1 = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 full sync",
        "events":
        [
            {
                "type":"initial_load"
            },
            {
                "type":"insert",
                "msgTypes":["new object", "old object"]
            },
            {
                "type":"delete",
                "msgTypes":["new object", "old object"]
            },
            {
                "type":"update",
                "msgTypes":["new object", "old object"]
            },
            {
                "type":"replace update",
                "msgTypes":["new object", "old object"]
            }
        ]
    }
    )";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 20, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = j + 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(syncStmt, &config, subConn, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    WAIT_WHILE(subRowStatis->typeNum[GMC_SUB_EVENT_INITIAL_LOAD] != total);

    /* update */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i;
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1 = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K0");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(subRowStatis->typeNum[GMC_SUB_EVENT_UPDATE] != total);

    /* delete */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i;
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K0");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(subRowStatis->typeNum[GMC_SUB_EVENT_DELETE] != total);

    /* insert */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1 = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(subRowStatis->typeNum[GMC_SUB_EVENT_INSERT] != total);

    /* replace */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1 = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE] != total);

    ret = GmcDisconnect(subConn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(syncStmt, config.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(syncStmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, syncStmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnReplace_UpdateWithStringCond)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace update", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"F1"
                            },
                            {
                                "property":"F2",
                                "value":"4"
                            }
                        ]
                }
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 10, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 0;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t total = 3;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = i + 3;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE] < 1) {
        DbSleep(1);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPush_TruncateBackground_And_Replace)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson1 =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson1, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"replace insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["old object"]},
                    {"type":"age", "msgTypes":["old object"]}
                ],
            "retry":true
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t total = 5;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDeleteAllFast(stmt, "subLabel1");
    uint32_t retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    ASSERT_EQ(GMERR_OK, ret);
    for (unsigned int i = 0; i < total; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    DbSleep(10);
    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT] != total) {
        DbSleep(10);
        printf("subRowStatis->replaceInsertNum:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    }
    EXPECT_EQ(5, (int32_t)(subRowStatis->typeNum[GMC_SUB_EVENT_DELETE] + subRowStatis->typeNum[GMC_SUB_EVENT_AGED]));
    EXPECT_EQ(5, (int32_t)subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    printf("deleteMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    printf("repInsertMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    printf("ageMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPush_TruncateBackground_And_Merge)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson1 =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson1, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"merge insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete","msgTypes":["old object"]},
                    {"type":"age","msgTypes":["old object"]}
                ],
            "retry":true
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 800;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDeleteAllFast(stmt, "subLabel1");
    uint32_t retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    ASSERT_EQ(GMERR_OK, ret);
    for (unsigned int i = 0; i < total; i++) {
        int32_t F0Value = i;
        // merge
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_MERGE));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "subLabel1_K0");
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    DbSleep(50);
    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_MERGE_INSERT] != total) {
        DbSleep(50);
        printf("subRowStatis->mergeInsertNum:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_MERGE_INSERT]);
    }
    EXPECT_EQ(800, (int32_t)(subRowStatis->typeNum[GMC_SUB_EVENT_DELETE] + subRowStatis->typeNum[GMC_SUB_EVENT_AGED]));
    EXPECT_EQ(800, (int32_t)subRowStatis->typeNum[GMC_SUB_EVENT_MERGE_INSERT]);
    printf("deleteMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    printf("merInsertMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_MERGE_INSERT]);
    printf("ageMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPush_TruncateBackground_And_Replace_NullOldMsg)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete"},
                    {"type":"age"}
                ],
            "retry":true
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 800;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDeleteAllFast(stmt, "subLabel1");
    uint32_t retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    ASSERT_EQ(GMERR_OK, ret);
    DbSleep(50);

    for (unsigned int i = 0; i < total; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    DbSleep(50);

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT] != total) {
        DbSleep(50);
        printf("subRowStatis->replaceInsertNum:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    }
    EXPECT_EQ(800, (int32_t)(subRowStatis->typeNum[GMC_SUB_EVENT_DELETE] + subRowStatis->typeNum[GMC_SUB_EVENT_AGED]));
    EXPECT_EQ(800, (int32_t)subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    printf("deleteMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    printf("repInsertMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    printf("ageMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    EXPECT_EQ(800, (int32_t)subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    EXPECT_EQ(0, (int32_t)subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPush_TruncateBackground_And_Age)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace insert", "msgTypes":["new object", "old object"]},
                    {"type":"replace update"},
                    {"type":"delete"},
                    {"type":"age"}
                ],
            "retry":true
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 900;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 对账操作
    ret = GmcBeginCheck(stmt, subLabelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int i = 0; i < total / 3; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcEndCheck(stmt, subLabelName, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDeleteAllFast(stmt, "subLabel1");
    uint32_t retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    ASSERT_EQ(GMERR_OK, ret);

    for (unsigned int i = 0; i < total; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT] != total) {
        DbSleep(100);
        printf("subRowStatis->replaceInsertNum:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    }
    EXPECT_EQ(total / 3, subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    EXPECT_EQ(total, subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    EXPECT_EQ(total / 3, subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE]);
    EXPECT_EQ(total * 2 / 3, subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    printf("deleteMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    printf("repInsertMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    printf("repUpdateMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE]);
    printf("ageMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPush_TruncateBackground_And_Replace_Twolabel)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete"},
                    {"type":"age"}
                ],
            "retry":true
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    const char *subLabelName2 = "subLabel2";
    const char *subLabelJson2 =
        R"([{
            "type":"record",
            "name":"subLabel2",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel2",
                        "name":"subLabel2_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson2, labelCfgJson));

    GmcSubConfigT config2;
    config2.subsName = "subVertexLabel2";
    config2.configJson = R"({
            "label_name":"subLabel2",
            "comment":"VertexLabel2 subscription",
            "events":
                [
                    {"type":"replace insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete"},
                    {"type":"age"}
                ],
            "retry":true
        })";

    SubVerifyT subVerify2 = {0};
    SubsCommInitAndMallocSubVerify(&subVerify2, config2.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify2);
    };
    SubRowStatisT *subRowStatis2 = (SubRowStatisT *)subVerify2.verifyHeader.headerStatis;
    ret = GmcSubscribe(stmt, &config2, subChan, SubsCallbackComm, &subVerify2);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 10;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // insert
        F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel2", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDeleteAllFast(stmt, "subLabel1");
    uint32_t retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDeleteAllFast(stmt, "subLabel2");
    retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel2");
    }
    ASSERT_EQ(GMERR_OK, ret);

    for (unsigned int i = 0; i < total / 3; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
        // subLabel2
        F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel2", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_DELETE] != total ||
           subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT] != total / 3 ||
           subRowStatis2->typeNum[GMC_SUB_EVENT_DELETE] != total ||
           subRowStatis2->typeNum[GMC_SUB_EVENT_REPLACE_INSERT] != total / 3) {
        printf("subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]: %d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
        printf("subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]:%d\n",
            subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
        printf("subRowStatis2->typeNum[GMC_SUB_EVENT_DELETE]:%d\n", subRowStatis2->typeNum[GMC_SUB_EVENT_DELETE]);
        printf("subRowStatis2->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]:%d\n",
            subRowStatis2->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
        DbSleep(10);
    }
    EXPECT_EQ(total * 2, subRowStatis->typeNum[GMC_SUB_EVENT_DELETE] + subRowStatis2->typeNum[GMC_SUB_EVENT_DELETE]);
    EXPECT_EQ(total * 2 / 3,
        subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT] + subRowStatis2->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    EXPECT_EQ(0, (int32_t)(subRowStatis->typeNum[GMC_SUB_EVENT_AGED] + subRowStatis2->typeNum[GMC_SUB_EVENT_AGED]));
    printf(
        "deleteMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE] + subRowStatis2->typeNum[GMC_SUB_EVENT_DELETE]);
    printf("repInsertMsg:%d\n",
        subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT] + subRowStatis2->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    printf("ageMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED] + subRowStatis2->typeNum[GMC_SUB_EVENT_AGED]);
    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "subVertexLabel2");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName2);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPush_TruncateBackground_And_Age_Mulity)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);
    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));
    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace insert", "msgTypes":["new object", "old object"]},
                    {"type":"replace update"},
                    {"type":"delete"},
                    {"type":"age"}
                ],
            "retry":true
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t total = 9000;

    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 对账操作
    ret = GmcBeginCheck(stmt, subLabelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    for (unsigned int i = 0; i < total / 3; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcEndCheck(stmt, subLabelName, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDeleteAllFast(stmt, "subLabel1");
    uint32_t retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    ASSERT_EQ(GMERR_OK, ret);
    for (unsigned int i = 0; i < 1000; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDeleteAllFast(stmt, "subLabel1");
    retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    ASSERT_EQ(GMERR_OK, ret);
    for (unsigned int i = 0; i < 1000; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDeleteAllFast(stmt, "subLabel1");
    retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    ASSERT_EQ(GMERR_OK, ret);
    for (unsigned int i = 0; i < 1000; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDeleteAllFast(stmt, "subLabel1");
    retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    ASSERT_EQ(GMERR_OK, ret);
    for (unsigned int i = 0; i < 1000; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDeleteAllFast(stmt, "subLabel1");
    retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    ASSERT_EQ(GMERR_OK, ret);
    for (unsigned int i = 0; i < 1000; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDeleteAllFast(stmt, "subLabel1");
    retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    ASSERT_EQ(GMERR_OK, ret);
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$QRY_AGE_TASK -s channel:");
#else
    system("gmsysview -q V\\$QRY_AGE_TASK -s "
           "usocket:/run/verona/unix_emserver");
#endif
    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_DELETE] != total / 3 + 5000) {
        DbSleep(100);
    }
    EXPECT_EQ(total / 3 + 5000, subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    EXPECT_EQ(5000, (int32_t)subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    EXPECT_EQ(total / 3, subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE]);
    EXPECT_EQ(total * 2 / 3, subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    GmcCheckInfoT *checkInfo = NULL;
    ret = GmcGetCheckInfo(stmt, "subLabel1", 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(total * 2 / 3, ((GmcCheckInfoT *)checkInfo)->shouldAgedCnt);
    EXPECT_EQ(total / 3 + 5000, ((GmcCheckInfoT *)checkInfo)->shouldTruncateCnt);
    EXPECT_EQ(total * 2 / 3, ((GmcCheckInfoT *)checkInfo)->realAgedCnt);
    EXPECT_EQ(total / 3 + 5000, ((GmcCheckInfoT *)checkInfo)->realTruncatedCnt);

#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -s channel:");
    system("gmsysview -q V\\$QRY_AGE_TASK -s channel:");
#else
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -s "
           "usocket:/run/verona/unix_emserver");
    system("gmsysview -q V\\$QRY_AGE_TASK -s "
           "usocket:/run/verona/unix_emserver");
#endif

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPush_TruncateBackground_In_Check)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace insert", "msgTypes":["new object", "old object"]},
                    {"type":"replace update"},
                    {"type":"delete"},
                    {"type":"age"}
                ],
            "retry":true
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 900;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 对账操作
    ret = GmcBeginCheck(stmt, subLabelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int i = 0; i < total / 3; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDeleteAllFast(stmt, "subLabel1");
    uint32_t retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }

    ret = GmcEndCheck(stmt, subLabelName, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDeleteAllFast(stmt, "subLabel1");
    retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    ASSERT_EQ(GMERR_OK, ret);

    for (unsigned int i = 0; i < total; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT] != total ||
           subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE] != total / 3) {
        DbSleep(100);
    }
    EXPECT_EQ(total, subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    EXPECT_EQ(total, subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    EXPECT_EQ(0u, subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    printf("deleteMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    printf("repInsertMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    printf("ageMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    GmcCheckInfoT *checkInfo = NULL;
    ret = GmcGetCheckInfo(stmt, "subLabel1", 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, ((GmcCheckInfoT *)checkInfo)->shouldAgedCnt);
    EXPECT_EQ(total, ((GmcCheckInfoT *)checkInfo)->shouldTruncateCnt);
    EXPECT_EQ(0u, ((GmcCheckInfoT *)checkInfo)->realAgedCnt);
    EXPECT_EQ(total, ((GmcCheckInfoT *)checkInfo)->realTruncatedCnt);

#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -s channel:");
#else
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -s "
           "usocket:/run/verona/unix_emserver");
#endif

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPush_TruncateBackground_In_Check_And_UpdateVerison2)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace insert", "msgTypes":["new object", "old object"]},
                    {"type":"replace update"},
                    {"type":"delete"},
                    {"type":"age"}
                ],
            "retry":true
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 900;

    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 对账操作
    ret = GmcBeginCheck(stmt, subLabelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int i = 0; i < total / 3; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDeleteAllFast(stmt, "subLabel1");
    uint32_t retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    for (unsigned int i = 10; i < 20; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcEndCheck(stmt, subLabelName, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$QRY_AGE_TASK -s channel:");
#else
    system("gmsysview -q V\\$QRY_AGE_TASK -s "
           "usocket:/run/verona/unix_emserver");
#endif

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_DELETE] != total) {
        DbSleep(1000);
        printf("deleteMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
        printf("repInsertMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
        printf("repUpdateMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE]);
        printf("ageMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    }
    EXPECT_EQ(total, subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    EXPECT_EQ(10u, subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    EXPECT_EQ(total / 3, subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE]);
    EXPECT_EQ(0u, subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    printf("deleteMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    printf("repInsertMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    printf("repUpdateMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE]);
    printf("ageMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    GmcCheckInfoT *checkInfo = NULL;
    ret = GmcGetCheckInfo(stmt, "subLabel1", 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, ((GmcCheckInfoT *)checkInfo)->shouldAgedCnt);
    EXPECT_EQ(total, ((GmcCheckInfoT *)checkInfo)->shouldTruncateCnt);
    EXPECT_EQ(0u, ((GmcCheckInfoT *)checkInfo)->realAgedCnt);
    EXPECT_EQ(total, ((GmcCheckInfoT *)checkInfo)->realTruncatedCnt);

#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -s channel:");
    system("gmsysview -q V\\$QRY_AGE_TASK -s channel:");
#else
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -s "
           "usocket:/run/verona/unix_emserver");
    system("gmsysview -q V\\$QRY_AGE_TASK -s "
           "usocket:/run/verona/unix_emserver");
#endif

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPush_TruncateBackground_In_Check_And_UpdateVerison1)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace insert", "msgTypes":["new object", "old object"]},
                    {"type":"replace update"},
                    {"type":"delete"},
                    {"type":"age"}
                ],
            "retry":true
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 900;

    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 对账操作
    ret = GmcBeginCheck(stmt, subLabelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int i = 0; i < total / 3; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDeleteAllFast(stmt, "subLabel1");
    uint32_t retryCnt = 0;
    // 锁冲突允许重试
    while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
        retryCnt++;
        ret = GmcDeleteAllFast(stmt, "subLabel1");
    }
    for (unsigned int i = total / 3 + 10; i < total / 3 + 20; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcEndCheck(stmt, subLabelName, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_DELETE] != total) {
        DbSleep(1000);
        printf("deleteMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
        printf("repInsertMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
        printf("repUpdateMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE]);
        printf("ageMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    }
    EXPECT_LE(total, subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    EXPECT_EQ(0u, subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    printf("deleteMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    printf("repInsertMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    printf("repUpdateMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE]);
    printf("ageMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);

    GmcCheckInfoT *checkInfo = NULL;
    ret = GmcGetCheckInfo(stmt, "subLabel1", 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0u, ((GmcCheckInfoT *)checkInfo)->shouldAgedCnt);
    EXPECT_EQ(total, ((GmcCheckInfoT *)checkInfo)->shouldTruncateCnt);
    EXPECT_EQ(0u, ((GmcCheckInfoT *)checkInfo)->realAgedCnt);
    EXPECT_EQ(total, ((GmcCheckInfoT *)checkInfo)->realTruncatedCnt);

#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -s channel:");
    system("gmsysview -q V\\$QRY_AGE_TASK -s channel:");
#else
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -s "
           "usocket:/run/verona/unix_emserver");
    system("gmsysview -q V\\$QRY_AGE_TASK -s "
           "usocket:/run/verona/unix_emserver");
#endif

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPush_Age_And_Replace_NullOldMsg)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"replace insert", "msgTypes":["new object", "old object"]},
                    {"type":"replace update"},
                    {"type":"delete"},
                    {"type":"age"}
                ],
            "retry":true
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 800;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 对账操作
    ret = GmcBeginCheck(stmt, subLabelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, subLabelName, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    for (unsigned int i = 0; i < total; i++) {
        int32_t F0Value = i;
        // replace
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_REPLACE));
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        uint32_t retryCnt = 0;
        // 锁冲突允许重试
        while (ret == GMERR_LOCK_NOT_AVAILABLE && retryCnt < 10) {
            retryCnt++;
            ret = GmcExecute(stmt);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    DbSleep(10);

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT] != total) {
        DbSleep(10);
        printf("subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]:%d\n",
            subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    }
    EXPECT_EQ(total, subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    EXPECT_EQ(total, subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
    printf("deleteMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_DELETE]);
    printf("repInsertMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT]);
    printf("repUpdateMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_UPDATE]);
    printf("ageMsg:%d\n", subRowStatis->typeNum[GMC_SUB_EVENT_AGED]);
#if (defined RTOSV2 || defined RTOSV2X)
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -s channel:");
#else
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_CHECK_INFO -s "
           "usocket:/run/verona/unix_emserver");
#endif

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexBitMap)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"bitmap", "size":32},
                    {"name":"F3", "type":"bytes"}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"subLabel1",
            "comment":"VertexLabel1 subscription",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"update", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"or",
                    "conditions":
                        [
                            {
                                "property":"F2",
                                "value":"0xffffffff"
                            }
                        ]
                }
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 2, 100);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 2;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = 32;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    subVerify.verifyRow.expecRows[0][0].expecValue.valInt = 0xffffffff;
    subVerify.verifyRow.expecRows[0][1].expecValue.valInt = 0xffffff00;
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBitMapT bitmap = {0, 31, NULL};
    uint8_t bits[4];
    memset_s(bits, 4, 0xffffffff, 4);
    bitmap.bits = bits;

    // insert
    int32_t F0Value = 2;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_INSERT));
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F1Value = 3;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, &bitmap, sizeof(GmcBitMapT));
    EXPECT_EQ(GMERR_OK, ret);
    char F3Value[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_BYTES, F3Value, 5);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // update
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "subLabel1", GMC_OPERATION_UPDATE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel1_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBitMapT bitmap2 = {0, 7, NULL};
    uint8_t bits2;
    memset_s(&bits2, 1, 0x00, 1);
    bitmap2.bits = &bits2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, &bitmap2, sizeof(GmcBitMapT));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_BYTES, F3Value, 5);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_INSERT] < 1 || subRowStatis->typeNum[GMC_SUB_EVENT_INSERT] < 1) {
        DbSleep(10);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

// 订阅deleted，写入1000条数据后开启核查，结束核查，核查事件回调的次数
TEST_F(StQueryStmgOldSubs, testSubDeletePushAge)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    Status ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *labelName = "myLabelName";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"myLabelName",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false}
                ],
            "subs_type":"status_merge",
            "keys":
                [
                    {
                        "node":"myLabelName",
                        "name":"myLabelPK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelJson, labelCfgJson));

    GmcSubConfigT subsConfig;
    subsConfig.subsName = "mySubsName";
    subsConfig.configJson = R"({
            "label_name":"myLabelName",
            "comment":"my subscription comment",
            "events":
                [
                    {"type":"delete"}
                ],
            "retry":true
        })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, subsConfig.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    ret = GmcSubscribe(stmt, &subsConfig, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    // insert 100 rows
    uint32_t insertRows = 100;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    for (uint32_t i = 0; i < insertRows; i++) {
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(i)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }
    // 启动、结束对账
    EXPECT_EQ(GMERR_OK, GmcBeginCheck(stmt, labelName, DB_INVALID_UINT8));
    EXPECT_EQ(GMERR_OK, GmcEndCheck(stmt, labelName, DB_INVALID_UINT8, false));
    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_AGED] != insertRows) {
        DbSleep(1);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subsConfig.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnInsertTree)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson1 = R"(
    [{
        "version": "2.0",
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "c0", "type": "uint32" },
            {
                "name": "c1",
                "type": "record",
                "fixed_array": true,
                "size": 1024,
                "fields": [
                    { "name": "f1", "type": "uint32", "nullable": true },
                    { "name": "f2", "type": "uint32", "nullable": true }
                ]
            },
            {
                "name": "c2",
                "type": "record",
                "vector": true,
                "size": 1024,
                "fields": [
                    { "name": "b1", "type": "uint32", "nullable": true },
                    { "name": "b2", "type": "uint32", "nullable": true }
                ]
            }
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "name": "table_pk",
                "index": { "type": "primary" },
                "node": "subLabel1",
                "fields": ["c0"],
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, subLabelJson1, labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "or",
            "conditions": [
                {
                    "property": "c1/f1",
                    "value": 1
                },
                {
                    "property": "c2/b1",
                    "value": 1
                }
            ]
        }
    }
    )";

    uint32_t total = 3;
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 1, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    const char *treePropertyName[2];
    treePropertyName[0] = "c1";
    treePropertyName[1] = "f1";
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->treeProp.treePropertyLevel = 2;
            expecRowData->treeProp.treePropertyName = treePropertyName;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_TREE;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcPrepareStmtByLabelName(stmt, subLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *node;
        ret = GmcGetRootNode(stmt, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "c0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetChildNode(stmt, "c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetChildNode(stmt, "c2", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_INSERT] < 1) {
        DbSleep(1);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnUpdateTree)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson1 = R"(
    [{
        "version": "2.0",
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "c0", "type": "uint32" },
            {
                "name": "c1",
                "type": "record",
                "array": true,
                "size": 1024,
                "fields": [
                    { "name": "f1", "type": "uint32", "nullable": true },
                    {
                        "name": "f2",
                        "type": "record",
                        "vector": true,
                        "size": 1024,
                        "fields": [
                            { "name": "b1", "type": "uint32", "nullable": true },
                            { "name": "b2", "type": "uint32", "nullable": true }
                        ]
                    }
                ]
            }
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "name": "table_pk",
                "index": { "type": "primary" },
                "node": "subLabel1",
                "fields": ["c0"],
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, subLabelJson1, labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "update", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "or",
            "conditions": [
                {
                    "property": "c1/f2/b1",
                    "value": 1
                }
            ]
        }
    }
    )";
    uint32_t total = 3;
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 1, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    const char *treePropertyName[3];
    treePropertyName[0] = "c1";
    treePropertyName[1] = "f2";
    treePropertyName[2] = "b1";
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->treeProp.treePropertyLevel = 3;
            expecRowData->treeProp.treePropertyName = treePropertyName;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_TREE;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcPrepareStmtByLabelName(stmt, subLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *node;
        ret = GmcGetRootNode(stmt, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "c0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *c1;

        ret = GmcNodeGetChild(node, "c1", &node);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeAppendElement(node, &c1);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *f2;
        ret = GmcNodeGetChild(c1, "f2", &f2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 3; j++) {
            ret = GmcNodeAppendElement(f2, &f2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(f2, "b1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
        }

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcPrepareStmtByLabelName(stmt, subLabelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *node;
        ret = GmcGetRootNode(stmt, &node);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "table_pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, 4));
        GmcNodeT *c1;
        ret = GmcNodeGetChild(node, "c1", &c1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(c1, 0, &c1);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *f2;
        ret = GmcNodeGetChild(c1, "f2", &f2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeRemoveElementByIndex(f2, 0);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_UPDATE] < 1) {
        DbSleep(1);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnReplaceWithCond_uint32)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);
    defer
    {
        ret = GmcDisconnect(subChan);
        EXPECT_EQ(GMERR_OK, ret);
        DestroyConnectionAndStmt(syncConn, stmt);
    };

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": true }
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, labelJson, labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */
    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "replace" , "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "F1",
                    "value": 3
                }
            ]
        }
    }
    )";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 2, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = 3;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(stmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    // insert first, then updates
    for (uint i = 0; i <= 1; ++i) {
        uint32_t total = (subVerify.receivedRows = 0);

        for (; total < 5; ++total) {
            uint32_t F0Value = total;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F1Value = total + i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }

        WAIT_WHILE(subVerify.receivedRows == 0);
        EXPECT_EQ(1u, subVerify.receivedRows);
    }
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnReplaceWithCond_fixed)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);
    defer
    {
        ret = GmcDisconnect(subChan);
        EXPECT_EQ(GMERR_OK, ret);
        DestroyConnectionAndStmt(syncConn, stmt);
    };

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name":"subs_fixed", "type":"fixed", "default":"ffff", "size":4}
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, labelJson, labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */
    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "replace" , "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "subs_fixed",
                    "value": "aaaa"
                }
            ]
        }
    }
    )";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 2, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = false;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = 4;
            expecRowData->expecValue.val = (char *)"aaaa";
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(stmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F0Value = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    const char *F1Value = "cccc";
    ret = GmcSetVertexProperty(stmt, "subs_fixed", GMC_DATATYPE_FIXED, F1Value, 4);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    const char *F1Value2 = "aaaa";
    ret = GmcSetVertexProperty(stmt, "subs_fixed", GMC_DATATYPE_FIXED, F1Value2, 4);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    const char *F1Value3 = "aaaa";
    ret = GmcSetVertexProperty(stmt, "subs_fixed", GMC_DATATYPE_FIXED, F1Value3, 4);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    const char *F1Value4 = "bbbb";
    ret = GmcSetVertexProperty(stmt, "subs_fixed", GMC_DATATYPE_FIXED, F1Value4, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    WAIT_WHILE(subVerify.receivedRows < 2);
    EXPECT_EQ(2u, subVerify.receivedRows);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnReplaceWithCond_float)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);
    defer
    {
        ret = GmcDisconnect(subChan);
        EXPECT_EQ(GMERR_OK, ret);
        DestroyConnectionAndStmt(syncConn, stmt);
    };

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "float", "nullable": true }
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, labelJson, labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */
    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "replace" , "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "F1",
                    "value": 3.1
                }
            ]
        }
    }
    )";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(stmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    // insert first, then update
    for (uint i = 0; i <= 1; ++i) {
        uint32_t total = (subVerify.receivedRows = 0);

        for (; total < 5; ++total) {
            uint32_t F0Value = total;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            float F1Value = total + i;
            F1Value += 0.1;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_FLOAT, &F1Value, sizeof(float));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }

        WAIT_WHILE(subVerify.receivedRows < 1);
        EXPECT_EQ(1u, subVerify.receivedRows);
    }
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnReplaceWithCond_double)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);
    defer
    {
        ret = GmcDisconnect(subChan);
        EXPECT_EQ(GMERR_OK, ret);
        DestroyConnectionAndStmt(syncConn, stmt);
    };

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "double", "nullable": true }
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, labelJson, labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "replace" , "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "F1",
                    "value": 3.1
                }
            ]
        }
    }
    )";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(stmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    // insert first, then update
    for (uint i = 0; i <= 1; ++i) {
        uint32_t total = (subVerify.receivedRows = 0);

        for (; total < 5; ++total) {
            uint32_t F0Value = total;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            double F1Value = total + i;
            F1Value += 0.1;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_DOUBLE, &F1Value, sizeof(double));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        WAIT_WHILE(subVerify.receivedRows < 1);
        EXPECT_EQ(1u, subVerify.receivedRows);
    }
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnReplaceWithCond_bytes)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);
    defer
    {
        ret = GmcDisconnect(subChan);
        EXPECT_EQ(GMERR_OK, ret);
        DestroyConnectionAndStmt(syncConn, stmt);
    };

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name":"subs_bytes", "type":"bytes", "default":"qwer", "size":4}
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, labelJson, labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "replace" , "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "subs_bytes",
                    "value": "aaaa"
                }
            ]
        }
    }
    )";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 2, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = false;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = 4;
            expecRowData->expecValue.val = (char *)"aaaa";
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(stmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F0Value = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    const char *F1Value = "qwer";
    ret = GmcSetVertexProperty(stmt, "subs_bytes", GMC_DATATYPE_BYTES, F1Value, 4);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    const char *F1Value2 = "aaaa";
    ret = GmcSetVertexProperty(stmt, "subs_bytes", GMC_DATATYPE_BYTES, F1Value2, 4);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    const char *F1Value3 = "aaaa";
    ret = GmcSetVertexProperty(stmt, "subs_bytes", GMC_DATATYPE_BYTES, F1Value3, 4);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    const char *F1Value4 = "bbbb";
    ret = GmcSetVertexProperty(stmt, "subs_bytes", GMC_DATATYPE_BYTES, F1Value4, 4);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    WAIT_WHILE(subVerify.receivedRows < 2);
    EXPECT_EQ(2u, subVerify.receivedRows);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnReplaceWithCond_string)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);
    defer
    {
        ret = GmcDisconnect(subChan);
        EXPECT_EQ(GMERR_OK, ret);
        DestroyConnectionAndStmt(syncConn, stmt);
    };

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name":"subs_string", "type":"string", "default":"qwe", "size":4}
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, labelJson, labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "replace" , "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "subs_string",
                    "value": "aaa"
                }
            ]
        }
    }
    )";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 2, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = false;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = 4;
            expecRowData->expecValue.val = (char *)"aaa";
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(stmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F0Value = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    const char *F1Value = "bbb";
    ret = GmcSetVertexProperty(stmt, "subs_string", GMC_DATATYPE_STRING, F1Value, 3);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    const char *F1Value2 = "aaa";
    ret = GmcSetVertexProperty(stmt, "subs_string", GMC_DATATYPE_STRING, F1Value2, 3);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    const char *F1Value3 = "aaa";
    ret = GmcSetVertexProperty(stmt, "subs_string", GMC_DATATYPE_STRING, F1Value3, 3);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    const char *F1Value4 = "bbb";
    ret = GmcSetVertexProperty(stmt, "subs_string", GMC_DATATYPE_STRING, F1Value4, 3);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    WAIT_WHILE(subVerify.receivedRows < 2);
    EXPECT_EQ(2u, subVerify.receivedRows);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnReplaceWithCond_boolean)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);
    defer
    {
        ret = GmcDisconnect(subChan);
        EXPECT_EQ(GMERR_OK, ret);
        DestroyConnectionAndStmt(syncConn, stmt);
    };

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "boolean", "nullable": true }
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, labelJson, labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "replace" , "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "F1",
                    "value": true
                }
            ]
        }
    }
    )";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 0, 0, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(stmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    bool F1Value = false;
    // insert first, then update
    for (uint i = 0; i <= 1; ++i) {
        uint32_t total = (subVerify.receivedRows = 0);

        for (; total < 2; ++total) {
            uint32_t F0Value = total;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            F1Value = !F1Value;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_BOOL, &F1Value, sizeof(bool));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        F1Value = !F1Value;

        WAIT_WHILE(subVerify.receivedRows < 1);
        EXPECT_EQ(1u, subVerify.receivedRows);
    }
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnReplaceTree)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson1 = R"(
    [{
        "version": "2.0",
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "c0", "type": "uint32" },
            {
                "name": "c1",
                "type": "record",
                "array": true,
                "size": 1024,
                "fields": [
                    { "name": "f1", "type": "uint32" , "nullable": true},
                    {
                        "name": "f2",
                        "type": "record",
                        "vector": true,
                        "size": 1024,
                        "fields": [
                            { "name": "b1", "type": "uint32", "nullable": true },
                            { "name": "b2", "type": "uint32" , "nullable": true}
                        ]
                    }
                ]
            }
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "name": "table_pk",
                "index": { "type": "primary" },
                "node": "subLabel1",
                "fields": ["c0"],
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, subLabelJson1, labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "replace insert", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "c1/f2/b1",
                    "value": 1
                },
                {
                    "property": "c1/f2/b2"
                }
            ]
        }
    }
    )";
    uint32_t total = 3;
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 1, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    const char *treePropertyName[3];
    treePropertyName[0] = "c1";
    treePropertyName[1] = "f2";
    treePropertyName[2] = "b1";
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->treeProp.treePropertyLevel = 3;
            expecRowData->treeProp.treePropertyName = treePropertyName;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_TREE;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcPrepareStmtByLabelName(stmt, subLabelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *node;
        ret = GmcGetRootNode(stmt, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "c0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetChildNode(stmt, "c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *c1;
        ret = GmcNodeAppendElement(node, &c1);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *f2;
        ret = GmcNodeGetChild(c1, "f2", &f2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 3; j++) {
            ret = GmcNodeAppendElement(f2, &f2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(f2, "b1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
        }

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_REPLACE_INSERT] < 1) {
        DbSleep(1);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, createLabelSubsWithPartitionCond)
{
    GmcConnT *subChan = nullptr;
    const char *subConnName = "subConnName";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *conn = nullptr;
    GmcStmtT *stmt = nullptr;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelName = "ip4foward_partition";
    const char *labelJson =
        R"([{
            "type": "record",
            "name": "ip4foward_partition",
            "config": {
                "check_validity": true
            },
            "max_record_count": 4000000,
            "fields": [
                { "name": "vr_id","type": "uint32","nullable": false  },
                { "name": "vrf_index","type": "uint32","default":100 ,"nullable":false},
                { "name": "dest_ip_addr", "type": "uint32" },
                { "name": "mask_len", "type": "uint8" },
                { "name": "nhp_group_flag","type": "uint8" } ,
                { "name": "qos_profile_id", "type": "uint16" },
                { "name": "primary_label", "type": "uint32" },
                { "name": "attribute_id","type": "uint32" },
                { "name": "nhp_group_id","type": "uint32" },
                { "name": "route_flags", "type": "uint32" },
                { "name": "flags", "type": "uint32" },
                { "name": "part", "type": "partition","nullable":false}
            ],
            "subs_type":"status_merge",
            "keys": [
                { "name": "primary_key", "index": { "type": "primary"},
                "node": "ip4foward_partition", "fields": [ "vr_id" ],
                "constraints": {"unique": true }
                }
            ]
            }])";

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelJson, labelCfgJson));

    const char *configJsonFormat = R"({
            "label_name":"ip4foward_partition",
            "events":
                [
                    {"type":"insert"},
                    {"type":"delete"}
                ],
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"part",
                                "value": %u
                            }
                        ]
                }
        })";
    int32_t maxSize = 2000;
    char normalconfigJson[maxSize], invalidConfigJson[maxSize];
    int32_t len1 = sprintf_s(normalconfigJson, maxSize, configJsonFormat, 1);
    ASSERT_TRUE(len1 >= 0);
    int32_t len2 = sprintf_s(invalidConfigJson, maxSize, configJsonFormat, 16);
    ASSERT_TRUE(len2 >= 0);

    GmcSubConfigT config;
    config.subsName = "sub_ip4foward_partition";
    config.configJson = invalidConfigJson;
    CallBackParaT callbackPara = {0};
    // 订阅条件为partition字段时，value值只能为[0,16)
    ret = GmcSubscribe(stmt, &config, subChan, SubCallbackStmg, &callbackPara);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(lastErrorStr, "Not normal property. The val of prop part should be |0, 16).");
    config.configJson = normalconfigJson;
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, config.subsName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushVertexOnMerge)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);
    defer
    {
        ret = GmcDisconnect(subChan);
        EXPECT_EQ(GMERR_OK, ret);
        DestroyConnectionAndStmt(syncConn, stmt);
    };
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* wrong subscribe */
    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "merge insert", "msgTypes":["new object", "old object"]},
                   { "type": "merge", "msgTypes":["new object", "old object"]}],
        "retry": true
    }
    )";

    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 3, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 0;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = j;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    /* right subscribe */
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "merge insert" , "msgTypes":["new object", "old object"]},
                   { "type": "merge update" , "msgTypes":["new object", "old object"]}],
        "retry": true
    }
    )";

    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(stmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert first, then update
    for (uint32_t i = 0; i <= 1; ++i) {
        uint32_t total = (subVerify.verifyRow.fetchRowsIndex = 0);
        for (; total < 3; ++total) {
            uint32_t F0Value = total;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "subLabel1_K0");
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F1Value = total + i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }

        WAIT_WHILE(subVerify.verifyRow.fetchRowsIndex != total);
    }
}

TEST_F(StQueryStmgOldSubs, testSubPushDeltaVertexOnUpdateTree)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson = R"(
    [{
        "version": "2.0",
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "c0", "type": "uint32" },
            {
                "name": "c1",
                "type": "record",
                "array": true,
                "size": 1024,
                "fields": [
                    { "name": "f1", "type": "uint32", "nullable": true },
                    {
                        "name": "f2",
                        "type": "record",
                        "vector": true,
                        "size": 1024,
                        "fields": [
                            { "name": "b1", "type": "uint32", "nullable": true },
                            { "name": "b2", "type": "uint32", "nullable": true }
                        ]
                    }
                ]
            }
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "name": "table_pk",
                "index": { "type": "primary" },
                "node": "subLabel1",
                "fields": ["c0"],
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, subLabelJson, labelCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "update", "msgTypes":["delta object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "or",
            "conditions": [
                {
                    "property": "c1/f2/b1",
                    "value": 1
                }
            ]
        }
    }
    )";

    uint32_t total = 3;
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 1, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    const char *treePropertyName[3];
    treePropertyName[0] = "c1";
    treePropertyName[1] = "f2";
    treePropertyName[2] = "b1";
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->treeProp.treePropertyLevel = 3;
            expecRowData->treeProp.treePropertyName = treePropertyName;
            expecRowData->fetchMode = GMC_SUB_FETCH_DELTA;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = 1;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_TREE;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcPrepareStmtByLabelName(stmt, subLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *node;
        ret = GmcGetRootNode(stmt, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "c0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *c1;

        ret = GmcNodeGetChild(node, "c1", &node);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeAppendElement(node, &c1);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *f2;
        ret = GmcNodeGetChild(c1, "f2", &f2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 3; j++) {
            ret = GmcNodeAppendElement(f2, &f2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(f2, "b1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
        }

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcPrepareStmtByLabelName(stmt, subLabelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *node;
        ret = GmcGetRootNode(stmt, &node);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "table_pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, 4));
        GmcNodeT *c1;
        ret = GmcNodeGetChild(node, "c1", &c1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetElementByIndex(c1, 0, &c1);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *f2;
        ret = GmcNodeGetChild(c1, "f2", &f2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 3; j++) {
            ret = GmcNodeAppendElement(f2, &f2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeSetPropertyByName(f2, "b1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
        }

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait untill all events are received */
    while (subRowStatis->typeNum[GMC_SUB_EVENT_UPDATE] < 1) {
        DbSleep(1);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushDeltaVertexOnMerge)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);
    defer
    {
        ret = GmcDisconnect(subChan);
        EXPECT_EQ(GMERR_OK, ret);
        DestroyConnectionAndStmt(syncConn, stmt);
    };

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": true }
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "merge", "msgTypes":["new object", "delta object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "F1",
                    "value": 3
                }
            ]
        }
    }
    )";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 1, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 1;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            expecRowData->expecValue.valInt = 3;
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(stmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // insert first, then update
    for (uint i = 0; i <= 1; ++i) {
        uint32_t total = (subVerify.verifyRow.fetchRowsIndex = 0);

        for (; total < 5; ++total) {
            uint32_t F0Value = total;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F0Value, 4);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "subLabel1_K0");
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F1Value = total + i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }

        WAIT_WHILE(subVerify.verifyRow.fetchRowsIndex == 0);
        EXPECT_EQ(1u, subVerify.verifyRow.fetchRowsIndex);
    }
}

TEST_F(StQueryStmgOldSubs, testSubPushSubsWithFixedRootCondition)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subVertexLabel1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            {
                "name": "c1",
                "type": "record",
                "array": true,
                "size": 1024,
                "fields": [
                    { "name": "f1", "type": "uint32", "nullable": true },
                    {
                        "name": "f2",
                        "type": "record",
                        "vector": true,
                        "size": 1024,
                        "fields": [
                            { "name": "b1", "type": "uint32", "nullable": true },
                            { "name": "b2", "type": "uint32", "nullable": true }
                        ]
                    }
                ]
            },
            { "name": "Fstring", "type":"string", "default":"qwe", "size":4},
            { "name": "F1", "type": "uint32", "nullable": true }
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 insert",
        "events":
        [
            {
                "type":"insert",
                "msgTypes": ["new object"]
            },
            {
                "type":"replace",
                "msgTypes": ["new object"]
            }

        ],
        "retry":true,
        "constraint":
        {
            "operator_type":"or",
            "conditions":
                [
                    {
                        "property":"F1",
                        "value":"1"
                    },
                    {
                        "property":"Fstring",
                        "value":"a"
                    }
                ]
        }
    })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 5, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 0;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            if (j == 0) {
                expecRowData->expecValue.valInt = 1;
            } else if (j == 1) {
                expecRowData->expecValue.valInt = 11;
            } else if (j == 2) {
                expecRowData->expecValue.valInt = 16;
            } else if (j == 3) {
                expecRowData->expecValue.valInt = 9;
            } else if (j == 4) {
                expecRowData->expecValue.valInt = 15;
            }
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; ++i) {
        uint32_t F0 = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t F1 = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        const char *F1V = "a";
        ret = GmcSetVertexProperty(stmt, "Fstring", GMC_DATATYPE_STRING, F1V, strlen(F1V));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 插入第二条满足条件的数据
    uint32_t F0 = 11;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1 = 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    const char *f1Value = "a";
    ret = GmcSetVertexProperty(stmt, "Fstring", GMC_DATATYPE_STRING, f1Value, strlen(f1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0 = 15;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1 = 2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    const char *F1Value2 = "123";
    ret = GmcSetVertexProperty(stmt, "Fstring", GMC_DATATYPE_STRING, F1Value2, 3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0 = 16;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1 = 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value2 = "a";
    ret = GmcSetVertexProperty(stmt, "Fstring", GMC_DATATYPE_STRING, F1Value2, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; ++i) {
        F0 = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        F1 = 10 - i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        const char *F1V = "a";
        ret = GmcSetVertexProperty(stmt, "Fstring", GMC_DATATYPE_STRING, F1V, 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 插入第二条满足条件的数据
    F0 = 11;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1 = 2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0 = 15;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1 = 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value2 = "a";
    ret = GmcSetVertexProperty(stmt, "Fstring", GMC_DATATYPE_STRING, F1Value2, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    F0 = 16;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1 = 2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (subRowStatis->allNum < 5) {
        DbSleep(100);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryStmgOldSubs, testSubPushSubsWithFixedRootCondition_fixed)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subVertexLabel1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            {
                "name": "c1",
                "type": "record",
                "array": true,
                "size": 1024,
                "fields": [
                    { "name": "f1", "type": "uint32", "nullable": true },
                    {
                        "name": "f2",
                        "type": "record",
                        "vector": true,
                        "size": 1024,
                        "fields": [
                            { "name": "b1", "type": "uint32", "nullable": true },
                            { "name": "b2", "type": "uint32", "nullable": true }
                        ]
                    }
                ]
            },
            { "name": "Fstring", "type":"string", "default":"qwe", "size":4},
            { "name": "F1", "type":"fixed", "default":"ffff", "size":4 }
        ],
        "subs_type":"status_merge",
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(stmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 insert",
        "events":
        [
            {
                "type":"insert",
                "msgTypes": ["new object"]
            },
            {
                "type":"replace",
                "msgTypes": ["new object"]
            }

        ],
        "retry":true,
        "constraint":
        {
            "operator_type":"or",
            "conditions":
                [
                    {
                        "property":"F1",
                        "value":"qwer"
                    },
                    {
                        "property":"Fstring",
                        "value":"a"
                    }
                ]
        }
    })";
    SubVerifyT subVerify = {0};
    SubsCommInitAndMallocSubVerify(&subVerify, config.subsName, 1, 3, 10);
    defer
    {
        SubsCommDestroySubVerify(&subVerify);
    };
    SubRowStatisT *subRowStatis = (SubRowStatisT *)subVerify.verifyHeader.headerStatis;
    for (uint32_t i = 0; i < subVerify.verifyRow.fetchRowsTypeNum; i++) {
        for (uint32_t j = 0; j < subVerify.verifyRow.expectVerifyRowsNum; j++) {
            SubVerifyRowDataT *expecRowData = &subVerify.verifyRow.expecRows[i][j];
            expecRowData->properyId = 0;
            expecRowData->fetchMode = GMC_SUB_FETCH_NEW;
            expecRowData->expecValue.isInt = true;
            expecRowData->expecValue.isNull = false;
            expecRowData->expecValue.len = sizeof(uint32_t);
            if (j == 0) {
                expecRowData->expecValue.valInt = 11;
            } else if (j == 1) {
                expecRowData->expecValue.valInt = 13;
            } else if (j == 2) {
                expecRowData->expecValue.valInt = 12;
            }
            expecRowData->getPropType = ST_GET_PROPERY_TYPE_ID;
        }
    }
    ret = GmcSubscribe(stmt, &config, subChan, SubsCallbackComm, &subVerify);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F0 = 11;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    const char *F1Value = "qwer";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_FIXED, F1Value, 4);
    EXPECT_EQ(GMERR_OK, ret);
    const char *f1Value = "a";
    ret = GmcSetVertexProperty(stmt, "Fstring", GMC_DATATYPE_STRING, f1Value, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    F0 = 12;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    const char *F1Value2 = "123";
    ret = GmcSetVertexProperty(stmt, "Fstring", GMC_DATATYPE_STRING, F1Value2, 3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0 = 13;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = "qwer";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_FIXED, F1Value, 4);
    EXPECT_EQ(GMERR_OK, ret);
    F1Value2 = "a";
    ret = GmcSetVertexProperty(stmt, "Fstring", GMC_DATATYPE_STRING, F1Value2, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    F0 = 11;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = "abcd";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_FIXED, F1Value, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    F0 = 12;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = "qwer";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_FIXED, F1Value, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "Fstring", GMC_DATATYPE_STRING, f1Value, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    F0 = 13;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = "7777";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_FIXED, F1Value, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (subRowStatis->allNum < 3) {
        DbSleep(100);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(syncConn, stmt);
}
