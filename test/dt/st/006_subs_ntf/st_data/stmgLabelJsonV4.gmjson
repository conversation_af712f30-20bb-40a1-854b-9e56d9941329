[{"type": "record", "name": "subsLabelStMerge", "schema_version": 4, "subs_type": "status_merge", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "uint32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "default": 100}], "keys": [{"node": "subsLabelStMerge", "name": "subLabel1_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]