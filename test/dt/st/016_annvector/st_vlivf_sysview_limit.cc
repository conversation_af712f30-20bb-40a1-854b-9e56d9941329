/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: system test
 * Author: g00832243
 * Create: 2024-07-11
 */

#include "st_annvector_common.h"

const char *g_stLLMSystemViewLimitJson =
    R"([{
        "type":"record",
        "name":"QuantTable",
        "config": {
            "auto_vector_quantization":
                [
                    {"type":"lvq", "field":"F1", "source":"F1", "metric":"ip", "ncodebits":4}
                ]
        },
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"fixed", "size":32768}
            ],
        "keys":
            [
                { "node":"QuantTable", "name":"PK", "fields":["F0"], "index":{"type":"primary"} },
                {
                    "node":"QuantTable",
                    "name":"vlivfIndex",
                    "fields":["F1"],
                    "index":{"type":"vlivf"},
                    "ood_threshold": 10000.0,
                    "ncentroids":[1100]
                }
            ]
        }])";

class StVlIvfSysviewLimit : public testing::Test {
protected:
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    static void SetUpTestCase()
    {
        Status ret =
            g_longSeqRuntime.LoadDataAndStartUp(&g_sysViewLimitDataSet, true, NULL, g_stLLMSystemViewLimitJson);
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        g_longSeqRuntime.ReleaseDataAndTearDown();
    }
    void SetUp()
    {
        CreateSyncConnectionAndStmt(&conn, &stmt);
    }
    void TearDown()
    {
        GmcFreeStmt(stmt);
        ASSERT_EQ(GMERR_OK, GmcDisconnect(conn));
    }
};

// 用例7: 不建表，直接查询
// 用例7: 预计返回空
TEST_F(StVlIvfSysviewLimit, VlIvfSysviewLimit1)
{
    // 数据库无数据，CENTROIDS_LIST等字段不出现
    char cmdOutput[CMD_LEN];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    uint32_t distributionCnt = GetViewFieldCount("V\\$STORAGE_VL_IVF_STAT", "CENTROIDS_LIST", cmdOutput, CMD_LEN);
    EXPECT_EQ(0, distributionCnt);
}

// 用例8: 插入后1100个簇后，索引视图查询
// 用例8: 返回按照vectorNum排序后的前1000个
TEST_F(StVlIvfSysviewLimit, VlIvfSysviewLimit2)
{
    VectorLabelDataMgr dataMgr(conn, stmt);
    Status ret = dataMgr.CreateTable(g_longSeqRuntime.para.labelJson);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dataMgr.BatchLoad(
        g_longSeqRuntime.vecBuf.centset.num, LongSeqDefaultLoadCentSetFields, &g_longSeqRuntime.vecBuf, false);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dataMgr.BatchInsert(
        g_longSeqRuntime.vecBuf.dataset.num, LongSeqDefaultSetFields, &g_longSeqRuntime.vecBuf, false);
    ASSERT_EQ(GMERR_OK, ret);

    float queryTime[g_longSeqRuntime.vecBuf.queryset.num];
    VectorAnnScanner scanner(stmt, g_longSeqRuntime.para.labelName, g_longSeqRuntime.vecBuf.dim);
    VectorAnnScanner::UserCtx userCtx(LongSeqScanDefaultLabelCallbackCheck, NULL);
    uint32_t totalCnt = 0;
    ret = scanner.LoopIndexScan(&userCtx, 1, &g_longSeqRuntime.vecBuf.queryset, &totalCnt, queryTime);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t limitCentNum = 1000;
    // 聚类中心大于1000
    EXPECT_GT(g_longSeqRuntime.vecBuf.centset.num, limitCentNum);

    // 只返回前1000个，所以DIS_DISTRIBUTION=1000
    char cmdOutput[CMD_LEN];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    uint32_t distributionCnt = GetViewFieldCount("V\\$STORAGE_VL_IVF_STAT", "DIS_DISTRIBUTION", cmdOutput, CMD_LEN);
    EXPECT_EQ(limitCentNum, distributionCnt);

    dataMgr.DropTable();
    system("sleep 1");
}

const char *g_json =
    R"([{
        "type":"record",
        "name":"QuantTable",
        "config": {
            "auto_vector_quantization":
                [
                    {"type":"lvq", "field":"F1", "source":"F1", "metric":"ip", "ncodebits":4}
                ]
        },
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"fixed", "size":32768}
            ],
        "keys":
            [
                { "node":"QuantTable", "name":"PK", "fields":["F0"], "index":{"type":"primary"} },
                {
                    "node":"QuantTable",
                    "name":"vlivfIndex",
                    "fields":["F1"],
                    "index":{"type":"vlivf"},
                    "ood_threshold": 10000.0,
                    "ncentroids":[1001]
                }
            ]
        }])";

// 用例9: 插入后1001个簇后
// 用例9: 返回按照vectorNum排序后的前1000个
TEST_F(StVlIvfSysviewLimit, VlIvfSysviewLimit3)
{
    const DataSetPath sysViewData = {.dataFile = "./10m_data/10comb_8192_split_1.fbin",
        .queryFile = "./10m_data/10comb_query_split_1.fbin",
        .centFile = "./10m_data/10comb_8192_split_1.fbin",
        .dataNum = 1200,
        .queryNum = 30,
        .centNum = 1001};

    g_longSeqRuntime.vecBuf.Release();
    Status ret = g_longSeqRuntime.LoadDataAndStartUp(&sysViewData, false, NULL, g_json);
    ASSERT_EQ(GMERR_OK, ret);

    VectorLabelDataMgr dataMgr(conn, stmt);
    ret = dataMgr.CreateTable(g_json);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dataMgr.BatchLoad(
        g_longSeqRuntime.vecBuf.centset.num, LongSeqDefaultLoadCentSetFields, &g_longSeqRuntime.vecBuf, false);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dataMgr.BatchInsert(
        g_longSeqRuntime.vecBuf.dataset.num, LongSeqDefaultSetFields, &g_longSeqRuntime.vecBuf, false);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t limitCentNum = 1000;
    // 聚类中心大于1000
    EXPECT_GT(g_longSeqRuntime.vecBuf.centset.num, limitCentNum);

    // 只返回前1000个，所以DIS_DISTRIBUTION=1000
    char cmdOutput[CMD_LEN];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    uint32_t distributionCnt = GetViewFieldCount("V\\$STORAGE_VL_IVF_STAT", "DIS_DISTRIBUTION", cmdOutput, CMD_LEN);
    EXPECT_EQ(limitCentNum, distributionCnt);

    // 服务端已启动
    g_longSeqRuntime.serverStarted = true;

    dataMgr.DropTable();
    system("sleep 1");
}

const char *g_json1 =
    R"([{
        "type":"record",
        "name":"QuantTable",
        "config": {
            "auto_vector_quantization":
                [
                    {"type":"lvq", "field":"F1", "source":"F1", "metric":"ip", "ncodebits":4}
                ]
        },
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"fixed", "size":32768}
            ],
        "keys":
            [
                { "node":"QuantTable", "name":"PK", "fields":["F0"], "index":{"type":"primary"} },
                {
                    "node":"QuantTable",
                    "name":"vlivfIndex",
                    "fields":["F1"],
                    "index":{"type":"vlivf"},
                    "ood_threshold": 10000.0,
                    "ncentroids":[10]
                }
            ]
        }])";

const char *g_json2 =
    R"([{
        "type":"record",
        "name":"QuantTable2",
        "config": {
            "auto_vector_quantization":
                [
                    {"type":"lvq", "field":"F1", "source":"F1", "metric":"ip", "ncodebits":4}
                ]
        },
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"fixed", "size":32768}
            ],
        "keys":
            [
                { "node":"QuantTable2", "name":"PK", "fields":["F0"], "index":{"type":"primary"} },
                {
                    "node":"QuantTable2",
                    "name":"vlivfIndex2",
                    "fields":["F1"],
                    "index":{"type":"vlivf"},
                    "ood_threshold": 10000.0,
                    "ncentroids":[10]
                }
            ]
        }])";

// 用例10: 插入1024条数据后删表，再创表，插入2048条
// 用例10: 视图中所有簇的RANGE不存在inf值
TEST_F(StVlIvfSysviewLimit, VlIvfSysviewLimit4)
{
    const DataSetPath sysViewDataSet1 = {.dataFile = "./10m_data/10comb_8192_split_1.fbin",
        .queryFile = "./10m_data/10comb_query_split_1.fbin",
        .centFile = "./10m_data/cluster_centers_500_split_1.fbin",
        .dataNum = 1024,
        .queryNum = 30,
        .centNum = 10};

    const DataSetPath sysViewDataSet2 = {.dataFile = "./10m_data/10comb_8192_split_1.fbin",
        .queryFile = "./10m_data/10comb_query_split_1.fbin",
        .centFile = "./10m_data/cluster_centers_500_split_1.fbin",
        .dataNum = 2048,
        .queryNum = 30,
        .centNum = 10};

    VectorLabelDataMgr dataMgr(conn, stmt);

    g_longSeqRuntime.vecBuf.Release();
    Status ret = g_longSeqRuntime.LoadDataAndStartUp(&sysViewDataSet1, false, NULL, g_json1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dataMgr.CreateTable(g_longSeqRuntime.para.labelJson);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dataMgr.BatchLoad(
        g_longSeqRuntime.vecBuf.centset.num, LongSeqDefaultLoadCentSetFields, &g_longSeqRuntime.vecBuf, false);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dataMgr.BatchInsert(
        g_longSeqRuntime.vecBuf.dataset.num, LongSeqDefaultSetFields, &g_longSeqRuntime.vecBuf, false);
    ASSERT_EQ(GMERR_OK, ret);

    dataMgr.DropTable();

    g_longSeqRuntime.vecBuf.Release();
    ret = g_longSeqRuntime.LoadDataAndStartUp(&sysViewDataSet2, false, NULL, g_json2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dataMgr.CreateTable(g_json2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dataMgr.BatchLoad(
        g_longSeqRuntime.vecBuf.centset.num, LongSeqDefaultLoadCentSetFields, &g_longSeqRuntime.vecBuf, false);
    ASSERT_EQ(GMERR_OK, ret);

    ret = dataMgr.BatchInsert(
        g_longSeqRuntime.vecBuf.dataset.num, LongSeqDefaultSetFields, &g_longSeqRuntime.vecBuf, false);
    ASSERT_EQ(GMERR_OK, ret);

    char cmdOutput[CMD_LEN];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char matchStr[] = "[inf, inf), 0";
    EXPECT_EQ(false, CheckFieldValue("V\\$STORAGE_VL_IVF_STAT", "RANGE", matchStr, cmdOutput, CMD_LEN));

    // 服务端已启动
    g_longSeqRuntime.serverStarted = true;

    dataMgr.DropTable();
}
