/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for directwrite dml operations with bitmap
 * Author: maojinyong
 * Create: 2023-12-16
 */

#include <string.h>
#include <math.h>
#include "client_common_st.h"
#include "storage_st_common.h"
#include "se_spacemgr.h"
#include "se_page_mgr.h"

class StDwBitmap : public StClient {
public:
    static void SetUpTestCase();
};
void StDwBitmap::SetUpTestCase()
{
    // 最新代码默认打开聚簇容器，此处将其设置为关闭状态，聚簇容器用例在其他文件中维护
    StartDbServerWithConfig("\"enableDmlOperStat=0\" \"enableDmlPerfStat=0\" \"enableClusterHash=0\" "
                            "\"trxMonitorEnable=0\" \"workerHungThreshold=6,200,300\" "
                            "\"maxSysDynSize=1024\" \"auditLogEnableDML=0\" "
                            "\"userPolicyMode=0\" \"compatibleV3=0\" \"directWrite=1\"");
    st_clt_init();
    GmcSignalRegisterNotify();
    CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
    CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
    EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
    // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
    EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
    if (IsEulerEnv()) {
        DbSleep(1000);
    } else {
        st_check_hpe_server_running();
    }
    printf("start response epoll and timeout epoll thread\n");
    printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
    st_connect();
}

static const char *g_label_config = R"({"max_record_count":1000000})";
static const char *g_label_name = "bitmap_label";
static const char *g_label_schema =
    R"([{
        "type":"record",
        "name":"bitmap_label",
        "config": {
            "direct_write":true
        },
        "fields":[
            {"name":"F0", "type":"uint32", "nullable":false},
            {"name":"F1", "type":"uint32", "nullable":false},
            {"name":"F2", "type":"bitmap", "size":128, "nullable":false}
        ],
        "keys":[
        {
                "node":"bitmap_label",
                "name":"PK",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }])";

/*
 * 根据主键查询数据，并校验其中的bitmap字段值是否符合预期
 */
static void CheckBitmap(GmcStmtT *stmt, uint32_t keyVal, uint8_t expectedBitVal)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyVal, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    bool isNull = true;
    uint32_t propSize = 0;
    uint8_t bits[128 / 8];
    memset(bits, 0, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(stmt, "F2", &propSize);  // 获取变长属性字段的长度
    EXPECT_EQ(128u, propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F2", bits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        EXPECT_EQ(expectedBitVal, bits[i]);
    }
}

/*
 * 插入数据时设置bitmap字段的值，并查询
 * 插入数据时，只能全量插入或者不插入，不支持局部插入
 */
TEST_F(StDwBitmap, dw_bitmap_full_set)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    Status ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // REPLACE 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    GmcBitMapT bitMap;
    bitMap.beginPos = 0;
    bitMap.endPos = 127;
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);  // 每一位都设置为1
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询验证
    CheckBitmap(stmt, F0Val, (uint8_t)0xff);

    // INSERT 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F0Val = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1Val = 2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    GmcBitMapT bitMap2;
    bitMap2.beginPos = 0;
    bitMap2.endPos = 127;
    uint8_t bits2[128 / 8];
    memset(bits2, 0xffff, 128 / 8);
    bits2[128 / 8 - 1] = '\0';
    bitMap2.bits = bits2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap2, sizeof(bitMap2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询验证
    CheckBitmap(stmt, F0Val, (uint8_t)0xff);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StDwBitmap, dw_bitmap_partial_set)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    Status ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // INSERT 插入数据。局部插入，不支持
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    GmcBitMapT bitMap;
    bitMap.beginPos = 10;
    bitMap.endPos = 100;
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // REPLACE 插入数据。局部插入，不支持
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

/*
 * 插入数据时未设置bitmap字段的值，默认值为0
 */
TEST_F(StDwBitmap, dw_bitmap_non_set)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    Status ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // REPLACE 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询验证
    CheckBitmap(stmt, F0Val, 0);

    // INSERT 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F0Val = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1Val = 2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 再次验证
    CheckBitmap(stmt, F0Val, 0);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

/*
 * 插入数据时设置bitmap字段的值，设置的范围与schema中定义的不一致
 */
TEST_F(StDwBitmap, dw_bitmap_wrong_set)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    Status ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    GmcBitMapT bitMap;
    bitMap.beginPos = 0;
    bitMap.endPos = 200;  // 错误的边界值，schema中定义的bitmap大小为128，即有效范围是[0, 127]
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

/*
 * 更新bitmap字段的值，并查询。支持全量更新和局部更新
 */
TEST_F(StDwBitmap, dw_bitmap_update)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    Status ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    GmcBitMapT bitMap;
    bitMap.beginPos = 0;
    bitMap.endPos = 127;
    uint8_t bits[128 / 8];
    memset(bits, 0xff, 128 / 8);  // 每一位都设置为1
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询验证
    CheckBitmap(stmt, F0Val, (uint8_t)0xff);

    // 更新数据。局部更新，只更新第8~15位，由原来的 1111 1111 更新为 1100 1100
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "PK");
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t tmpBits[2];
    tmpBits[0] = 204;  // 1100 1100
    tmpBits[1] = '\0';
    bitMap.beginPos = 8;
    bitMap.endPos = 15;
    bitMap.bits = tmpBits;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新后查询验证
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    bool isNull = true;
    uint32_t propSize = 0;
    uint8_t retBits[128 / 8];
    memset(retBits, 0, 128 / 8);
    retBits[128 / 8 - 1] = '\0';
    ret = GmcGetVertexPropertySizeByName(stmt, "F2", &propSize);
    EXPECT_EQ(128u, propSize);
    ret = GmcGetVertexPropertyByName(stmt, "F2", retBits, propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    for (int i = 0; i < 128 / 8 - 1; i++) {
        if (i == 1) {
            EXPECT_EQ((uint8_t)204, retBits[i]);
            continue;
        }
        EXPECT_EQ(0xFF, retBits[i]);
    }

    // 更新数据。全局更新，即更新第0~127位
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "PK");
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t updateBits[16];
    for (int i = 0; i < 15; i++) {
        updateBits[i] = 204;
    }
    updateBits[15] = '\0';
    bitMap.beginPos = 0;
    bitMap.endPos = 127;
    bitMap.bits = updateBits;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新后查询验证
    CheckBitmap(stmt, F0Val, (uint8_t)204);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

/*
 * insert时设置bitmap的值，replace时设置bitmap的新值，然后查询
 * 主要为了验证DmCheckBitMapConstrict()中的约束
 */
TEST_F(StDwBitmap, dw_bitmap_replace_update)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    Status ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // insert 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F0Val = 1;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F1Val = 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    GmcBitMapT bitMap;
    bitMap.beginPos = 0;
    bitMap.endPos = 127;
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);  // 每一位都设置为1
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询验证
    CheckBitmap(stmt, F0Val, (uint8_t)0xff);

    // repace 插入数据。主键不变，同时更新bitmap字段的值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    F1Val = 2;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    GmcBitMapT bitMap2;
    bitMap2.beginPos = 8;
    bitMap2.endPos = 15;
    uint8_t bits2[2];
    bits2[0] = 204;  // 1100 1100
    bits2[1] = '\0';
    bitMap2.bits = bits2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap2, sizeof(bitMap2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

static void *write_bitmap_by_replace(void *args)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    Status ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    uint32_t nums = *(uint32_t *)args;
    for (uint32_t i = 0; i < nums; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        GmcBitMapT bitMap;
        bitMap.beginPos = 0;
        bitMap.endPos = 127;
        uint8_t bits[128 / 8];
        memset(bits, 0xffff, 128 / 8);
        bits[128 / 8 - 1] = '\0';
        bitMap.bits = bits;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
            continue;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

static void *write_bitmap_by_insert(void *args)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    Status ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    uint32_t nums = *(uint32_t *)args;
    for (uint32_t i = 0; i < nums; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        GmcBitMapT bitMap;
        bitMap.beginPos = 0;
        bitMap.endPos = 127;
        uint8_t bits[128 / 8];
        memset(bits, 0xffff, 128 / 8);
        bits[128 / 8 - 1] = '\0';
        bitMap.bits = bits;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
            continue;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

/*
 * 多线程插入数据
 */
TEST_F(StDwBitmap, dw_bitmap_insert_concurrent)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    Status ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t insertNum = 10000;  // 自验阶段把值调大
    pthread_t tid1, tid2;
    ret = pthread_create(&tid1, NULL, write_bitmap_by_insert, &insertNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid2, NULL, write_bitmap_by_replace, &insertNum);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(tid1, NULL);
    pthread_join(tid2, NULL);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t actualNum = 0;
    ret = GmcGetVertexRecordCount(stmt, &actualNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(insertNum, actualNum);

    ret = GmcTruncateVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}
