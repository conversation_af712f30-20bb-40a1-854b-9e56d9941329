/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for direct write dml operations
 * Author: maojinyong
 * Create: 2023-03-16
 */

#include "storage_st_common.h"

using namespace std;

class StDirectWriteCHHashIdx : public StStorage {
protected:
    static void SetUpTestCase()
    {
        StServerClientPrepare(" \"enableClusterHash=0\" \"directWrite=1\" ");
    }
    static void TearDownTestCase()
    {
        StServerClientExit();
    }
    virtual void SetUp()
    {}
    virtual void TearDown()
    {
        SaveLogAfterFailed();
    }
};

class StDirectWriteCHHashIdx4ClusteredHash : public StStorage {
protected:
    static void SetUpTestCase()
    {
        StServerClientPrepare("\"enableClusterHash=1\" \"directWrite=1\" ", &epollThreadId);
        GmcSignalRegisterNotify();
    }
    static void TearDownTestCase()
    {
        StServerClientExit(&epollThreadId);
    }
    virtual void SetUp()
    {}
    virtual void TearDown()
    {
        SaveLogAfterFailed();
    }
};

static void DwChainedHashIndexGraphQuery(void)
{
    const char *labelName2 = "GetCHHashIndex";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "config": {
                "direct_write": true
            },
            "name":"GetCHHashIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint64", "nullable":false},
                    {"name":"F2", "type":"uint32", "default":2, "nullable":false},
                    {"name":"F3", "type":"fixed", "size":11, "nullable":false}],
            "keys":
                [
                    {
                        "node":"GetCHHashIndex",
                        "name":"GetCHHashIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"GetCHHashIndex",
                        "name":"GetCHHashIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"GetCHHashIndex",
                        "name":"GetCHHashIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"hashcluster"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, labelName2, DB_MAX_UINT32, GMC_OPERATION_INSERT));

    // insert vertex1
    uint32_t f0Value = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long f1Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int f2Value = 1314;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *f3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 查找，基于主键查询
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName2, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "GetCHHashIndex_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));

    uint32_t f0V;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f0Value, f0V);

    long long f1V;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(long long), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f1Value, f1V);

    int f2V;
    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(int), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f2Value, f2V);

    char f3V[11];
    ret = GmcGetVertexPropertyByName(stmt, "F3", f3V, 11, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    if (memcmp(f3V, f3Value, 11) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }
    ret = GmcDropVertexLabel(stmt, labelName2);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
};

TEST_F(StDirectWriteCHHashIdx, dw_chained_hash_index_graph_query)
{
    DwChainedHashIndexGraphQuery();
}

TEST_F(StDirectWriteCHHashIdx4ClusteredHash, dw_chained_hash_index_graph_query)
{
    DwChainedHashIndexGraphQuery();
}
