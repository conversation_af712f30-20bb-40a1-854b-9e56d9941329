/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: directwrite_st_lpm_index_for_clustered_hash.cc
 * Description: testcase for direct write lpm index clustered hash label
 * Author: zhangjinglong
 * Create: 2023-05-08
 */
#include "storage_st_common.h"

using namespace std;

class StDirectWriteLpm4ClusteredHash : public StStorage {
protected:
    static void SetUpTestCase()
    {
        StServerClientPrepare("\"enableClusterHash=1\" \"directWrite=1\" ", &epollThreadId);
        GmcSignalRegisterNotify();
    }
    static void TearDownTestCase()
    {
        StServerClientExit(&epollThreadId);
    }
};

static const char *g_testLpm4IndexInsertJson =
    R"([{
        "type":"record",
        "config": {
            "direct_write": true
        },
        "name":"Lpm4IndexInsert",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"vr_id", "type":"uint32"},
                {"name":"vrf_index", "type":"uint32"},
                {"name":"dest_ip_addr", "type":"uint32"},
                {"name":"mask_len", "type":"uint8"}],
        "keys":
            [
                {
                    "node":"Lpm4IndexInsert",
                    "name":"Lpm4IndexInsert_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"Lpm4IndexInsert",
                    "name":"ip4forward_lpm",
                    "fields":["vr_id", "vrf_index", "dest_ip_addr", "mask_len"],
                    "index":{"type":"lpm4_tree_bitmap"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

static const char *g_testLpm6IndexInsertJson =
    R"([{
        "type":"record",
        "config": {
            "direct_write": true
        },
        "name":"Lpm6IndexInsert",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"vr_id", "type":"uint32"},
                {"name":"vrf_index", "type":"uint32"},
                {"name":"dest_ip_addr", "type": "fixed", "size": 16},
                {"name":"mask_len", "type":"uint8"}],
        "keys":
            [
                {
                    "node":"Lpm6IndexInsert",
                    "name":"Lpm6IndexInsert_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"Lpm6IndexInsert",
                    "name":"ip6forward_lpm",
                    "fields":["vr_id", "vrf_index", "dest_ip_addr", "mask_len"],
                    "index":{"type":"lpm6_tree_bitmap"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

static const char *g_testIndexUpdateByPkJson =
    R"([{
        "type": "record",
        "config": {
            "direct_write": true
        },
        "name": "Lpm6IndexUpdateByPk",
        "fields": [
            { "name": "primary_label", "type": "uint8", "comment": "标签" },
            { "name": "attribute_id", "type": "uint8", "comment": "属性ID" },
            { "name": "vr_id", "type": "uint32", "comment": "Vs索引" },
            { "name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引" },
            { "name": "dest_ip_addr", "type": "fixed", "size": 16, "comment": "目的IP" },
            { "name": "mask_len", "type": "uint8", "comment": "掩码长度" }
        ],
        "keys": [
            {
                "name": "primary_key",
                "index": { "type": "primary" },
                "node": "Lpm6IndexUpdateByPk",
                "fields": [ "primary_label", "attribute_id" ],
                "constraints": { "unique": true },
                "comment": "根据主键索引"
            },
            {
                "name": "lpm6_key",
                "node": "Lpm6IndexUpdateByPk",
                "index": { "type": "lpm6_tree_bitmap"},
                "fields": [ "vr_id", "vrf_index", "dest_ip_addr", "mask_len" ],
                "constraints": { "unique": true },
                "comment": "根据lpm6_tree_bitmap索引"
            }
        ]
    }])";

static const char *g_labelLpm4IndexInsert = "Lpm4IndexInsert";
static const char *g_labelLpm6IndexInsert = "Lpm6IndexInsert";

static const char *g_testMaxRecordCountJson = R"({"max_record_count":1000})";
static const char *g_testMaxRecordCountJson2 = R"({"max_record_count":999999})";

static const uint32_t DEST_IP_ADDR_SIZE = 16;

typedef struct {
    uint32_t f0Value;
    uint32_t vrId;
    uint32_t vrfIndex;
    uint32_t destIpAddr;
    uint8_t maskLenValue;
} TestLpm4VertexValueT;

typedef struct {
    uint32_t f0Value;
    uint32_t vrId;
    uint32_t vrfIndex;
    char *destIpAddr;
    uint8_t maskLenValue;
} TestLpm6VertexValueT;

static void LpmQueryVertex(GmcStmtT *stmt, uint32_t value, const char *name)
{
    EXPECT_NE(stmt, nullptr);
    for (unsigned int cnt = 0;; ++cnt) {
        bool isFinish;
        Status ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            EXPECT_EQ(1u, cnt);
            break;
        }
        bool isNull = false;
        uint32_t tmpValue = 0;
        ret = GmcGetVertexPropertyByName(stmt, name, &tmpValue, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, isNull);
        EXPECT_EQ(value, tmpValue);
    }
}

static void Lpm4SetVertexProperty(GmcStmtT *stmt, const TestLpm4VertexValueT &vertex)
{
    EXPECT_NE(stmt, nullptr);
    Status ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &(vertex.f0Value), sizeof(vertex.f0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &(vertex.vrId), sizeof(vertex.vrId));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &(vertex.vrfIndex), sizeof(vertex.vrfIndex));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &(vertex.destIpAddr), sizeof(vertex.destIpAddr));
    EXPECT_EQ(GMERR_OK, ret);
    ret =
        GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &(vertex.maskLenValue), sizeof(vertex.maskLenValue));
    EXPECT_EQ(GMERR_OK, ret);
}

static void Lpm4SetIndexKey(GmcStmtT *stmt, const TestLpm4VertexValueT &vertex)
{
    EXPECT_NE(stmt, nullptr);
    Status ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &(vertex.vrId), sizeof(vertex.vrId));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &(vertex.vrfIndex), sizeof(vertex.vrfIndex));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &(vertex.destIpAddr), sizeof(vertex.destIpAddr));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &(vertex.maskLenValue), sizeof(vertex.maskLenValue));
    EXPECT_EQ(GMERR_OK, ret);
}

static void Lpm6SetVertexProperty(GmcStmtT *stmt, const TestLpm6VertexValueT &vertex)
{
    EXPECT_NE(stmt, nullptr);
    Status ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &(vertex.f0Value), sizeof(vertex.f0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &(vertex.vrId), sizeof(vertex.vrId));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &(vertex.vrfIndex), sizeof(vertex.vrfIndex));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, vertex.destIpAddr, DEST_IP_ADDR_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    ret =
        GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &(vertex.maskLenValue), sizeof(vertex.maskLenValue));
    EXPECT_EQ(GMERR_OK, ret);
}

static void Lpm6SetIndexKey(GmcStmtT *stmt, const TestLpm6VertexValueT &vertex)
{
    EXPECT_NE(stmt, nullptr);
    Status ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &(vertex.vrId), sizeof(vertex.vrId));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &(vertex.vrfIndex), sizeof(vertex.vrfIndex));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, vertex.destIpAddr, DEST_IP_ADDR_SIZE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &(vertex.maskLenValue), sizeof(vertex.maskLenValue));
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm4_index_graph_insert_001)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, g_testLpm4IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm4IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestLpm4VertexValueT vertexValue;
    vertexValue.f0Value = 2;
    vertexValue.vrId = 13;
    vertexValue.vrfIndex = 1004;
    vertexValue.destIpAddr = 0xffffff00;
    vertexValue.maskLenValue = 5;
    Lpm4SetVertexProperty(stmt, vertexValue);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelLpm4IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm4_index_graph_scan_002)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, g_testLpm4IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm4IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestLpm4VertexValueT vertexValue;
    vertexValue.f0Value = 232;
    vertexValue.vrId = 13;
    vertexValue.vrfIndex = 1004;
    vertexValue.destIpAddr = 0xffffff00;
    vertexValue.maskLenValue = 5;
    Lpm4SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelLpm4IndexInsert, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4forward_lpm"));
    Lpm4SetIndexKey(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    LpmQueryVertex(stmt, vertexValue.f0Value, "F0");

    GmcResetStmt(stmt);

    ret = GmcDropVertexLabel(stmt, g_labelLpm4IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm4_index_graph_insert_and_scan_zero_masklen_003)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, g_testLpm4IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm4IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestLpm4VertexValueT vertexValue;
    vertexValue.f0Value = 232;
    vertexValue.vrId = 13;
    vertexValue.vrfIndex = 1004;
    vertexValue.destIpAddr = 0xffffff00;
    vertexValue.maskLenValue = 0;
    Lpm4SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex2
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm4IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestLpm4VertexValueT vertexValue2;
    vertexValue2.f0Value = 233;
    vertexValue2.vrId = 13;
    vertexValue2.vrfIndex = 1004;
    vertexValue2.destIpAddr = 0xacffe123;
    vertexValue2.maskLenValue = 31;
    Lpm4SetVertexProperty(stmt, vertexValue2);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelLpm4IndexInsert, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4forward_lpm"));
    TestLpm4VertexValueT vertexValue3;
    vertexValue3.vrId = 13;
    vertexValue3.vrfIndex = 1004;
    vertexValue3.destIpAddr = 0xacffe123;
    vertexValue3.maskLenValue = 0;
    Lpm4SetIndexKey(stmt, vertexValue3);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    LpmQueryVertex(stmt, vertexValue2.f0Value, "F0");

    TestLpm4VertexValueT vertexValue4;
    vertexValue4.vrId = 13;
    vertexValue4.vrfIndex = 1004;
    vertexValue4.destIpAddr = 0xacffcccc;
    vertexValue4.maskLenValue = 32;
    Lpm4SetIndexKey(stmt, vertexValue4);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    LpmQueryVertex(stmt, vertexValue.f0Value, "F0");

    GmcResetStmt(stmt);

    ret = GmcDropVertexLabel(stmt, g_labelLpm4IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm4_index_graph_delete_004)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_testLpm4IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm4IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestLpm4VertexValueT vertexValue;
    vertexValue.f0Value = 232;
    vertexValue.vrId = 13;
    vertexValue.vrfIndex = 1004;
    vertexValue.destIpAddr = 0xffffff00;
    vertexValue.maskLenValue = 5;
    Lpm4SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除顶点。直连写不支持基于二级索引删除
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm4IndexInsert, DB_MAX_UINT32, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4forward_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    TestLpm4VertexValueT vertexValue2;
    vertexValue2.vrId = 13;
    vertexValue2.vrfIndex = 1004;
    vertexValue2.destIpAddr = 0xffffff00;
    vertexValue2.maskLenValue = 5;
    Lpm4SetIndexKey(stmt, vertexValue2);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    GmcResetStmt(stmt);

    ret = GmcDropVertexLabel(stmt, g_labelLpm4IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm4_index_graph_insert_fail_005)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, g_testLpm4IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm4IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestLpm4VertexValueT vertexValue;
    vertexValue.f0Value = 1;
    vertexValue.vrId = 13;
    vertexValue.vrfIndex = 1004;
    vertexValue.destIpAddr = 0xffffff00;
    vertexValue.maskLenValue = 5;
    Lpm4SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex2
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm4IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestLpm4VertexValueT vertexValue2;
    vertexValue2.f0Value = 2;
    vertexValue2.vrId = 13;
    vertexValue2.vrfIndex = 1004;
    vertexValue2.destIpAddr = 0xff000000;
    vertexValue2.maskLenValue = 5;
    Lpm4SetVertexProperty(stmt, vertexValue2);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);

    ret = GmcDropVertexLabel(stmt, g_labelLpm4IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：基于lpm4索引快速查询记录数（GmcGetVertexCount）
TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm4_index_graph_get_count_006)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_testLpm4IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm4IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestLpm4VertexValueT vertexValue;
    vertexValue.f0Value = 232;
    vertexValue.vrId = 13;
    vertexValue.vrfIndex = 1004;
    vertexValue.destIpAddr = 0xffffff00;
    vertexValue.maskLenValue = 5;
    Lpm4SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelLpm4IndexInsert, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip4forward_lpm"));
    TestLpm4VertexValueT vertexValue2;
    vertexValue2.vrId = 13;
    vertexValue2.vrfIndex = 1004;
    vertexValue2.destIpAddr = 0xffffff00;
    vertexValue2.maskLenValue = 5;
    Lpm4SetIndexKey(stmt, vertexValue2);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    LpmQueryVertex(stmt, vertexValue.f0Value, "F0");

    // 基于lpm索引查询Vertex Count的值
    TestLpm4VertexValueT vertexValue3;
    vertexValue3.vrId = 13;
    vertexValue3.vrfIndex = 1004;
    vertexValue3.destIpAddr = 0xffffff00;
    vertexValue3.maskLenValue = 5;
    uint64_t count = 0;
    Lpm4SetIndexKey(stmt, vertexValue3);
    ret = GmcGetVertexCount(stmt, g_labelLpm4IndexInsert, "ip4forward_lpm", &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, count);

    GmcResetStmt(stmt);

    ret = GmcDropVertexLabel(stmt, g_labelLpm4IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm6_index_graph_insert_007)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, g_testLpm6IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK,
        GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm6IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT));
    TestLpm6VertexValueT vertexValue;
    vertexValue.f0Value = 2;
    vertexValue.vrId = 3;
    vertexValue.vrfIndex = 14;
    vertexValue.maskLenValue = 5;
    char destIpAddrValue[16] = {(char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue.destIpAddr = destIpAddrValue;
    Lpm6SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelLpm6IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm6_index_graph_insert_fail_008)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, g_testLpm6IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm6IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    TestLpm6VertexValueT vertexValue;
    vertexValue.f0Value = 1;
    vertexValue.vrId = 3;
    vertexValue.vrfIndex = 14;
    vertexValue.maskLenValue = 5;
    char destIpAddrValue[16] = {(char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue.destIpAddr = destIpAddrValue;
    Lpm6SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex2
    vertexValue.f0Value = 2;
    vertexValue.vrId = 3;
    vertexValue.vrfIndex = 14;
    vertexValue.maskLenValue = 5;
    char destIpAddrValue2[16] = {(char)255, (char)255, (char)0, (char)0, (char)0, (char)0, (char)0, (char)0, (char)0,
        (char)0, (char)0, (char)0, (char)0, (char)0, (char)0, (char)0};
    vertexValue.destIpAddr = destIpAddrValue2;
    Lpm6SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);

    ret = GmcDropVertexLabel(stmt, g_labelLpm6IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm6_index_graph_scan_009)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, g_testLpm6IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    EXPECT_EQ(GMERR_OK,
        GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm6IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT));
    TestLpm6VertexValueT vertexValue;
    vertexValue.f0Value = 252;
    vertexValue.vrId = 3;
    vertexValue.vrfIndex = 14;
    vertexValue.maskLenValue = 5;
    char destIpAddrValue[16] = {(char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue.destIpAddr = destIpAddrValue;
    Lpm6SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelLpm6IndexInsert, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip6forward_lpm"));
    TestLpm6VertexValueT vertexValue2;
    vertexValue2.vrId = 3;
    vertexValue2.vrfIndex = 14;
    vertexValue2.maskLenValue = 5;
    char value2[16] = {(char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue2.destIpAddr = value2;
    Lpm6SetIndexKey(stmt, vertexValue2);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    LpmQueryVertex(stmt, vertexValue.f0Value, "F0");

    GmcResetStmt(stmt);

    ret = GmcDropVertexLabel(stmt, g_labelLpm6IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm6_index_graph_insert_and_scan_zero_masklen_010)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, g_testLpm6IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    EXPECT_EQ(GMERR_OK,
        GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm6IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT));
    TestLpm6VertexValueT vertexValue;
    vertexValue.f0Value = 232;
    vertexValue.vrId = 13;
    vertexValue.vrfIndex = 1004;
    vertexValue.maskLenValue = 0;
    char destIpAddrValue[16] = {(char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue.destIpAddr = destIpAddrValue;
    Lpm6SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex2
    EXPECT_EQ(GMERR_OK,
        GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm6IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT));
    TestLpm6VertexValueT vertexValue2;
    vertexValue2.f0Value = 233;
    vertexValue2.vrId = 13;
    vertexValue2.vrfIndex = 1004;
    vertexValue2.maskLenValue = 31;
    char destIpAddrValue2[16] = {(char)1, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue2.destIpAddr = destIpAddrValue2;
    Lpm6SetVertexProperty(stmt, vertexValue2);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelLpm6IndexInsert, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip6forward_lpm"));
    TestLpm6VertexValueT vertexValue3;
    vertexValue3.vrId = 13;
    vertexValue3.vrfIndex = 1004;
    vertexValue3.maskLenValue = 0;
    char destIpAddrValue3[16] = {(char)1, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue3.destIpAddr = destIpAddrValue3;
    Lpm6SetIndexKey(stmt, vertexValue3);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    LpmQueryVertex(stmt, vertexValue2.f0Value, "F0");

    vertexValue3.maskLenValue = 32;
    destIpAddrValue3[0] = (char)255;
    Lpm6SetIndexKey(stmt, vertexValue3);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    LpmQueryVertex(stmt, vertexValue.f0Value, "F0");

    GmcResetStmt(stmt);

    ret = GmcDropVertexLabel(stmt, g_labelLpm6IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm6_index_graph_insert_delete_update_not_support_011)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_testLpm6IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    EXPECT_EQ(GMERR_OK,
        GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm6IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT));
    TestLpm6VertexValueT vertexValue;
    vertexValue.f0Value = 252;
    vertexValue.vrId = 3;
    vertexValue.vrfIndex = 14;
    vertexValue.maskLenValue = 5;
    char destIpAddrValue[16] = {(char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue.destIpAddr = destIpAddrValue;
    Lpm6SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除顶点
    EXPECT_EQ(GMERR_OK,
        GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm6IndexInsert, DB_MAX_UINT32, GMC_OPERATION_DELETE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip6forward_lpm"));
    TestLpm6VertexValueT vertexValue2;
    vertexValue2.vrId = 3;
    vertexValue2.vrfIndex = 14;
    vertexValue2.maskLenValue = 5;
    char destIpAddrValue2[16] = {(char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue2.destIpAddr = destIpAddrValue2;
    Lpm6SetIndexKey(stmt, vertexValue2);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 更新顶点
    EXPECT_EQ(GMERR_OK,
        GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm6IndexInsert, DB_MAX_UINT32, GMC_OPERATION_UPDATE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip6forward_lpm"));
    TestLpm6VertexValueT vertexValue3;
    vertexValue3.vrId = 3;
    vertexValue3.vrfIndex = 14;
    vertexValue3.maskLenValue = 5;
    char destIpAddrValue3[16] = {(char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue3.destIpAddr = destIpAddrValue3;
    Lpm6SetIndexKey(stmt, vertexValue3);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    GmcResetStmt(stmt);

    ret = GmcDropVertexLabel(stmt, g_labelLpm6IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

// 验证场景：基于lpm6索引快速查询记录数（GmcGetVertexCount）
TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm6_index_graph_get_count_012)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_testLpm6IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    EXPECT_EQ(GMERR_OK,
        GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm6IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT));
    TestLpm6VertexValueT vertexValue;
    vertexValue.f0Value = 252;
    vertexValue.vrId = 3;
    vertexValue.vrfIndex = 14;
    vertexValue.maskLenValue = 5;
    char destIpAddrValue[16] = {(char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue.destIpAddr = destIpAddrValue;
    Lpm6SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelLpm6IndexInsert, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip6forward_lpm"));
    TestLpm6VertexValueT vertexValue2;
    vertexValue2.vrId = 3;
    vertexValue2.vrfIndex = 14;
    vertexValue2.maskLenValue = 5;
    char destIpAddrValue2[16] = {(char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue2.destIpAddr = destIpAddrValue2;
    Lpm6SetIndexKey(stmt, vertexValue2);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    LpmQueryVertex(stmt, vertexValue.f0Value, "F0");

    // 查询Vertex Count的值
    uint64_t count = 0;
    Lpm6SetIndexKey(stmt, vertexValue2);
    ret = GmcGetVertexCount(stmt, g_labelLpm6IndexInsert, "ip6forward_lpm", &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, count);

    GmcResetStmt(stmt);

    ret = GmcDropVertexLabel(stmt, g_labelLpm6IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

static int32_t TestInsertVertexLpm6(
    GmcStmtT *stmt, const char *labelName, uint8_t operBegin, uint8_t operEnd, uint32_t *vrId, uint32_t *vrfId)
{
    EXPECT_NE(stmt, nullptr);
    int32_t ret;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (uint8_t loop = operBegin; loop <= operEnd; loop++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // lpm6 index
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // lpm6 index
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, vrfId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // lpm6 index
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, wrFixed, 16);
        EXPECT_EQ(GMERR_OK, ret);
        // lpm6 index
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        // pk
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT8, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);
        // pk
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT8, &loop, sizeof(loop));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm6_index_graph_update_by_pk_013)
{
    const char *labelName = "Lpm6IndexUpdateByPk";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建vertex lable
    Status ret = GmcCreateVertexLabel(stmt, g_testIndexUpdateByPkJson, g_testMaxRecordCountJson2);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据,总共写入128条数据；
    // ip为"0xcdcd910a222254988475111139002020", mask_len从1、2、3...128,总共128条数据；
    uint32_t vrId = 0;
    uint32_t vrfId = 0;
    ret = TestInsertVertexLpm6(stmt, labelName, 1, 128, &vrId, &vrfId);
    EXPECT_EQ(GMERR_OK, ret);

    // 根据主键更新。lpm6
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value0 = 5;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &value0, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT8, &value0, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "primary_key"));
    uint32_t wrUnit32 = 1;
    char wrFixed[16] = {(char)0xcd, (char)0xcd, (char)0x91, (char)0x0a, (char)0x22, (char)0x22, (char)0x54, (char)0x98,
        (char)0x84, (char)0x75, (char)0x11, (char)0x11, (char)0x39, (char)0x00, (char)0x20, (char)0x20};
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &wrUnit32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &wrUnit32, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, &wrFixed, 16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &value0, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    GmcResetStmt(stmt);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

static void UpdateOrDeleteByLpm6Idx(GmcStmtT *stmt, GmcOperationTypeE opType, bool isCs)
{
    Status ret = isCs ? GmcPrepareStmtByLabelName(stmt, g_labelLpm6IndexInsert, opType) :
                        GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm6IndexInsert, DB_MAX_UINT32, opType);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "ip6forward_lpm"));
    TestLpm6VertexValueT vertexValue2;
    vertexValue2.vrId = 3;
    vertexValue2.vrfIndex = 14;
    vertexValue2.maskLenValue = 5;
    char destIpAddrValue2[16] = {(char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue2.destIpAddr = destIpAddrValue2;
    Lpm6SetIndexKey(stmt, vertexValue2);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
}

TEST_F(StDirectWriteLpm4ClusteredHash, dw_lpm6_index__delete_update_not_support_error)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, g_testLpm6IndexInsertJson, g_testMaxRecordCountJson);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    EXPECT_EQ(GMERR_OK,
        GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelLpm6IndexInsert, DB_MAX_UINT32, GMC_OPERATION_INSERT));
    TestLpm6VertexValueT vertexValue;
    vertexValue.f0Value = 252;
    vertexValue.vrId = 3;
    vertexValue.vrfIndex = 14;
    vertexValue.maskLenValue = 5;
    char destIpAddrValue[16] = {(char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255,
        (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255, (char)255};
    vertexValue.destIpAddr = destIpAddrValue;
    Lpm6SetVertexProperty(stmt, vertexValue);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char buf[512] = {0};
    // 不支持基于 lpm6 索引更新
    UpdateOrDeleteByLpm6Idx(stmt, GMC_OPERATION_UPDATE, false);
    const char *lastError1 = GmcGetLastError();
    ret = strcpy_s(buf, sizeof(buf), lastError1);
    EXPECT_EQ(GMERR_OK, ret);

    UpdateOrDeleteByLpm6Idx(stmt, GMC_OPERATION_UPDATE, true);
    const char *lastError2 = GmcGetLastError();
    EXPECT_EQ(0, strcmp(buf, lastError2));

    // 不支持基于 lpm6 索引删除
    UpdateOrDeleteByLpm6Idx(stmt, GMC_OPERATION_DELETE, false);
    const char *lastError3 = GmcGetLastError();
    ret = strcpy_s(buf, sizeof(buf), lastError3);
    EXPECT_EQ(GMERR_OK, ret);

    UpdateOrDeleteByLpm6Idx(stmt, GMC_OPERATION_DELETE, true);
    const char *lastError4 = GmcGetLastError();
    EXPECT_EQ(0, strcmp(buf, lastError4));

    GmcResetStmt(stmt);

    ret = GmcDropVertexLabel(stmt, g_labelLpm6IndexInsert);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}
