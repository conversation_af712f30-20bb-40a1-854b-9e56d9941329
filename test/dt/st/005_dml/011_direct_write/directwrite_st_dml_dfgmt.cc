/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st for direct write defragmentation of dml operations
 * Author: wangweicheng
 * Create: 2023-07-17
 */

#include <string.h>
#include <math.h>
#include "client_common_st.h"
#include "storage_st_common.h"

class StDirectWriteDfgmtCheck : public StClient {
public:
    static void SetUpTestCase();
};
void StDirectWriteDfgmtCheck::SetUpTestCase()
{
    StartDbServerWithConfig(" \"memCompactEnable=1\" \"minFragmentationRateThreshold=40\" "
                            "\"minFragmentationMemThreshold=1\" \"directWrite=1\" ");
    st_clt_init();
    GmcSignalRegisterNotify();
    CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
    CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
    EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
    // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
    EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
    if (IsEulerEnv()) {
        DbSleep(1000);
    } else {
        st_check_hpe_server_running();
    }
    printf("start response epoll and timeout epoll thread\n");
    printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
    st_connect();
}

#define STRING_LEN 10
#define BYTES_LEN 20

static const char *g_label_config = "{\"defragmentation\":true}";
static const char *g_pk = "TEST_PK";
// 简单表
static const char *g_label_name = "TEST_SC_T1";
static const char *g_label_schema = R"([{
    "type":"record",
    "config": {
        "direct_write": true
    },
    "name":"TEST_SC_T1",
    "fields":[
        { "name":"F0", "type":"int64", "nullable":false},
        { "name":"F1", "type":"uint64", "nullable":true},
        { "name":"F2", "type":"int32", "nullable":true},
        { "name":"F3", "type":"uint32", "nullable":true},
        { "name":"F4", "type":"int16", "nullable":true},
        { "name":"F5", "type":"uint16", "nullable":true},
        { "name":"F6", "type":"int8", "nullable":true},
        { "name":"F7", "type":"uint8", "nullable":true},
        { "name":"F8", "type":"boolean", "nullable":true},
        { "name":"F9", "type":"float", "nullable":true},
        { "name":"F10", "type":"double", "nullable":true},
        { "name":"F11", "type":"time", "nullable":true},
        { "name":"F12", "type":"char", "nullable":true},
	    { "name":"F13", "type":"uchar", "nullable":true},
        { "name":"F14", "type":"fixed", "size":16, "nullable":true},
        { "name":"F15", "type":"partition", "nullable":false},
        { "name":"F16", "type":"string", "size":100, "nullable":true},
        { "name":"F17", "type":"bytes", "size":100, "nullable":true},
        { "name": "vr_id", "type": "uint32", "comment": "Vs索引", "nullable":false },
        { "name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引", "nullable":false },
        { "name": "dest_ip_addr_lpm6", "type": "fixed", "size": 16, "comment": "目的地址" },
        { "name": "mask_len", "type": "uint8", "comment": "掩码长度", "nullable":false },
        { "name":"T1", "type": "record",
	    "fields": [
                {"name":"P0", "type":"int64", "nullable":true},
                {"name":"P1", "type":"uint64", "nullable":true},
                {"name":"P2", "type":"int32", "nullable":true},
                {"name":"P3", "type":"uint32", "nullable":true},
                {"name":"P4", "type":"int16", "nullable":true},
                {"name":"P5", "type":"uint16", "nullable":true},
                {"name":"P6", "type":"int8", "nullable":true},
                {"name":"P7", "type":"uint8", "nullable":true},
                {"name":"P8", "type":"boolean", "nullable":true},
                {"name":"P9", "type":"float", "nullable":true},
                {"name":"P10", "type":"double", "nullable":true},
                {"name":"P11", "type":"time", "nullable":true},
                {"name":"P12", "type":"char", "nullable":true},
                {"name":"P13", "type":"uchar", "nullable":true},
                {"name":"P14", "type":"fixed", "size":16, "nullable":true},
                {"name":"P15", "type":"string", "size":100, "nullable":true},
                {"name":"P16", "type":"bytes", "size":100, "nullable":true}
            ]
        },
        {"name":"T2", "type": "record", "array": true, "size": 1024,
        "fields": [
                {"name":"A0", "type":"int64", "nullable":true},
                {"name":"A1", "type":"uint64", "nullable":true},
                {"name":"A2", "type":"int32", "nullable":true},
                {"name":"A3", "type":"uint32", "nullable":true},
                {"name":"A4", "type":"int16", "nullable":true},
                {"name":"A5", "type":"uint16", "nullable":true},
                {"name":"A6", "type":"int8", "nullable":true},
                {"name":"A7", "type":"uint8", "nullable":true},
                {"name":"A8", "type":"boolean", "nullable":true},
                {"name":"A9", "type":"float", "nullable":true},
                {"name":"A10", "type":"double", "nullable":true},
                {"name":"A11", "type":"time", "nullable":true},
                {"name":"A12", "type":"char", "nullable":true},
                {"name":"A13", "type":"uchar", "nullable":true},
                {"name":"A14", "type":"fixed", "size":16, "nullable":true},
                {"name":"A15", "type":"string", "size":13312, "nullable":true},
                {"name":"A16", "type":"string", "size":13312, "nullable":true},
                {"name":"A17", "type":"string", "size":13312, "nullable":true},
                {"name":"A18", "type":"string", "size":13312, "nullable":true},
                {"name":"A19", "type":"string", "size":13312, "nullable":true},
                {"name":"A20", "type":"string", "size":13312, "nullable":true},
                {"name":"A21", "type":"string", "size":13312, "nullable":true},
                {"name":"A22", "type":"string", "size":13312, "nullable":true},
                {"name":"A23", "type":"string", "size":13312, "nullable":true},
                {"name":"A24", "type":"string", "size":13312, "nullable":true},
                {"name":"A25", "type":"string", "size":13312, "nullable":true},
                {"name":"A26", "type":"string", "size":13312, "nullable":true},
                {"name":"A27", "type":"string", "size":13312, "nullable":true},
                {"name":"A28", "type":"string", "size":13312, "nullable":true},
                {"name":"A29", "type":"string", "size":13312, "nullable":true},
                {"name":"A30", "type":"string", "size":13312, "nullable":true},
                {"name":"A31", "type":"string", "size":13312, "nullable":true},
                {"name":"A32", "type":"string", "size":100, "nullable":true},
                {"name":"A33", "type":"bytes", "size":100, "nullable":true}
            ]
        },
        {"name":"T3", "type": "record", "vector": true, "size": 1024,
        "fields": [
                {"name":"V0", "type":"int64", "nullable":true},
                {"name":"V1", "type":"uint64", "nullable":true},
                {"name":"V2", "type":"int32", "nullable":true},
                {"name":"V3", "type":"uint32", "nullable":true},
                {"name":"V4", "type":"int16", "nullable":true},
                {"name":"V5", "type":"uint16", "nullable":true},
                {"name":"V6", "type":"int8", "nullable":true},
                {"name":"V7", "type":"uint8", "nullable":true},
                {"name":"V8", "type":"boolean", "nullable":true},
                {"name":"V9", "type":"float", "nullable":true},
                {"name":"V10", "type":"double", "nullable":true},
                {"name":"V11", "type":"time", "nullable":true},
                {"name":"V12", "type":"char", "nullable":true},
                {"name":"V13", "type":"uchar", "nullable":true},
                {"name":"V14", "type":"fixed", "size":16, "nullable":true},
                {"name":"V15", "type":"string", "size":100, "nullable":true},
                {"name":"V16", "type":"bytes", "size":100, "nullable":true}
            ]
        }
    ],
    "keys":[
        {
            "node":"TEST_SC_T1",
            "name":"TEST_PK",
            "fields":["F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        },
        {
            "node":"TEST_SC_T1",
            "name":"uniq_localhash",
            "fields":["F3"],
            "index":{"type":"localhash"},
            "constraints":{"unique":true}
        },
        {
            "node":"TEST_SC_T1",
            "name":"nonuniq_localhash",
            "fields":["F2"],
            "index":{"type":"localhash"},
            "constraints":{"unique":false}
        },
        {
            "node":"TEST_SC_T1",
            "name":"nonuniq_hashcluster",
            "fields":["F2"],
            "index":{"type":"hashcluster"},
            "constraints":{"unique":false}
        },
        {
            "node":"TEST_SC_T1",
            "name":"local",
            "fields":["F2"],
            "index":{"type":"local"},
            "constraints":{"unique":false}
        },
        {
            "node": "TEST_SC_T1",
            "name": "lpm6",
            "index": { "type": "lpm6_tree_bitmap" },
            "fields": [ "vr_id", "vrf_index", "dest_ip_addr_lpm6", "mask_len" ],
            "constraints": { "unique": true }
        }
    ]
}])";

Status SetOtherPropertyValue(GmcNodeT *node, int32_t value)
{
    Status ret = GMERR_OK;

    uint64_t F1 = (uint64_t)value;
    ret = GmcNodeSetPropertyByName(node, "F1", GMC_DATATYPE_UINT64, &F1, sizeof(F1));
    RETURN_IFERR(ret);

    int16_t F4 = (value)&0x7fff;
    ret = GmcNodeSetPropertyByName(node, "F4", GMC_DATATYPE_INT16, &F4, sizeof(F4));
    RETURN_IFERR(ret);

    uint16_t F5 = (value)&0xffff;
    ret = GmcNodeSetPropertyByName(node, "F5", GMC_DATATYPE_UINT16, &F5, sizeof(F5));
    RETURN_IFERR(ret);

    int8_t F6 = (value)&0x7f;
    ret = GmcNodeSetPropertyByName(node, "F6", GMC_DATATYPE_INT8, &F6, sizeof(F6));
    RETURN_IFERR(ret);

    uint8_t F7 = (value)&0xff;
    ret = GmcNodeSetPropertyByName(node, "F7", GMC_DATATYPE_UINT8, &F7, sizeof(F7));
    RETURN_IFERR(ret);

    bool F8 = value;
    ret = GmcNodeSetPropertyByName(node, "F8", GMC_DATATYPE_BOOL, &F8, sizeof(F8));
    RETURN_IFERR(ret);

    float F9 = value;
    ret = GmcNodeSetPropertyByName(node, "F9", GMC_DATATYPE_FLOAT, &F9, sizeof(F9));
    RETURN_IFERR(ret);

    double F10 = value;
    ret = GmcNodeSetPropertyByName(node, "F10", GMC_DATATYPE_DOUBLE, &F10, sizeof(F10));
    RETURN_IFERR(ret);

    uint64_t F11 = (uint64_t)value;
    ret = GmcNodeSetPropertyByName(node, "F11", GMC_DATATYPE_TIME, &F11, sizeof(F11));
    RETURN_IFERR(ret);

    char F12 = 'a' + ((value)&0x1A);
    ret = GmcNodeSetPropertyByName(node, "F12", GMC_DATATYPE_CHAR, &F12, sizeof(F12));
    RETURN_IFERR(ret);

    unsigned char F13 = 'A' + ((value)&0x1A);
    ret = GmcNodeSetPropertyByName(node, "F13", GMC_DATATYPE_UCHAR, &F13, sizeof(F13));
    RETURN_IFERR(ret);

    uint8_t F14[16] = {0};
    (void)snprintf_s((char *)F14, sizeof(F14), sizeof(F14), "f%014d", value);
    ret = GmcNodeSetPropertyByName(node, "F14", GMC_DATATYPE_FIXED, F14, sizeof(F14));
    RETURN_IFERR(ret);

    uint8_t F15 = (value)&0xf;
    ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_PARTITION, &F15, sizeof(F15));
    RETURN_IFERR(ret);

    uint32_t vr_id = 1;
    ret = GmcNodeSetPropertyByName(node, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    RETURN_IFERR(ret);

    uint32_t vrf_index = 1;
    ret = GmcNodeSetPropertyByName(node, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
    RETURN_IFERR(ret);

    char dest_ip_addr_lpm6[16] = {0};
    (void)snprintf_s(
        (char *)dest_ip_addr_lpm6, sizeof(dest_ip_addr_lpm6), sizeof(dest_ip_addr_lpm6), "aaaaaaa%08d", value);
    ret = GmcNodeSetPropertyByName(
        node, "dest_ip_addr_lpm6", GMC_DATATYPE_FIXED, dest_ip_addr_lpm6, sizeof(dest_ip_addr_lpm6));
    RETURN_IFERR(ret);

    uint8_t mask_len = 128;
    ret = GmcNodeSetPropertyByName(node, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
    RETURN_IFERR(ret);

    uint8_t F16[STRING_LEN] = {0};
    (void)snprintf_s((char *)F16, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "F16", GMC_DATATYPE_STRING, F16, strlen((char *)F16));
    RETURN_IFERR(ret);

    uint8_t F17[BYTES_LEN] = {0};
    (void)snprintf_s((char *)F17, BYTES_LEN, BYTES_LEN, "ABCDEFGHIJ%08d", value);
    ret = GmcNodeSetPropertyByName(node, "F17", GMC_DATATYPE_BYTES, F17, sizeof(F17));
    RETURN_IFERR(ret);

    return GMERR_OK;
}

Status SetT1Property(GmcNodeT *node, int32_t value)
{
    int ret = GMERR_OK;

    int64_t P0 = value;
    ret = GmcNodeSetPropertyByName(node, "P0", GMC_DATATYPE_INT64, &P0, sizeof(P0));
    RETURN_IFERR(ret);

    uint64_t P1 = (uint64_t)value;
    ret = GmcNodeSetPropertyByName(node, "P1", GMC_DATATYPE_UINT64, &P1, sizeof(P1));
    RETURN_IFERR(ret);

    int32_t P2 = value;
    ret = GmcNodeSetPropertyByName(node, "P2", GMC_DATATYPE_INT32, &P2, sizeof(P2));
    RETURN_IFERR(ret);

    uint32_t P3 = (uint32_t)value;
    ret = GmcNodeSetPropertyByName(node, "P3", GMC_DATATYPE_UINT32, &P3, sizeof(P3));
    RETURN_IFERR(ret);

    int16_t P4 = (value)&0x7fff;
    ret = GmcNodeSetPropertyByName(node, "P4", GMC_DATATYPE_INT16, &P4, sizeof(P4));
    RETURN_IFERR(ret);

    uint16_t P5 = (value)&0xffff;
    ret = GmcNodeSetPropertyByName(node, "P5", GMC_DATATYPE_UINT16, &P5, sizeof(P5));
    RETURN_IFERR(ret);

    int8_t P6 = (value)&0x7f;
    ret = GmcNodeSetPropertyByName(node, "P6", GMC_DATATYPE_INT8, &P6, sizeof(P6));
    RETURN_IFERR(ret);

    uint8_t P7 = (value)&0xff;
    ret = GmcNodeSetPropertyByName(node, "P7", GMC_DATATYPE_UINT8, &P7, sizeof(P7));
    RETURN_IFERR(ret);

    bool P8 = value;
    ret = GmcNodeSetPropertyByName(node, "P8", GMC_DATATYPE_BOOL, &P8, sizeof(P8));
    RETURN_IFERR(ret);

    float P9 = value;
    ret = GmcNodeSetPropertyByName(node, "P9", GMC_DATATYPE_FLOAT, &P9, sizeof(P9));
    RETURN_IFERR(ret);

    double P10 = value;
    ret = GmcNodeSetPropertyByName(node, "P10", GMC_DATATYPE_DOUBLE, &P10, sizeof(P10));
    RETURN_IFERR(ret);

    uint64_t P11 = (uint64_t)value;
    ret = GmcNodeSetPropertyByName(node, "P11", GMC_DATATYPE_TIME, &P11, sizeof(P11));
    RETURN_IFERR(ret);

    char P12 = 'a' + ((value)&0x1A);
    ret = GmcNodeSetPropertyByName(node, "P12", GMC_DATATYPE_CHAR, &P12, sizeof(P12));
    RETURN_IFERR(ret);

    unsigned char P13 = 'A' + ((value)&0x1A);
    ret = GmcNodeSetPropertyByName(node, "P13", GMC_DATATYPE_UCHAR, &P13, sizeof(P13));
    RETURN_IFERR(ret);

    uint8_t P14[16] = {0};
    (void)snprintf_s((char *)P14, sizeof(P14), sizeof(P14), "f%014d", value);
    ret = GmcNodeSetPropertyByName(node, "P14", GMC_DATATYPE_FIXED, P14, sizeof(P14));
    RETURN_IFERR(ret);

    uint8_t P15[STRING_LEN] = {0};
    (void)snprintf_s((char *)P15, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "P15", GMC_DATATYPE_STRING, P15, strlen((char *)P15));
    RETURN_IFERR(ret);

    uint8_t P16[BYTES_LEN] = {0};
    (void)snprintf_s((char *)P16, BYTES_LEN, BYTES_LEN, "ABCDEFGHI%08d", value);
    ret = GmcNodeSetPropertyByName(node, "P16", GMC_DATATYPE_BYTES, P16, sizeof(P16));
    RETURN_IFERR(ret);

    return GMERR_OK;
}

Status SetT2Property(GmcNodeT *node, int32_t value)
{
    Status ret = GMERR_OK;

    int64_t A0 = value;
    ret = GmcNodeSetPropertyByName(node, "A0", GMC_DATATYPE_INT64, &A0, sizeof(A0));
    RETURN_IFERR(ret);

    uint64_t A1 = (uint64_t)value;
    ret = GmcNodeSetPropertyByName(node, "A1", GMC_DATATYPE_UINT64, &A1, sizeof(A1));
    RETURN_IFERR(ret);

    int32_t A2 = value;
    ret = GmcNodeSetPropertyByName(node, "A2", GMC_DATATYPE_INT32, &A2, sizeof(A2));
    RETURN_IFERR(ret);

    uint32_t A3 = (uint32_t)value;
    ret = GmcNodeSetPropertyByName(node, "A3", GMC_DATATYPE_UINT32, &A3, sizeof(A3));
    RETURN_IFERR(ret);

    int16_t A4 = (value)&0x7fff;
    ret = GmcNodeSetPropertyByName(node, "A4", GMC_DATATYPE_INT16, &A4, sizeof(A4));
    RETURN_IFERR(ret);

    uint16_t A5 = (value)&0xffff;
    ret = GmcNodeSetPropertyByName(node, "A5", GMC_DATATYPE_UINT16, &A5, sizeof(A5));
    RETURN_IFERR(ret);

    int8_t A6 = (value)&0x7f;
    ret = GmcNodeSetPropertyByName(node, "A6", GMC_DATATYPE_INT8, &A6, sizeof(A6));
    RETURN_IFERR(ret);

    uint8_t A7 = (value)&0xff;
    ret = GmcNodeSetPropertyByName(node, "A7", GMC_DATATYPE_UINT8, &A7, sizeof(A7));
    RETURN_IFERR(ret);

    bool A8 = value;
    ret = GmcNodeSetPropertyByName(node, "A8", GMC_DATATYPE_BOOL, &A8, sizeof(A8));
    RETURN_IFERR(ret);

    float A9 = value;
    ret = GmcNodeSetPropertyByName(node, "A9", GMC_DATATYPE_FLOAT, &A9, sizeof(A9));
    RETURN_IFERR(ret);

    double A10 = value;
    ret = GmcNodeSetPropertyByName(node, "A10", GMC_DATATYPE_DOUBLE, &A10, sizeof(A10));
    RETURN_IFERR(ret);

    uint64_t A11 = (uint64_t)value;
    ret = GmcNodeSetPropertyByName(node, "A11", GMC_DATATYPE_TIME, &A11, sizeof(A11));
    RETURN_IFERR(ret);

    char A12 = 'a' + ((value)&0x1A);
    ret = GmcNodeSetPropertyByName(node, "A12", GMC_DATATYPE_CHAR, &A12, sizeof(A12));
    RETURN_IFERR(ret);

    unsigned char A13 = 'A' + ((value)&0x1A);
    ret = GmcNodeSetPropertyByName(node, "A13", GMC_DATATYPE_UCHAR, &A13, sizeof(A13));
    RETURN_IFERR(ret);

    uint8_t A14[16] = {0};
    (void)snprintf_s((char *)A14, sizeof(A14), sizeof(A14), "f%014d", value);
    ret = GmcNodeSetPropertyByName(node, "A14", GMC_DATATYPE_FIXED, A14, sizeof(A14));
    RETURN_IFERR(ret);

    uint8_t A15[STRING_LEN] = {0};
    (void)snprintf_s((char *)A15, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A15", GMC_DATATYPE_STRING, A15, strlen((char *)A15));
    RETURN_IFERR(ret);

    uint8_t A16[STRING_LEN] = {0};
    (void)snprintf_s((char *)A16, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A16", GMC_DATATYPE_STRING, A16, strlen((char *)A16));
    RETURN_IFERR(ret);

    uint8_t A17[STRING_LEN] = {0};
    (void)snprintf_s((char *)A17, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A17", GMC_DATATYPE_STRING, A17, strlen((char *)A17));
    RETURN_IFERR(ret);

    uint8_t A18[STRING_LEN] = {0};
    (void)snprintf_s((char *)A18, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A18", GMC_DATATYPE_STRING, A18, strlen((char *)A18));
    RETURN_IFERR(ret);

    uint8_t A19[STRING_LEN] = {0};
    (void)snprintf_s((char *)A19, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A19", GMC_DATATYPE_STRING, A19, strlen((char *)A19));
    RETURN_IFERR(ret);

    uint8_t A20[STRING_LEN] = {0};
    (void)snprintf_s((char *)A20, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A20", GMC_DATATYPE_STRING, A20, strlen((char *)A20));
    RETURN_IFERR(ret);

    uint8_t A21[STRING_LEN] = {0};
    (void)snprintf_s((char *)A21, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A21", GMC_DATATYPE_STRING, A21, strlen((char *)A21));
    RETURN_IFERR(ret);

    uint8_t A22[STRING_LEN] = {0};
    (void)snprintf_s((char *)A22, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A22", GMC_DATATYPE_STRING, A22, strlen((char *)A22));
    RETURN_IFERR(ret);

    uint8_t A23[STRING_LEN] = {0};
    (void)snprintf_s((char *)A23, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A23", GMC_DATATYPE_STRING, A23, strlen((char *)A23));
    RETURN_IFERR(ret);

    uint8_t A24[STRING_LEN] = {0};
    (void)snprintf_s((char *)A24, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A24", GMC_DATATYPE_STRING, A24, strlen((char *)A24));
    RETURN_IFERR(ret);

    uint8_t A25[STRING_LEN] = {0};
    (void)snprintf_s((char *)A25, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A25", GMC_DATATYPE_STRING, A25, strlen((char *)A25));
    RETURN_IFERR(ret);

    uint8_t A26[STRING_LEN] = {0};
    (void)snprintf_s((char *)A26, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A26", GMC_DATATYPE_STRING, A26, strlen((char *)A26));
    RETURN_IFERR(ret);

    uint8_t A27[STRING_LEN] = {0};
    (void)snprintf_s((char *)A27, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A27", GMC_DATATYPE_STRING, A27, strlen((char *)A27));
    RETURN_IFERR(ret);

    uint8_t A28[STRING_LEN] = {0};
    (void)snprintf_s((char *)A28, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A28", GMC_DATATYPE_STRING, A28, strlen((char *)A28));
    RETURN_IFERR(ret);

    uint8_t A29[STRING_LEN] = {0};
    (void)snprintf_s((char *)A29, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A29", GMC_DATATYPE_STRING, A29, strlen((char *)A29));
    RETURN_IFERR(ret);

    uint8_t A30[STRING_LEN] = {0};
    (void)snprintf_s((char *)A30, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A30", GMC_DATATYPE_STRING, A30, strlen((char *)A30));
    RETURN_IFERR(ret);

    uint8_t A31[STRING_LEN] = {0};
    (void)snprintf_s((char *)A31, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A31", GMC_DATATYPE_STRING, A31, strlen((char *)A31));
    RETURN_IFERR(ret);

    uint8_t A32[STRING_LEN] = {0};
    (void)snprintf_s((char *)A32, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A32", GMC_DATATYPE_STRING, A32, strlen((char *)A32));
    RETURN_IFERR(ret);

    uint8_t A33[BYTES_LEN] = {0};
    (void)snprintf_s((char *)A33, BYTES_LEN, STRING_LEN, "ABCDEFGHI%08d", value);
    ret = GmcNodeSetPropertyByName(node, "A33", GMC_DATATYPE_BYTES, A33, sizeof(A33));
    RETURN_IFERR(ret);

    return GMERR_OK;
}

Status SetT3Property(GmcNodeT *node, int32_t value)
{
    Status ret = GMERR_OK;

    int64_t V0 = value;
    ret = GmcNodeSetPropertyByName(node, "V0", GMC_DATATYPE_INT64, &V0, sizeof(V0));
    RETURN_IFERR(ret);

    uint64_t V1 = (uint64_t)value;
    ret = GmcNodeSetPropertyByName(node, "V1", GMC_DATATYPE_UINT64, &V1, sizeof(V1));
    RETURN_IFERR(ret);

    int32_t V2 = value;
    ret = GmcNodeSetPropertyByName(node, "V2", GMC_DATATYPE_INT32, &V2, sizeof(V2));
    RETURN_IFERR(ret);

    uint32_t V3 = (uint32_t)value;
    ret = GmcNodeSetPropertyByName(node, "V3", GMC_DATATYPE_UINT32, &V3, sizeof(V3));
    RETURN_IFERR(ret);

    int16_t V4 = (value)&0x7fff;
    ret = GmcNodeSetPropertyByName(node, "V4", GMC_DATATYPE_INT16, &V4, sizeof(V4));
    RETURN_IFERR(ret);

    uint16_t V5 = (value)&0xffff;
    ret = GmcNodeSetPropertyByName(node, "V5", GMC_DATATYPE_UINT16, &V5, sizeof(V5));
    RETURN_IFERR(ret);

    int8_t V6 = (value)&0x7f;
    ret = GmcNodeSetPropertyByName(node, "V6", GMC_DATATYPE_INT8, &V6, sizeof(V6));
    RETURN_IFERR(ret);

    uint8_t V7 = (value)&0xff;
    ret = GmcNodeSetPropertyByName(node, "V7", GMC_DATATYPE_UINT8, &V7, sizeof(V7));
    RETURN_IFERR(ret);

    bool V8 = value;
    ret = GmcNodeSetPropertyByName(node, "V8", GMC_DATATYPE_BOOL, &V8, sizeof(V8));
    RETURN_IFERR(ret);

    float V9 = value;
    ret = GmcNodeSetPropertyByName(node, "V9", GMC_DATATYPE_FLOAT, &V9, sizeof(V9));
    RETURN_IFERR(ret);

    double V10 = value;
    ret = GmcNodeSetPropertyByName(node, "V10", GMC_DATATYPE_DOUBLE, &V10, sizeof(V10));
    RETURN_IFERR(ret);

    uint64_t V11 = (uint64_t)value;
    ret = GmcNodeSetPropertyByName(node, "V11", GMC_DATATYPE_TIME, &V11, sizeof(V11));
    RETURN_IFERR(ret);

    char V12 = 'a' + ((value)&0x1A);
    ret = GmcNodeSetPropertyByName(node, "V12", GMC_DATATYPE_CHAR, &V12, sizeof(V12));
    RETURN_IFERR(ret);

    unsigned char V13 = 'A' + ((value)&0x1A);
    ret = GmcNodeSetPropertyByName(node, "V13", GMC_DATATYPE_UCHAR, &V13, sizeof(V13));
    RETURN_IFERR(ret);

    uint8_t V14[16] = {0};
    (void)snprintf_s((char *)V14, sizeof(V14), sizeof(V14), "f%014d", value);
    ret = GmcNodeSetPropertyByName(node, "V14", GMC_DATATYPE_FIXED, V14, sizeof(V14));
    RETURN_IFERR(ret);

    uint8_t V15[STRING_LEN] = {0};
    (void)snprintf_s((char *)V15, STRING_LEN, STRING_LEN, "s%08d", value);
    ret = GmcNodeSetPropertyByName(node, "V15", GMC_DATATYPE_STRING, V15, strlen((char *)V15));
    RETURN_IFERR(ret);

    uint8_t V16[BYTES_LEN] = {0};
    (void)snprintf_s((char *)V16, BYTES_LEN, BYTES_LEN, "ABCDEFGHI%08d", value);
    ret = GmcNodeSetPropertyByName(node, "V16", GMC_DATATYPE_BYTES, V16, sizeof(V16));
    RETURN_IFERR(ret);

    return GMERR_OK;
}

Status ReplaceVertexWithLpm(GmcStmtT *stmt)
{
    int ret = GMERR_OK;
    int32_t vertexCount = 20000;
    int32_t expAffectRows = 1;

    for (int32_t i = 0; i < vertexCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
        RETURN_IFERR(ret);

        GmcNodeT *root, *tree1, *tree2, *tree3;
        ret = GmcGetRootNode(stmt, &root);
        RETURN_IFERR(ret);
        ret = GmcNodeGetChild(root, "T1", &tree1);
        RETURN_IFERR(ret);
        ret = GmcNodeGetChild(root, "T2", &tree2);
        RETURN_IFERR(ret);
        ret = GmcNodeGetChild(root, "T3", &tree3);
        RETURN_IFERR(ret);

        // 设置主键索引
        int64_t F0 = (int64_t)i;
        ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_INT64, &F0, sizeof(F0));
        RETURN_IFERR(ret);

        // 设置非唯一二级索引
        int32_t F2 = i;
        ret = GmcNodeSetPropertyByName(root, "F2", GMC_DATATYPE_INT32, &F2, sizeof(F2));
        RETURN_IFERR(ret);

        // 设置唯一二级索引
        uint32_t F3 = (uint32_t)i;
        ret = GmcNodeSetPropertyByName(root, "F3", GMC_DATATYPE_UINT32, &F3, sizeof(F3));
        RETURN_IFERR(ret);
        // 设置根节点其余字段
        ret = SetOtherPropertyValue(root, i);
        RETURN_IFERR(ret);
        // 设置T1
        ret = SetT1Property(tree1, i);
        RETURN_IFERR(ret);
        // 设置T2
        for (int32_t j = 0; j < 3; j++) {
            ret = GmcNodeAppendElement(tree2, &tree2);
            RETURN_IFERR(ret);
            ret = SetT2Property(tree2, i);
            RETURN_IFERR(ret);
        }
        // 设置T3
        for (int32_t j = 0; j < 3; j++) {
            ret = GmcNodeAppendElement(tree3, &tree3);
            RETURN_IFERR(ret);
            ret = SetT3Property(tree3, i);
            RETURN_IFERR(ret);
        }

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);

        int32_t affect;
        int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affect, sizeof(int32_t));
        RETURN_IFERR(ret);
        if (affect != expAffectRows) {
            printf("[testGmcGetStmtAttr] expect is %d, affect is %d\n", expAffectRows, affect);
            return GMERR_INTERNAL_ERROR;
        }
    }
    return GMERR_OK;
}

// 取出 $STORAGE_MEMDATA_STAT $STORAGE_HEAP_STAT $STORAGE_ART_INDEX_STAT 视图中的部分字段
void SeCompactVerify(int32_t *totalFreeChunkCount, int32_t *defragmentationCnt, int32_t *scaleInCount)
{
    // 查视图校验
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));

    const char *filter0 = "INSTANCE_ID=\'1\'";
    GetViewFieldResultFilter("V\\$STORAGE_MEMDATA_STAT", "TOTAL_FREE_CHUNK_COUNT", filter0, cmdOutput2, 64);
    *totalFreeChunkCount = atoi(cmdOutput2);

    const char *filter1 = "LABEL_NAME=\'TEST_SC_T1\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "DEFRAGMENTATION_CNT", filter1, cmdOutput2, 64);
    *defragmentationCnt = atoi(cmdOutput2);

    const char *filter2 = "INDEX_NAME=\'lpm6\'";
    GetViewFieldResultFilter("V\\$STORAGE_ART_INDEX_STAT", "SCALE_IN_COUNT", filter2, cmdOutput2, 64);
    *scaleInCount = atoi(cmdOutput2);
}

Status DeleteVertexWithLpm(GmcStmtT *stmt)
{
    int ret = GMERR_OK;
    int32_t vertexCount = 20000;
    int32_t expAffectRows = 1;

    for (int32_t i = 0; i < vertexCount; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyName(stmt, g_pk);
        RETURN_IFERR(ret);
        int64_t F0 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F0, sizeof(F0));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);

        int32_t affect;
        int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affect, sizeof(int32_t));
        RETURN_IFERR(ret);
        if (affect != expAffectRows) {
            printf("[testGmcGetStmtAttr] expect is %d, affect is %d\n", expAffectRows, affect);
            return GMERR_INTERNAL_ERROR;
        }
    }
    return GMERR_OK;
}

TEST_F(StDirectWriteDfgmtCheck, vertexWithLpmDfgmt)
{
    int ret;
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = ReplaceVertexWithLpm(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t totalFreeChunkCount, defragmentationCnt, scaleInCount;
    int32_t totalFreeChunkCount2, defragmentationCnt2, scaleInCount2;
    SeCompactVerify(&totalFreeChunkCount, &defragmentationCnt, &scaleInCount);

    // delete
    ret = DeleteVertexWithLpm(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_MEMDATA_STAT");
    system("gmsysview -q V\\$STORAGE_HEAP_STAT");
    system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT");

    SeCompactVerify(&totalFreeChunkCount2, &defragmentationCnt2, &scaleInCount2);

    sleep(5);                                              // 等待后台缩容完成
    ASSERT_LT(0, defragmentationCnt2);                     // 触发heap缩容
    ASSERT_LT(0, scaleInCount2);                           // 触发lpm索引缩容
    ASSERT_LT(totalFreeChunkCount, totalFreeChunkCount2);  // 归还memdata

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}
