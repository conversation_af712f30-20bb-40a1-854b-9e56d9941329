[{"type": "container", "name": "T0", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"type": "choice", "name": "T1Choice", "fields": [{"type": "case", "name": "T11Case", "default": true, "fields": [{"name": "F0", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "T12Case", "fields": [{"name": "F0", "type": "uint32", "nullable": false}]}]}], "keys": [{"node": "T0", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]