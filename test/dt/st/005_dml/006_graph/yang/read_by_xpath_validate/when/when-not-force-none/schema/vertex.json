[{"name": "when-not-force-none", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32"}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "T1", "type": "container", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "T2", "type": "container", "clause": [{"type": "when", "formula": "../T3"}], "presence": true, "fields": [{"name": "F0", "type": "uint32"}]}, {"name": "T3", "type": "container", "presence": true, "fields": [{"name": "F0", "type": "uint32"}]}]}], "keys": [{"node": "when-not-force-none", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L1", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}], "keys": [{"node": "L1", "name": "k0", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"node": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]