[{"name": "huawei-aaa:aaa", "type": "container", "presence": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "alive-user-qrys", "type": "container", "is_config": false, "presence": true, "fields": []}, {"name": "lam", "type": "container", "presence": true, "fields": [{"name": "users", "type": "container", "presence": true, "fields": []}]}], "keys": [{"node": "huawei-aaa:aaa", "name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "user-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "user-name", "type": "string", "is_config": false}, {"name": "ip", "type": "string", "is_config": false}, {"name": "access-time", "type": "string", "is_config": false}], "keys": [{"node": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "name": "k0", "fields": [":pid", "user-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aaa:aaa::lam::users::user", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "group-name", "type": "string", "default": "admin", "clause": [{"type": "must", "formula": "re-match('aaa', 'a*', 1)"}, {"type": "must", "formula": "/huawei-aaa:aaa/alive-user-qrys/huawei-aaa:aaa::alive-user-qrys::alive-user-qry[last(2)]"}, {"type": "must", "formula": "/huawei-aaa:aaa/alive-user-qrys/huawei-aaa:aaa::alive-user-qrys::alive-user-qry[position(3) = 1]"}, {"type": "must", "formula": "name() and name(/huawei-aaa:aaa/alive-user-qrys, 4)"}, {"type": "must", "formula": "concat(5) and concat('a', 'b', 'c')"}, {"type": "must", "formula": "contains('a', 'b', 6)"}, {"type": "must", "formula": "substring-before('a', 'b', 7)"}]}, {"name": "password", "type": "string", "clause": [{"type": "must", "formula": "substring('12345',2,3,8) and substring('12345',2)"}, {"type": "must", "formula": "string-length('12345',9) and string-length()"}, {"type": "must", "formula": "normalize-space('  12345   ',10) and normalize-space()"}, {"type": "must", "formula": "boolean(/huawei-aaa:aaa, 11)"}, {"type": "must", "formula": "true(12) and false()"}, {"type": "must", "formula": "lang('en',13)"}, {"type": "must", "formula": "floor(1.2345, 14)"}]}, {"name": "level", "type": "uint32", "clause": [{"type": "must", "formula": "derived-from(/huawei-aaa:aaa, 'identity',15)"}, {"type": "must", "formula": "derived-from-or-self(/huawei-aaa:aaa, 'identity',16)"}, {"type": "must", "formula": "enum-value(../../..,17)"}, {"type": "must", "formula": "bit-is-set(/huawei-aaa:aaa, 'bit-name',18)"}, {"type": "must", "formula": "local-name() and local-name(/huawei-aaa:aaa/alive-user-qrys/huawei-aaa:aaa::alive-user-qrys::alive-user-qry/user-id，19)"}, {"type": "must", "formula": "ceiling(100.1001,20)"}, {"type": "must", "formula": "round(1.2345,21)"}]}, {"name": "service-terminal", "type": "uint32", "clause": [{"type": "must", "formula": "deref(.)/../../../alive-user-qrys/huawei-aaa:aaa::alive-user-qrys::alive-user-qry/user-id"}, {"type": "must", "formula": "/huawei-aaa:aaa/alive-user-qrys/huawei-aaa:aaa::alive-user-qrys::alive-user-qry/user-id and deref(.)/../../../alive-user-qrys"}, {"type": "must", "formula": "deref(.)/../../../alive-user-qrys and deref(.)/../../../alive-user-qrys"}, {"type": "must", "formula": "current() and deref(.)/../../../alive-user-qrys"}, {"type": "must", "formula": "deref(.)/../../../alive-user-qrys and current()"}, {"type": "must", "formula": "deref(../level)"}, {"type": "leafref", "formula": "/huawei-aaa:aaa/alive-user-qrys/huawei-aaa:aaa::alive-user-qrys::alive-user-qry/user-id"}]}, {"name": "service-api", "type": "boolean", "default": false}, {"name": "password-force-change", "type": "boolean", "default": true}], "keys": [{"node": "huawei-aaa:aaa::lam::users::user", "name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]