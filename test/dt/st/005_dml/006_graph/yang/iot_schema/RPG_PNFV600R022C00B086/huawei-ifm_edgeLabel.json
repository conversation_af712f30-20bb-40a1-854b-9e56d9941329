[{"name": "root_interface", "source_vertex_label": "root", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "sourceNodeName": "root::huawei-ifm:ifm::interfaces", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_traffic-mirror-apply", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "traffic-mirror-apply_acl-instance", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl-instance_status", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance::statuses::status", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance::statuses", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_traffic-redirect-apply", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "traffic-redirect-apply_acl-instance", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl-instance_status_2", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_traffic-remark-apply", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "traffic-remark-apply_acl-instance", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl-instance_status_3", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_traffic-filter-apply", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "traffic-filter-apply_acl-instance", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "acl-instance_statistic", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_observe-port", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-mirror:mirror::observe-ports::observe-port", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-mirror:mirror::observe-ports", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_request-option", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:dhcp-client-if::request-option", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:dhcp-client-if", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_option121", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::option121s::option121", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::option121s", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_option", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "option_sub-option", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "sub-option_ip-addresses", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option::option-format::sub-ip-format::ip-addresses", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option::option-format::sub-ip-format", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "option_ip-addresses", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::ip-format::ip-addresses", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::ip-format", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_static-bind", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::static-binds::static-bind", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::static-binds", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_excluded-ip-address", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::excluded-ip-addresses::excluded-ip-address", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::excluded-ip-addresses", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_ip-address", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::dns-list::ip-address", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::dns-list", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_gateway-list", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::gateway-list", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_nbns-list", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::nbns-list", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_logging", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::logging", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_sim", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-cellular:cellular-state::sims::sim", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-cellular:cellular-state::sims", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_current-apn", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-cellular:cellular-state::current-apns::current-apn", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-cellular:cellular-state::current-apns", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_apn-profile", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-cellular:cellular::apn-profiles::apn-profile", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-cellular:cellular::apn-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_cellular-profile", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-cellular:cellular::cellular-profiles::cellular-profile", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-cellular:cellular::cellular-profiles", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_qinq-with-pe-segment", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l2-sub-interface::flow-type::qinq::qinqs::qinq-with-pe-segments::qinq-with-pe-segment", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l2-sub-interface::flow-type::qinq::qinqs::qinq-with-pe-segments", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_qinq-vid", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l2-sub-interface::flow-type::qinq::qinqs::qinq-vids::qinq-vid", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l2-sub-interface::flow-type::qinq::qinqs::qinq-vids", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_policy-vlan", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l2-sub-interface::flow-type::dot1q::dot1q::policy::policy::policy-vlans::policy-vlan", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l2-sub-interface::flow-type::dot1q::dot1q::policy::policy::policy-vlans", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_description", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::user-vlan-comm::user-vlan-common::user-vlan-dot1q::descriptions::description", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::user-vlan-comm::user-vlan-common::user-vlan-dot1q::descriptions", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_description_2", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::user-vlan-comm::user-vlan-common::user-vlan-qinqs::descriptions::description", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::user-vlan-comm::user-vlan-common::user-vlan-qinqs::descriptions", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_user-vlan-qinq", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::user-vlan-comm::user-vlan-common::user-vlan-qinqs::user-vlan-qinq", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::user-vlan-comm::user-vlan-common::user-vlan-qinqs", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_policy-vlan-group", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::qinq-stacking-policy::stacking-policy::policy-vlan-groups::policy-vlan-group", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::qinq-stacking-policy::stacking-policy::policy-vlan-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_policy-vlan_2", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::qinq-stacking-policy::stacking-policy::policy-vlans::policy-vlan", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::qinq-stacking-policy::stacking-policy::policy-vlans", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_mapping-vid", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::qinq-mapping::qinq-mapping::mapping-vids::mapping-vid", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::qinq-mapping::qinq-mapping::mapping-vids", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_stacking-vid", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::qinq-stacking::qinq-stacking::stacking-vid", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::qinq-stacking::qinq-stacking", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_qinq-vid_2", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::qinq-termination::qinq-termination::qinq-vids::qinq-vid", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::qinq-termination::qinq-termination::qinq-vids", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_vrrp", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::dot1q-termination::dot1q-termination::vrrps::vrrp", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::dot1q-termination::dot1q-termination::vrrps", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_policy-vlan-group_2", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::dot1q-termination::dot1q-termination::dot1q-vlans-policy::policy-vlan-groups::policy-vlan-group", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::dot1q-termination::dot1q-termination::dot1q-vlans-policy::policy-vlan-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_policy-vlan_3", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::dot1q-termination::dot1q-termination::dot1q-vlans-policy::policy-vlans::policy-vlan", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::dot1q-termination::dot1q-termination::dot1q-vlans-policy::policy-vlans", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_dot1q-vlans-group", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::dot1q-termination::dot1q-termination::dot1q-vlans::dot1q-vlans-group", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::flow-type::dot1q-termination::dot1q-termination::dot1q-vlans", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_vlan-group", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::vlan-groups::vlan-group", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::l3-sub-interface::vlan-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_port-am-isolate", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::port-am-isolates::port-am-isolate", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::port-am-isolates", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_port-isolate-group", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::port-isolate-groups::port-isolate-group", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::port-isolate-groups", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_vlan-mapping-remark", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::l2-attribute::vlan-mapping-remarks::vlan-mapping-remark", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::l2-attribute::vlan-mapping-remarks", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_vlan-mapping", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::l2-attribute::vlan-mappings::vlan-mapping", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::l2-attribute::vlan-mappings", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_vlan-stacking-remark", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::l2-attribute::vlan-stacking-remarks::vlan-stacking-remark", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::l2-attribute::vlan-stacking-remarks", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_vlan-stacking", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::l2-attribute::vlan-stackings::vlan-stacking", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::l2-attribute::vlan-stackings", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_static-arp", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::huawei-arp:static-arps::static-arp", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::huawei-arp:static-arps", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_address", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::state::addresses::address", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::state::addresses", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "interface_address_2", "source_vertex_label": "root::huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "root::huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::address::common-address::addresses::address", "sourceNodeName": "root::huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::address::common-address::addresses", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]