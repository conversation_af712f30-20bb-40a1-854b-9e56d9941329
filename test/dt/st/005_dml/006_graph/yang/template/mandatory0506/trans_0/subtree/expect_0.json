{"ID": 1, "ifm": {"global": {"@statistic-interval": 300, "@ipv4-ignore-primary-sub": false, "@sub-interface-link-trap-enable": true, "ipv4-conflict-enable": {"@preempt-enable": false}, "ipv6-conflict-enable": {"@preempt-enable": false}}, "damp": {"@tx-off": false, "level": {"auto": {"auto": {"@level": "light"}}}}, "interfaces": {"ifm:interface.1": [{"ID": 1, "PID": 1, "name": "interface01", "@clear-ip-df": false, "@link-up-down-trap-enable": true, "@statistic-mode": "interface-based", "@spread-mtu-flag": false, "@vrf-name": "_public_", "@l2-mode-enable": false, "@down-delay-time": 0, "@network-layer-status": "ipv4-ipv6-up", "control-flap": {"@suppress": 2000, "@reuse": 750, "@ceiling": 6000, "@decay-ok": 54, "@decay-ng": 54}, "trap-threshold": {"@input-rising-rate": 100, "@input-resume-rate": 100, "@output-rising-rate": 100, "@output-resume-rate": 100}, "damping": {"ignore-damp": {"ignore-damp": {"@ignore-global-damp": false}}}, "mode-flexe": {"@bandwidth": 2}, "mode-channelize": {"@bandwidth": 2}, "vlanif-attribute": {"@damping-time": 0, "band-width-type": {"band-width-mbps": {"@band-width": 1000}}}, "trunk": {"@min-up-num": 1, "@work-mode": "manual", "@inactive-port-shutdown": false, "@preempt-enable": false, "@preempt-delay-minutes": 0, "@preempt-delay-seconds": 0, "@preempt-delay-milliseconds": 0}, "ethernet": {"main-interface": {"@vlan-swap": "disable", "@qinq-protocol": "0x8100", "@l2-mode": "enable", "l2-attribute": {"@link-type": "hybrid", "@untag-discarding": false}}, "l2-sub-interface": {"local-switch": "disable", "flow-action": {"action-type": "pop-outer"}}}, "bdif-attribute": {"@damping-time": 0, "band-width-type": {"band-width-mbps": {"@band-width": 1000}}}}]}}}