[{"name": "remove-empty-np-3", "alias": "remove-empty-np-3", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32"}, {"name": "name", "type": "string"}, {"name": "choice-1", "type": "choice", "fields": [{"name": "caseA", "type": "case", "fields": [{"name": "type", "type": "uint32"}, {"name": "name", "type": "string"}]}, {"name": "caseB", "type": "case", "fields": [{"name": "class", "type": "uint32"}]}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L0", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32"}, {"name": "choice-2", "type": "choice", "fields": [{"name": "caseA", "type": "case", "fields": [{"name": "type", "type": "uint32"}, {"name": "name", "type": "string"}]}, {"name": "caseB", "type": "case", "fields": [{"name": "class", "type": "uint32"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]