[{"type": "record", "name": "V1", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "uint32", "type": "uint32"}, {"type": "container", "name": "company", "fields": [{"name": "name", "type": "string"}]}], "keys": [{"node": "V1", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "record", "name": "V2", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}], "keys": [{"fields": ["PID", "name"], "node": "V2", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}]