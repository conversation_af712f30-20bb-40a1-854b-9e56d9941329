    └── index-test-when-0 [NP]
        ├── F0 [uint32] nullable: True
        └── list_1 [list]
            ├── PK [uint32] nullable: False
            ├── F0 [uint32] nullable: True
            ├── F1 [uint32] nullable: True
            ├── F2 [uint32] nullable: True
            └── list_2 [list]
                ├── PK [uint32] nullable: False
                └── con1 [NP] [when /index-test-when-0/list_1[PK = 1]/PK = current()/F0]
                    ├── F0 [uint32] nullable: True
                    ├── F1 [uint32] nullable: True
                    ├── F2 [uint32] nullable: True
                    └── F3 [uint32] nullable: True
    └── yang_npa [list]
        ├── labelId [uint32] nullable: False
        ├── nodeId [uint16] nullable: False
        └── propeId [uint32] nullable: False
