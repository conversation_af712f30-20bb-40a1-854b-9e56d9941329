[{"type": "container", "name": "root0", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": "empty0", "type": "empty", "nullable": true}, {"name": "union0", "type": "union", "union_types": ["uint32", "string", "empty"]}, {"name": "dgi-threshold-ratio", "type": "string", "nullable": true, "pattern": ["(unconcern)|(\\d+|0[xX][0-9a-fA-F]+)"]}, {"type": "container", "name": "C1", "fields": [{"name": "empty1", "type": "empty", "nullable": true}, {"name": "union1", "type": "union", "union_types": ["uint64", "string", "empty"]}]}], "keys": [{"name": "PK", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "L0", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "empty0", "type": "empty", "nullable": false}, {"name": "union0", "type": "union", "union_types": ["uint32", "uint64", "int32", "int64", "empty", "string", "boolean"]}], "keys": [{"name": "k0", "fields": [":pid", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]