%table A(a:int4, b:str, c:int4)
%resource rsc1(a:int4, b:str, c:int4 -> d:int4, e:byte128){pending_id(-1, "1111111111111111")}
%table External(a: int4, b: int4) {external, index(0(a))}

rsc1(a , b , c , -, -) :- A(a, b, c).
External(a,b) :- rsc1(a , - , - , b, -).

namespace A1{
    %table A1(a:int4, b:str, c:int4)
    %resource rsc0(a:int4, b:str, c:int4 -> d:int4, e:byte128){pending_id(-1, "1111111111111111")}
    %table External1(a: int4, b: int4) {external, index(0(a))}

    rsc0(a , b , c , -, -) :- A1(a, b, c).
    External1(a,b) :- rsc0(a , - , - , b, -).
}

