%version v1.0.0
%table inpA(a:int4, b:int4, c:int4, d:int4)
%table inpB(a:int4, b:int4, c:int4, d:int4)
%table inpC(a:int4, b:int4, c:int4, d:int4)
%table inpD(a:int4, b:int4, c:int4, d:int4)

%table outA(a:int4, b:int4, c:int4, d:int4){
    tbm,
    index(0(a))
}

%table outB(a:int4, b:int4, c:int4, d:int4){
    tbm,
    index(0(a))
}


%table outC(a:int4, b:int4, c:int4, d:int4){
    tbm,
    index(0(a))
}


%table outD(a:int4, b:int4, c:int4, d:int4){
    tbm,
    index(0(a))
}

%function func(a:int4, b:int4 -> c:int4, d:int4) {
    access_delta(inpC)
}

%function init()
%function uninit()
outA(a, b, c, d) :- inpA(a, b, c, d), inpB(a, b, c, d), func(a, b, c, d).
outB(a, b, c, d) :- inpA(a, b, c, d).

outC(a, b, c, d) :- inpC(a, b, c, d), inpD(a, b, c, d).
outD(a, b, c, d) :- inpC(a, b, c, d).
