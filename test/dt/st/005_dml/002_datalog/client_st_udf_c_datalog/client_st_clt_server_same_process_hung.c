/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2024-09-25
 */

#include <unistd.h>
#include "gm_udf.h"
#include "stdio.h"
#include "assert.h"

#define INSERT 0
#define DELETE 1

int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    return GMERR_OK;
}

int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    return GMERR_OK;
}

// 测试插入
int32_t dtl_tbm_tbl_outB(uint32_t op, void *tuple)
{
    if (op == DELETE) {
        return GMERR_OK;
    }
    sleep(5);
    return GMERR_OK;
}
