/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2023-08-30
 */

#include "gm_udf.h"
#include "stdio.h"

typedef enum OpCode {
    OP_UDF_INSERT = 0u,
    OP_UDF_DELETE,
    OP_UDF_UPDATE,
} OpCodeT;

#pragma pack(1)
typedef struct TupleG {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;

    int32_t a;
    int32_t b;

    uint32_t cLen;
    char *cStr;
    uint32_t dLen;
    char *dStr;
} TupleG;

#pragma pack(0)

// 加载调用 init 初始化
int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    printf("init successfully.\n");
    return GMERR_OK;
}

// 卸载/加载失败调用 uninit 去初始化
int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    printf("uninit successfully.\n");
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_outG(int op, void *tuple)
{
    TupleG *result = (TupleG *)(tuple);
    char *msg = "";
    switch (op) {
        case OP_UDF_INSERT: {
            msg = "insert";
            break;
        }
        case OP_UDF_DELETE: {
            msg = "delete";
            break;
        }
        case OP_UDF_UPDATE: {
            msg = "update";
            break;
        }
        default:
            break;
    }
    printf("tbm callback outG operation : %s, tuple a: %d, b: %d, c: %s, d: %s, dtlReservedCount: %d, upgradeVersion: "
           "%d\n",
        msg, result->a, result->b, result->cStr, result->dStr, result->dtlReservedCount, result->upgradeVersion);
    return GMERR_OK;
}
