/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2023-03-09
 */

#include "gm_udf.h"
#include "stdio.h"
#include "assert.h"
#define INSERT 0
#define DELETE 1
#define UPDATE 2
#define BUF_LEN 1024

static char *g_filename = "./msgNotifyRollBackTest.log";

typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} TupleA;

typedef struct {
    uint32_t op;
    void *oldTup;
    void *newTup;
} Batch;

// 测试混合场景
int32_t dtl_msg_notify_outB(Batch *batches, uint32_t arrLen)
{
    FILE *fp = fopen(g_filename, "a+");
    if (fp == NULL) {
        printf("open %s error\n", g_filename);
        return GMERR_DATA_EXCEPTION;
    }
    printf("batch start.\n");
    char buf[BUF_LEN] = {0};
    const int32_t errorVal = 21;
    for (uint32_t i = 0; i < arrLen; ++i) {
        uint32_t op = batches[i].op;
        TupleA *oldTup = (TupleA *)(batches[i].oldTup);
        TupleA *newTup = (TupleA *)(batches[i].newTup);
        if (op == INSERT) {
            printf("insert a: %d, b: %d, c:%d, count: %d\n", newTup->a, newTup->b, newTup->c, newTup->dtlReservedCount);
        } else if (op == DELETE) {
            printf("delete a: %d, b: %d, c:%d, count: %d\n", oldTup->a, oldTup->b, oldTup->c, oldTup->dtlReservedCount);
        } else if (op == UPDATE) {
            printf("update a: change %d->%d, b: change %d->%d, c: change: %d->%d, count: %d\n", oldTup->a, newTup->a,
                oldTup->b, newTup->b, oldTup->c, newTup->c, newTup->dtlReservedCount);
        }

        // 构造更新失败
        if (newTup != NULL && newTup->b == errorVal && newTup->c == errorVal && newTup->dtlReservedCount > 0) {
            printf("write data error!, idx: %d\n", i);
            return GMERR_DATA_EXCEPTION;
        }
        if (oldTup != NULL) {
            (void)sprintf_s(
                buf, BUF_LEN, "%s %d %d %d %d\n", buf, oldTup->a, oldTup->b, oldTup->c, oldTup->dtlReservedCount);
        }
        if (newTup != NULL) {
            (void)sprintf_s(
                buf, BUF_LEN, "%s %d %d %d %d\n", buf, newTup->a, newTup->b, newTup->c, newTup->dtlReservedCount);
        }
    }
    (void)fprintf(fp, "%s", buf);
    printf("batch end.\n");
    (void)fclose(fp);
    return GMERR_OK;
}
