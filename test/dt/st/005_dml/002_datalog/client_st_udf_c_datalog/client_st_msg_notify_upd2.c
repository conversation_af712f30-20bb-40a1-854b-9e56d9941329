/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: system test
 * Author:
 * Create: 2023-03-09
 */

#include "gm_udf.h"
#include "stdio.h"
#include "assert.h"
#define UPDATE 2
typedef struct {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} TupleA;

typedef struct {
    uint32_t op;
    void *oldTup;
    void *newTup;
} Batch;

// 测试更新
int32_t dtl_msg_notify_outB(Batch *batches, uint32_t arrLen)
{
    printf("batch start.\n");
    for (uint32_t i = 0; i < arrLen; ++i) {
        uint32_t op = batches[i].op;
        TupleA *oldTup = (TupleA *)(batches[i].oldTup);
        TupleA *newTup = (TupleA *)(batches[i].newTup);
        if (op != UPDATE) {
            printf("operation type error!\n");
            return GMERR_DATA_EXCEPTION;
        }
        if (oldTup == NULL || newTup == NULL) {
            printf("data error! NULL value occur\n");
            return GMERR_DATA_EXCEPTION;
        }
        printf("a: change %d->%d, b: change %d->%d, c: change: %d->%d, count: %d, upgradeVersion: %d\n", oldTup->a,
            newTup->a, oldTup->b, newTup->b, oldTup->c, newTup->c, newTup->dtlReservedCount, newTup->upgradeVersion);
    }
    printf("batch end.\n");
    return GMERR_OK;
}
