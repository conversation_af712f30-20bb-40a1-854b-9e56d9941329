// 2、多个输出

%aggregate agg1(a:int4 -> b:int4, c:int4){
    ordered
}

%table table2(a:int4, b:int8, c:int4, d:int4){
    index(1(a,b))
}

%table table3(a:int4, b:int8, min:int4, max:int4){
    index(1(a, b))
}

table3(a, b, min, max) :- table2(a, b, c, -) GROUP-BY(a, b) agg1(c, min, max).
null(0) :- table3(a, b, min, max).



// 3. many_to_many

%aggregate agg2(a:int4, b:str, c:int4 -> max:int4, min:int4){
    ordered,
    many_to_many
}

%table table4(a:int4, b:str, c:int4, d:int4){
    index(1(d))
}

%table table5(d:int4, max:int4, min:int4){
    index(1(d))
}

table5(d, max, min) :- table4(a, b, c, d) GROUP-BY(d) agg2(a, b, c, max, min).
null(0) :- table5(d, max, min).


// 4. many_to_one + access_delta + access_current

%aggregate agg3(a:int4, b:int4 -> sum:int4){
    ordered, 
    many_to_one,
    access_current(table4),
    access_delta(table5)
}

%table table6(a:int4, b:int4, c:int4)
%table table7(a:int4, sum:int4)
table7(a, sum) :- table6(a, b, c) GROUP-BY(a) agg3(b, c, sum).
null(0) :- table7(a, sum).
