{"type": "record", "name": "ip4forward00000", "schema_version": 8, "fields": [{"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F20", "type": "fixed", "nullable": true, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "F21", "type": "boolean", "nullable": true}, {"name": "F22", "type": "float", "nullable": true}, {"name": "F23", "type": "double", "nullable": true}, {"name": "F24", "type": "bitmap", "nullable": true, "size": 128}, {"name": "F25", "type": "uint8: 8", "nullable": true}, {"name": "F3", "type": "int32", "nullable": true}, {"name": "F4", "type": "int32", "nullable": true}, {"name": "F5", "type": "int32", "nullable": true}, {"name": "F6", "type": "int32", "nullable": true}, {"name": "F7", "type": "int32", "nullable": true}, {"name": "F8", "type": "int32", "nullable": true}, {"name": "F9", "type": "int32", "nullable": true}, {"name": "F10", "type": "int32", "nullable": true}], "keys": [{"name": "ip4_key", "index": {"type": "primary"}, "node": "ip4forward00000", "fields": ["F0"], "constraints": {"unique": true}}, {"name": "ip4_hashcluster", "index": {"type": "hashcluster"}, "node": "ip4forward00000", "fields": ["F1"]}, {"name": "ip4_key_local", "index": {"type": "local"}, "node": "ip4forward00000", "fields": ["F2"]}]}