CREATE TABLE t_trf_org(log_id INTEGER PRIMARY KEY,start_time INTEGER,end_time INTEGER,vsys_id INTEGER,src_ip INTEGER,dst_ip INTEGER,nat_src_ip INTEGER,nat_dst_ip INTEGER,src_port INTEGER,dst_port INTEGER,nat_src_port INTEGER,nat_dst_port INTEGER,protocol_type INTEGER,user_id INTEGER,user_group_id INTEGER,app_category_id INTEGER,sub_app_id INTEGER,app_id INTEGER,src_zone_id INTEGER,dst_zone_id INTEGER,sec_policy_id INTEGER,qos_rule_id INTEGER,in_intf_id INTEGER,out_intf_id INTEGER,send_byte INTEGER,receive_byte INTEGER,send_pkt INTEGER,receive_pkt INTEGER,src_loc_id INTEGER,dst_loc_id INTEGER,src_mask_id INTEGER,dst_mask_id INTEGER,url_sub_category_id INTEGER,close_reason INTEGER,url_subcategory_type INTEGER);
