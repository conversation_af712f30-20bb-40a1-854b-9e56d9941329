/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "grd_clouddb_impl.h"

#include "gtest/gtest.h"

#include <string>
#include "db_test_tool.h"

using namespace std;

static const char *g_cursorKey = "cursor";
static const char *g_equioIdKey = "equipId";
const char *g_collectionName = "SyncLog";
static const uint8_t NUMBER_TO_BE_IGNORED = 5;
GRD_DB *GrdCloudDb::db_ = {0};
ExitStatusT GrdCloudDb::exitStatus_;
uint32_t GrdCloudDb::lockTimeMs_ = 6000;
bool GrdCloudDb::lockStatus_ = false;
int GrdCloudDb::cursor_ = 0;
int GrdCloudDb::maxDownloadSize_ = 10;
static uint8_t g_numberOfLogToBeIgnored[NUMBER_TO_BE_IGNORED] = {0};
static bool g_isErrorInsertMode = false;
static uint8_t g_errorBatch = 0;
static uint8_t g_countBatch = 0;

std::vector<std::string> SYNC_LOG_KEYS = {"equipId", "timestamp", "syncLogEvent"};

void GrdCloudDb::SetUpTestCase()
{
    EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB), 0);
    EXPECT_EQ(DbTestTool::MakeDir(TEST_DB), 0);
    cursor_ = 0;
    ASSERT_EQ(GRD_DBOpen(TEST_DB_FILE, CONFIG_STR, GRD_DB_OPEN_CREATE, &db_), GRD_OK);
    ASSERT_EQ(GRD_CreateCollection(db_, g_collectionName, OPTION_STR, 0), GRD_OK);
}

void GrdCloudDb::TearDownTestCase()
{
    ASSERT_EQ(GRD_DropCollection(db_, g_collectionName, 0), GRD_OK);
    ASSERT_EQ(GRD_DBClose(db_, GRD_DB_CLOSE), GRD_OK);
    EXPECT_EQ(DbTestTool::RemoveDir(TEST_DB), 0);
    db_ = nullptr;
    cursor_ = 0;
}

void SerializeCloudRecord(GRD_CloudRecordT *record, void **buf, uint32_t *bufSize)
{
    size_t buffSize1 = sizeof(uint8_t);
    *bufSize += buffSize1;                   // equipId key length
    *bufSize += record->fields[0].valueLen;  // equipId value length
    *bufSize += record->fields[1].valueLen;  // timestamp value length
    size_t buffSize2 = sizeof(uint32_t);
    *bufSize += buffSize2;                   // log event key length
    *bufSize += record->fields[2].valueLen;  // log event value length

    *buf = malloc(*bufSize);
    if (*buf == nullptr) {
        return;
    }
    errno_t ret = memset_s(*buf, *bufSize, 0, *bufSize);
    if (ret != EOK) {
        return;
    }
    uint8_t *bufTmp = (uint8_t *)*buf;
    uint32_t bufSizeTmp = *bufSize;

    // equipId key
    ret = memcpy_s(bufTmp, bufSizeTmp, &record->fields[0].valueLen, sizeof(uint8_t));
    if (ret != EOK) {
        return;
    }
    bufTmp += sizeof(uint8_t);
    bufSizeTmp -= (uint32_t)sizeof(uint8_t);
    // equipId value
    ret = memcpy_s(bufTmp, bufSizeTmp, record->fields[0].value, record->fields[0].valueLen);
    if (ret != EOK) {
        return;
    }
    bufTmp += record->fields[0].valueLen;
    bufSizeTmp -= record->fields[0].valueLen;
    // timestamp value
    ret = memcpy_s(bufTmp, bufSizeTmp, record->fields[1].value, record->fields[1].valueLen);
    if (ret != EOK) {
        return;
    }
    bufTmp += record->fields[1].valueLen;
    bufSizeTmp -= record->fields[1].valueLen;
    // log event key
    ret = memcpy_s(bufTmp, bufSizeTmp, &record->fields[2].valueLen, sizeof(uint32_t));
    if (ret != EOK) {
        return;
    }
    bufTmp += sizeof(uint32_t);
    bufSizeTmp -= (uint32_t)sizeof(uint32_t);
    // log event value
    ret = memcpy_s(bufTmp, bufSizeTmp, record->fields[2].value, record->fields[2].valueLen);
    if (ret != EOK) {
        return;
    }
    bufTmp += record->fields[2].valueLen;
    bufSizeTmp -= record->fields[2].valueLen;
    EXPECT_EQ(bufSizeTmp, 0);
}

static bool CheckIfIgnored(uint8_t index)
{
    if (g_countBatch + 1 == g_errorBatch) {
        for (uint8_t i = 0; i < NUMBER_TO_BE_IGNORED; ++i) {
            if (index + 1 == g_numberOfLogToBeIgnored[i]) {
                return true;
            }
        }
    }
    return false;
}

int GrdCloudDb::BatchInsert(const char *tableName, GRD_CloudRecordT *records, uint32_t recordSize,
    GRD_CloudRecordT **extends, uint32_t *extendSize)
{
    if (strcmp(tableName, g_collectionName) != 0) {
        return NOK;
    }

    for (int i = 0; i < (int)recordSize; i++) {
        if (g_isErrorInsertMode && CheckIfIgnored(i)) {
            continue;
        }
        int curCursor = cursor_++;
        GRD_KVItemT key = {&curCursor, sizeof(int)};
        GRD_KVItemT value = {0, 0};
        SerializeCloudRecord(&records[i], &value.data, &value.dataLen);
        if (value.data == nullptr) {
            return NOK;
        }
        int32_t ret = GRD_KVPut(db_, g_collectionName, &key, &value);
        if (ret != GRD_OK) {
            return ret;
        }
        if (value.data != nullptr) {
            free(value.data);
            value.data = nullptr;
        }
    }
    *extendSize = recordSize;
    if (g_isErrorInsertMode) {
        g_countBatch++;
        if (g_countBatch == g_errorBatch) {
            return NOK;
        }
    }
    return OK;
}

void VirtualCloudDBGetEquipId(uint8_t *buf, uint32_t bufSize, char **equipId)
{
    uint8_t equipIdLen = *buf;
    buf += sizeof(uint8_t);
    *equipId = static_cast<char *>(malloc(equipIdLen));
    if (*equipId == nullptr) {
        return;
    }
    errno_t ret = memset_s(*equipId, equipIdLen, 0, equipIdLen);
    if (ret != EOK) {
        return;
    }
    ret = memcpy_s(*equipId, equipIdLen, buf, equipIdLen);
    if (ret != EOK) {
        return;
    }
}

void DeSerializeCloudRecord(const std::string &cursor, uint8_t *buf, GRD_CloudRecordT *record)
{
    record->fieldSize = 4;
    record->fields = static_cast<GRD_CloudFieldT *>(malloc(sizeof(GRD_CloudFieldT) * 4));
    if (record->fields == nullptr) {
        return;
    }
    errno_t ret = memset_s(record->fields, sizeof(GRD_CloudFieldT) * 4, 0, sizeof(GRD_CloudFieldT) * 4);
    if (ret != EOK) {
        return;
    }
    // equip id
    record->fields[0].type = GRD_CLOUD_FIELD_TYPE_STRING;
    const char *fieldName = SYNC_LOG_KEYS[0].c_str();
    record->fields[0].key = static_cast<char *>(malloc(DM_STR_LEN(fieldName)));
    if (record->fields[0].key == nullptr) {
        return;
    }
    ret = memset_s(record->fields[0].key, DM_STR_LEN(fieldName), 0, DM_STR_LEN(fieldName));
    if (ret != EOK) {
        return;
    }
    ret = memcpy_s(record->fields[0].key, DM_STR_LEN(fieldName), fieldName, DM_STR_LEN(fieldName));
    if (ret != EOK) {
        return;
    }
    record->fields[0].valueLen = *buf;
    buf += sizeof(uint8_t);

    uint32_t valueLen = record->fields[0].valueLen;
    record->fields[0].value = static_cast<char *>(malloc(valueLen));
    if (record->fields[0].value == nullptr) {
        return;
    }
    ret = memcpy_s(record->fields[0].value, valueLen, buf, valueLen);
    if (ret != EOK) {
        return;
    }
    buf += valueLen;

    // timestamp
    record->fields[1].type = GRD_CLOUD_FIELD_TYPE_STRING;
    const char *fieldName1 = SYNC_LOG_KEYS[1].c_str();
    record->fields[1].key = static_cast<char *>(malloc(DM_STR_LEN(fieldName1)));
    if (record->fields[1].key == nullptr) {
        return;
    }
    ret = memcpy_s(record->fields[1].key, DM_STR_LEN(fieldName1), fieldName1, DM_STR_LEN(fieldName1));
    if (ret != EOK) {
        return;
    }
    size_t tmpLen = sizeof(uint64_t);
    record->fields[1].valueLen = tmpLen;

    valueLen = record->fields[1].valueLen;
    record->fields[1].value = static_cast<char *>(malloc(valueLen));
    if (record->fields[1].value == nullptr) {
        return;
    }
    ret = memcpy_s(record->fields[1].value, valueLen, buf, valueLen);
    if (ret != EOK) {
        return;
    }
    buf += valueLen;

    // sync log event
    record->fields[2].type = GRD_CLOUD_FIELD_TYPE_BYTES;
    const char *fieldName2 = SYNC_LOG_KEYS[2].c_str();
    record->fields[2].key = static_cast<char *>(malloc(DM_STR_LEN(fieldName2)));
    if (record->fields[2].key == nullptr) {
        return;
    }
    ret = memcpy_s(record->fields[2].key, DM_STR_LEN(fieldName2), fieldName2, DM_STR_LEN(fieldName2));
    if (ret != EOK) {
        return;
    }
    uint32_t *tmpBuf = reinterpret_cast<uint32_t *>(buf);
    record->fields[2].valueLen = *tmpBuf;
    buf += sizeof(uint32_t);

    valueLen = record->fields[2].valueLen;
    record->fields[2].value = static_cast<char *>(malloc(valueLen));
    if (record->fields[2].value == nullptr) {
        return;
    }
    ret = memcpy_s(record->fields[2].value, valueLen, buf, valueLen);
    if (ret != EOK) {
        return;
    }

    // cursor
    record->fields[3].type = GRD_CLOUD_FIELD_TYPE_STRING;
    record->fields[3].key = static_cast<char *>(malloc(DM_STR_LEN(g_cursorKey)));
    if (record->fields[3].key == nullptr) {
        return;
    }
    ret = memcpy_s(record->fields[3].key, DM_STR_LEN(g_cursorKey), g_cursorKey, DM_STR_LEN(g_cursorKey));
    if (ret != EOK) {
        return;
    }
    record->fields[3].valueLen = DM_STR_LEN(cursor.c_str());
    record->fields[3].value = static_cast<char *>(malloc(DM_STR_LEN(cursor.c_str())));
    if (record->fields[3].value == nullptr) {
        return;
    }
    ret = memcpy_s(record->fields[3].value, DM_STR_LEN(cursor.c_str()), cursor.c_str(), DM_STR_LEN(cursor.c_str()));
    if (ret != EOK) {
        return;
    }
}

int GrdCloudDb::Query(const char *tableName, GRD_CloudRecordT *extends, uint32_t extendSize, GRD_CloudRecordT **records,
    uint32_t *recordSize)
{
    if (strcmp(tableName, g_collectionName) != 0) {
        return NOK;
    }
    char *equipId = nullptr;
    int cursor = 0;
    int dataSize = cursor_;
    for (int i = 0; i < (int)extendSize; i++) {
        for (int j = 0; j < extends[i].fieldSize; j++) {
            if (extends[i].fields[j].condition == GRD_QUERY_CONDITION_TYPE_NOT_USE) {
                continue;
            }
            if (strcmp(extends[i].fields[j].key, g_equioIdKey) == 0) {
                equipId = static_cast<char *>(extends[i].fields[j].value);
            }
            if (strcmp(extends[i].fields[j].key, g_cursorKey) == 0) {
                char *tmp = static_cast<char *>(extends[i].fields[j].value);
                if (strlen(tmp) == 0) {
                    cursor = 0;
                } else {
                    cursor = std::stoi(tmp) + 1;
                    dataSize = cursor_ - cursor;
                }
            }
        }
    }
    int i = cursor;
    dataSize = dataSize > maxDownloadSize_ ? maxDownloadSize_ : dataSize;
    if (dataSize == 0) {
        return QR_END;
    }
    int recordCount = 0;
    size_t allocSize = sizeof(GRD_CloudRecordT) * dataSize;
    *records = static_cast<GRD_CloudRecordT *>(malloc(allocSize));
    if (*records == nullptr) {
        return NOK;
    }
    errno_t err = memset_s(*records, allocSize, 0, allocSize);
    if (err != EOK) {
        return NOK;
    }
    for (; i < cursor_; i++) {
        GRD_KVItemT valueItem = {nullptr, 0};
        GRD_KVItemT key = {&i, sizeof(int)};
        int32_t ret = GRD_KVGet(db_, g_collectionName, &key, &valueItem);
        if (ret != GRD_OK) {
            return NOK;
        }
        char *curEquipId = nullptr;
        if (valueItem.data == nullptr) {
            return NOK;
        }
        VirtualCloudDBGetEquipId(static_cast<uint8_t *>(valueItem.data), valueItem.dataLen, &curEquipId);
        if (curEquipId == nullptr) {
            return NOK;
        }

        // should not equal to current equip id
        if (strcmp(curEquipId, equipId) != 0) {
            // another equipId
            DeSerializeCloudRecord(to_string(i), static_cast<uint8_t *>(valueItem.data), &(*records)[recordCount++]);
        }
        GRD_KVFreeItem(&valueItem);
        free(curEquipId);
        curEquipId = nullptr;
        if (recordCount >= maxDownloadSize_) {
            break;
        }
    }
    *recordSize = (uint32_t)recordCount;
    if (i == cursor_) {
        return QR_END;
    }
    return OK;
}

int GrdCloudDb::Lock(uint32_t *lockTimeMs)
{
    if (exitStatus_.syncMethod == SyncMethod::LOCK) {
        *lockTimeMs = lockTimeMs_;
        return exitStatus_.errorCode;
    }
    lockStatus_ = true;
    *lockTimeMs = lockTimeMs_;
    return OK;
}

int GrdCloudDb::UnLock()
{
    if (exitStatus_.syncMethod == SyncMethod::UNLOCK) {
        return exitStatus_.errorCode;
    }
    lockStatus_ = false;
    return OK;
}

int GrdCloudDb::HeartBeat()
{
    return OK;
}
int GrdCloudDb::Close()
{
    return OK;
}

void GrdCloudDb::Init()
{
    lockTimeMs_ = 6000;
    lockStatus_ = false;
    db_ = nullptr;
    cursor_ = 0;
}

void GrdCloudDb::SetMaxDownloadSize(int size)
{
    maxDownloadSize_ = size;
}

void SetNumberToBeIgnored(const uint8_t ignoredNumber[], uint8_t arrayLength)
{
    for (uint8_t i = 0; i < arrayLength && i < NUMBER_TO_BE_IGNORED; ++i) {
        g_numberOfLogToBeIgnored[i] = ignoredNumber[i];
    }
}

void InitAndClearKeyToBeIgnored(void)
{
    for (uint8_t i = 0; i < NUMBER_TO_BE_IGNORED; ++i) {
        g_numberOfLogToBeIgnored[i] = 0u;
    }
}

void ErrorInsertModeOpen(uint8_t errorBatch)
{
    g_isErrorInsertMode = true;
    g_countBatch = 0;
    g_errorBatch = errorBatch;
}

void ErrorInsertModeClose(void)
{
    g_isErrorInsertMode = false;
    g_countBatch = 0;
    g_errorBatch = 0;
}
