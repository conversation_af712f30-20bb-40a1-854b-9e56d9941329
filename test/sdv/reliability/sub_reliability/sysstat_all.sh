#!/bin/bash
# ----------------------------------------------------------------------------
# Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
# Description: 监控系统内存及DB内存状态及运行客户端内存状态
# ----------------------------------------------------------------------------

# 常量
CUR_DIR=$(pwd)
LOG_PATH=${CUR_DIR}/sys_stat_$(date +%Y%m%d)_$(date +%H%M%S).csv
GMSYSVIEW="gmsysview -q"

# 全局变量
g_process_name=$1

function cur_time() {
    date "+%Y-%m-%d %H:%M:%S"
    return 0
}

# 格式化输出日志到屏幕和文件,屏蔽输出到屏幕，直接输出到文件
function format_log() {
#    printf "$@"
    printf "$@" >> ${LOG_PATH}
    return 0
}

function getMem() {
    process_name=$1
    memory_type=$2
    shmem="$(cat /proc/$(pidof ${process_name})/status 2>/dev/null | grep ${memory_type} | awk '{printf "%.4f", $2/1024}')"
    if [ "x${shmem}" != "x" ];then
        echo ${shmem}
	else
	    echo null
    fi
}

format_log "%-20s,%-20s,%-20s,%-20s,%-20s,%-20s,%-20s," "date_time" "sys_used_mem(MB)" "sys_swap_mem(MB)" "gmserver_shm(MB)" "gmserver_dym(MB)" "gmserver_sub_dym(MB)" "gmserver_sub_shm(MB)"

process=(gmserver sub)
# 进程内存三种类型：VmRss、RssAnon和RssShmem
# 目前数组只包含VmRSS，后续如果需要，可以添加
typelist=(VmRSS)
for p in ${process[@]}
do
	for type in ${typelist[@]}
	do
		format_log "%-20s," "$p"_"$type(MB)"
	done
done
format_log "\n"

while true
do
    sys_used_mem="$(free -m | grep Mem | awk '{print $3}')"
    sys_swap_mem="$(free -m | grep Swap | awk '{print $3}')"
    gmserver_shm="$(${GMSYSVIEW} 'V$COM_SHMEM_CTX' -l 1 | grep GLOBAL_PHY_SIZE | awk -F [][] '{print $2}')"
    if [[ -z $gmserver_shm ]]; then
        gmserver_shm=null
    fi
    gmserver_dym="$(${GMSYSVIEW} 'V$COM_DYN_CTX'  -l 1 | grep GLOBAL_PHY_SIZE | awk -F [][] '{print $2}')"
    if [[ -z $gmserver_dym ]]; then
        gmserver_dym=null
    fi
    gmserver_sub_dym="$(${GMSYSVIEW} 'V$COM_DYN_CTX' -f PARENT_CTX_NAME='RtMsgTopDynmemCtx' | grep TOTAL_ALLOC_SIZE: | awk -F [][] '{print $2}')"
    if [[ -z $gmserver_sub_dym ]]; then
        gmserver_sub_dym=null
    fi
    gmserver_sub_shm="$(${GMSYSVIEW} 'V$COM_SHMEM_CTX' -f CTX_NAME='RtMsgTopShareMemCtx' | grep TOTAL_ALLOC_SIZE: | awk -F [][] '{print $2}')"
    if [[ -z $gmserver_sub_shm ]]; then
        gmserver_sub_shm=null
    fi
	format_log "%-20s,%-20s,%-20s,%-20s,%-20s,%-20s,%-20s," "$(cur_time)" $sys_used_mem $sys_swap_mem $gmserver_shm $gmserver_dym $gmserver_sub_dym $gmserver_sub_shm
	
	#获取进程内存
	for p in ${process[@]}
	do
		for type in ${typelist[@]}
		do
			format_log "%-20s," $(getMem $p ${type})
		done
	done
	format_log "\n"

    sleep 1
done

