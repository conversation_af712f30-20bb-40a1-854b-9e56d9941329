# -*- coding: utf-8 -*-
import os


class PathUtil:
    _path_cache = {}

    @staticmethod
    def get_local_cfe_home_path():
        path = os.path.join(PathUtil.get_project_path(), "tools/cfe")
        return path

    @staticmethod
    def get_dst_cfe_home_path(user):
        if user == "root":
            return f"/root/CFE_Tool"
        return f"/root/CFE_Tool"

    @staticmethod
    def get_dst_cfe_bin_path(user):
        path = f"{PathUtil.get_dst_cfe_home_path(user)}/cfe/cfe"
        return path

    @staticmethod
    def get_project_path():
        prj_path = os.path.dirname(os.path.dirname(__file__))
        return prj_path

    @staticmethod
    def get_temp_path():
        tmp_path = os.path.join(PathUtil.get_project_path(), "temp")
        if not os.path.exists(tmp_path):
            try:
                os.mkdir(tmp_path)
            except IOError as err:
                raise err
        return tmp_path

    @staticmethod
    def get_cfe_tar_dir():
        return os.path.join(PathUtil.get_project_path(), "tools")
