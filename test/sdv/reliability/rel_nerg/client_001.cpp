
/*****************************************************************************
 Description  : 按需持久化可靠性测试
 Notes        : 
 History      :
 Author       : 潘鹏 pwx860460
 Modification :
 Date         : 
*****************************************************************************/

#include "incre_pst_common.h"


GmcConnT *g_conn1 = NULL;
GmcStmtT *g_stmt1 = NULL;
// 客户端端异常重启
int main()
{
    int ret = testGmcConnect(&g_conn1, &g_stmt1);
    RETURN_IFERR(ret);
    
     for (int i = 0; i <= 30; i++) {
        AW_FUN_Log(LOG_STEP, "\n>>> GmcFlushData %d \n", i);
    // 落盘
        ret = GmcFlushData(g_stmt1, NULL, false);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "GmcFlushData failed, ret = %d.\n", ret);
        }
        usleep(100);
    }
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    RETURN_IFERR(ret);
    AW_FUN_Log(LOG_STEP, "client_001 test end.");
}


