/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include <stdio.h>
#include <string.h>
#include "gm_udf.h"
#include "assert.h"
#pragma pack(1)
typedef struct {
    int32_t count;
    int32_t a;
    int32_t b;
} DEL;
#pragma pack(0)

typedef struct Para {
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
} Para;
#pragma pack(0)
int32_t dtl_ext_func_ns2_funcA0989(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    return GMERR_OK;
}

#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int8_t a;
    int16_t b;
    int32_t c;
    int64_t d;
    int8_t e[1];
    int8_t f[1];
    int8_t g[1];
    int8_t a1;
    int16_t b1;
    int32_t c1;
    int64_t d1;
    int8_t e1[1];
    int8_t f1[1];
    int8_t g1[1];
    int8_t a2;
    int16_t b2;
    int32_t c2;
    int64_t d2;
    int8_t e2[1];
    int8_t f2[1];
    int8_t g2[1];
    int8_t a3;
    int16_t b3;
    int32_t c3;
    int64_t d3;
    int8_t e3[1];
    int8_t f3[1];
    int8_t g3[1];
    int8_t a4;
    int16_t b4;
    int32_t c4;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[128];
    int8_t g4[256];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[128];
    int8_t g5[256];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[128];
    int8_t g6[256];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[128];
    int8_t g7[256];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[128];
    int32_t a9Len;
    char *a9;
} TupleAgg;
#pragma pack(0)

#pragma pack(1)
typedef struct FuncAggIn {
    int32_t count;
    int64_t d4;
    int8_t e4[1];
    int8_t f4[128];
    int8_t g4[256];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[128];
    int8_t g5[256];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[128];
    int8_t g6[256];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[128];
    int8_t g7[256];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[128];
    int32_t a9Len;
    char *a9;
} FuncAggIn;

typedef struct FuncAggOut {
    int64_t d4;
    int8_t e4[1];
    int8_t f4[128];
    int8_t g4[256];
    int8_t a5;
    int16_t b5;
    int32_t c5;
    int64_t d5;
    int8_t e5[1];
    int8_t f5[128];
    int8_t g5[256];
    int8_t a6;
    int16_t b6;
    int32_t c6;
    int64_t d6;
    int8_t e6[1];
    int8_t f6[128];
    int8_t g6[256];
    int8_t a7;
    int16_t b7;
    int32_t c7;
    int64_t d7;
    int8_t e7[1];
    int8_t f7[128];
    int8_t g7[256];
    int8_t a8;
    int16_t b8;
    int32_t c8;
    int64_t d8;
    int8_t e8[1];
    int8_t f8[128];
    int32_t a9Len;
    char *a9;
} FuncAggOut;
#pragma pack(0)


int32_t dtl_agg_func_ns2_aggA0988(GmUdfReaderT *input, size_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    int32_t ret = 0;
    int32_t sum = 0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        outStruct->d4 = inpStruct->d4;
        memcpy(outStruct->e4, inpStruct->e4, sizeof(inpStruct->e4));
        memcpy(outStruct->f4, inpStruct->f4, sizeof(inpStruct->f4));
        memcpy(outStruct->g4, inpStruct->g4, sizeof(inpStruct->g4));
        outStruct->a5 = inpStruct->a5;
        outStruct->b5 = inpStruct->b5;
        outStruct->c5 = inpStruct->c5;
        outStruct->d5 = inpStruct->d5;
        memcpy(outStruct->e5, inpStruct->e5, sizeof(inpStruct->e5));
        memcpy(outStruct->f5, inpStruct->f5, sizeof(inpStruct->f5));
        memcpy(outStruct->g5, inpStruct->g5, sizeof(inpStruct->g5));
        outStruct->a6 = inpStruct->a6;
        outStruct->b6 = inpStruct->b6;
        outStruct->c6 = inpStruct->c6;
        outStruct->d6 = inpStruct->d6;
        memcpy(outStruct->e6, inpStruct->e6, sizeof(inpStruct->e6));
        memcpy(outStruct->f6, inpStruct->f6, sizeof(inpStruct->f6));
        memcpy(outStruct->g6, inpStruct->g6, sizeof(inpStruct->g6));
        outStruct->a7 = inpStruct->a7;
        outStruct->b7 = inpStruct->b7;
        outStruct->c7 = inpStruct->c7;
        outStruct->d7 = inpStruct->d7;
        memcpy(outStruct->e7, inpStruct->e7, sizeof(inpStruct->e7));
        memcpy(outStruct->f7, inpStruct->f7, sizeof(inpStruct->f7));
        memcpy(outStruct->g7, inpStruct->g7, sizeof(inpStruct->g7));
        outStruct->a8 = inpStruct->a8;
        outStruct->b8 = inpStruct->b8;
        outStruct->c8 = inpStruct->c8;
        outStruct->d8 = inpStruct->d8;
        memcpy(outStruct->e8, inpStruct->e8, sizeof(inpStruct->e8));
        memcpy(outStruct->f8, inpStruct->f8, sizeof(inpStruct->f8));
        outStruct->a9Len = inpStruct->a9Len;
        outStruct->a9 = inpStruct->a9;
        sum += inpStruct->e4[0];
    }
    outStruct->e4[0] = sum;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
