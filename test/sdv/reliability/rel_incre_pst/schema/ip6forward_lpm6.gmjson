[{"comment": "前缀表，对应7#表", "version": "2.0", "type": "record", "name": "ip6forward", "fields": [{"name": "id", "type": "uint32", "comment": "pk"}, {"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "dest_ip_addr", "type": "fixed", "size": 16, "comment": "目的地址", "nullable": false}, {"name": "mask_len", "type": "uint8", "comment": "掩码长度"}, {"name": "nhp_group_flag", "type": "uint8", "comment": "标识Nhp或NhpG"}, {"name": "qos_profile_id", "type": "uint16", "comment": "QosID"}, {"name": "primary_label", "type": "uint32", "comment": "标签"}, {"name": "attribute_id", "type": "uint32", "comment": "属性ID"}, {"name": "nhp_group_id", "type": "uint32", "comment": "下一跳索引还是下一跳组索引，根据nhp_group_flag决定"}, {"name": "path_flags", "type": "uint32", "comment": "path标记"}, {"name": "test_lpm6", "type": "fixed", "size": 16}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "ip6forward", "fields": ["id"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip6forward", "fields": ["qos_profile_id", "nhp_group_id"], "constraints": {"unique": false}, "comment": "根据nhp_group_id + vrid索引"}, {"name": "localhash_key_2", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "ip6forward", "fields": ["primary_label", "attribute_id"], "constraints": {"unique": true}}, {"name": "hashcluster_key", "index": {"type": "hashcluster"}, "node": "ip6forward", "fields": ["qos_profile_id", "nhp_group_id"], "constraints": {"unique": false}}, {"name": "hashcluster_update", "index": {"type": "hashcluster"}, "node": "ip6forward", "fields": ["qos_profile_id", "attribute_id"], "constraints": {"unique": false}}, {"name": "lpm6_test", "index": {"type": "lpm6_tree_bitmap"}, "node": "ip6forward", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "constraints": {"unique": true}}]}]