#! /bin/bash
# ----------------------------------------------------------------------------
# Description:  008 注入一个cpu使用率过载至80%故障(ares）
# Author: 
# Create: 2023-1-3
# History: 
# ----------------------------------------------------------------------------
# cfe工具路径应根据实际路径使用
CFE_DIR="/root/CFE_Tool"
CFE_BIN_DIR="$CFE_DIR/cfe"

test_result="./test_result.txt"
total_result="./001_result.txt"

#修改工具的权限
# if [ -d $CFE_DIR ]; then
# 	chmod -R 777 $CFE_DIR
# else
# 	echo "$CFE_DIR not exist"
#     exit 1
# fi
function clear_fault()
{
    $CFE_BIN_DIR/cfe "clean rCPU_Overloadl"
}
function inject_fault()
{
    #注入故障
    $CFE_BIN_DIR/cfe "inject rCPU_Overloadl (cpuid, usage) values(1, 80)" 2>&1 | tee -a ${total_result}
}
#停止服务
stop.sh -f
modifyCfg.sh workerHungThreshold=6,200,300
#启动服务
start.sh
./yang_rel_scene --gtest_filter=*001
inject_fault
sh RecoverFault.sh &
sleep 3
# 异步写,全量subtree查询
./yang_rel_scene --gtest_filter=*002
sleep 1
# 异步更新,全量subtree查询
./yang_rel_scene --gtest_filter=*003
sleep 1
./yang_rel_scene --gtest_filter=*004
# 建链(1024个)测试
sleep 1
./yang_rel_scene --gtest_filter=*005

modifyCfg.sh recover

echo "[       OK ] YangDB_reliability_scene_008.sh"
echo "[  PASSED  ] 1 test."
