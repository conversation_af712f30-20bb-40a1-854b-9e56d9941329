/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: sqlite tables headfile
 * Create: 2024-11-05
 */

#ifndef SQLITE_TABLES_H
#define SQLITE_TABLES_H

#include <stdint.h>
#include <unistd.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <sqlite3.h>
#include <arpa/inet.h>

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_ARG_NUM 5
#define MAX_FILE_PATH_LEN 256
#define MAX_IP_LEN 256
#define IPV4_LIMIT 256
#define IPV4_HIGH_LOW 254

// 生成随机字符串
inline void GenerateRandomString(char *str, size_t size)
{
    // 定义用于生成随机字符串的字符集
    char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    for (size_t n = 0; n < size - 1; n++) {
        int key = rand() % (int)(sizeof(charset) - 1);
        str[n] = charset[key];
    }
    str[size - 1] = '\0';
}

// 生成随机的 IP 地址（IPv4 或 IPv6）
inline void GenerateRandomIp(char *ipstr, int isipv6)
{
    if (isipv6) {
        sprintf(ipstr, "%x%x%x%x%x%x%x%x", rand() % 0xffff, rand() % 0xffff, rand() % 0xffff, rand() % 0xffff,
            rand() % 0xffff, rand() % 0xffff, rand() % 0xffff, rand() % 0xffff);
    } else {
        sprintf(ipstr, "%d%d%d%d", rand() % IPV4_HIGH_LOW + 1, rand() % IPV4_LIMIT, rand() % IPV4_LIMIT,
            rand() % IPV4_HIGH_LOW + 1);
    }
}

// 将 IPv4 地址字符串转换为整数
inline unsigned int IpToInt(const char *ipstr)
{
    struct in_addr ipaddr;
    inet_aton(ipstr, &ipaddr);
    return ntohl(ipaddr.s_addr);
}

#ifdef __cplusplus
}
#endif

#endif
