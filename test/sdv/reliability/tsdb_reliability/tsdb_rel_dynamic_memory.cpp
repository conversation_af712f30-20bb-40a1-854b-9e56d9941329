
/*****************************************************************************
 Description  : d动态内存不足可靠性测试
 History      :
 Author       : qwx 620469
 Modification :
 Date         : 2025-4-21
*****************************************************************************/

#include "tsdb_incre_common.h"
#if defined ENV_RTOSV2X
#define MEGABYTE (10 * 1024)
#else
#define MEGABYTE (1024 * 1024 * 1024)
#endif

int ret = 0;

class tsdb_rel_dynamic_memory : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

void tsdb_rel_dynamic_memory::SetUp()
{

    printf("[INFO] Persistence cfg test Start.\n");
    int ret = ChangeTsGmserverCfg((char *)"recover", NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    system("rm /data/gmdb -rf");
    system("rm ./gmdb/* -rf");
    system("ipcrm -a");
    ret = mkdir(g_dbFilePath, S_IRUSR | S_IWUSR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"dataFileDirPath", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"maxTotalDynSize", (char *)"400");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"maxTotalShmSize", (char *)"228");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"maxSysDynSize", (char *)"100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"maxSysShmSize", (char *)"32");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"maxSeMem", (char *)"100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
// euler和环境环境清共享内存
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -ts");
#endif
    AW_CHECK_LOG_BEGIN();
    system("sh $TEST_HOME/tools/start.sh -ts");
}

void tsdb_rel_dynamic_memory::TearDown()
{
    int ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeTsGmserverCfg((char *)"recover", NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    system("chattr -R -i gmdb/");
    system("rm ./gmdb/* -rf");
// euler和环境环境清共享内存
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    // 重启服务，避免构建卡死
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm ./gmdb/* -rf");
    system("rm /data/gmdb -rf");
    system("sh $TEST_HOME/tools/start.sh -ts");
    printf("[INFO] Incremental Persistence cfg test End.\n");
}

// 动态内存不足，建连
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    // 建连
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 动态内存不足，建表，故障恢复后继续建表
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);

    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");

    ret = DropCmTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 动态内存不足，插入数据
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");
    Test_dml_acl2(g_conn_sync, g_stmt_sync, "testdb0", 20);

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");
    Test_dml_acl2(g_conn_sync, g_stmt_sync, "testdb0", 20);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 动态内存不足，disk_limit
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 600");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    // 在线修改disk_limit
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "alter table %s set (disk_limit = '1 MB')", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");

    // 故障取消后视图查询后台任务生效
    g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 动态内存不足，ttl
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm /data/gmdb -rf");
    system("rm ./gmdb/* -rf");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsLcmCheckPeriod=3\" ");
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 600");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    sleep(6);

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");
    sleep(12);

    // ttl后查询
    AW_FUN_Log(LOG_STEP, "ttl后查询.");
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd, "select * from %s", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t dataCount = 0;
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_RESULT_ROWS, &dataCount, sizeof(int));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(dataCount, 0);

    // 故障取消后视图查询后台任务生效
    g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 动态内存不足，查询视图
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    // 校验
    const char *queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");

    int data_num = 30;
    int count2 = 20;
    uint32_t i = 0;
    bool eof = false;
    // 校验
    queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    while (!eof) {
        ret = GmcFetch(g_stmt_sync, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)(data_num * count2));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 动态内存不足，查询逻辑表
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    // 校验
    const char *queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");
    // 校验
    int data_num = 30;
    int count2 = 20;
    uint32_t i = 0;
    bool eof = false;
    // 校验
    queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    while (!eof) {
        ret = GmcFetch(g_stmt_sync, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, (uint32_t)(data_num * count2));

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 动态内存不足，查询内存表
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表、写入数据
    char sqlCmd[512] = {0};
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet,"
        " description text, id1 integer, time1 integer, "
        "INDEX idx1(id))with (enGine = 'mEmOry', max_size =1000000, time_col = 'time', interval = '1 hour' );",
        "testdb0");
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    constexpr int64_t count2 = 10;
    ret = insertOrderData(g_stmt_sync, "testdb0", count2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    // 校验
    const char *queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");

    uint32_t i = 0;
    bool eof = false;
    // 校验
    queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    while (!eof) {
        ret = GmcFetch(g_stmt_sync, &eof);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (eof) {
            break;
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(i, 10);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 动态内存不足，tsdb_aging
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 600");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    // 动态内存不足，tsdb_aging
    char sqlCmd[512] = {0};
    uint32_t cmdLen = 0;
    (void)sprintf(sqlCmd, "SELECT tsdb_aging('%s');", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");

    // 故障取消后视图查询后台任务生效
    g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 动态内存不足，主动Truncate
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 600");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    // 主动Truncate
    char sqlCmd[512] = "truncate TABLE testdb0;";
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");

    // 故障取消后视图查询后台任务生效
    g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ROW_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 动态内存不足，在线修改表结构
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    char sqlCmd[512];

    // 新增列
    sprintf(sqlCmd, "alter table testdb0 add ip2 inet;");
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 新增列
    sprintf(sqlCmd, "alter table testdb0 add  ns2 text;");
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 新增列
    sprintf(sqlCmd, "alter table testdb0 add message blob;");
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 新增列
    sprintf(sqlCmd, "alter table testdb0 add message2 blob;");
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 新增列
    sprintf(sqlCmd, "alter table testdb0 add id2 integer;");
    ret = GmcExecDirect(g_stmt_sync, sqlCmd, 512);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");

    char g_command[1024];
    snprintf(g_command, 1024, "gmsysview -sql \"select * from  testdb0 limit 3\" -s %s", g_connServerTsdb);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "ip2", "ns2", "message", "message2", "id2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 动态内存不足，备份目录
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    system("rm /data/gmdb -rf");
    char sqlCmd[512];
    // 备份目录
    ret = GmcFlushDataBackup(g_stmt_sync, g_newDataDir, false, GMC_DATABASE_BACKUP_SCHEMA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");

    system("rm /data/gmdb -rf");
    // 备份目录
    ret = GmcFlushDataBackup(g_stmt_sync, g_newDataDir, false, GMC_DATABASE_BACKUP_SCHEMA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 杀掉服务
    system("sh $TEST_HOME/tools/stop.sh -ts");

    // 切换起服务地址
    (void)sprintf(sqlCmd, "sh $TEST_HOME/tools/modifyCfg.sh -ts \"dataFileDirPath=%s\"", g_newDataDir);
    system(sqlCmd);
    // 使用新目录重启服务
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 动态内存不足，在线目录切换
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    InitTsCiCfgModify();

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_dml_acl(g_conn_sync, g_stmt_sync, "testdb0", 20);

    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    // 切换目录
    ret = GmcSwapDataDirSleep(g_stmt_sync, g_newDataDir, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");

    // 切换目录
    ret = GmcSwapDataDirSleep(g_stmt_sync, g_dbFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropCmTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

int thr_count = 0;
void *tsdb_rel_dynamic_memory_014_001(void *args)
{
    int ret = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    Test_dml_acl2(g_conn_sync, g_stmt_sync, "testdb0", 20);

    ret = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    thr_count++;
    return 0;
}

void *tsdb_rel_dynamic_memory_014_002(void *args)
{
    int ret = 0;
    int m = *(int *)args;
    GmcConnT *g_conn_sync1 = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;   //  stmt 句柄
    YangConnOptionT tsConnOptions = {0};
    tsConnOptions.isCsMode = true;

    ret = TestTsGmcConnect(&g_conn_sync1, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验
    const char *queryCommand = "select * from testdb0;";
    ret = GmcExecDirect(g_stmt_sync, queryCommand, 150);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(g_conn_sync1, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    thr_count++;
    return 0;
}

void *tsdb_rel_dynamic_memory_014_003(void *args)
{
    int ret = 0;
    int m = *(int *)args;
    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");

    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");

    thr_count++;
    return 0;
}

// 并发动态内存不足，写入、查询数据
TEST_F(tsdb_rel_dynamic_memory, tsdb_rel_dynamic_memory_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int tableNum = 1;
    // 建表、写入数据
    ret = DropCmTable(g_stmt_sync, tableNum);
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thr1, thr2, thr3;
    int thr_num = 1;
    int index[thr_num];
    for (int k = 0; k < thr_num; k++) {
        index[k] = k;
        ret = pthread_create(&thr1, NULL, tsdb_rel_dynamic_memory_014_001, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&thr2, NULL, tsdb_rel_dynamic_memory_014_002, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&thr3, NULL, tsdb_rel_dynamic_memory_014_003, (void *)&index[k]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int k = 0; k < thr_num; k++) {
        ret = pthread_join(thr1, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(thr2, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(thr3, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    while (thr_count < 3) {
        sleep(3);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}
