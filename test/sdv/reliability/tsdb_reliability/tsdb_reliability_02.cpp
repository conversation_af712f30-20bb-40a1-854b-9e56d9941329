 /*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "tsdb_rel_scene_dml.h"
// 客户端退出
int main()
{
    GmcConnT *g_conn;
    GmcStmtT *g_stmt;
    int ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    
    // 创建同步连接
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    Test_dml_acl(g_conn, g_stmt, "testdb0", 20);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    return 0;
}
