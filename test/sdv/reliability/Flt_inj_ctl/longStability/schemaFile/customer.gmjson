[{"version": "2.0", "name": "customer", "type": "record", "check_validity": true, "fields": [{"name": "cid", "type": "uint64"}, {"name": "cname", "type": "string", "nullable": true}, {"name": "married", "type": "boolean"}, {"name": "mail", "type": "string"}, {"name": "birthday", "type": "time"}, {"name": "phone", "type": "fixed", "size": 11, "nullable": false}, {"name": "auto_increment", "type": "uint64", "auto_increment": true, "nullable": true}, {"name": "part", "type": "partition", "nullable": false}, {"name": "order", "type": "record", "array": true, "fields": [{"name": "oid", "type": "int32"}, {"name": "oname", "type": "string"}]}, {"name": "job", "type": "record", "vector": true, "fields": [{"name": "oid", "type": "int32"}, {"name": "oname", "type": "string"}, {"name": "adress", "type": "bytes"}]}], "keys": [{"name": "pk", "node": "customer", "fields": ["cid"], "index": {"type": "primary"}}, {"name": "localhash_non_unique", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "customer", "fields": ["birthday"], "constraints": {"unique": false}}]}]