#! /bin/bash
# ----------------------------------------------------------------------------
# Description:增.删.非主键查.订阅并发
# Author: qinjianhua wx620469
# Create: 2021-08-30
# History: 
# ----------------------------------------------------------------------------
ENV_TYPE=$1
tEnv=$(uname -a|grep Hongmeng|wc -l)
if [ "x$tEnv" == "x1" ];then
ENV_TYPE="harmony"
fi
echo ">>> ENV_TYPE: $ENV_TYPE"
function check_subId()
{
    subId=`ps -ef | grep teststa | grep -v grep | awk '{print $2}' | wc -l`
    if [ $subId -le 4 ];then
       echo "----------sub end ！----------"
       kill -9 `pidof teststa`
    fi
}

function check_gmserverId()
{
  gmserverId=`ps -ef | grep gmserver | grep -v grep | awk '{print $2}' | wc -l`
  hpkId=`ps | grep vm.elf | grep -v grep | awk '{print $2}' | wc -l`
  if [ $gmserverId -le 0 ] && [ $hpkId -le 0 ];then
    echo "----------The service does not exist!----------"
    kill -9 `pidof teststa`
    exit 1
  fi
}
kill -9 `pidof teststa`
if [ X"$ENV_TYPE" == "Xharmony" ] || [ X"$ENV_TYPE" == "Xsoho" ];then
    echo "X$ENV_TYPE"
else
    stop.sh -f
    start.sh -f
fi

../test_execute_all.sh

usr="user123"
pwd="password.123"
environ="RTOS"
server_locator="usocket:/run/verona/unix_emserver"
SCHEMA_PATH=${TEST_HOME}/ComponentbasedTest/schema_file/
CYCLE_COUNT=1
i=1
error_code=110024
times=1000
multi_process=1
multi_threads=50
each_thread=max_record_numulti_process/multi_threads*2
if [ X"$ENV_TYPE" == "Xharmony" ];then
  CYCLE_COUNT2=1
  let thread_count=1
else
  CYCLE_COUNT2=1
  let thread_count=50
fi

function op_alldir_allschema(){
  start_id=0
  end_id=10000

  loop2=0
  while [ ${loop2} -lt ${CYCLE_COUNT2} ]
  do
      ${TEST_HOME}/perf/stability/teststa -v  -m replace -n if  -b  $start_id -e $end_id -c $thread_count  &
      ${TEST_HOME}/perf/stability/teststa -v  -m delete -n if   -b  $start_id -e $end_id  -c $thread_count  &
      let loop2+=1
  done

  loop2=0
  while [ ${loop2} -lt ${CYCLE_COUNT2} ]
  do
      ${TEST_HOME}/perf/stability/teststa -v -a -m replace -n if -b  $start_id -e $end_id -c $thread_count  &
      ${TEST_HOME}/perf/stability/teststa -v -a -m delete -n if -b  $start_id -e $end_id  -c $thread_count  &
      let loop2+=1
  done

  loop2=0
  while [ ${loop2} -lt ${CYCLE_COUNT2} ]
  do
      ${TEST_HOME}/perf/stability/teststa -v  -m scan -n if -b $start_id -e 1 -c $thread_count &
      let loop2+=1
  done

  loop2=0
  while [ ${loop2} -lt ${CYCLE_COUNT2} ]
  do
      ${TEST_HOME}/perf/stability/teststa -v -p 1:if &
      let loop2+=1
  done
  sleep 10
  kill -9 `pidof teststa`
  gmsysview -q V\$DRT_CONN_SUBS_STAT
  echo -e "\033[45m once operation end: $i ---- $schema \033[0m at:" `date "+%m-%d %H:%M:%S"`
}

sleep 60
loop=0
while [ ${loop} -lt ${CYCLE_COUNT} ]
do
    op_alldir_allschema ${SCHEMA_PATH}
    let loop+=1
done
kill -9 `pidof teststa`
schema1=aaa_account_scheme
schema2=if
schema3=if_statistics
schema4=ip4forward
schema5=nhp_group
${TEST_HOME}/perf/stability/teststa -D -n $schema1 
${TEST_HOME}/perf/stability/teststa -D -n $schema2
${TEST_HOME}/perf/stability/teststa -D -n $schema3 
${TEST_HOME}/perf/stability/teststa -D -n $schema4 
${TEST_HOME}/perf/stability/teststa -D -n $schema5 
check_gmserverId
kill -9 `pidof teststa`
# 校验日志
if [ X"$ENV_TYPE" == "Xharmony" ] || [ X"$ENV_TYPE" == "Xsoho" ];then
    echo "[  PASSED  ] 1 test."
else
    ./check_testcaselog.sh $0
fi
