#! /bin/bash
# ----------------------------------------------------------------------------
# Description: 多进程多线程写时gmserver进程死循环
# Author: yangningwei ywx1037054
# Create: Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
# History: 第一次
# ----------------------------------------------------------------------------

function check_gmserverId()
{
    gmserverId=`ps -ef | grep gmserver | grep -v grep | awk '{print $2}' | wc -l`
    if [ $gmserverId -le 0 ];then
       echo "----------The service does not exist!----------"
       kill -9 `pidof teststa`
       exit 1
    fi
}
kill -9 `pidof teststa`
start.sh -f
../test_execute_all.sh
PSTOOL="ps -aux "
function check_gmserverStatus()
{
    if [ "$3" == "CHECK_CLEAN" ];then
      gmserver_status=`ps aux|grep 'gmserver -p /usr/local/file/gmserver.ini -b'|grep -v grep|awk '{print $8}'`
      ps aux|grep 'gmserver -p /usr/local/file/gmserver.ini -b'
    else 
      # 注意此处检查僵尸进程状态前面[]使用了\,后面<>就不能使用，否则无法检索出来
      gmserver_status=`ps aux|grep '\[gmserver\] <defunct>'|grep -v grep|awk '{print $8}'`
      echo "the defunct is: $gmserver_status"
    fi
    
    echo $gmserver_status
    if [ "$gmserver_status" == "$1" ];then
        echo "the status is right, $gmserver_status"
    else
        echo "[  FAILED  ]"
        let CIDA_EXCUTE_STATUS+=1
        echo "[ERROR]the status is wrong"
        echo "[ERROR]the status is wrong, expect is $1, actual is $gmserver_status line.num is $2 at:" `date "+%m-%d %H:%M:%S"`" ">>./test_shell.txt
    fi
}

usr="user123"
pwd="password.123"
environ="RTOS"
server_locator="usocket:/run/verona/unix_emserver"
SCHEMA_PATH=${TEST_HOME}/ComponentbasedTest/schema_file/
CYCLE_COUNT=1
i=1
times=1000
multi_process=30
multi_threads=200
each_thread=max_record_num/multi_process/multi_threads
let thread_count=128
CIDA_EXCUTE_STATUS=0
Concurrent_ctl=1
CFE="${TEST_HOME}/cfe/cfe/cfe"
function Random_Time()
{
        echo $(($(($RANDOM%$2))+$1))
        sleep $(($(($RANDOM%$2))+$1))
        return 0
}

function Sys_Count(){
    gmsysview -q V\$STORAGE_VERTEX_COUNT >temp.txt
    grep -r -A 1 $1 ./temp.txt >temp2.txt
    awk NR==2 ./temp2.txt >temp3.txt
    num_count=`awk '{print $3}' ./temp3.txt `
    echo "num_count=$num_count"
    expect_num=$2
    if [ "$num_count" != "$expect_num" ]
    then
      if [ "$expect_num" != 0 ] && [ "$Concurrent_ctl" != 0 ]
      then
        echo "[  FAILED  ]"
        let CIDA_EXCUTE_STATUS+=1
        echo "[ERROR] COUNT OF MAX IS WRONG ACTUAL IS : $num_count, EXPECT IS : $expect_num at:" `date "+%m-%d %H:%M:%S"`" ">>./test_shell.txt
      fi
    fi
    rm -rf ./temp*.txt
}

function Sys_Count_Easy(){
    gmsysview -q V\$STORAGE_VERTEX_COUNT >temp.txt
    grep -r -A 1 $1 ./temp.txt >temp2.txt
    awk NR==2 ./temp2.txt >temp3.txt
    num_count=`awk '{print $3}' ./temp3.txt `
    echo "num_count=$num_count"
    expect_num=$2-10000
    if [ "$num_count" -ge "$expect_num" ]
    then
      if [ "$expect_num" != 0 ] && [ "$Concurrent_ctl" != 0 ]
      then
        echo "[  FAILED  ]"
        let CIDA_EXCUTE_STATUS+=1
        echo "[ERROR] COUNT OF MAX IS WRONG ACTUAL IS : $num_count, EXPECT IS : $expect_num at:" `date "+%m-%d %H:%M:%S"`" ">>./test_shell.txt
      fi
    fi
    rm -rf ./temp*.txt
}

function Delete_tabel(){
      
      ${TEST_HOME}/perf/stability/teststa -v  -D -n 'aaa_account_scheme' &
      sleep 1
      ${TEST_HOME}/perf/stability/teststa -v  -D -n 'if_statistics' &
      sleep 1
      ${TEST_HOME}/perf/stability/teststa -v  -D -n 'if' &
      sleep 1
      ${TEST_HOME}/perf/stability/teststa -v  -D -n 'ip4forward' &
      sleep 1
      ${TEST_HOME}/perf/stability/teststa -v  -D -n 'nhp_group' &
      sleep 5
}

function Insert_data(){
  start_id=0
  end_id=10000

for file in `ls $1`
  do
    if [ -d $1"/"$file ]
    then
      op_alldir_allschema $1"/"$file
    else
      schema=${file%.*}
      echo -e "\033[45m once operation start: $i ---- $schema \033[0m at:" `date "+%m-%d %H:%M:%S"`
      end_id=`grep max_record_num $1"/"$file|cut -d ':' -f 2|cut -d ',' -f 1`
      echo "end_id=$end_id"
      ${TEST_HOME}/perf/stability/teststa -v  -m replace -n $schema  -b  $start_id -e $end_id -c $thread_count &
    fi
  done
}
# gmserver进程死循环
function DISABLED_TEST_rProc_c() 
{
    inject_val=gmserver
    pre_inject_val=$(${PSTOOL}| grep -v grep | grep ${inject_val} | awk '{print $8}')
    $CFE "inject rProc_c(pid) values($(pidof ${inject_val}))"
}
# gmserver进程清除死循环(没有cfe指定的清除工具，循环中杀死所有的defunct进程)
function DISABLED_TEST_Clean_rProc_c() 
{
  num_of_z=$(${PSTOOL}| grep -v grep | grep '\[gmserver\]'| awk '{print $2}'|wc -l)
  echo ${num_of_z}
  while [ ${num_of_z} -gt 0 ]
  do
    num_of_z=$(${PSTOOL}| grep -v grep | grep '\[gmserver\]'| awk '{print $2}'|wc -l)
    pid_of_z=$(${PSTOOL}| grep -v grep | grep '\[gmserver\]'| awk '{print $2}'|head -n 1 )
    echo "now kill the pid is : $pid_of_z"
    kill -9 $pid_of_z
  done
}

function op_alldir_allschema(){
      ${TEST_HOME}/perf/stability/teststa -v  -m replace -n 'aaa_account_scheme'  -b  0 -e 8192 -c $thread_count -a &
      sleep 1
      ${TEST_HOME}/perf/stability/teststa -v  -m replace -n 'if_statistics'  -b  0 -e 65536 -c $thread_count -a &
      sleep 1
      ${TEST_HOME}/perf/stability/teststa -v  -m replace -n 'if' -b  0 -e 128000 -c $thread_count -a &
      sleep 1
      ${TEST_HOME}/perf/stability/teststa -v  -m replace -n 'ip4forward'  -b  0 -e 400000 -c $thread_count -a &
      sleep 1
      ${TEST_HOME}/perf/stability/teststa -v  -m replace -n 'nhp_group'  -b  0 -e 409600 -c $thread_count -a &
      sleep 20
}

loop=0
while [ ${loop} -lt ${CYCLE_COUNT} ]
do
    echo $CIDA_EXCUTE_STATUS
    op_alldir_allschema 
    Sys_Count_Easy 'aaa_account_scheme' 8192
    Sys_Count_Easy 'if_statistics' 65536
    Sys_Count_Easy 'if' 128000
    Sys_Count_Easy 'ip4forward' 400000
    Sys_Count_Easy 'nhp_group' 409600
 
    echo "INJECT SERVER WHILE"
    DISABLED_TEST_rProc_c
    sleep 10
    check_gmserverStatus Zl $LINENO
    Concurrent_ctl=0

    Sys_Count_Easy 'aaa_account_scheme' 8192
    Sys_Count_Easy 'if_statistics' 65536
    Sys_Count_Easy 'if' 128000
    Sys_Count_Easy 'ip4forward' 400000
    Sys_Count_Easy 'nhp_group' 409600
    gmserver_pid=`ps -ef|grep gmserver|grep -v grep|awk '{print$2}'`
    gmserver_status=`ps -ef|grep gmserver|grep -v grep|awk '{print$2}'|wc -l`
    echo "gmserver_pid=$gmserver_pid"
    if [ $gmserver_status -le 0 ];then
        echo "[  FAILED  ]"
        let CIDA_EXCUTE_STATUS+=1
        echo "[ERROR] GM_SERVER is not exist at:" `date "+%m-%d %H:%M:%S"`" ">>./test_shell.txt
    fi

    DISABLED_TEST_Clean_rProc_c
    echo "CLEAN SERVER WHILE"
    
    Delete_tabel
    sleep 5
    Concurrent_ctl=0
    Sys_Count 'aaa_account_scheme' 0
    Sys_Count 'if_statistics' 0
    Sys_Count 'if' 0
    Sys_Count 'ip4forward' 0
    Sys_Count 'nhp_group' 0
    check_gmserverStatus Sl $LINENO "CHECK_CLEAN"
    let loop+=1
    # echo $CIDA_EXCUTE_STATUS
done

check_gmserverId
kill -9 `pidof teststa`
if [ "$CIDA_EXCUTE_STATUS" == 0 ]
then 
  echo "[  PASSED  ] 1 test."
fi
