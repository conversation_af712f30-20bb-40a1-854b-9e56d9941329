/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2017-2022. All rights reserved.
 * File Name: syCommon.h
 * Author: yaosiyuan ywx758883
 * Date: 2021-5-31
 * Describle:
 */
#ifndef _SYCOMMON_H
#define _SYCOMMON_H

#include <stdarg.h>
#include <semaphore.h>
#include "gtest/gtest.h"
#include "gmc.h"
#include "gmc_errno.h"
#include "gmc_types.h"
#include "t_datacom_lite.h"

#ifdef __cplusplus
extern "C" {
#endif

#if defined ENV_RTOSV2X
#define THRNUM 2
#define CYCLENUM 1
#elif defined ENV_RTOSV2
#define THRNUM 20
#define CYCLENUM 1
#else
#define THRNUM 100
#define CYCLENUM 10
#endif

#define FAILED (-1)
#define RECORDCOUNTSTART 0
#define RECORDCOUNTEND 1000
#define PK_ID 0
#define GMIMPORT (char *)"gmimport"
#define GMEXPORT (char *)"gmexport"
#define GMSYSVIEW (char *)"gmsysview"
#define FULLTABLE 0xff
#define LOG_LEVEL 1

AsyncUserDataT g_data = {0};
SnUserDataT *g_userData;
typedef void *(*ThreadFunc)(void *);
typedef enum tagRunMode { MODE_EULER = 0, MODE_DAP = 1, MODE_HONGMENG = 2 } GtRunModeE;

#define TEST_ERROR(log, args...)                                                                \
    do {                                                                                        \
        fprintf(stdout, "Error: pid-%d %s:%d " log "\n", getpid(), __FILE__, __LINE__, ##args); \
    } while (0)

#define CHECK_AND_BREAK(ret, log, args...)                                                             \
    if ((ret) != GMERR_OK) {                                                                           \
        fprintf(stdout, "Error: %s:%d " log " failed, ret = %d\n", __FILE__, __LINE__, ##args, (ret)); \
        break;                                                                                         \
    }

// 申请内存
void mallocSubData(SnUserDataT **userData, uint32_t sizeMalloc)
{
    SnUserDataT *mallocData = NULL;
    mallocData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(mallocData, 0, sizeof(SnUserDataT));
    mallocData->new_value = (int *)malloc(sizeof(int) * sizeMalloc);
    memset(mallocData->new_value, 0, sizeof(int) * sizeMalloc);
    mallocData->old_value = (int *)malloc(sizeof(int) * sizeMalloc);
    memset(mallocData->old_value, 0, sizeof(int) * sizeMalloc);
    mallocData->isReplace_insert = (bool *)malloc(sizeof(bool) * sizeMalloc);
    memset(mallocData->isReplace_insert, 0, sizeof(bool) * sizeMalloc);
    *userData = mallocData;
}

// 释放malloc申请的内存
void freeMallocSqace(SnUserDataT *userData)
{
    free(userData->new_value);
    free(userData->old_value);
    free(userData->isReplace_insert);
    free(userData);
}

// [out] result: 执行系统调用的结果, 使用结束后必须调用free()释放内存
int MyGtExecSystemCmd(char **result, const char *format, ...)
{
    int ret;
    errno = 0;
    va_list args;
    va_start(args, format);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), format, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d, %s.", ret, strerror(errno));
        va_end(args);
        return -1;
    }
    va_end(args);

    FILE *fd = popen(cmd, "r");
    if (fd == NULL) {
        TEST_ERROR("popen failed, %s.", strerror(errno));
        return -1;
    }

    // XXX 优化为动态获取流长度
    uint32_t size = 1024 * 100;
    char *tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        TEST_ERROR("malloc failed, %s.", strerror(errno));
        (void)pclose(fd);
        return -1;
    }
    memset(tmpResult, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat(tmpResult, buf);
    }

    ret = pclose(fd);
    if (ret == -1) {
        TEST_ERROR("pclose failed, %s.", strerror(errno));
        free(tmpResult);
        return -1;
    }
    *result = tmpResult;
    return GMERR_OK;
}

// [out] result: 执行系统调用的结果, 使用结束后必须调用free()释放内存
int GtExecSysviewCmd(char **result, const char *viewName, const char *filter = "", ...)
{
    int ret;
    va_list args;
    va_start(args, filter);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), filter, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d.", ret);
        va_end(args);
        return -1;
    }
    va_end(args);

    char *buf = NULL;
    ret = MyGtExecSystemCmd(&buf, "%s/gmsysview -u %s -p %s -s %s -q '%s' %s", g_toolPath, g_userName, g_passwd,
        g_connServer, viewName, cmd);
    if (ret != GMERR_OK) {
        TEST_ERROR("exec system cmd failed, ret = %d.", ret);
        free(buf);
        buf = NULL;
        return ret;
    }

    buf[strlen(buf) - 1] = '\0';
    *result = buf;
    return GMERR_OK;
}

// 创建订阅关系
int createSubscribe(const char *file, const char *subName, GmcStmtT *stmt, GmcConnT *conn,
    void (*snCallBack)(GmcStmtT *, const GmcSubMsgInfoT *, void *))
{
    int ret = 0;
    char *schema = NULL;
    readJanssonFile(file, &schema);
    EXPECT_NE((void *)NULL, schema);
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = schema;
    ret = GmcSubscribe(stmt, &tmp_schema, conn, snCallBack, g_userData);
    free(schema);
    return ret;
}

// 建表
int createVertexLabel(const char *file, GmcStmtT *stmt, const char *config, int expected = GMERR_OK)
{
    int ret = 0;
    char *schema = NULL;
    readJanssonFile(file, &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(stmt, schema, config);
    free(schema);
    return ret;
}

// 异步删表
int DropVertexLabelAsync(GmcStmtT *stmt, const char *labelName, void (*callBack)(void *, Status, const char *))
{
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = GmcDropVertexLabelAsync(stmt, labelName, callBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    return ret;
}

// 工具导入
int toolModelOperation(const char *toolMode, const char *cmdType, const char *fileName, const char *labelName)
{
    int ret = 0;
    char cmd[512];
    if (strcmp(toolMode, (char *)"gmimport") == 0) {
        snprintf(cmd, 512, "%s/gmimport -c %s -f %s", g_toolPath, cmdType, fileName);
    } else if (strcmp(toolMode, (char *)"gmexport") == 0) {
        snprintf(cmd, 512, "%s/gmexport -c %s -t %s", g_toolPath, cmdType, labelName);
    } else if (strcmp(toolMode, (char *)"gmsysview") == 0) {
        snprintf(cmd, 512, "%s/gmsysview %s", g_toolPath, cmdType);
    }
    ret = system(cmd);
    return ret;
}

// 事务
int syTransStart(GmcConnT *conn, char model)
{
    int ret = 0;
    GmcTxConfigT transcction_config;
    transcction_config.readOnly = false;
    transcction_config.transMode = model;
    transcction_config.type = GMC_TX_ISOLATION_COMMITTED;
    ret = GmcTransStart(conn, &transcction_config);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

int gmsysview_popen(const char *result[], const char *format, ...)
{
    int ret = 0;
    char buf[1024] = {0};
    FILE *p_file = NULL;
    char command[1024] = {0};

    va_list args;
    va_start(args, format);
    ret = vsnprintf(command, sizeof(command), format, args);
    va_end(args);
    p_file = popen(command, (char *)"r");
    if (NULL == p_file) {
        ret = 1;
        printf("popen %s error/n", p_file);
    }

    while (fgets(buf, sizeof(buf), p_file) != NULL) {
        strcpy((char *)result, buf);
    }

    if (pclose(p_file) == 1) {
        printf("pclose failed.");
        return 1;
    }
    return ret;
}

#ifdef __cplusplus
}
#endif
#endif
