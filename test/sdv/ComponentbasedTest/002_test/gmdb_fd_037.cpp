/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <semaphore.h>
#include <sys/sem.h>
#include <sys/shm.h>
#include <time.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

// 订阅连接与普通连接的混用
int main(int argc, char **argv)
{
    system("sh $TEST_HOME/tools/start.sh");
    const char *g_subConnName = "subConnName";
    int ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *g_conn_async;
    GmcStmtT *g_stmt_async;
    GmcStmtT *g_stmt_sub;
    GmcConnT *g_conn_sub;
    // 封装创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    char *gmdb_fd_047 = NULL;
    readJanssonFile("../schema_file/aaa_account_scheme.gmjson", &gmdb_fd_047);
    EXPECT_NE((void *)NULL, gmdb_fd_047);
    char g_label_config[] = "{\"max_record_count\":8192}";
    ret = GmcCreateVertexLabelAsync(g_stmt_sub, gmdb_fd_047, g_label_config, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    free(gmdb_fd_047);
    ret = GmcSubSetFetchMode(g_stmt_async, GMC_SUB_FETCH_OLD);
    EXPECT_EQ(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    printf("[  PASSED  ] 1 test.\n");
    return 0;
}
