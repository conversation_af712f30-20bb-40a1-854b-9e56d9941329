{"version": "2.0", "type": "record", "name": "test_devm_sys_state", "config": {"check_validity": false}, "max_record_count": 128000, "fields": [{"name": "chassis_id", "type": "uint32"}, {"name": "slot_id", "type": "uint32"}, {"name": "card_id", "type": "uint32"}, {"name": "current_state", "type": "uint32"}, {"name": "poweroff_time", "type": "uint32"}, {"name": "register_time", "type": "uint32"}, {"name": "unregister_time", "type": "uint32"}], "keys": [{"name": "sys_state_index", "index": {"type": "primary"}, "node": "test_devm_sys_state", "fields": ["chassis_id", "slot_id", "card_id"], "constraints": {"unique": true}}]}