{"label_name": "test_frm_mod", "comment": "test_frm_mod message_queue sub", "schema_version": 0, "events": [{"type": "initial_load"}, {"type": "replace insert", "msgTypes": ["new object", "old object"]}, {"type": "update", "msgTypes": ["new object", "old object"]}, {"type": "delete", "msgTypes": ["new object", "old object"]}, {"type": "age", "msgTypes": ["new object", "old object"]}], "subs_type": "message_queue"}