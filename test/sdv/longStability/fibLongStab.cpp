/* ****************************************************************************
 Description  :7#/8#/9#/10# 表长稳测试
线程1：针对fib表，写10、9/8，循环写和删除7号表，数据量100条；
线程2：写10/8/7号表，循环写9号表，全表扫描，回滚，数据量100条；
线程3:单表订阅9号表的insert;

 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2021/09/24
**************************************************************************** */

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"

#include "fibLongStability.h"
#include "insufficientAddressSpace.h"
#include "ClientSingnoProtect.h"

#define FIB_SECNE_NUM 4  //场景数

uint32_t g_dataN = 0;
int16_t g_sceneFlag[FIB_SECNE_NUM + 1] = {0};
int16_t g_sceneThreadN[FIB_SECNE_NUM + 1] = {1};
pthread_t graph_fib_thr1, graph_fib_thr2[THREADS_NUM_MAX], graph_fib_thr3[THREADS_NUM_MAX];

void FibStabilityDefaultScene()
{
    int ret = 0;
    //有另外程序起服务和预置数据
    // longStabilityStartServer();
    ret = testEnvInit();
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = create_epoll_thread();
    TEST_EXPECT_INT32(GMERR_OK, ret);
    //创表
    create_fib_label();

    pthread_create(&graph_fib_thr1, g_pthreadAttrPtr, sub_fib_9, NULL);
    pthread_create(&graph_fib_thr2[0], g_pthreadAttrPtr, fib_thread1, NULL);
    pthread_create(&graph_fib_thr3[0], g_pthreadAttrPtr, fib_thread2, NULL);

    pthread_join(graph_fib_thr1, NULL);
    pthread_join(graph_fib_thr2[0], NULL);
    pthread_join(graph_fib_thr3[0], NULL);

    longStabilityCleanEnv();
}

void FibStabilitySceneTest()
{
    int ret = 0;
    //有另外程序起服务和预置数据
    // longStabilityStartServer();
    ret = testEnvInit();
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = create_epoll_thread();
    TEST_EXPECT_INT32(GMERR_OK, ret);
    //创表
    create_fib_label();

    if (g_sceneFlag[1] == 1) {
        printf("\n");
        printf("Fib sub scene:1 threadNo 0\n");
        pthread_create(&graph_fib_thr1, g_pthreadAttrPtr, sub_fib_9, NULL);
    }

    if (g_sceneFlag[2] == 1) {
        for (int j = 0; j < g_sceneThreadN[2]; j++) {
            printf("Fib scene:2 threadNo:%d\n", j);
            pthread_create(&graph_fib_thr2[j], g_pthreadAttrPtr, fib_thread1, &g_dataN);
        }
    }

    if (g_sceneFlag[3] == 1) {
        for (int j = 0; j < g_sceneThreadN[3]; j++) {
            printf("Fib scene:3 threadNo:%d\n", j);
            pthread_create(&graph_fib_thr3[j], g_pthreadAttrPtr, fib_thread2, &g_dataN);
        }
    }
    // join线程
    if (g_sceneFlag[1] == 1) {
        pthread_join(graph_fib_thr1, NULL);
    }
    if (g_sceneFlag[2] == 1) {
        for (int j = 0; j < g_sceneThreadN[2]; j++) {
            pthread_join(graph_fib_thr2[j], NULL);
        }
    }
    if (g_sceneFlag[3] == 1) {
        for (int j = 0; j < g_sceneThreadN[3]; j++) {
            pthread_join(graph_fib_thr3[j], NULL);
        }
    }

    longStabilityCleanEnv();
}

int main(int argc, char **argv)
{

    pthread_t addrSpaceThread;
    createInsufficientAddressSpaceThread(false, &addrSpaceThread);
    if (argc == 1) {
        //默认原V3场景
        FibStabilityDefaultScene();
    } else {
        //将标志位/数据量/线程数置0，默认不启动任何场景和线程数
        g_dataN = 0;
        for (int j = 0; j < FIB_SECNE_NUM + 1; j++) {
            g_sceneFlag[j] = 0;
            g_sceneThreadN[j] = 0;
        }
        int ret =
            longStabilitySetSceneFlag(argv, g_sceneFlag, g_sceneThreadN, FIB_SECNE_NUM, &g_dataN, FIB_THREAD1_DATA_END);
        if (ret != 0) {
            return 1;
        }
        //按入参启动线程
        FibStabilitySceneTest();
    }
    pthread_join(addrSpaceThread, NULL);
}
