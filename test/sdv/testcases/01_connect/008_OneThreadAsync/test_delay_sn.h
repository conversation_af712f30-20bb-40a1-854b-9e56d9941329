/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: 异步单线程epoll测试头文件
 * Author: ynw
 * Create: Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * History:
 */
#ifndef _TEST_DELAY_SN_H_
#define _TEST_DELAY_SN_H_
#include <pthread.h>
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <sys/epoll.h>
#include <sys/timerfd.h>
#include "semaphore.h"
#include "t_datacom_lite.h"

uint64_t g_delay_count = 1;
uint64_t g_delay_s = 1;
uint64_t g_recv_times = 0;

pthread_mutex_t g_delayLock;

inline int testWaitAsyncRecv_delay(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true)
{
    int waitCnt = 0;
    int waitMs = 1000;
    AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
    while (user_data->recvNum != expRecvNum) {
        if (g_runMode == 1) {
            while (waitMs > 0) {
                waitMs--;
            }
            waitMs = 1000;
        } else {
            while (waitMs > 0) {
                usleep(1);
                waitMs--;
            }
            waitMs = 1000;
        }

        waitCnt++;
        if (timeout > 0 && waitCnt >= timeout) {
            printf("[INFO] Recv Timeout, all OpNum : %d, actually recived num : %d\n", expRecvNum, user_data->recvNum);
            return -1;  // 接收超时
        }
    }
    if (isAutoReset) {
        user_data->recvNum = 0;
    }
    return 0;
}

void create_vertex_label_callback_delay(void *userData, int32_t status, const char *errMsg)
{
    for (int i = 0; i < g_delay_count; i++) {
        sleep(g_delay_s);
    }

    AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
    user_data->status = status;
    user_data->recvNum++;

    pthread_mutex_lock(&g_delayLock);
    ++g_recv_times;
    pthread_mutex_unlock(&g_delayLock);
}

void drop_vertex_label_callback_delay(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_delay(userData, status, errMsg);
}

void get_vertex_label_callback_delay(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_delay(userData, status, errMsg);
}
void insert_vertex_callback_delay_new(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
    if (user_data->expTotalNum == user_data->recvNum) {
        return;
    }
    printf("out sleep,user_data->recvNum %d, user_data->expTotalNum %d\n", user_data->recvNum, user_data->expTotalNum);
    user_data->status = status;
    user_data->affectRows = affectedRows;
    user_data->recvNum++;
    pthread_mutex_lock(&g_delayLock);
    ++g_recv_times;
    pthread_mutex_unlock(&g_delayLock);
}

void insert_vertex_callback_delay(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    for (int i = 0; i < g_delay_count; i++) {
        sleep(g_delay_s);
    }

    AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
    user_data->status = status;
    user_data->affectRows = affectedRows;
    user_data->recvNum++;
    pthread_mutex_lock(&g_delayLock);
    ++g_recv_times;
    pthread_mutex_unlock(&g_delayLock);
}

void update_vertex_callback_delay(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback_delay(userData, affectedRows, status, errMsg);
}

void delete_vertex_callback_delay(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback_delay(userData, affectedRows, status, errMsg);
}

void batch_execute_callback_delay(
    void *userData, uint32_t totalNum, uint32_t successNum, int32_t status, const char *errMsg)
{
    for (int i = 0; i < g_delay_count; i++) {
        sleep(g_delay_s);
    }

    AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
    user_data->status = status;
    user_data->totalNum = totalNum;
    user_data->succNum = successNum;
    user_data->recvNum++;
    pthread_mutex_lock(&g_delayLock);
    ++g_recv_times;
    pthread_mutex_unlock(&g_delayLock);
}

void merge_vertex_callback_delay(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback_delay(userData, affectedRows, status, errMsg);
}

void replace_vertex_callback_delay(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback_delay(userData, affectedRows, status, errMsg);
}

void truncate_vertex_label_callback_delay(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_delay(userData, status, errMsg);
}

void create_kv_table_callback_delay(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_delay(userData, status, errMsg);
}

void drop_kv_table_callback_delay(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_delay(userData, status, errMsg);
}

void get_kv_table_callback_delay(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_delay(userData, status, errMsg);
}

void set_kv_callback_delay(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback_delay(userData, affectedRows, status, errMsg);
}

void delete_kv_callback_delay(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback_delay(userData, affectedRows, status, errMsg);
}

void truncate_kv_table_callback_delay(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_delay(userData, status, errMsg);
}

void delete_vertex_by_cond_callback_delay(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback_delay(userData, affectedRows, status, errMsg);
}

void update_vertex_by_cond_callback_delay(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    insert_vertex_callback_delay(userData, affectedRows, status, errMsg);
}

void use_namespace_callback_delay(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_delay(userData, status, errMsg);
}

void create_namespace_callback_delay(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_delay(userData, status, errMsg);
}

void drop_namespace_callback_delay(void *userData, int32_t status, const char *errMsg)
{
    create_vertex_label_callback_delay(userData, status, errMsg);
}

#endif  // _TEST_DELAY_SN_H_
