[{"version": "2.0", "max_record_count": 10, "config": {"check_validity": false}, "type": "record", "name": "schema_datatype", "fields": [{"name": "F1", "type": "int8"}, {"name": "F2", "type": "uint8"}, {"name": "F3", "type": "int16"}, {"name": "F4", "type": "uint16"}, {"name": "F5", "type": "int32"}, {"name": "F6", "type": "uint32"}, {"name": "F7", "type": "int64"}, {"name": "F8", "type": "uint64"}, {"name": "F9", "type": "int"}, {"name": "F10", "type": "time"}, {"name": "F11", "type": "float"}, {"name": "F12", "type": "double"}, {"name": "F13", "type": "boolean"}, {"name": "F14", "type": "bytes", "size": 6}, {"name": "F15", "type": "string", "size": 6}, {"name": "F16", "type": "fixed", "size": 6}, {"name": "F17", "type": "time"}], "keys": [{"name": "pk", "fields": ["F1"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]