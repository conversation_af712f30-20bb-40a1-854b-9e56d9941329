/*****************************************************************************
 Description  : 在一个订阅关系中下发所有事件
 Notes        : 注释如下
 History      :
 Author       : jiangdingshan
 Modification :
 Date         : 2021/09/16
*****************************************************************************/

extern "C" {
}
#include "KvSub.h"

class KvSub : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();

    SnUserDataT *user_data;
};

void KvSub::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void KvSub::TearDownTestCase()
{
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void KvSub::SetUp()
{
    g_subIndex = 0;
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //创建kv表
    GmcKvDropTable(g_stmt_sync, g_tableName);
    ret = GmcKvCreateTable(g_stmt_sync, g_tableName, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //打开kv表
    ret = GmcKvPrepareStmtByLabelName(g_stmt_sync, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}
void KvSub::TearDown()
{
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AddWhiteList(GMERR_NO_DATA);
    AW_CHECK_LOG_END();
    ret = GmcKvDropTable(g_stmt_sync, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_sub_info);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
}

//同步连接，建表，下发所有事件类型，set,delete各1024条数据，触发订阅(KV)
TEST_F(KvSub, SN_021_019)
{
    uint32_t i, userDataIdx = 0;

    readJanssonFile("schema/KV_subInfo_001.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, kv_sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    char key[128] = "zhangsan";

    //写
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;

        sprintf(key, "zhangsan_%d", i);
        int value = i;

        GmcKvTupleT kvInfo = {0};
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvSet(g_stmt_sync, key, strlen(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_KV_SET, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    //删
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;
        sprintf(key, "zhangsan_%d", i);

        ret = GmcKvRemove(g_stmt_sync, key, strlen(key));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
    AddWhiteList(GMERR_NO_DATA);
}
//订阅 set各1024条数据
TEST_F(KvSub, SN_021_020)
{
    uint32_t i, userDataIdx = 0;

    readJanssonFile("schema/KV_subInfo_002.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, kv_sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    char key[128] = "zhangsan";

    //写
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;

        sprintf(key, "zhangsan_%d", i);
        int value = i;

        GmcKvTupleT kvInfo = {0};
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvSet(g_stmt_sync, key, strlen(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_KV_SET, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    //删
    for (i = 0; i < g_data_num; i++) {

        sprintf(key, "zhangsan_%d", i);
        ret = GmcKvRemove(g_stmt_sync, key, strlen(key));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

//订阅 delete各1024条数据
TEST_F(KvSub, SN_021_021)
{
    uint32_t i, userDataIdx = 0;

    readJanssonFile("schema/KV_subInfo_003.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, kv_sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    char key[128] = "zhangsan";

    //写
    for (i = 0; i < g_data_num; i++) {

        sprintf(key, "zhangsan_%d", i);
        int value = i;

        GmcKvTupleT kvInfo = {0};
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvSet(g_stmt_sync, key, strlen(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    //删
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;
        sprintf(key, "zhangsan_%d", i);

        ret = GmcKvRemove(g_stmt_sync, key, strlen(key));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// KV全量订阅
TEST_F(KvSub, SN_021_022)
{
    uint32_t i, userDataIdx = 0;

    readJanssonFile("schema/KV_subInfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, kv_sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    EXPECT_EQ(GMERR_OK, ret);

    char key[128] = "zhangsan";

    //写
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;

        sprintf(key, "zhangsan_%d", i);
        int value = i;

        GmcKvTupleT kvInfo = {0};
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvSet(g_stmt_sync, key, strlen(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_KV_SET, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    //删
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;
        sprintf(key, "zhangsan_%d", i);

        ret = GmcKvRemove(g_stmt_sync, key, strlen(key));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}

// KV可靠订阅
TEST_F(KvSub, SN_021_023)
{
    uint32_t i, userDataIdx = 0;

    readJanssonFile("schema/KV_subInfoRel.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, kv_sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    EXPECT_EQ(GMERR_OK, ret);

    char key[128] = "zhangsan";

    //写
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;

        sprintf(key, "zhangsan_%d", i);
        int value = i;

        GmcKvTupleT kvInfo = {0};
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvSet(g_stmt_sync, key, strlen(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_KV_SET, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    //删
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        ((bool *)(user_data->isReplace_insert))[userDataIdx] = true;
        userDataIdx++;
        sprintf(key, "zhangsan_%d", i);

        ret = GmcKvRemove(g_stmt_sync, key, strlen(key));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    EXPECT_EQ(GMERR_OK, ret);
}
