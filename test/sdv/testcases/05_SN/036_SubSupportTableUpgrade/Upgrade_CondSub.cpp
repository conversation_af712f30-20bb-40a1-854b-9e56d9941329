/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : 订阅支持表结构多版本 基本功能 -- 条件订阅
 Notes        : 001.低、高版本订阅uint32字段
                002.低、高版本订阅int32字段
                003.低、高版本订阅char字段
                004.低、高版本订阅int16字段
                005.低、高版本订阅uint32字段和int32字段
                006.低、高版本订阅char字段或int32字段
                007.低版本订阅uint32字段，高版本订阅int32字段
                008.低版本订阅int32字段，高版本订阅char字段
                009.低版本订阅int16字段，高版本订阅新增字段

 History      :
 Author       : liaoxiang lwx1036939
 Modification : 2023/3/7
*****************************************************************************/

#include "SubSupportTableUpgrade.h"
SnUserDataT *user_data1;
SnUserDataT *user_data2;

class Upgrade_CondSub : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    }
};

void Upgrade_CondSub::SetUp()
{
    int ret = 0;
    char *expectValue = (char *)"successfully";

    ret = testSnMallocUserData(&user_data1, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSnMallocUserData(&user_data2, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char errorMsg6[128] = {};
    (void)snprintf(errorMsg6, sizeof(errorMsg6), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg6);

    AW_CHECK_LOG_BEGIN();

    // create vertex table
    ret = TestCreateLabel(g_stmt, g_vertexFilePath, g_vertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表升级
    ret = TestUpdateVertexLabel(g_vertexUpdatePath, expectValue, g_vertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅连接
    ret = testSubConnect(&g_connSub, &g_stmtSub, 1, g_epoll_reg_info, g_vertexSubConnName, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub1, &g_stmtSub1, 1, g_epoll_reg_info, g_vertexSubConnName1, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void Upgrade_CondSub::TearDown()
{
    AW_CHECK_LOG_END();
    testSnFreeUserData(user_data1);
    testSnFreeUserData(user_data2);

    // drop vertex table
    int ret = GmcDropVertexLabel(g_stmt, g_vertexLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(g_connSub, g_stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(g_connSub1, g_stmtSub1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001.低、高版本订阅uint32字段
TEST_F(Upgrade_CondSub, SN_036_Upgrade_CondSub_001)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    // 低版本订阅 uint32 字段
    TestGmcSubscribe(g_stmt, g_connSub, g_vertexUint32SubPath, g_vertexSubName, user_data1, true);

    // 高版本订阅 uint32 字段
    TestGmcSubscribe(g_stmt, g_connSub1, g_vertexUint32SubPath1, g_vertexSubName1, user_data2, false);

    // 低版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, 0, GMC_OPERATION_INSERT);

    // 高版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_endNum, g_endNum * 2, 1, GMC_OPERATION_INSERT);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 002.低、高版本订阅int32字段
TEST_F(Upgrade_CondSub, SN_036_Upgrade_CondSub_002)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    // 低版本订阅 int32 字段
    TestGmcSubscribe(g_stmt, g_connSub, g_vertexInt32SubPath, g_vertexSubName, user_data1, true);

    // 高版本订阅 int32 字段
    TestGmcSubscribe(g_stmt, g_connSub1, g_vertexInt32SubPath1, g_vertexSubName1, user_data2, false);

    // 低版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, 0, GMC_OPERATION_INSERT);

    // 高版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_endNum, g_endNum * 2, 1, GMC_OPERATION_INSERT);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 003.低、高版本订阅char字段
TEST_F(Upgrade_CondSub, SN_036_Upgrade_CondSub_003)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    // 低版本订阅 char 字段
    TestGmcSubscribe(g_stmt, g_connSub, g_vertexCharSubPath, g_vertexSubName, user_data1, true);

    // 高版本订阅 char 字段
    TestGmcSubscribe(g_stmt, g_connSub1, g_vertexCharSubPath1, g_vertexSubName1, user_data2, false);

    // 低版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, 0, GMC_OPERATION_INSERT);

    // 高版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_endNum, g_endNum * 2, 1, GMC_OPERATION_INSERT);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 004.低、高版本订阅int16字段
TEST_F(Upgrade_CondSub, SN_036_Upgrade_CondSub_004)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    // 低版本订阅 int16 字段
    TestGmcSubscribe(g_stmt, g_connSub, g_vertexInt16SubPath, g_vertexSubName, user_data1, true);

    // 高版本订阅 int16 字段
    TestGmcSubscribe(g_stmt, g_connSub1, g_vertexInt16SubPath1, g_vertexSubName1, user_data2, false);

    // 低版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, 0, GMC_OPERATION_INSERT);

    // 高版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_endNum, g_endNum * 2, 1, GMC_OPERATION_INSERT);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 005.低、高版本订阅uint32字段和int32字段
TEST_F(Upgrade_CondSub, SN_036_Upgrade_CondSub_005)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    // 低版本订阅 uint32 和 int32 字段
    TestGmcSubscribe(g_stmt, g_connSub, g_vertexUint32AndInt32SubPath, g_vertexSubName, user_data1, true);

    // 高版本订阅 uint32 和 int32 字段
    TestGmcSubscribe(g_stmt, g_connSub1, g_vertexUint32AndInt32SubPath1, g_vertexSubName1, user_data2, false);

    // 低版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, 0, GMC_OPERATION_INSERT);

    // 高版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_endNum, g_endNum * 2, 1, GMC_OPERATION_INSERT);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 006.低、高版本订阅char字段或int32字段
TEST_F(Upgrade_CondSub, SN_036_Upgrade_CondSub_006)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    // 低版本订阅 char 或 int32 字段
    TestGmcSubscribe(g_stmt, g_connSub, g_vertexInt32OrCharSubPath, g_vertexSubName, user_data1, true);

    // 高版本订阅 char 或 int32 字段
    TestGmcSubscribe(g_stmt, g_connSub1, g_vertexInt32OrCharSubPath1, g_vertexSubName1, user_data2, false);

    // 低版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, 0, GMC_OPERATION_INSERT);

    // 高版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_endNum, g_endNum * 2, 1, GMC_OPERATION_INSERT);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 007.低版本订阅uint32字段，高版本订阅int32字段
TEST_F(Upgrade_CondSub, SN_036_Upgrade_CondSub_007)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    // 低版本订阅 uint32 字段
    TestGmcSubscribe(g_stmt, g_connSub, g_vertexUint32SubPath, g_vertexSubName, user_data1, true);

    // 高版本订阅 int32 字段
    TestGmcSubscribe(g_stmt, g_connSub1, g_vertexInt32SubPath1, g_vertexSubName1, user_data2, false);

    // 低版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, 0, GMC_OPERATION_INSERT);

    // 高版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_endNum, g_endNum * 2, 1, GMC_OPERATION_INSERT);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 008.低版本订阅int32字段，高版本订阅char字段
TEST_F(Upgrade_CondSub, SN_036_Upgrade_CondSub_008)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    // 低版本订阅 int32 字段
    TestGmcSubscribe(g_stmt, g_connSub, g_vertexInt32SubPath, g_vertexSubName, user_data1, true);

    // 高版本订阅 char 字段
    TestGmcSubscribe(g_stmt, g_connSub1, g_vertexCharSubPath1, g_vertexSubName1, user_data2, false);

    // 低版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, 0, GMC_OPERATION_INSERT);

    // 高版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_endNum, g_endNum * 2, 1, GMC_OPERATION_INSERT);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_INSERT, g_endNum * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}

// 009.低版本订阅int16字段，高版本订阅新增字段
TEST_F(Upgrade_CondSub, SN_036_Upgrade_CondSub_009)
{
    AW_FUN_Log(LOG_STEP, "START\n");

    int ret = 0;

    // 高版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_endNum, g_endNum * 2, 1, GMC_OPERATION_INSERT);
    TestPkScan(g_stmt, g_vertexLabelName, g_endNum, g_endNum * 2, 1);

    // 低版本订阅 int32 字段
    TestGmcSubscribe(g_stmt, g_connSub, g_vertexInt16SubPath, g_vertexSubName, user_data1, true);

    // 高版本订阅新增字段
    TestGmcSubscribe(g_stmt, g_connSub1, g_vertexInt64SubPath, g_vertexSubName1, user_data2, false);

    // 低版本执行 insert 操作
    TestWriteVertex(g_stmt, g_vertexLabelName, g_startNum, g_endNum, 0, GMC_OPERATION_INSERT);

    // 高版本执行 update 操作
    TestUpdateVertex(g_stmt, g_vertexLabelName, g_endNum, g_endNum * 2, 1, GMC_OPERATION_UPDATE);

    // 校验推送次数
    ret = testWaitSnRecv(user_data1, GMC_SUB_EVENT_INSERT, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(user_data2, GMC_SUB_EVENT_UPDATE, g_endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, g_vertexSubName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END\n");
}
