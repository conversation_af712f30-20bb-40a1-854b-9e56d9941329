[{"comment": "标准下一跳表，对应42#表", "version": "2.0", "type": "record", "name": "T42", "config": {"check_validity": true}, "fields": [{"name": "nhp_index", "type": "uint32", "nullable": false}, {"name": "next_hop", "type": "uint32", "nullable": false}, {"name": "out_if_index", "type": "uint32", "nullable": false}, {"name": "vr_id", "type": "uint32", "nullable": true}, {"name": "vrf_index", "type": "uint32", "nullable": true}, {"name": "flags", "type": "uint32", "nullable": true}, {"name": "if_type", "type": "uint32", "nullable": true}, {"name": "iid_flags", "type": "uint32", "nullable": true}, {"name": "work_if_index", "type": "uint32", "nullable": true}, {"name": "app_source_id", "type": "uint32", "nullable": true}, {"name": "group_smooth_id", "type": "uint32", "nullable": true}, {"name": "app_obj_id", "type": "uint64", "nullable": true}, {"name": "app_version", "type": "uint32", "nullable": true}, {"name": "attr_flag", "type": "uint32", "nullable": true}, {"name": "fwd_if_type", "type": "uint16", "nullable": true}, {"name": "reserved", "type": "uint16", "nullable": true}, {"name": "time_stamp_create", "type": "time", "nullable": true}, {"name": "time_stamp_smooth", "type": "time", "nullable": true}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nhp_std", "fields": ["nhp_index", "next_hop", "out_if_index"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "nhpindex_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["nhp_index"], "constraints": {"unique": false}, "comment": "根据nhp_index索引"}, {"name": "ip_ifindex_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["next_hop", "out_if_index"], "constraints": {"unique": false}, "comment": "根据出接口和下一跳查询"}, {"name": "ifindex_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["out_if_index"], "constraints": {"unique": false}, "comment": "根据出接口查询"}, {"name": "iftype_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["if_type"], "constraints": {"unique": false}, "comment": "根据出接口类型查询"}, {"name": "work_if_index_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["work_if_index"], "constraints": {"unique": false}, "comment": "根据work_if_index查询"}]}]