[{"type": "record", "name": "<PERSON><PERSON><PERSON><PERSON>", "schema_version": 3, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "uint8", "nullable": false}, {"name": "F7", "type": "time", "nullable": true}, {"name": "F8", "type": "fixed", "nullable": false, "size": 16}, {"name": "F9", "type": "string", "size": 20, "nullable": true}, {"name": "F10", "type": "uint8: 5", "nullable": false, "default": "0x1f"}, {"name": "F11", "type": "uint16: 10", "nullable": false, "default": "0x3ff"}, {"name": "F12", "type": "uint32", "nullable": false}, {"name": "F13", "type": "uint32", "nullable": false}, {"name": "T1", "type": "record", "vector": true, "fields": [{"name": "F0", "type": "int64", "nullable": true}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "string", "size": 100, "nullable": true}, {"name": "F3", "type": "fixed", "nullable": false, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "F4", "type": "bytes", "nullable": false, "size": 10, "default": "0xffffffffffffffffffff"}, {"name": "T2", "type": "record", "vector": true, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "string", "size": 20, "nullable": true}, {"name": "F5", "type": "fixed", "size": 7, "nullable": true}]}]}, {"name": "R1", "type": "record", "fields": [{"name": "F14", "type": "int64", "nullable": true}, {"name": "F15", "type": "uint64", "nullable": true}, {"name": "F16", "type": "int32", "nullable": true}, {"name": "F17", "type": "uint32", "nullable": true}, {"name": "F18", "type": "int16", "nullable": true}, {"name": "F19", "type": "uint16", "nullable": true}, {"name": "F20", "type": "int8", "nullable": true}, {"name": "F21", "type": "uint8", "nullable": true}, {"name": "F22", "type": "time", "nullable": true}, {"name": "F23", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F24", "type": "uint8: 5", "nullable": true}, {"name": "F25", "type": "uint16: 10", "nullable": true}, {"name": "F26", "type": "uint32: 17", "nullable": true}, {"name": "F27", "type": "uint64: 33", "nullable": true}, {"name": "F28", "type": "boolean", "nullable": true}, {"name": "F29", "type": "float", "nullable": true}, {"name": "F30", "type": "double", "nullable": true}, {"name": "F32", "type": "bytes", "nullable": true}, {"name": "F33", "type": "string", "nullable": true}]}, {"name": "F34", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F35", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F36", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F37", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F38", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F39", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F40", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F41", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F42", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F43", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F44", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F45", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F46", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F47", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F48", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F49", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F50", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F51", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F52", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F53", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F54", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F55", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F56", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F57", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F58", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F59", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F60", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F61", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F62", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F63", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F64", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F65", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F66", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F67", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F68", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F69", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F70", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F71", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F72", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F73", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F74", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F75", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F76", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F77", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F78", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F79", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F80", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F81", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F82", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F83", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F84", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F85", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F86", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F87", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F88", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F89", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F90", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F91", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F92", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F93", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F94", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F95", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F96", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F97", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F98", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F99", "type": "fixed", "nullable": true, "size": 13312}, {"name": "F100", "type": "fixed", "nullable": true, "size": 13312}], "keys": [{"name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "hashcluster_unique_key", "index": {"type": "hashcluster"}, "fields": ["F1", "F2"], "constraints": {"unique": true}}, {"name": "localhash_key", "fields": ["F4", "F5"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"name": "local_key", "fields": ["F3"], "index": {"type": "local"}, "constraints": {"unique": false}}, {"node": "T1", "name": "member_key", "index": {"type": "none"}, "fields": ["F0"], "constraints": {"unique": true}}, {"node": "T1/T2", "name": "member_key", "index": {"type": "none"}, "fields": ["F3"], "constraints": {"unique": false}}]}]