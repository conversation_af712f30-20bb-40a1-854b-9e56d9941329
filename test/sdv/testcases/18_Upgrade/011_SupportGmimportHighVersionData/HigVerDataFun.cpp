/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: HigVerDataFun.cpp
 * Description: 
 * Author: yang<PERSON><PERSON> ywx1060383
 * Create: 2025-04-27
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "HigVerData.h"
#include "t_datacom_lite.h"

using namespace std;

class HigVerDataFun : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh ");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn, &g_stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void HigVerDataFun::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void HigVerDataFun::TearDown()
{
    AW_CHECK_LOG_END();
}

// 001.创建简单表，表新增定长字段，表升级，写数据，导出数据，清空数据，表降级，导入高版本数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertex1.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/vertex1upgradeV1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int version = 1;
    for (int64_t i = 0; i < 10; i++) {
        ret = recordInsertVertex(g_stmt, labelName, i, version);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/vertex.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.创建一般复杂表，根节点新增定长字段，表升级，写数据，导出数据，清空数据，表降级，导入高版本数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex2";
    readJanssonFile("schemaFile/vertex2.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/vertex2upgradeV1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int version = 1;
    for (int64_t i = 0; i < 10; i++) {
        ret = recordInsertGeneralVertex(g_stmt, labelName, i, version);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/vertex2.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.创建一般复杂表，根节点新增变长字段，表升级，写数据，导出数据，清空数据，表降级，导入高版本数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex2";
    readJanssonFile("schemaFile/vertex2.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/vertex3upgradeV1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int version = 1;
    int addField = 2;
    for (int64_t i = 0; i < 10; i++) {
        ret = recordInsertGeneralVertex(g_stmt, labelName, i, version, addField);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/vertex2.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.创建一般复杂表，根节点新增record node，表升级，写数据，导出数据，清空数据，表降级，导入高版本数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex2";
    readJanssonFile("schemaFile/vertex2.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/vertex4upgradeV1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int version = 1;
    int addField = 3;
    for (int64_t i = 0; i < 10; i++) {
        ret = recordInsertGeneralVertex(g_stmt, labelName, i, version, addField);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/vertex2.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 10, successNum: 10, duplicateNum: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.创建一般复杂表，node节点新增定长字段，表升级，写数据，导出数据，清空数据，表降级，导入高版本数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex2";
    readJanssonFile("schemaFile/vertex2.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/vertex5upgradeV1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int version = 1;
    int addField = 4;
    for (int64_t i = 0; i < 10; i++) {
        ret = recordInsertGeneralVertex(g_stmt, labelName, i, version, addField);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/vertex2.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 10, successNum: 10, duplicateNum: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.创建一般复杂表，node节点新增变长字段，表升级，写数据，导出数据，清空数据，表降级，导入高版本数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex2";
    readJanssonFile("schemaFile/vertex2.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/vertex6upgradeV1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int version = 1;
    int addField = 5;
    for (int64_t i = 0; i < 10; i++) {
        ret = recordInsertGeneralVertex(g_stmt, labelName, i, version, addField);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/vertex2.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 10, successNum: 10, duplicateNum: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.创建特殊复杂表，根节点新增变长字段，表升级，写数据，导出数据，清空数据，表降级，导入高版本数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "specialLabel";
    readJanssonFile("schemaFile/SpecialTableSchema.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/SpecialTableSchemaUpgrade.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/specialLabel.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 10, successNum: 10, duplicateNum: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.创建特殊复杂表，根节点新增record node，表升级，写数据，导出数据，清空数据，表降级，导入高版本数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "specialLabel";
    readJanssonFile("schemaFile/SpecialTableSchema.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/SpecialTableSchemaUpgrade2.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    int addField = 3;
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg, bytesValue, stringValue, true, addField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/specialLabel.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 10, successNum: 10, duplicateNum: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.创建特殊复杂表，node节点新增变长字段，表升级，写数据，导出数据，清空数据，表降级，导入高版本数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "specialLabel";
    readJanssonFile("schemaFile/SpecialTableSchema.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/SpecialTableSchemaUpgrade3.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t startValue = 0;
    uint32_t endValue = 10;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtSpeciallabelCfgT vertexCfg = {startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_INSERT};
    int addField = 2;
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfg, bytesValue, stringValue, true, addField);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/specialLabel.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 10, successNum: 10, duplicateNum: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.创建表，表升级2次，写数据，导出数据，清空数据，表降级1次，导入高版本数据，清空数据，表降低第二次，导入高版本数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertex1.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/vertex1upgradeV1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schemaUpdatePath2 = (char *)"./schemaFile/vertex1upgradeV2.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath2, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int version = 2;
    for (int64_t i = 0; i < 10; i++) {
        ret = recordInsertVertex(g_stmt, labelName, i, version);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级V1");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 1, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/vertex.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级V0");
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.创建表未进行升级，gmimport  -ignore high_version_data 导入不存在的字段数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertex1.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "导入不存在的字段数据");
    char importPath[] = "./exportFile/vertex_011.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 1, successNum: 1, duplicateNum: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.创建表，表升级，写数据，导出数据，清空数据，表降级，导入高版本不存在的字段数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertex1.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/vertex1upgradeV1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int version = 1;
    for (int64_t i = 0; i < 10; i++) {
        ret = recordInsertVertex(g_stmt, labelName, i, version);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/vertex_012.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 1, successNum: 1, duplicateNum: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.gmimport  -IGNORE high_version_data 导入高版本数据，预期成功
TEST_F(HigVerDataFun, Upgrade_011_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertex1.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/vertex1upgradeV1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int version = 1;
    for (int64_t i = 0; i < 10; i++) {
        ret = recordInsertVertex(g_stmt, labelName, i, version);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/vertex.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -IGNORE high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.gmimport  -ignore HIGH_VERSION_DATA 导入高版本数据，预期报错
TEST_F(HigVerDataFun, Upgrade_011_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertex1.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/vertex1upgradeV1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int version = 1;
    for (int64_t i = 0; i < 10; i++) {
        ret = recordInsertVertex(g_stmt, labelName, i, version);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入高版本数据");
    char importPath[] = "./exportFile/vertex.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore HIGH_VERSION_DATA -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "unexpected params of options (\"-ignore\") HIGH_VERSION_DATA.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// FMEA数据文件丢失/损坏
// 015.创建表，表升级，写数据，导出数据，清空数据，表降级，导入损坏的数据文件，预期导入失败
TEST_F(HigVerDataFun, Upgrade_011_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char *schemaFile = NULL;
    char labelName[] = "vertex";
    readJanssonFile("schemaFile/vertex1.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    ret = GmcCreateVertexLabel(g_stmt, schemaFile, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    AW_FUN_Log(LOG_STEP, "表升级");
    char *expectValue = (char *)"upgrade successfully";
    char *schemaUpdatePath = (char *)"./schemaFile/vertex1upgradeV1.gmjson";
    ret = TestUpdateVertexLabel(schemaUpdatePath, expectValue, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int version = 1;
    for (int64_t i = 0; i < 10; i++) {
        ret = recordInsertVertex(g_stmt, labelName, i, version);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "导出高版本数据");
    char exportPath[] = "./exportFile";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -ns %s -s %s", g_toolPath, labelName,
        exportPath, g_testNameSpace, g_connServer);
    ret = executeCommand(g_command, "export file successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "Truncate数据");
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "表降级");
    char *expectValueDown = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName, 0, expectValueDown, (char *)"sync");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "导入损坏的数据文件");
    char importPath[] = "./exportFile/vertex_015.gmdata";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -t %s -f %s -ignore high_ver_data -ns %s -s %s",
        g_toolPath, labelName, importPath, g_testNameSpace, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ret = 1004006");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
