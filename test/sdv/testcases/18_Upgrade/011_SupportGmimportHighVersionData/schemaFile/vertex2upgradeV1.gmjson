[{"type": "record", "name": "vertex2", "schema_version": 1, "fields": [{"name": "F0", "type": "uint8", "nullable": false}, {"name": "F1", "type": "int8", "nullable": true}, {"name": "F2", "type": "uint16", "nullable": true}, {"name": "F3", "type": "int16", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "int32", "nullable": true}, {"name": "F6", "type": "uint64", "nullable": true}, {"name": "F7", "type": "int64", "nullable": true}, {"name": "F8", "type": "uchar", "nullable": true}, {"name": "F9", "type": "char", "nullable": true}, {"name": "F10", "type": "float", "nullable": true}, {"name": "F11", "type": "double", "nullable": true}, {"name": "F12", "type": "boolean", "nullable": true}, {"name": "F13", "type": "time", "nullable": true}, {"name": "F14", "type": "string", "nullable": true}, {"name": "T1", "type": "record", "vector": true, "fields": [{"name": "A0", "type": "uint8", "nullable": true}, {"name": "A1", "type": "int8", "nullable": true}, {"name": "A2", "type": "uint16", "nullable": true}, {"name": "A3", "type": "int16", "nullable": true}, {"name": "A4", "type": "uint32", "nullable": true}, {"name": "A5", "type": "int32", "nullable": true}, {"name": "A6", "type": "uint64", "nullable": true}, {"name": "A7", "type": "int64", "nullable": true}, {"name": "A8", "type": "uchar", "nullable": true}, {"name": "A9", "type": "char", "nullable": true}, {"name": "A10", "type": "float", "nullable": true}, {"name": "A11", "type": "double", "nullable": true}, {"name": "A12", "type": "boolean", "nullable": true}, {"name": "A13", "type": "time", "nullable": true}, {"name": "A14", "type": "string", "nullable": true}, {"name": "A15", "type": "fixed", "nullable": true, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "A16", "type": "bytes", "nullable": true, "size": 10, "default": "0xffffffffffffffffffff"}, {"name": "U1", "type": "record", "vector": true, "fields": [{"name": "B0", "type": "uint8", "nullable": true}, {"name": "B1", "type": "int8", "nullable": true}, {"name": "B2", "type": "uint16", "nullable": true}, {"name": "B3", "type": "int16", "nullable": true}, {"name": "B4", "type": "uint32", "nullable": true}, {"name": "B5", "type": "int32", "nullable": true}, {"name": "B6", "type": "uint64", "nullable": true}, {"name": "B7", "type": "int64", "nullable": true}, {"name": "B8", "type": "uchar", "nullable": true}, {"name": "B9", "type": "char", "nullable": true}, {"name": "B10", "type": "float", "nullable": true}, {"name": "B11", "type": "double", "nullable": true}, {"name": "B12", "type": "boolean", "nullable": true}, {"name": "B13", "type": "time", "nullable": true}, {"name": "B14", "type": "string", "nullable": true}, {"name": "B15", "type": "fixed", "nullable": true, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "B16", "type": "bytes", "nullable": true, "size": 10, "default": "0xffffffffffffffffffff"}]}]}, {"name": "T2", "type": "record", "array": true, "size": 3, "fields": [{"name": "C0", "type": "uint8", "nullable": true}, {"name": "C1", "type": "int8", "nullable": true}, {"name": "C2", "type": "uint16", "nullable": true}, {"name": "C3", "type": "int16", "nullable": true}, {"name": "C4", "type": "uint32", "nullable": true}, {"name": "C5", "type": "int32", "nullable": true}, {"name": "C6", "type": "uint64", "nullable": true}, {"name": "C7", "type": "int64", "nullable": true}, {"name": "C8", "type": "uchar", "nullable": true}, {"name": "C9", "type": "char", "nullable": true}, {"name": "C10", "type": "float", "nullable": true}, {"name": "C11", "type": "double", "nullable": true}, {"name": "C12", "type": "boolean", "nullable": true}, {"name": "C13", "type": "time", "nullable": true}, {"name": "C14", "type": "string", "nullable": true}, {"name": "C15", "type": "fixed", "nullable": true, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "C16", "type": "bytes", "nullable": true, "size": 10, "default": "0xffffffffffffffffffff"}, {"name": "U2", "type": "record", "array": true, "size": 2, "fields": [{"name": "D0", "type": "uint8", "nullable": true}, {"name": "D1", "type": "int8", "nullable": true}, {"name": "D2", "type": "uint16", "nullable": true}, {"name": "D3", "type": "int16", "nullable": true}, {"name": "D4", "type": "uint32", "nullable": true}, {"name": "D5", "type": "int32", "nullable": true}, {"name": "D6", "type": "uint64", "nullable": true}, {"name": "D7", "type": "int64", "nullable": true}, {"name": "D8", "type": "uchar", "nullable": true}, {"name": "D9", "type": "char", "nullable": true}, {"name": "D10", "type": "float", "nullable": true}, {"name": "D11", "type": "double", "nullable": true}, {"name": "D12", "type": "boolean", "nullable": true}, {"name": "D13", "type": "time", "nullable": true}, {"name": "D14", "type": "string", "nullable": true}, {"name": "D15", "type": "fixed", "nullable": true, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "D16", "type": "bytes", "nullable": true, "size": 10, "default": "0xffffffffffffffffffff"}]}]}, {"name": "F15", "type": "uint8", "nullable": true}, {"name": "F16", "type": "int8", "nullable": true}, {"name": "F17", "type": "uint16", "nullable": true}, {"name": "F18", "type": "int16", "nullable": true}, {"name": "F19", "type": "uint32", "nullable": true}, {"name": "F20", "type": "int32", "nullable": true}, {"name": "F21", "type": "uint64", "nullable": true}, {"name": "F22", "type": "int64", "nullable": true}, {"name": "F23", "type": "uchar", "nullable": true}, {"name": "F24", "type": "char", "nullable": true}, {"name": "F25", "type": "float", "nullable": true}, {"name": "F26", "type": "double", "nullable": true}, {"name": "F27", "type": "boolean", "nullable": true}, {"name": "F28", "type": "time", "nullable": true}], "keys": [{"node": "vertex2", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "hashcluster_key", "index": {"type": "hashcluster"}, "fields": ["F1", "F2"], "constraints": {"unique": false}}, {"name": "localhash_key", "fields": ["F4", "F5"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"name": "local_key", "fields": ["F3"], "index": {"type": "local"}, "constraints": {"unique": false}}]}]