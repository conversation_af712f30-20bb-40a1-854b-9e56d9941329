[{"name": "T1", "type": "record", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "uint16"}, {"name": "F2", "type": "uint32"}, {"name": "F3", "type": "uint16"}, {"name": "F4", "type": "uint16"}, {"name": "F5", "type": "uint32"}, {"name": "F6", "type": "uint16"}, {"name": "F7", "type": "uint16"}, {"name": "F8", "type": "uint16"}], "keys": [{"node": "T1", "name": "T1_PK", "index": {"type": "primary"}, "fields": ["F0"], "constraints": {"unique": true}, "config": {"hash_type": "cceh", "init_hash_capacity": 10}, "comment": "cceh模式主键索引"}, {"node": "T1", "name": "T1_TK", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F0", "F2", "F4", "F6", "F8"], "constraints": {"unique": true}, "config": {"hash_type": "cceh", "init_hash_capacity": 10}, "comment": "cceh模式local索引"}]}]