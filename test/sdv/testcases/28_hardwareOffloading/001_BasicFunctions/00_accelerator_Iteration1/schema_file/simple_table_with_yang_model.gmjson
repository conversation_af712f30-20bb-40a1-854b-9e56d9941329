[{"type": "list", "name": "T0", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "boolean", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "boolean", "nullable": false}, {"name": "F4", "type": "int32", "nullable": true}], "keys": [{"node": "T0", "name": "PK", "fields": ["PID", "F0", "F1", "F2"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T0", "name": "localhash_key_1", "fields": ["PID", "F3", "F4"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}]}]