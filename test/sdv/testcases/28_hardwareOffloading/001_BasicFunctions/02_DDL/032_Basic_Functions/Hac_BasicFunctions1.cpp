/*****************************************************************************
 Description  : 支持基础能力（连接、断连、增删改查）
 Notes        : 1:GmcGetPrimaryKeyName 设置stmt 为NULL
                2:GmcGetPrimaryKeyName 设置primaryKeyName为NULL
                3:GmcGetPrimaryKeyName  设置stmt 为NULL，设置primaryKeyName为NULL
                4:建表带primary Key，创建vertex并open，调用GmcGetPrimaryKeyName获取primary Key的name
                5:建表不带primary Key，创建vertex并open，调用GmcGetPrimaryKeyName获取primary Key的name，预期失败
                6:预置primary Key的name最大长度（127字节），获取primary Key的name
                7:预置primary Key的name最大长度（128字节），循环100万次获取primary Key的name，测试性能
                8:最大连接数时，获取primary Key的name ,预期成功
                9:工具导入建表，获取获取primary Key的name
                10:创建vertex并open，调用GmcGetPrimaryKeyName获取primary Key的name，同步做insert操作，
                update，delete 根据primary Key的name，查询vertex的属性值

 History      :
 Author       : jiangdingshan
 Modification :
 Date         : 2021/03/24
*****************************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include <sys/time.h>
#include "../../common/hash_util.h"

#define PK_NAME_LENGTH 128
#ifdef ENV_SUSE
#define MAX_CONN MAX_CONN_SIZE - 1
#else
#define MAX_CONN MAX_CONN_SIZE
#endif

int ret = 0;
GmcConnT *conn;
GmcStmtT *stmt;
char const *label_name = "T39";
char const *label_name_all_type = "T39_all_type";
char const *label_name_no_pk = "T39_all_type_no_PK";
char const *label_name_long_pk = "T39_all_type_longpk";

char const *g_label_config = "{\"max_record_count\":1000}";
char const *g_label_config_1 = "{\"max_record_count\":1000}";
char const *g_label_config_2 = "{\"max_record_count\":1000}";

class Basic_Functions : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Basic_Functions::SetUpTestCase()
{
    // 配置相关环境变量及重启server
    InitCfg();
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void Basic_Functions::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复配置文件
    RecoverCfg();
}

void Basic_Functions::SetUp()
{
    AW_ADD_ERR_WHITE_LIST(2, "GMERR-1004005", "GMERR-1009013");
    char *test_schema = NULL;
    readJanssonFile("schema_file/test_SetProperty_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(stmt, label_name);
    ret = GmcCreateVertexLabel(stmt, test_schema, g_label_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(test_schema);
    AW_CHECK_LOG_BEGIN();
}
void Basic_Functions::TearDown()
{
    AW_CHECK_LOG_END();
    ret = GmcDropVertexLabel(stmt, label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// GmcGetPrimaryKeyName 设置stmt1 为NULL
TEST_F(Basic_Functions, HardWare_Offloading_001_DDL_032_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcStmtT *stmt1 = NULL;
    char primaryKeyName[PK_NAME_LENGTH] = {'\0'};
    ret = GmcGetPrimaryKeyName(stmt1, primaryKeyName, sizeof(primaryKeyName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// GmcGetPrimaryKeyName 设置primaryKeyName为NULL
TEST_F(Basic_Functions, HardWare_Offloading_001_DDL_032_002)  // 该接口需要前置INSERT操作
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *primaryKeyName = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t i = 0;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &i, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char a[4] = "qaa";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, a, strlen(a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetPrimaryKeyName(stmt, primaryKeyName, sizeof(primaryKeyName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// GmcGetPrimaryKeyName 设置stmt1 为NULL,设置primaryKeyName为NULL
TEST_F(Basic_Functions, HardWare_Offloading_001_DDL_032_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcStmtT *stmt1 = NULL;
    char *primaryKeyName = NULL;
    ret = GmcGetPrimaryKeyName(stmt1, primaryKeyName, sizeof(primaryKeyName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//建表带primary Key，创建vertex并open，调用GmcGetPrimaryKeyName获取primaryKey的name
TEST_F(Basic_Functions, HardWare_Offloading_001_DDL_032_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    void *vertexLabel = NULL;
    uint32_t value = 100;
    char *teststr = (char *)"testver";
    char *prepkname = (char *)"T39_K0";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char primaryKeyName[PK_NAME_LENGTH] = {'\0'};
    ret = GmcGetPrimaryKeyName(stmt, primaryKeyName, sizeof(primaryKeyName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(6, strlen(primaryKeyName));
    EXPECT_STRCASEEQ(prepkname, primaryKeyName);  //判断字符串是否相同

    //设置属性
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //插入顶点
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, primaryKeyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    bool isEof = false;
    int F0V;
    bool isNull;
    while (!isEof) {
        ret = GmcFetch(stmt, &isEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isEof == true) {
            break;
        }
        ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(int), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_MACRO_EXPECT_EQ_INT(100, F0V);
    }
    GmcResetStmt(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//建表不带primary Key，创建vertex并open，调用GmcGetPrimaryKeyName获取primaryKey的name，预期失败
TEST_F(Basic_Functions, HardWare_Offloading_001_DDL_032_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    void *vertexLabel = NULL;
    char *test_schema_no_pk = NULL;
    char *teststr = (char *)"testver";
    char primaryKeyName[PK_NAME_LENGTH] = {'\0'};

    readJanssonFile("schema_file/test_all_type_schema_no_pk.gmjson", &test_schema_no_pk);
    ASSERT_NE((void *)NULL, test_schema_no_pk);
    ret = GmcCreateVertexLabel(stmt, test_schema_no_pk, g_label_config_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(test_schema_no_pk);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(stmt, label_name_no_pk, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetPrimaryKeyName(stmt, primaryKeyName, sizeof(primaryKeyName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(stmt, label_name_no_pk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//预置primary Key的name最大长度（128字节），获取primary Key的name
TEST_F(Basic_Functions, HardWare_Offloading_001_DDL_032_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    void *vertexLabel = NULL;
    char *test_schema_long_pk = NULL;
    char *prepkname = (char *)"T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_"
                              "ON_T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_";
    char primaryKeyName[PK_NAME_LENGTH] = {'\0'};

    readJanssonFile("schema_file/test_all_type_longpk_schema.gmjson", &test_schema_long_pk);
    ASSERT_NE((void *)NULL, test_schema_long_pk);
    ret = GmcCreateVertexLabel(stmt, test_schema_long_pk, g_label_config_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(test_schema_long_pk);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(stmt, label_name_long_pk, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetPrimaryKeyName(stmt, primaryKeyName, sizeof(primaryKeyName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(128, strlen(primaryKeyName) + 1);
    EXPECT_STRCASEEQ(prepkname, primaryKeyName);  //判断字符串是否相同

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(stmt, label_name_long_pk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//预置primary Key的name最大长度（128字节），循环100万次获取primary Key的name，测试性能
TEST_F(Basic_Functions, HardWare_Offloading_001_DDL_032_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    struct timeval start, end;
    double timeUsed;
    void *vertexLabel = NULL;
    char *test_schema_long_pk = NULL;
    char *prepkname = (char *)"T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_"
                              "ON_T39_K0_ON_T39_K0_ON_T39_K0_ON_T39_K0_";
    char primaryKeyName[PK_NAME_LENGTH] = {'\0'};

    readJanssonFile("schema_file/test_all_type_longpk_schema.gmjson", &test_schema_long_pk);
    ASSERT_NE((void *)NULL, test_schema_long_pk);
    ret = GmcCreateVertexLabel(stmt, test_schema_long_pk, g_label_config_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(test_schema_long_pk);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(stmt, label_name_long_pk, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //记录开始时间
    gettimeofday(&start, NULL);
    for (int i = 0; i < 1000000; i++) {
        ret = GmcGetPrimaryKeyName(stmt, primaryKeyName, sizeof(primaryKeyName));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(128, strlen(primaryKeyName) + 1);
    EXPECT_STRCASEEQ(prepkname, primaryKeyName);  //判断字符串是否相同
    //记录最后查询结束时间
    gettimeofday(&end, NULL);

    timeUsed = (end.tv_sec - start.tv_sec) + (end.tv_usec - start.tv_usec) / 1000000.0;
#ifdef ENV_RTOSV2X
    EXPECT_GE(0.5 * 5, timeUsed);
#else
    EXPECT_GE(0.5, timeUsed);  // val1 >= val2则成功，否则失败
#endif

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(stmt, label_name_long_pk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//最大连接数时，获取primary Key的name ,预期成功
TEST_F(Basic_Functions, HardWare_Offloading_001_DDL_032_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    Basic_Functions::TearDown();

    GmcConnT *conn_t[MAX_CONN] = {0};
    GmcStmtT *stmt_t[MAX_CONN] = {0};
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    void *vertexLabel = NULL;
    uint32_t value = 100;
    char *teststr = (char *)"testver";
    char *prepkname = (char *)"T39_K0";

    char *test_schema = NULL;
    readJanssonFile("schema_file/test_SetProperty_schema.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);

    uint32_t currentConn = 0;
    ret = testGetConnNum(&currentConn);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    for (int i = 0; i < MAX_CONN - 1 - currentConn; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt1, test_schema, g_label_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(test_schema);
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(stmt1, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char primaryKeyName[PK_NAME_LENGTH] = {'\0'};
    ret = GmcGetPrimaryKeyName(stmt1, primaryKeyName, sizeof(primaryKeyName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(6, strlen(primaryKeyName));
    EXPECT_STRCASEEQ(prepkname, primaryKeyName);  //判断字符串是否相同

    //设置属性
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //插入顶点
    ret = GmcExecute(stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt1, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1, primaryKeyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isEof = false;
    int F0V;
    bool isNull;
    while (!isEof) {
        ret = GmcFetch(stmt1, &isEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isEof == true) {
            break;
        }
        ret = GmcGetVertexPropertyByName(stmt1, "F0", &F0V, sizeof(int), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_MACRO_EXPECT_EQ_INT(100, F0V);
    }
    GmcResetStmt(stmt1);

    ret = GmcDropVertexLabel(stmt1, label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < MAX_CONN - 1 - currentConn; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    Basic_Functions::SetUp();
    AW_FUN_Log(LOG_STEP, "test end.");
}

//工具导入建表，获取获取primary Key的name
TEST_F(Basic_Functions, HardWare_Offloading_001_DDL_032_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
#ifdef FEATURE_PERSISTENCE
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
#endif

    GmcStmtT *stmt1 = NULL;
    void *vertexLabel = NULL;
    char *prepkname = (char *)"T39_K0";
    const char *schema_path = "./gmimport_files";
    char cmd[1024];
    snprintf(
        cmd, 1024, "%s/gmimport -c cache -f %s  -s %s -ns %s", g_toolPath, schema_path, g_connServer, g_testNameSpace);
    sleep(1);
    system(cmd);

    // 创建一个stmt，同时创建 元数据
    ret = GmcAllocStmt(conn, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *schema_json = NULL;
    // 读取schema json
    readJanssonFile("./schema_file/test_all_type_schema.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // 接口创建同名VertexLabel
    char labelName[32] = "T39_all_type";
    ret = GmcCreateVertexLabel(stmt1, schema_json, g_label_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);  // 预期返回label已存在

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(stmt1, label_name_all_type, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char primaryKeyName[PK_NAME_LENGTH] = {'\0'};
    ret = GmcGetPrimaryKeyName(stmt1, primaryKeyName, sizeof(primaryKeyName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(6, strlen(primaryKeyName));
    EXPECT_STRCASEEQ(prepkname, primaryKeyName);  //判断字符串是否相同

    ret = GmcExecute(stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt1, label_name_all_type);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema_json);
    GmcResetStmt(stmt1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//创建vertex并open，调用GmcGetPrimaryKeyName获取primary Key的name，同步做insert操作，update，delete 根据primary
//Key的name，查询vertex的属性值
TEST_F(Basic_Functions, HardWare_Offloading_001_DDL_032_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    void *vertexLabel = NULL;
    uint32_t value = 100;
    char *teststr = (char *)"testver";
    char *prepkname = (char *)"T39_K0";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char primaryKeyName[PK_NAME_LENGTH] = {'\0'};
    ret = GmcGetPrimaryKeyName(stmt, primaryKeyName, sizeof(primaryKeyName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(6, strlen(primaryKeyName));
    EXPECT_STRCASEEQ(prepkname, primaryKeyName);  //判断字符串是否相同

    //设置属性
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, teststr, (strlen(teststr)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //插入顶点
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, primaryKeyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isEof = false;
    int F0V;
    bool isNull;
    while (!isEof) {
        ret = GmcFetch(stmt, &isEof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isEof == true) {
            break;
        }
        ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(int), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(false, isNull);
        AW_MACRO_EXPECT_EQ_INT(100, F0V);
    }
    GmcResetStmt(stmt);

    memset(primaryKeyName, 0, sizeof(primaryKeyName));

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetPrimaryKeyName(stmt, primaryKeyName, sizeof(primaryKeyName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(6, strlen(primaryKeyName));
    EXPECT_STRCASEEQ(prepkname, primaryKeyName);  //判断字符串是否相同
    memset(primaryKeyName, 0, sizeof(primaryKeyName));
    //设置属性
    // 2021.11.23: 主键不可更新
    char *teststr1 = (char *)"testver1";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, teststr1, (strlen(teststr1)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetPrimaryKeyName(stmt, primaryKeyName, sizeof(primaryKeyName));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(6, strlen(primaryKeyName));
    EXPECT_STRCASEEQ(prepkname, primaryKeyName);  //判断字符串是否相同
    memset(primaryKeyName, 0, sizeof(primaryKeyName));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
