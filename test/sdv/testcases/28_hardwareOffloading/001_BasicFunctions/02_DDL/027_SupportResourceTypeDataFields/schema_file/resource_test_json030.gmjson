[{"type": "record", "name": "TestResource030", "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "resource", "nullable": true}, {"name": "F4", "type": "resource", "nullable": true}], "keys": [{"name": "T35_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]