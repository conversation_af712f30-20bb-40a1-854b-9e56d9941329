{"comment": "协议报文优先级表项", "version": "2.0", "type": "record", "name": "qos_global_priority", "config": {"check_validity": false}, "max_record_count": 8, "fields": [{"name": "vrid", "type": "uint32", "comment": "虚拟路由"}, {"name": "type", "type": "uint32", "comment": "标识value是内部优先级还是报文优先级"}, {"name": "value", "type": "uint32", "comment": "协议报文优先级"}, {"name": "abFlag", "type": "uint32", "comment": "保留字段"}], "keys": [{"name": "qos_global_priority_key", "index": {"type": "primary"}, "node": "qos_global_priority", "fields": ["vrid", "type"], "constraints": {"unique": true}, "comment": "协议报文优先级"}]}