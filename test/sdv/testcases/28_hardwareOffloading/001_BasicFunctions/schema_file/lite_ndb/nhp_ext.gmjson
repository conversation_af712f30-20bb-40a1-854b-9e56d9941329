{"comment": "扩展下一跳表，对应43#表", "version": "2.0", "type": "record", "name": "nhp_ext", "config": {"check_validity": true}, "max_record_count": 4096000, "fields": [{"name": "iid_index", "type": "uint32", "comment": "下一跳索引"}, {"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "tvrf_index", "type": "uint32", "comment": "隧道所在vrf索引"}, {"name": "tunnel_id", "type": "uint32", "comment": "隧道索引"}, {"name": "tunnel_type", "type": "uint8", "comment": "隧道类型"}, {"name": "reserved0", "type": "uint8", "nullable": true, "comment": "保留字段0"}, {"name": "reserved1", "type": "uint16", "nullable": true, "comment": "保留字段1"}, {"name": "attr_flag", "type": "uint32", "comment": "公用字段"}, {"name": "status_high_prio", "type": "uint8", "default": 0}, {"name": "status_normal_prio", "type": "uint8", "default": 0}, {"name": "errcode_high_prio", "type": "uint8", "default": 0}, {"name": "errcode_normal_prio", "type": "uint8", "default": 0}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的NHP_EXT上下文"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的NHP_EXT上下文"}, {"name": "app_source_id", "type": "uint32"}, {"name": "group_smooth_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}, {"name": "time_stamp_create", "type": "time"}, {"name": "time_stamp_smooth", "type": "time"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nhp_ext", "fields": ["iid_index", "vr_id", "tvrf_index", "tunnel_id", "tunnel_type"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "iidindex_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_ext", "fields": ["iid_index"], "constraints": {"unique": false}, "comment": "根据iid_index索引"}, {"name": "tunnel_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_ext", "fields": ["vr_id", "tvrf_index", "tunnel_id", "tunnel_type"], "constraints": {"unique": false}, "comment": "根据隧道key索引"}, {"name": "sdwan_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_ext", "fields": ["tunnel_id", "tunnel_type"], "constraints": {"unique": false}, "comment": "根据sdwan隧道key索引"}]}