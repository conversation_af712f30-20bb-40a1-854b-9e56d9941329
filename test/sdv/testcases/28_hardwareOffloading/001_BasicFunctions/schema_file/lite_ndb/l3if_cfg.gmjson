{"version": "2.0", "type": "record", "name": "l3if_cfg", "config": {"check_validity": true}, "max_record_count": 64000, "fields": [{"name": "ifindex", "type": "uint32", "comment": "主键"}, {"name": "vr_id", "type": "uint32"}, {"name": "vrf_index", "type": "uint32"}, {"name": "l3if_key_type", "type": "uint32", "comment": "指定l3if_key类型"}, {"name": "l3if_key", "type": "fixed", "size": 16, "comment": "FWM_L3IF_KEY_S,包含联合体, 写该字段必须初始化全0"}, {"name": "ipv6_enable", "type": "uint32", "comment": "ipv6 enable 配置状态"}], "keys": [{"name": "l3if_pk", "index": {"type": "primary"}, "node": "l3if_cfg", "fields": ["ifindex"], "constraints": {"unique": true}}]}