{"version": "2.0", "type": "record", "name": "syslog_enable", "config": {"check_validity": false}, "max_record_count": 8, "fields": [{"name": "vsid", "type": "uint32", "comment": "vs id"}, {"name": "enable_flag", "type": "uint32", "default": 1, "comment": "syslog enable status"}, {"name": "reserve", "type": "uint32", "comment": "reserve field"}], "keys": [{"name": "syslog_enable_key", "index": {"type": "primary"}, "node": "syslog_enable", "fields": ["vsid"], "constraints": {"unique": true}, "comment": "primary key"}]}