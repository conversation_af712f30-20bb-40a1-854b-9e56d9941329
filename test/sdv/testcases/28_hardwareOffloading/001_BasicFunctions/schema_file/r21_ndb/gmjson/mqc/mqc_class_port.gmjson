{"version": "2.0", "type": "record", "name": "mqc_class_port", "config": {"check_validity": false}, "max_record_count": 2048, "fields": [{"name": "class_id", "type": "uint32", "comment": "流分类ID"}, {"name": "match_id", "type": "uint32", "comment": "匹配ID"}, {"name": "if_index", "type": "uint32", "comment": "接口索引"}, {"name": "is_phy_type", "type": "uint16", "comment": "是否为物理端口"}, {"name": "is_inbound", "type": "uint16", "comment": "是否是出入方向"}, {"name": "lag_id", "type": "uint32", "comment": "保留字段"}, {"name": "tb", "type": "uint16", "comment": "目标板"}, {"name": "tp", "type": "uint16", "comment": "目标端口"}], "keys": [{"name": "port_key", "index": {"type": "primary"}, "node": "mqc_class_port", "fields": ["class_id", "match_id", "if_index"], "constraints": {"unique": true}, "comment": "关键字名称"}], "super_fields": [{"name": "port_sf", "fields": ["class_id", "match_id", "if_index", "is_phy_type", "is_inbound", "lag_id", "tb", "tp"]}]}