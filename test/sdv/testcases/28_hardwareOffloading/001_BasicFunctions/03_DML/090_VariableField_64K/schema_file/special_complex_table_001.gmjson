[{"type": "record", "name": "TEST_SC_T1", "special_complex": true, "fields": [{"name": "f0", "type": "int64", "nullable": false}, {"name": "f1", "type": "uint64", "nullable": true}, {"name": "f2", "type": "int32", "nullable": true}, {"name": "f3", "type": "uint32", "nullable": true}, {"name": "f4", "type": "int16", "nullable": true}, {"name": "f5", "type": "uint16", "nullable": true}, {"name": "f6", "type": "int8", "nullable": true}, {"name": "f7", "type": "uint8", "nullable": true}, {"name": "f8", "type": "boolean", "nullable": true}, {"name": "f9", "type": "float", "nullable": true}, {"name": "f10", "type": "double", "nullable": true}, {"name": "f11", "type": "time", "nullable": true}, {"name": "f12", "type": "char", "nullable": true}, {"name": "f13", "type": "uchar", "nullable": true}, {"name": "f14", "type": "fixed", "size": 16, "nullable": true}, {"name": "f15", "type": "partition", "nullable": false}, {"name": "f16", "type": "string", "size": 65536, "nullable": true}, {"name": "f17", "type": "bytes", "size": 65536, "nullable": true}, {"name": "t1", "type": "record", "fields": [{"name": "p0", "type": "int64", "nullable": true}, {"name": "p1", "type": "uint64", "nullable": true}, {"name": "p2", "type": "int32", "nullable": true}, {"name": "p3", "type": "uint32", "nullable": true}, {"name": "p4", "type": "int16", "nullable": true}, {"name": "p5", "type": "uint16", "nullable": true}, {"name": "p6", "type": "int8", "nullable": true}, {"name": "p7", "type": "uint8", "nullable": true}, {"name": "p8", "type": "boolean", "nullable": true}, {"name": "p9", "type": "float", "nullable": true}, {"name": "p10", "type": "double", "nullable": true}, {"name": "p11", "type": "time", "nullable": true}, {"name": "p12", "type": "char", "nullable": true}, {"name": "p13", "type": "uchar", "nullable": true}, {"name": "p14", "type": "fixed", "size": 16, "nullable": true}, {"name": "p15", "type": "string", "size": 65536, "nullable": true}, {"name": "p16", "type": "bytes", "size": 65536, "nullable": true}]}, {"name": "t2", "type": "record", "fixed_array": true, "size": 1, "fields": [{"name": "a0", "type": "int64", "nullable": true}, {"name": "a1", "type": "uint64", "nullable": true}, {"name": "a2", "type": "int32", "nullable": true}, {"name": "a3", "type": "uint32", "nullable": true}, {"name": "a4", "type": "int16", "nullable": true}, {"name": "a5", "type": "uint16", "nullable": true}, {"name": "a6", "type": "int8", "nullable": true}, {"name": "a7", "type": "uint8", "nullable": true}, {"name": "a8", "type": "boolean", "nullable": true}, {"name": "a9", "type": "float", "nullable": true}, {"name": "a10", "type": "double", "nullable": true}, {"name": "a11", "type": "time", "nullable": true}, {"name": "a12", "type": "char", "nullable": true}, {"name": "a13", "type": "uchar", "nullable": true}, {"name": "a14", "type": "fixed", "size": 16, "nullable": true}, {"name": "a15", "type": "string", "size": 65536, "nullable": true}, {"name": "a16", "type": "bytes", "size": 65536, "nullable": true}]}, {"name": "t3", "type": "record", "vector": true, "size": 1024, "fields": [{"name": "v0", "type": "int64", "nullable": true}, {"name": "v1", "type": "uint64", "nullable": true}, {"name": "v2", "type": "int32", "nullable": true}, {"name": "v3", "type": "uint32", "nullable": true}, {"name": "v4", "type": "int16", "nullable": true}, {"name": "v5", "type": "uint16", "nullable": true}, {"name": "v6", "type": "int8", "nullable": true}, {"name": "v7", "type": "uint8", "nullable": true}, {"name": "v8", "type": "boolean", "nullable": true}, {"name": "v9", "type": "float", "nullable": true}, {"name": "v10", "type": "double", "nullable": true}, {"name": "v11", "type": "time", "nullable": true}, {"name": "v12", "type": "char", "nullable": true}, {"name": "v13", "type": "uchar", "nullable": true}, {"name": "v14", "type": "fixed", "size": 16, "nullable": true}, {"name": "v15", "type": "string", "size": 65536, "nullable": true}, {"name": "v16", "type": "bytes", "size": 65536, "nullable": true}]}], "keys": [{"node": "TEST_SC_T1", "name": "TEST_PK", "fields": ["f0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]