[{"version": "2.0", "type": "record", "name": "Full_SizeStr", "comment": "默认值str为主键,16k改爲13K", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "Default_StrSize", "type": "string", "nullable": true}, {"name": "Default_BytSize", "type": "bytes", "nullable": true}, {"name": "Less_Str16K", "type": "string", "size": 13300, "nullable": true}, {"name": "Less_Byt16K", "type": "bytes", "size": 13300, "nullable": true}, {"name": "Eq_Str16K", "type": "string", "size": 13312, "nullable": true}, {"name": "Eq_Byt16K", "type": "bytes", "size": 13312, "nullable": true}, {"name": "Eq_Str13K", "type": "string", "size": 13312, "nullable": true}, {"name": "Eq_Byt13K", "type": "bytes", "size": 13312, "nullable": true}, {"name": "Eq_Str8K", "type": "string", "size": 8192, "nullable": true}, {"name": "Eq_Byt8K", "type": "bytes", "size": 8192, "nullable": true}, {"name": "Eq_Str4K", "type": "string", "size": 4096, "nullable": true}, {"name": "Eq_Byt4K", "type": "bytes", "size": 4096, "nullable": true}, {"name": "Eq_Str256B", "type": "string", "size": 256, "nullable": true}, {"name": "Eq_Byt256B", "type": "bytes", "size": 256, "nullable": true}, {"name": "Eq_Str128B", "type": "string", "size": 128, "nullable": true}, {"name": "Eq_Byt128B", "type": "bytes", "size": 128, "nullable": true}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "Full_SizeStr", "fields": ["F0"], "constraints": {"unique": true}, "comment": "主键索引"}]}]