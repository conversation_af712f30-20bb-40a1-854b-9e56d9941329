[{"nftable_table_name": "test1", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0, "nftable_chain": [{"nftable_chain_name": "chain1", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain2", "nftable_chain_type": "type filter hook prerouting priority 20; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain3", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain4", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain5", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain6", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain7", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain8", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain9", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain10", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain11", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain12", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain13", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain14", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain15", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain16", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain17", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain18", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain19", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain20", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain21", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain22", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain23", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain24", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain25", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain26", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain27", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain28", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain29", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain30", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain31", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain32", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test1", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}]}, {"nftable_table_name": "test2", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0, "nftable_chain": [{"nftable_chain_name": "chain1", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain2", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain3", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain4", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain5", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain6", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain7", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain8", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain9", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain10", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain11", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain12", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain13", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain14", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain15", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain16", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain17", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain18", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain19", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain20", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain21", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain22", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain23", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain24", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain25", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain26", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain27", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain28", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain29", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain30", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain31", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain32", "nftable_chain_type": "type filter hook prerouting priority 10; policy accept;", "nftable_chain_table": "test2", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}]}, {"nftable_table_name": "test3", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0, "nftable_chain": [{"nftable_chain_name": "chain1", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain2", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain3", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain4", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain5", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain6", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain7", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain8", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain9", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain10", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain11", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain12", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain13", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain14", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain15", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain16", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain17", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain18", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain19", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain20", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain21", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain22", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain23", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain24", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain25", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain26", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain27", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain28", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain29", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain30", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0, "nftable_rule": [{"nftable_rule_handle": 33, "nftable_rule_position": 0, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"}, {"nftable_rule_handle": 34, "nftable_rule_position": 1, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"}, {"nftable_rule_handle": 35, "nftable_rule_position": 2, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"}, {"nftable_rule_handle": 36, "nftable_rule_position": 3, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"}, {"nftable_rule_handle": 37, "nftable_rule_position": 4, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"}, {"nftable_rule_handle": 38, "nftable_rule_position": 5, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"}, {"nftable_rule_handle": 39, "nftable_rule_position": 6, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"}, {"nftable_rule_handle": 40, "nftable_rule_position": 7, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************* oif LTE masquerade"}, {"nftable_rule_handle": 41, "nftable_rule_position": 8, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************0 oif LTE masquerade"}, {"nftable_rule_handle": 42, "nftable_rule_position": 9, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************1 oif LTE masquerade"}, {"nftable_rule_handle": 43, "nftable_rule_position": 10, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************2 oif LTE masquerade"}, {"nftable_rule_handle": 44, "nftable_rule_position": 11, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************3 oif LTE masquerade"}, {"nftable_rule_handle": 45, "nftable_rule_position": 12, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************4 oif LTE masquerade"}, {"nftable_rule_handle": 46, "nftable_rule_position": 13, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************5 oif LTE masquerade"}, {"nftable_rule_handle": 47, "nftable_rule_position": 14, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************6 oif LTE masquerade"}, {"nftable_rule_handle": 48, "nftable_rule_position": 15, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************7 oif LTE masquerade"}, {"nftable_rule_handle": 49, "nftable_rule_position": 16, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************8 oif LTE masquerade"}, {"nftable_rule_handle": 50, "nftable_rule_position": 17, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************9 oif LTE masquerade"}, {"nftable_rule_handle": 51, "nftable_rule_position": 18, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************0 oif LTE masquerade"}, {"nftable_rule_handle": 52, "nftable_rule_position": 19, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************1 oif LTE masquerade"}, {"nftable_rule_handle": 53, "nftable_rule_position": 20, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************2 oif LTE masquerade"}, {"nftable_rule_handle": 54, "nftable_rule_position": 21, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 55, "nftable_rule_position": 22, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************4 oif LTE masquerade"}, {"nftable_rule_handle": 56, "nftable_rule_position": 23, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************5 oif LTE masquerade"}, {"nftable_rule_handle": 57, "nftable_rule_position": 24, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************6 oif LTE masquerade"}, {"nftable_rule_handle": 58, "nftable_rule_position": 25, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************7 oif LTE masquerade"}, {"nftable_rule_handle": 59, "nftable_rule_position": 26, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************8 oif LTE masquerade"}, {"nftable_rule_handle": 60, "nftable_rule_position": 27, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 61, "nftable_rule_position": 28, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 62, "nftable_rule_position": 29, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 63, "nftable_rule_position": 30, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 64, "nftable_rule_position": 31, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 65, "nftable_rule_position": 32, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-*************4 oif LTE masquerade"}, {"nftable_rule_handle": 66, "nftable_rule_position": 33, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 67, "nftable_rule_position": 34, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 68, "nftable_rule_position": 35, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 69, "nftable_rule_position": 36, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 70, "nftable_rule_position": 37, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 71, "nftable_rule_position": 38, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 72, "nftable_rule_position": 39, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 73, "nftable_rule_position": 40, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 74, "nftable_rule_position": 41, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 75, "nftable_rule_position": 42, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 76, "nftable_rule_position": 43, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 77, "nftable_rule_position": 44, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 78, "nftable_rule_position": 45, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}, {"nftable_rule_handle": 79, "nftable_rule_position": 46, "nftable_rule_table": "test3", "nftable_rule_chain": "chain30", "nftable_rule_description": "ip saddr *************-************** oif LTE masquerade"}]}, {"nftable_chain_name": "chain31", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain32", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test3", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}]}, {"nftable_table_name": "test4", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0, "nftable_chain": [{"nftable_chain_name": "chain1", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain2", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain3", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain4", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain5", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain6", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain7", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain8", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain9", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain10", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain11", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain12", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain13", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain14", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain15", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain16", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain17", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain18", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain19", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain20", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain21", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain22", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain23", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain24", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain25", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain26", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain27", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain28", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain29", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain30", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain31", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}, {"nftable_chain_name": "chain32", "nftable_chain_type": "type nat hook postrouting priority 30; policy accept;", "nftable_chain_table": "test4", "nftable_chain_family": "", "nftable_chain_policy": 0, "nftable_chain_hooknum": 0, "nftable_chain_prio": 0, "nftable_chain_use": 0, "nftable_chain_handle": "0", "nftable_chain_basechainflag": 0, "nftable_chain_policyflag": 0, "nftable_chain_devflag": 0}]}, {"nftable_table_name": "test5", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test6", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test7", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test8", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test9", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test10", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test11", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test12", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test13", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test14", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test15", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test16", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test17", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test18", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test19", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test20", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test21", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test22", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test23", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test24", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test25", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test26", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test27", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test28", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test29", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test30", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test31", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test32", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test33", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test34", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test35", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test36", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test37", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test38", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test39", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test40", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test41", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test42", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test43", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test44", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test45", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test46", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test47", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test48", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test49", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test50", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test51", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test52", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test53", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test54", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test55", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test56", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test57", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test58", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test59", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test60", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test61", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test62", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test63", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test64", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test65", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test66", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test67", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test68", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test69", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test70", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test71", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test72", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test73", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test74", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test75", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test76", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test77", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test78", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test79", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test80", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test81", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test82", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test83", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test84", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test85", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test86", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test87", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test88", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test89", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test90", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test91", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test92", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test93", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test94", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test95", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test96", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test97", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test98", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test99", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test100", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test101", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test102", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test103", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test104", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test105", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test106", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test107", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test108", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test109", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test110", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test111", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test112", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test113", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test114", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test115", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test116", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test117", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test118", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test119", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test120", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test121", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test122", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test123", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test124", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test125", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test126", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test127", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}, {"nftable_table_name": "test128", "nftable_table_family": "", "nftable_table_flags": 0, "nftable_table_use": 0}]