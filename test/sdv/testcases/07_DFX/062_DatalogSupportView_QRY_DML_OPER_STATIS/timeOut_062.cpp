/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : dalalog支持QRY_DML_INFO视图统计
 Notes        : 001.输入表为table，含trisanent（tuple)）a是过期字段且为主键，无回调，a记录过期，查询a字段记录删除

                002.输入表为table，a是过期字段不为主键，有回调，a记录过期，2s后查询a字段过期，回调查看表记录，a已被删除，查视图，插入数据查视图

                003.可更新表，a是过期字段，有回调，a记录过期，2s后查询a字段过期，回调查看表记录，能查到該过期记录，查视图

                004.timeout 回调 udf sleep 导致服务端挂死，预期无法查询视图
 History      :
 Author       : youwanyong ywx1157510
 Modification : 2022/10/22
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <atomic>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "QRY_DML_OPER_STATIS_interface.h"

class DatalogSupportView_QRY_DML_OPER_STATIS_timeOut : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DatalogSupportView_QRY_DML_OPER_STATIS_timeOut::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret;
    // 创建连接

    g_conn = NULL;
    g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 修改配置项
    system(" gmadmin -cfgName enableDmlOperStat -cfgval 1");
    AW_CHECK_LOG_BEGIN();
}
void DatalogSupportView_QRY_DML_OPER_STATIS_timeOut::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.输入表为table，含trisanent（tuple)）a是过期字段且为主键，无回调，a记录过期，查询a字段记录删
TEST_F(DatalogSupportView_QRY_DML_OPER_STATIS_timeOut, DFX_062_Timeout_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "timeout";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA[] = "A";
    char tableB[] = "B";
    int64_t records[][4] = {{1, 1, 4000, +1}, {2, 2, 4000, +1}, {3, 3, 4000, +1}, {4, 4, 4000, +1}, {5, 5, 4000, +1}, {6, 6, 4000, +1}};

    EXPECT_EQ(GMERR_OK,
        InsertTimeoutInt64Field(g_conn, g_stmt, tableA, records, sizeof(records) / sizeof(records[0]), false));

    // 查A表
    ScanOutTableThreeInt64Field(g_stmt, tableA);

    // 查B表
    ScanOutTableThreeInt64Field(g_stmt, tableB);

    // 视图校验
    queryView("A", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 1", "FAIL_COUNT: 0", "TOTAL_COUNT: 1");
    queryView("B", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");

    // sleep 等待过期
    AW_FUN_Log(LOG_STEP, "等待过期");
    sleep(10);
    ScanOutTableThreeInt64Field(g_stmt, tableA);

    ScanOutTableThreeInt64Field(g_stmt, tableB);
    // 视图校验
    queryView("A", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 2", "FAIL_COUNT: 0", "TOTAL_COUNT: 2");
    queryView("B", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.输入表为table，a是过期字段不为主键，有回调，a记录过期，2s后查询a字段过期，回调查看表记录，a已被删除，查视图，插入数据查视图
TEST_F(DatalogSupportView_QRY_DML_OPER_STATIS_timeOut, DFX_062_Timeout_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "timeout02";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA[] = "A";
    char tableB[] = "B";
    TableThreeMixedFieldTimeout records[6];
    for (uint32_t i = 0; i < sizeof(records) / sizeof(records[0]); i++) {
        TableThreeMixedFieldTimeout *obj = &records[i];
        obj->a = i;
        Fill2UintArray(obj->b, false);
        BuildNewString((char *)"hello world", (char **)&obj->c, i);
        obj->t = 10;
        obj->dtlReservedCount = 1;
    }
    queryView("A", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");
    queryView("B", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");
    EXPECT_EQ(GMERR_OK, InsertInpTableThreeMixedFieldTimeout(
                            g_conn, g_stmt, tableA, records, sizeof(records) / sizeof(records[0]), false));
    ScanOutTableThreeMixedFieldTimeout(g_stmt, tableA);
    ScanOutTableThreeMixedFieldTimeout(g_stmt, tableA);
    for (uint32_t i = 0; i < sizeof(records) / sizeof(records[0]); i++) {
        free(records[i].c);
    }
    // 等待流控队列处理结束
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    sleep(10);
    // 视图校验
    queryView("A", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 2", "FAIL_COUNT: 0", "TOTAL_COUNT: 2");
    queryView("B", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.可更新表，a是过期字段，有回调，a记录过期，2s后查询a字段过期，回调查看表记录，能查到該过期记录，查视图
TEST_F(DatalogSupportView_QRY_DML_OPER_STATIS_timeOut, DFX_062_Timeout_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "timeout03";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA[] = "A";
    char tableB[] = "B";
    TableThreeMixedFieldTimeout records[6];
    for (uint32_t i = 0; i < sizeof(records) / sizeof(records[0]); i++) {
        TableThreeMixedFieldTimeout *obj = &records[i];
        obj->a = i;
        Fill2UintArray(obj->b, false);
        BuildNewString((char *)"hello world", (char **)&obj->c, i);
        obj->t = 10;
        obj->dtlReservedCount = 1;
    }
    queryView("A");
    queryView("B");
    EXPECT_EQ(GMERR_OK, InsertInpTableThreeMixedFieldTimeout(
                            g_conn, g_stmt, tableA, records, sizeof(records) / sizeof(records[0]), false));
    ScanOutTableThreeMixedFieldTimeout(g_stmt, tableA);
    ScanOutTableThreeMixedFieldTimeout(g_stmt, tableB);
    for (uint32_t i = 0; i < sizeof(records) / sizeof(records[0]); i++) {
        free(records[i].c);
    }
    // 等待流控队列处理结束
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    sleep(10);
    // 视图校验
    queryView("A", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 2", "FAIL_COUNT: 0", "TOTAL_COUNT: 2");
    queryView("B", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.timeout 回调 udf sleep 导致服务端挂死，预期无法查询视图
TEST_F(DatalogSupportView_QRY_DML_OPER_STATIS_timeOut, DFX_062_Timeout_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char nsName[128] = "timeout04";

    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA[] = "A";
    char tableB[] = "B";
    TableThreeMixedFieldTimeout records[6];
    for (uint32_t i = 0; i < sizeof(records) / sizeof(records[0]); i++) {
        TableThreeMixedFieldTimeout *obj = &records[i];
        obj->a = i;
        Fill2UintArray(obj->b, false);
        BuildNewString((char *)"hello world", (char **)&obj->c, i);
        obj->t = 10;
        obj->dtlReservedCount = 1;
    }
    queryView("A", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");
    queryView("B", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");
    EXPECT_EQ(GMERR_OK, InsertInpTableThreeMixedFieldTimeout(
                            g_conn, g_stmt, tableA, records, sizeof(records) / sizeof(records[0]), false));
    ScanOutTableThreeMixedFieldTimeout(g_stmt, tableA);
    ScanOutTableThreeMixedFieldTimeout(g_stmt, tableA);
    for (uint32_t i = 0; i < sizeof(records) / sizeof(records[0]); i++) {
        free(records[i].c);
    }
    // 等待流控队列处理结束
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    sleep(100);
    // 视图校验
    queryView("A", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 2", "FAIL_COUNT: 0", "TOTAL_COUNT: 2");
    queryView("B", "DML_TYPE: INSERT_VERTEX", "SUCCESS_COUNT: 0", "FAIL_COUNT: 0", "TOTAL_COUNT: 0");

    // 卸载datalog.so
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
