/*  版权所有 (c) 华为技术有限公司 2021-2022 */
#include "gm_udf.h"

#pragma pack(1)

typedef struct Func {
    int32_t count;
    int32_t a;
    int32_t b;
} Func;

typedef struct FuncAggIn {
    int32_t count;
    int32_t a;
    int32_t b;
} FuncAggIn;

typedef struct FuncAggOut {
    int32_t c;
    int32_t d;
} FuncAggOut;

#pragma pack(0)

int32_t dtl_agg_func_ns3_funcA(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t sum1 = inpStruct->a;
    int32_t sum2 = inpStruct->b;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        sum1 += inpStruct->a;
        sum2 += inpStruct->b;
    }
    outStruct->c = sum1;
    outStruct->d = sum2;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    return GMERR_OK;
}

int32_t dtl_ext_func_ns6_func(void *tuple, GmUdfCtxT *ctx)
{
    Func *funcB = (Func *)tuple;
    funcB->b = funcB->a + 1;
    return GMERR_OK;
}
