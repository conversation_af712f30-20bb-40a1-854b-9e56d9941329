/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2017-2027. All rights reserved.
 * File Name: syCommon.h
 * Author: yaosiyuan ywx758883
 * Date: 2021-5-31
 * Describle:
 */
#ifndef _SYCOMMON_H
#define _SYCOMMON_H

#include <stdarg.h>
#include <semaphore.h>
#include "gtest/gtest.h"
#include "syCommon.h"
#include "t_datacom_lite.h"

#ifdef __cplusplus
extern "C" {
#endif

#define RECORDCOUNTSTART 0
#define RECORDCOUNTEND 1000
#define THREADRECORDNUM 2000
#define GMIMPORT (char *)"gmimport"
#define GMEXPORT (char *)"gmexport"
#define GMSYSVIEW (char *)"gmsysview"
#define FULLTABLE 0xff
#define LOG_LEVEL 1

sem_t g_semSub;
int g_time = 0;
uint32_t g_subPushCount[10];
uint32_t g_sizeMalloc = 1000;
int g_subIndex = 0, g_maxRecordCount;
int g_chanRingLen = 256;
GmcConnT *g_conn = NULL;
GmcConnT *g_conn_sub = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_sub = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
void *g_vertexLabel = NULL;
AsyncUserDataT g_data = {0};
SnUserDataT *g_userData;
char *g_subConnName = (char *)"subConnName";
typedef void *(*thread_func)(void *);

typedef enum tagRunMode { MODE_EULER = 0, MODE_DAP = 1, MODE_HONGMENG = 2 } GtRunModeE;

#define COMPARE_NE(expect_value, actual_value)                                \
    do {                                                                      \
        if ((expect_value) == (actual_value)) {                               \
            printf("[Error file: %s, line: %d]\n", __FILE__, __LINE__);       \
            printf("Value of: " #actual_value " = %p\n", (actual_value));     \
            printf("Not Expected: " #expect_value " = %p\n", (expect_value)); \
            return -1;                                                        \
        };                                                                    \
    } while (0)

#define TEST_INFO(format, ...)                      \
    do {                                            \
        if (LOG_LEVEL >= 2) {                       \
            fprintf(stdout,                         \
                "["                                 \
                "Test"                              \
                "]["                                \
                "Info"                              \
                "]: File:%s Func: %s Lineno:%d. ",  \
                __FILE__, __func__, __LINE__);      \
            fprintf(stdout, format, ##__VA_ARGS__); \
            fprintf(stdout, "\n");                  \
        }                                           \
    } while (0)

#define TEST_ERROR(log, args...)                                                                \
    do {                                                                                        \
        fprintf(stdout, "Error: pid-%d %s:%d " log "\n", getpid(), __FILE__, __LINE__, ##args); \
    } while (0)

#define CHECK_AND_BREAK(ret, log, args...)                                                             \
    if ((ret) != GMERR_OK) {                                                                           \
        fprintf(stdout, "Error: %s:%d " log " failed, ret = %d\n", __FILE__, __LINE__, ##args, (ret)); \
        break;                                                                                         \
    }

// schema 拼装的字符串开头和结尾
const char *schemaJsonHead =
    R"([{
    "type":"record",
    "name":"partition_test",
    "fields":
        [
            {"name":"F0", "type":"int32"},
    )";

const char *schemaAllTypes =
    R"([{
    "version":"2.0",
    "type":"record",
    "name":"schema_datatype",
    "fields":[
        { "name":"F1", "type":"uint32", "default":1 },
        { "name":"F2", "type":"uint8", "default":1 },
        { "name":"F3", "type":"int16", "default":1 },
        { "name":"F4", "type":"uint16", "default":1 },
        { "name":"F5", "type":"int32", "default":1 },
        { "name":"F6", "type":"uint32", "default":1 },
        { "name":"F7", "type":"int64", "default":1 },
        { "name":"F8", "type":"uint64", "default":1 },
        { "name":"F9", "type":"int8", "default":1 },
        { "name":"F10", "type":"double", "nullable":true, "default":1 },
        { "name":"F11", "type":"time", "nullable":true, "default":1 },
        { "name":"F12", "type":"char", "nullable":true, "default":"1" },
        { "name":"F13", "type":"uchar", "nullable":true, "default":"1" },
        { "name":"F14", "type":"string","size":1024, "nullable":true, "default":"1" },
        { "name":"F15", "type":"bytes","size":7, "nullable":true, "default":"1" },
        { "name":"F16", "type":"fixed","size":7, "nullable":true, "default":"1111111" },
        { "name":"F17", "type":"uint8: 4", "nullable":true, "default":"0x01" },
        { "name":"F18", "type":"uint16: 4", "nullable":true, "default":"0x0f" },
        { "name":"F19", "type":"uint32: 8", "nullable":true, "default":"0xff" },
        { "name":"F20", "type":"uint64: 16", "nullable":true, "default":"0xffff" },
        { "name":"F21", "type":"fixed","size":532 },
        { "name":"F22", "type":"float", "nullable":true, "default":1 },
        { "name":"vr_id", "type":"uint32", "default":1 },
        { "name":"vrf_index", "type":"uint32", "default":1 },
        { "name":"dest_ip_addr", "type":"uint32", "default":1 },
        { "name":"mask_len", "type":"uint8", "default":1 },
        { "name":"dest_ip_addr6", "type":"fixed", "size": 16, "default":"1111111111111111" }
    ],
    "keys":
        [
            {
                "node":"schema_datatype",
                "name":"PK",
                "fields":["F1"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }]
)";

const char *schemaAllTypes1 =
    R"([{
    "version":"2.0",
    "type":"record",
    "name":"schema_datatype1",
    "fields":[
        { "name":"F1", "type":"uint32", "default":1 },
        { "name":"F2", "type":"uint8", "default":1 },
        { "name":"F3", "type":"int16", "default":1 },
        { "name":"F4", "type":"uint16", "default":1 },
        { "name":"F5", "type":"int32", "default":1 },
        { "name":"F6", "type":"uint32", "default":1 },
        { "name":"F7", "type":"int64", "default":1 },
        { "name":"F8", "type":"uint64", "default":1 },
        { "name":"F9", "type":"int8", "default":1 },
        { "name":"F10", "type":"double", "nullable":true, "default":1 },
        { "name":"F11", "type":"time", "nullable":true, "default":1 },
        { "name":"F12", "type":"char", "nullable":true, "default":"1" },
        { "name":"F13", "type":"uchar", "nullable":true, "default":"1" },
        { "name":"F14", "type":"string","size":1024, "nullable":true, "default":"1" },
        { "name":"F15", "type":"bytes","size":7, "nullable":true, "default":"1" },
        { "name":"F16", "type":"fixed","size":7, "nullable":true, "default":"1111111" },
        { "name":"F17", "type":"uint8: 4", "nullable":true, "default":"0x01" },
        { "name":"F18", "type":"uint16: 4", "nullable":true, "default":"0x0f" },
        { "name":"F19", "type":"uint32: 8", "nullable":true, "default":"0xff" },
        { "name":"F20", "type":"uint64: 16", "nullable":true, "default":"0xffff" },
        { "name":"F21", "type":"fixed","size":532 },
        { "name":"F22", "type":"float", "nullable":true, "default":1 },
        { "name":"vr_id", "type":"uint32", "default":1 },
        { "name":"vrf_index", "type":"uint32", "default":1 },
        { "name":"dest_ip_addr", "type":"uint32", "default":1 },
        { "name":"mask_len", "type":"uint8", "default":1 },
        { "name":"dest_ip_addr6", "type":"fixed", "size": 16, "default":"1111111111111111" }
    ],
    "keys":
        [
            {
                "node":"schema_datatype1",
                "name":"PK",
                "fields":["F1"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }]
)";

const char *schemaBigObj =
    R"([{
    "version":"2.0",
    "type":"record",
    "name":"schema_datatype",
    "fields":[
        { "name":"F1", "type":"uint32", "default":1 },
        { "name":"F2", "type":"uint8", "default":1 },
        { "name":"F3", "type":"int16", "default":1 },
        { "name":"F4", "type":"uint16", "default":1 },
        { "name":"F5", "type":"int32", "default":1 },
        { "name":"F6", "type":"uint32", "default":1 },
        { "name":"F7", "type":"int64", "default":1 },
        { "name":"F8", "type":"uint64", "default":1 },
        { "name":"F9", "type":"int8", "default":1 },
        { "name":"F10", "type":"double", "nullable":true, "default":1 },
        { "name":"F11", "type":"time", "nullable":true, "default":1 },
        { "name":"F12", "type":"char", "nullable":true, "default":"1" },
        { "name":"F13", "type":"uchar", "nullable":true, "default":"1" },
        { "name":"F14", "type":"string","size":1024, "nullable":true, "default":"1" },
        { "name":"F15", "type":"bytes","size":7, "nullable":true, "default":"1" },
        { "name":"F16", "type":"fixed","size":7, "nullable":true, "default":"1111111" },
        { "name":"F17", "type":"uint8: 4", "nullable":true, "default":"0x01" },
        { "name":"F18", "type":"uint16: 4", "nullable":true, "default":"0x0f" },
        { "name":"F19", "type":"uint32: 8", "nullable":true, "default":"0xff" },
        { "name":"F20", "type":"uint64: 16", "nullable":true, "default":"0xffff" },
        { "name":"F22", "type":"float", "nullable":true, "default":1 },
        { "name":"F23", "type":"string" },
        { "name":"F24", "type":"string" },
        { "name":"F25", "type":"string" },
        { "name":"F26", "type":"string" },
        { "name":"vr_id", "type":"uint32", "default":1 },
        { "name":"vrf_index", "type":"uint32", "default":1 },
        { "name":"dest_ip_addr", "type":"uint32", "default":1 },
        { "name":"mask_len", "type":"uint8", "default":1 },
        { "name":"dest_ip_addr6", "type":"fixed", "size": 16, "default":"1111111111111111" }
    ],
    "keys":[
)";

const char *schemaAllTypesTail =
    R"(
        ]
}]
)";

const char *schemaJsonTail =
    R"(
        ],
    "keys":
        [
            {
                "node":"VertexLabel",
                "name":"PK",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }])";

const char *subAllType =
    R"({
    "name":"subVertexLabel",
    "label_name":"schema_datatype",
    "comment":"VertexLabel subscription",
    "events":
        [
            {"type":"insert","msgTypes":["new object","old object"]},
            {"type":"delete","msgTypes":["new object","old object"]},
            {"type":"age","msgTypes":["new object","old object"]},
            {"type":"update","msgTypes":["new object","old object"]},
            {"type":"replace","msgTypes":["new object","old object"]}
        ],
    "retry":true,
    "is_reliable":true
})";

const char *deltalStore =
    R"({
    "version": "1.0",
    "delta_stores": [{
        "name": "ds1",
        "init_mem_size": 8192,
        "max_mem_size": 3276800,
        "extend_mem_size": 8192,
        "page_size": 8192
    }
    ]
}
)";

// 申请内存
void mallocSubData(SnUserDataT **userData, uint32_t sizeMalloc)
{
    SnUserDataT *mallocData = NULL;
    mallocData = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(mallocData, 0, sizeof(SnUserDataT));
    mallocData->new_value = (int *)malloc(sizeof(int) * sizeMalloc);
    memset(mallocData->new_value, 0, sizeof(int) * sizeMalloc);
    mallocData->old_value = (int *)malloc(sizeof(int) * sizeMalloc);
    memset(mallocData->old_value, 0, sizeof(int) * sizeMalloc);
    mallocData->isReplace_insert = (bool *)malloc(sizeof(bool) * sizeMalloc);
    memset(mallocData->isReplace_insert, 0, sizeof(bool) * sizeMalloc);
    *userData = mallocData;
}

// 释放malloc申请的内存
void freeMallocSqace(SnUserDataT *userData)
{
    free(userData->new_value);
    free(userData->old_value);
    free(userData->isReplace_insert);
    free(userData);
}

// 配置stlm日志
int GtSetStlmLog(bool isDisableSuppres)
{
    if (g_runMode != MODE_DAP) {
        return GMERR_OK;
    }
    int ret;
    if (isDisableSuppres) {
        // 修改日志抑制条件 (使stlm日志不容易被抑制)
        ret = system("/usr/local/bin/stlmbox --logsuppressparamset --suppress_level 1 --suppress_interval 1 "
                     "--suppress_threshhold 4098 --suppress_timecount 60");
        return ret;
    } else {
        // 恢复日志抑制条件
        ret = system("/usr/local/bin/stlmbox --logsuppressparamset --suppress_level 0 --suppress_interval 1 "
                     "--suppress_threshhold 500 --suppress_timecount 3600");
        return ret;
    }
    return GMERR_OK;
}

int compare_file_content(char *expect_file_path, char *actual_file_path, int lenth = 3)
{
    int ret = 0;
    char *expect_value = NULL;
    ret = readJanssonFile(expect_file_path, &expect_value);
    COMPARE_NE((char *)NULL, expect_value);

    char *actual_value = NULL;
    ret = readJanssonFile(actual_file_path, &actual_value);
    COMPARE_NE((char *)NULL, (char *)actual_value);

    ret = strncmp(expect_value, actual_value, lenth);
    if (ret != 0) {
        printf("[Error file: %s, line: %d]\n", __FILE__, __LINE__);
        printf("Value of: actual_value : \n%s\n", actual_value);
        printf("Expected: expect_value : \n%s\n", expect_value);
    };

    free(expect_value);
    free(actual_value);
    return ret;
}

// 功能同strcat, 增加支持格式化入参
int GtStrcat(char *dest, size_t dest_max, const char *src, ...)
{
    int ret;
    errno = 0;
    char *tmpSrc = (char *)malloc(dest_max);

    va_list args;
    va_start(args, src);
    ret = vsnprintf(tmpSrc, dest_max, src, args);
    if (ret <= 0) {
        TEST_INFO("call vsnprintf failed, ret = %d, errno = %d, %s\n", ret, errno, strerror(errno));
        va_end(args);
        free(tmpSrc);
        return ret;
    }
    va_end(args);

    strncat(dest, tmpSrc, dest_max);
    if (errno != GMERR_OK) {
        TEST_INFO("call strncat failed, errno = %d, %s", errno, strerror(errno));
        free(tmpSrc);
        return errno;
    }
    free(tmpSrc);
    return GMERR_OK;
}

// schema拼接后建表
int spliceSchemaCreateTable(GmcStmtT *stmt, char *schemaString, int expected = GMERR_OK, char *config = NULL)
{
    int ret = 0;
    int32_t jsonLen = 1024 * 200;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    ret = GtStrcat(schemaJson, jsonLen, schemaAllTypes);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaString, 12, " ", 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaAllTypesTail);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    ret = GmcCreateVertexLabel(stmt, schemaJson, config);
    EXPECT_EQ(expected, ret);
    free(schemaJson);
    return ret;
}

// [out] result: 执行系统调用的结果, 使用结束后必须调用free()释放内存
int ExecSystemCmd(char **result, const char *format, ...)
{
    int ret;
    errno = 0;
    va_list args;
    va_start(args, format);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), format, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d, %s.", ret, strerror(errno));
        va_end(args);
        return -1;
    }
    va_end(args);

    TEST_INFO("cmd = \"%s\"", cmd);
    FILE *fd = popen(cmd, "r");
    if (fd == NULL) {
        TEST_ERROR("popen failed, %s.", strerror(errno));
        return -1;
    }

    // XXX 优化为动态获取流长度
    int size = 1024 * 100;
    char *tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        TEST_ERROR("malloc failed, %s.", strerror(errno));
        return -1;
    }
    memset(tmpResult, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat(tmpResult, buf);
    }

    ret = pclose(fd);
    if (ret == -1) {
        TEST_ERROR("pclose failed, %s.", strerror(errno));
        free(tmpResult);
        return -1;
    }
    *result = tmpResult;
    return GMERR_OK;
}

// [out] result: 执行系统调用的结果, 使用结束后必须调用free()释放内存
int GtExecSysviewCmd(char **result, const char *viewName, const char *filter = "", ...)
{
    int ret;
    va_list args;
    va_start(args, filter);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), filter, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d.", ret);
        va_end(args);
        return -1;
    }
    va_end(args);

    char *buf = NULL;
    ret = ExecSystemCmd(&buf, "%s/gmsysview -s %s -q '%s' %s", g_toolPath, g_connServer, viewName, cmd);
    if (ret != GMERR_OK) {
        TEST_ERROR("exec system cmd failed, ret = %d.", ret);
        free(buf);
        buf = NULL;
        return ret;
    }

    TEST_INFO("sysview result = \"%s\"", buf);
    *result = buf;
    return GMERR_OK;
}

int checkSysviewInfo(const char *sysview, const char *filter, int32_t *actualValue)
{
    int ret = 0;
    char *result = NULL;
    ret = GtExecSysviewCmd(&result, sysview, filter);
    if (ret != GMERR_OK) {
        TEST_ERROR("execute sysview command failed, ret = %d", ret);
        return FAILED;
    }
    *actualValue = atoi(result);
    free(result);
    result = NULL;
    return ret;
}

int sysviewTimeInfo(int32_t writeNum)
{
    int ret = 0;
    int32_t writeCount = 0;
    const char *sysview = (char *)"V$CLT_PROCESS_TIME_CONSUMPTION";
    char filter[128] = "| grep  WRITE_COUNT | awk 'NR==1{print $2}'";
    (void)sprintf(filter, "-f PID=%d | grep  WRITE_COUNT | awk 'NR==1{print $2}'", getpid());
    ret = checkSysviewInfo(sysview, filter, &writeCount);
    EXPECT_EQ(GMERR_OK, ret);
    if (writeCount != RECORDCOUNTSTART) {  // 当写请求视图处于间隔周期，不校验预期
        EXPECT_EQ(writeNum, writeCount);
    }
    // 视图查询相同进程信息
    int32_t totalTime = 0;
    char filter1[128] = "| grep  TOTAL_TIME | awk 'NR==1{print $2}'";
    (void)sprintf(filter1, "-f PID=%d | grep  TOTAL_TIME | awk 'NR==1{print $2}'", getpid());
    ret = checkSysviewInfo(sysview, filter1, &totalTime);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t averageTime = 0;
    char filter2[128] = "| grep  AVERAGE_TIME | awk 'NR==1{print $2}'";
    (void)sprintf(filter2, "-f PID=%d | grep  AVERAGE_TIME | awk 'NR==1{print $2}'", getpid());
    ret = checkSysviewInfo(sysview, filter2, &averageTime);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_LE(averageTime * writeCount, totalTime);
    return ret;
}

// 创建订阅关系
int createSubscribe(char *file, GmcStmtT *stmt, char *subName, GmcConnT *conn,
    void (*snCallBack)(GmcStmtT *, const GmcSubMsgInfoT *, void *))
{
    int ret = 0;
    char *schema = NULL;
    readJanssonFile(file, &schema);
    EXPECT_NE((void *)NULL, schema);
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = schema;
    ret = GmcSubscribe(stmt, &tmp_schema, conn, snCallBack, g_userData);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    return ret;
}

// 建表
int createVertexLabel(char *file, GmcStmtT *stmt, char *config, int expected = GMERR_OK)
{
    int ret = 0;
    char *schema = NULL;
    readJanssonFile(file, &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(stmt, schema, config);
    EXPECT_EQ(expected, ret);
    free(schema);
    return ret;
}

int createVertexLabelAsync(
    GmcStmtT *stmt, const char *file, const char *config, void (*callBack)(void *, Status, const char *))
{
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(stmt, schemaAllTypes, NULL, callBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    return ret;
}

// 异步删表
int DropVertexLabelAsync(GmcStmtT *stmt, const char *labelName, void (*callBack)(void *, Status, const char *))
{
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = GmcDropVertexLabelAsync(stmt, labelName, callBack, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    return ret;
}

// 建边
int createEdgeLabel(char *file, GmcStmtT *stmt, const char *config)
{
    int ret = 0;
    char *schema = NULL;
    readJanssonFile(file, &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateEdgeLabel(stmt, schema, config);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    return ret;
}

// 创建deltastore
int createDeltastore(char *file, GmcStmtT *stmt)
{
    int ret = 0;
    char *schema = NULL;
    readJanssonFile(file, &schema);
    EXPECT_NE((void *)NULL, schema);
    free(schema);
    return ret;
}

// 工具导入
int toolModelOperation(const char *toolMode, const char *cmdType, const char *fileName, const char *labelName)
{
    int ret = 0;
    char cmd[512];
    if (strcmp(toolMode, (char *)"gmimport") == 0) {
        snprintf(cmd, 512, "%s/gmimport -c %s -f %s", g_toolPath, cmdType, fileName);
    } else if (strcmp(toolMode, (char *)"gmexport") == 0) {
        snprintf(cmd, 512, "%s/gmexport -c %s -t %s", g_toolPath, cmdType, labelName);
    } else if (strcmp(toolMode, (char *)"gmsysview") == 0) {
        snprintf(cmd, 512, "%s/gmsysview %s", g_toolPath, cmdType);
    }
    ret = system(cmd);
    return ret;
}

int checkPartition(GmcStmtT *stmt, char *labelName, uint8_t partitionStart, uint8_t partitionEnd)
{
    int ret = 0;
    bool isAbnormal = false;
    for (uint8_t i = partitionStart; i < partitionEnd; i++) {
        ret = GmcBeginCheck(stmt, labelName, i);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcEndCheck(stmt, labelName, i, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

// 事务
int syTransStart(GmcConnT *conn, char model)
{
    int ret = 0;
    GmcTxConfigT transcction_config;
    transcction_config.readOnly = false;
    transcction_config.transMode = model;
    transcction_config.type = GMC_TX_ISOLATION_COMMITTED;
    transcction_config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &transcction_config);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

int gmsysview_popen(char *result[], const char *format, ...)
{
    int ret = 0;
    char buf[1024] = {0};
    FILE *p_file = NULL;
    char command[1024] = {0};

    va_list args;
    va_start(args, format);
    ret = vsnprintf(command, sizeof(command), format, args);
    va_end(args);
    p_file = popen(command, (char *)"r");
    if (NULL == p_file) {
        ret = 1;
        printf("popen %s error/n", p_file);
        goto END;
    }

    while (fgets(buf, sizeof(buf), p_file) != NULL) {
        strcpy((char *)result, buf);
    }

END:
    if (pclose(p_file) == 1) {
        printf("pclose failed.");
        return 1;
    }
    return ret;
}

#ifdef __cplusplus
}
#endif
#endif
