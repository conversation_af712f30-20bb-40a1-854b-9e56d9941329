/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: pubsubTest.cpp
 * Description: datalog pubsub
 * Author: wuxueqi 00495442
 * Create: 2022-09-14
 */


#include "pubsub.h"
#include "t_datacom_lite.h"

class pubsubTest : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    };
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void pubsubTest::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret = CompileAndLoad(g_fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建连
    uint32_t msgReadTimeout = 60000 * 5;
    ConnOptionT *connOption;
    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, NULL, &msgReadTimeout);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync, GMC_CONN_TYPE_SYNC, true, g_epoll_reg_info, NULL, NULL, connOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testFreeConnOptions(connOption);

    ClearSubTmoutLog();

    AW_CHECK_LOG_BEGIN();
}

void pubsubTest::TearDown()
{
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_connSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog(g_fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 019.datalog订阅，设置单次订阅阈值20，订阅回调小于20ms
TEST_F(pubsubTest, DFX_067_002_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 设置23
    SetSubCfgAndConn(23, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 10;
    g_subCallTmOutSet = 20;

    int ret = GmcUseNamespace(g_stmtSync, "public");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t dataStart = 1;
    uint32_t writeCount = 1000, batchNum = 10;
    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"schema_file/out3.gmjson", &userData1,
        dataStart + writeCount * batchNum, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GtlabelCfgT vertexCfg = {0};
    for (uint32_t i = 0; i < batchNum; i++) {
        vertexCfg = {
            (int32_t)(dataStart + writeCount * i), writeCount, userData1.data, NULL, NULL, writeCount * i, true, false};
        ret = writeTable(g_connSync, g_stmtSync, g_inp3, vertexCfg, inp3Set);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, writeCount * batchNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u \n", g_subTriggerCnt, g_subCallTmOutCt);

    // 小于默认值1
    ret = checkSubAvgTmoutLog(23, (char *)"subVertexLabel", 0);
    EXPECT_EQ(ret, 0);


    AW_MACRO_EXPECT_EQ_INT(GetBatchCallbackTimes(writeCount) * batchNum, userData1.data->callbackTimes);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 读输入表
    vertexCfg = {dataStart, writeCount * batchNum, userData1.data, NULL, NULL, 0, true, false};
    ret = readTable(g_stmtSync, g_inp3, vertexCfg, inp3Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读中间表
    ret = readTable(g_stmtSync, g_mid3, vertexCfg, inp3GetProject, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName, &userData1, dataStart, writeCount * batchNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}


// 020.datalog订阅，设置单次订阅阈值20，订阅回调小于20ms
TEST_F(pubsubTest, DFX_067_002_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 设置23
    SetSubCfgAndConn(23, 0);

    // 设置 call_back sleep time
    g_subCallSleepTm = 25;
    g_subCallTmOutSet = 20;

    int ret = GmcUseNamespace(g_stmtSync, "public");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t dataStart = 1;
    uint32_t writeCount = 1000, batchNum = 10;
    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"schema_file/out3.gmjson", &userData1,
        dataStart + writeCount * batchNum, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GtlabelCfgT vertexCfg = {0};
    for (uint32_t i = 0; i < batchNum; i++) {
        vertexCfg = {
            (int32_t)(dataStart + writeCount * i), writeCount, userData1.data, NULL, NULL, writeCount * i, true, false};
        ret = writeTable(g_connSync, g_stmtSync, g_inp3, vertexCfg, inp3Set);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, writeCount * batchNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u \n", g_subTriggerCnt, g_subCallTmOutCt);

    // 小于23
    ret = checkSubTmoutLog(23, (char *)"subVertexLabel", 1);
    EXPECT_EQ(ret, 0);

    // 小于默认值1
#if !defined(RUN_SIMULATE)
    ret = checkSubAvgTmoutLog(1, (char *)"subVertexLabel", 1);
    EXPECT_EQ(ret, 0);
#endif

    AW_MACRO_EXPECT_EQ_INT(GetBatchCallbackTimes(writeCount) * batchNum, userData1.data->callbackTimes);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 读输入表
    vertexCfg = {dataStart, writeCount * batchNum, userData1.data, NULL, NULL, 0, true, false};
    ret = readTable(g_stmtSync, g_inp3, vertexCfg, inp3Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读中间表
    ret = readTable(g_stmtSync, g_mid3, vertexCfg, inp3GetProject, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName, &userData1, dataStart, writeCount * batchNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}



// 021 019.datalog订阅，设置平均订阅阈值20，订阅回调小于20ms
TEST_F(pubsubTest, DFX_067_002_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 设置23
    SetSubCfgAndConn(0, 35);

    // 设置 call_back sleep time: 设备单条超时时间为1000ms，其他环境200ms；平均超时时间改为60ms，其他环境1ms
#if defined(RUN_SIMULATE)
    g_subCallSleepTm = 1001;
#else
    g_subCallSleepTm = 210;
#endif
    g_subCallTmOutSet = 1;

    int ret = GmcUseNamespace(g_stmtSync, "public");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t dataStart = 1;
    uint32_t writeCount = 1000, batchNum = 10;
    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"schema_file/out3.gmjson", &userData1,
        dataStart + writeCount * batchNum, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GtlabelCfgT vertexCfg = {0};
    for (uint32_t i = 0; i < batchNum; i++) {
        vertexCfg = {
            (int32_t)(dataStart + writeCount * i), writeCount, userData1.data, NULL, NULL, writeCount * i, true, false};
        ret = writeTable(g_connSync, g_stmtSync, g_inp3, vertexCfg, inp3Set);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, writeCount * batchNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u \n", g_subTriggerCnt, g_subCallTmOutCt);

    // 小于25
#if defined(RUN_SIMULATE)
    ret = checkSubTmoutLog(1000, (char *)"subVertexLabel", 1);
#else
    ret = checkSubTmoutLog(200, (char *)"subVertexLabel", 1);
#endif
    EXPECT_EQ(ret, 0);

    // 小于25
#if defined(RUN_SIMULATE)
    ret = checkSubAvgTmoutLog(35, (char *)"subVertexLabel", 1);
#else
    ret = checkSubAvgTmoutLog(25, (char *)"subVertexLabel", 0);
#endif
    EXPECT_EQ(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(GetBatchCallbackTimes(writeCount) * batchNum, userData1.data->callbackTimes);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 读输入表
    vertexCfg = {dataStart, writeCount * batchNum, userData1.data, NULL, NULL, 0, true, false};
    ret = readTable(g_stmtSync, g_inp3, vertexCfg, inp3Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读中间表
    ret = readTable(g_stmtSync, g_mid3, vertexCfg, inp3GetProject, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName, &userData1, dataStart, writeCount * batchNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}


// 022 020.datalog订阅，设置平均订阅阈值20，订阅回调大于20ms
TEST_F(pubsubTest, DFX_067_002_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 设置平均，单次
    SetSubCfgAndConn(0, 15);

    // 设置 call_back sleep time
    g_subCallSleepTm = 180;
    g_subCallTmOutSet = 1;

    int ret = GmcUseNamespace(g_stmtSync, "public");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t dataStart = 1;
    uint32_t writeCount = 1000, batchNum = 10;
    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"schema_file/out3.gmjson", &userData1,
        dataStart + writeCount * batchNum, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GtlabelCfgT vertexCfg = {0};
    for (uint32_t i = 0; i < batchNum; i++) {
        vertexCfg = {
            (int32_t)(dataStart + writeCount * i), writeCount, userData1.data, NULL, NULL, writeCount * i, true, false};
        ret = writeTable(g_connSync, g_stmtSync, g_inp3, vertexCfg, inp3Set);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, writeCount * batchNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u \n", g_subTriggerCnt, g_subCallTmOutCt);

    // 单次超时日志检测
    ret = checkSubTmoutLog(200, (char *)"subVertexLabel", 0);
    EXPECT_EQ(ret, 0);

    // 平均超时日志检测
    ret = checkSubAvgTmoutLog(15, (char *)"subVertexLabel", 1);
    EXPECT_EQ(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(GetBatchCallbackTimes(writeCount) * batchNum, userData1.data->callbackTimes);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 读输入表
    vertexCfg = {dataStart, writeCount * batchNum, userData1.data, NULL, NULL, 0, true, false};
    ret = readTable(g_stmtSync, g_inp3, vertexCfg, inp3Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读中间表
    ret = readTable(g_stmtSync, g_mid3, vertexCfg, inp3GetProject, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName, &userData1, dataStart, writeCount * batchNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}


// 023 023.datalog订阅，设置单次订阅20，平均订阅阈值10，回调大于20ms
TEST_F(pubsubTest, DFX_067_002_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 设置23
    SetSubCfgAndConn(0, 35);

    // 设置 call_back sleep time: 设备单条超时时间为1000ms，其他环境200ms；平均超时时间改为60ms，其他环境1ms
#if defined(RUN_SIMULATE)
    g_subCallSleepTm = 1100;
#else
    g_subCallSleepTm = 360;
#endif
    g_subCallTmOutSet = 1;

    int ret = GmcUseNamespace(g_stmtSync, "public");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t dataStart = 1;
    uint32_t writeCount = 1000, batchNum = 10;
    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"schema_file/out3.gmjson", &userData1,
        dataStart + writeCount * batchNum, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GtlabelCfgT vertexCfg = {0};
    for (uint32_t i = 0; i < batchNum; i++) {
        vertexCfg = {
            (int32_t)(dataStart + writeCount * i), writeCount, userData1.data, NULL, NULL, writeCount * i, true, false};
        ret = writeTable(g_connSync, g_stmtSync, g_inp3, vertexCfg, inp3Set);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, writeCount * batchNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u \n", g_subTriggerCnt, g_subCallTmOutCt);

    // 单次超时日志检测
#if defined(RUN_SIMULATE)
    ret = checkSubTmoutLog(1000, (char *)"subVertexLabel", 1);
#else
    ret = checkSubTmoutLog(200, (char *)"subVertexLabel", 1);
#endif
    EXPECT_EQ(ret, 0);

    // 平均超时日志检测
    ret = checkSubAvgTmoutLog(35, (char *)"subVertexLabel", 1);
    EXPECT_EQ(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(GetBatchCallbackTimes(writeCount) * batchNum, userData1.data->callbackTimes);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 读输入表
    vertexCfg = {dataStart, writeCount * batchNum, userData1.data, NULL, NULL, 0, true, false};
    ret = readTable(g_stmtSync, g_inp3, vertexCfg, inp3Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读中间表
    ret = readTable(g_stmtSync, g_mid3, vertexCfg, inp3GetProject, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName, &userData1, dataStart, writeCount * batchNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}


// 024 026.datalog订阅，设置单次订阅8，平均订阅阈值3，触发1000次回调，每次回调超时
TEST_F(pubsubTest, DFX_067_002_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 设置单次和平均超时阈值
    SetSubCfgAndConn(5, 2);

    // 设置 call_back sleep time
    g_subCallSleepTm = 6;
    g_subCallTmOutSet = 2000;

    int ret = GmcUseNamespace(g_stmtSync, "public");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t dataStart = 1;
    uint32_t writeCount = 1000, batchNum = 4;
    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"schema_file/out3.gmjson", &userData1,
        dataStart + writeCount * batchNum, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GtlabelCfgT vertexCfg = {0};
    for (uint32_t i = 0; i < batchNum; i++) {
        vertexCfg = {
            (int32_t)(dataStart + writeCount * i), writeCount, userData1.data, NULL, NULL, writeCount * i, true, false};
        ret = writeTable(g_connSync, g_stmtSync, g_inp3, vertexCfg, inp3Set);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, writeCount * batchNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>> sub_trigger_cnt: %u subCallTmOutCt : %u \n", g_subTriggerCnt, g_subCallTmOutCt);

    // 单次超时日志检测
    ret = checkSubTmoutLog(5, (char *)"subVertexLabel", 1, 0);
    EXPECT_EQ(ret, 0);

    // 平均超时日志检测
    ret = checkSubAvgTmoutLog(2, (char *)"subVertexLabel", 1, 0);
    EXPECT_EQ(ret, 0);

    AW_MACRO_EXPECT_EQ_INT(GetBatchCallbackTimes(writeCount) * batchNum, userData1.data->callbackTimes);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    // 读输入表
    vertexCfg = {dataStart, writeCount * batchNum, userData1.data, NULL, NULL, 0, true, false};
    ret = readTable(g_stmtSync, g_inp3, vertexCfg, inp3Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读中间表
    ret = readTable(g_stmtSync, g_mid3, vertexCfg, inp3GetProject, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName, &userData1, dataStart, writeCount * batchNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

