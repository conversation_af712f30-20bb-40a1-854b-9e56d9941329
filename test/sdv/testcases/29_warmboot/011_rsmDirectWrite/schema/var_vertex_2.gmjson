[{"comment": "变长vertex label", "version": "2.0", "type": "record", "name": "rsm_var_vertex", "schema_version": 2, "fields": [{"name": "A0", "type": "int32", "nullable": false}, {"name": "A1", "type": "int64", "nullable": false}, {"name": "A2", "type": "int64", "nullable": false}, {"name": "A3", "type": "int64", "nullable": false}, {"name": "S1", "type": "string", "nullable": false}, {"name": "S2", "type": "string", "nullable": false}, {"name": "add1", "type": "string", "nullable": true, "default": ""}, {"name": "add2", "type": "string", "nullable": true, "default": ""}], "keys": [{"node": "rsm_var_vertex", "name": "<PERSON><PERSON><PERSON>", "index": {"type": "primary"}, "fields": ["A0"], "constraints": {"unique": true}, "comment": "主键索引"}, {"node": "rsm_var_vertex", "name": "localhash_key", "fields": ["A1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}, "comment": "localhash索引"}]}]