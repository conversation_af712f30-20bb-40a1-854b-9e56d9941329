#include "RsmCommon.h"

class RsmConfigurationItem : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void RsmConfigurationItem::SetUpTestCase()
{
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void RsmConfigurationItem::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void RsmConfigurationItem::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    AW_CHECK_LOG_BEGIN();
}

void RsmConfigurationItem::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    testEnvClean();
    AW_CHECK_LOG_END();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// GmcCreateRsmTablespace参数测试
TEST_F(RsmConfigurationItem, warmboot_008_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_NAME);
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg, errorMsg1, errorMsg2);
    int ret = 0;
    TestPrepareEnvAndConn();

    AW_FUN_Log(LOG_STEP, "参数传NULL");
    GmcStmtT *stmtSync = NULL;
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsm_tablespace",
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 2,
    };
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, GmcCreateRsmTablespace(stmtSync, &tspCfg));
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, GmcCreateRsmTablespace(g_stmtSync, NULL));

    AW_FUN_Log(LOG_STEP, "错误的tspCfg");
    GmcTspCfgT tspCfgErr = {
        .tablespaceName = "01rsm_tablespace",
        .initSize = 1,
        .stepSize = 1,
        .maxSize = 2,
    };
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, GmcCreateRsmTablespace(g_stmtSync, &tspCfgErr));

    GmcTspCfgT tspCfgErr2 = {
        .tablespaceName = "rsm_tablespace",
        .initSize = 1025,
        .stepSize = 1,
        .maxSize = 1026,
    };
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, GmcCreateRsmTablespace(g_stmtSync, &tspCfgErr2));

    TestCleanEnvAndConn();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");

    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");

    AW_FUN_Log(LOG_STEP, "warmboot后,参数测试");
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, GmcCreateRsmTablespace(stmtSync, &tspCfg));
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, GmcCreateRsmTablespace(g_stmtSync, NULL));
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, GmcCreateRsmTablespace(g_stmtSync, &tspCfgErr));
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, GmcCreateRsmTablespace(g_stmtSync, &tspCfgErr2));

    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// isUseRsm=0,GmcCreateTspTablespace创建rsmTsp
TEST_F(RsmConfigurationItem, warmboot_008_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=0");
    system("${TEST_HOME}/tools/start.sh");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNEXPECTED_NULL_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    TestPrepareEnvAndConn();
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");

    AW_FUN_Log(LOG_STEP, "isUseRsm=0,GmcCreateTspTablespace创建rsmTsp");
    GmcTspCfgT tspCfg = {
        .tablespaceName = "A",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, GmcCreateRsmTablespace(g_stmtSync, &tspCfg));
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

class NotRsmCompile : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void NotRsmCompile::SetUpTestCase()
{
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void NotRsmCompile::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void NotRsmCompile::SetUp()
{
    AW_FUN_Log(LOG_STEP, "SetUp.");
    AW_CHECK_LOG_BEGIN();
}

void NotRsmCompile::TearDown()
{
    AW_FUN_Log(LOG_STEP, "TearDown.");
    testEnvClean();
    AW_CHECK_LOG_END();
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/start.sh");
}

// 非保留内存故障重启恢复，GmcCreateRsmTablespace创建rsmTsp
TEST_F(NotRsmCompile, warmboot_008_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=0");
    system("${TEST_HOME}/tools/start.sh");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNEXPECTED_NULL_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    TestPrepareEnvAndConn();
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");

    AW_FUN_Log(LOG_STEP, "不编译rsm,GmcCreateTspTablespace创建rsmTsp");
    GmcTspCfgT tspCfg = {
        .tablespaceName = "A",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, GmcCreateRsmTablespace(g_stmtSync, &tspCfg));
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 编译时不包含rsmem，isUseRsm=0，gmserver首次启动不带-rb，reboot带-rb
TEST_F(NotRsmCompile, warmboot_008_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "isUseRsm=0, gmserver首次启动不带-rb");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=0");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    system("gmsysview -q V\\$DB_SERVER");
    TestCleanEnvAndConn();
    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 编译时不包含rsmem，isUseRsm=1，gmserver首次启动不带-rb，reboot带-rb
TEST_F(NotRsmCompile, warmboot_008_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONNECTION_FAILURE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "不编rsm, isUseRsm=1, gmserver首次启动不带-rb");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");

    int ret = 0;
    system("gmsysview -q V\\$DB_SERVER");
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    system("gmsysview -q V\\$DB_SERVER");
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 编译时包含rsmem，isUseRsm=0，gmserver首次启动不带-rb，reboot带-rb
TEST_F(RsmConfigurationItem, warmboot_008_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_CONNECTION_FAILURE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "isUseRsm=0, gmserver首次启动不带-rb");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=0");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    system("gmsysview -q V\\$DB_SERVER");
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 0", "USE_RSM: 0", "RECOVERY_TIME: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    system("gmsysview -q V\\$DB_SERVER");
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 编译时包含rsmem，isUseRsm=1，gmserver首次启动不带-rb，reboot带-rb
TEST_F(RsmConfigurationItem, warmboot_008_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "isUseRsm=1, gmserver首次启动不带-rb");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    system("gmsysview -q V\\$DB_SERVER");
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 0", "USE_RSM: 1", "RECOVERY_TIME: ");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();

    AW_FUN_Log(LOG_STEP, "-f 停止服务");
    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    TestWaitRsmRecoverFinish();

    system("gmsysview -q V\\$DB_SERVER");
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 1", "USE_RSM: 1", "RECOVERY_TIME: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    const char *viewCataLabelRecoveryInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewCataLabelRecoveryInfo, NULL, "TOTAL_LABEL_CNT: 0", "RECOVERY_LABEL_CNT: 0", "RECOVERY_FINISHED: 1");

    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，isUseRsm配置项错误
TEST_F(RsmConfigurationItem, warmboot_008_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "保留内存,isUseRsm配置项错误");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=2");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=A");
    system("${TEST_HOME}/tools/start.sh");
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，RsmBlockSize和RsmKeyRange配置使用默认值
TEST_F(RsmConfigurationItem, warmboot_008_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    system("gmsysview -q V\\$DB_SERVER");
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 0", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    system("gmsysview -q V\\$DB_SERVER");
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 1", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$DB_SERVER");
    TestWaitRsmRecoverFinish();
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 1", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 1", "RECOVERY_FINISHED: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, g_lablePk, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，RsmBlockSize配置项错误
TEST_F(RsmConfigurationItem, warmboot_008_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    AW_FUN_Log(LOG_STEP, "保留内存,RsmBlockSize配置项错误");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmBlockSize=132");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmBlockSize=9");
    system("${TEST_HOME}/tools/start.sh");
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，RsmBlockSize配置项配置正确
TEST_F(RsmConfigurationItem, warmboot_008_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmBlockSize=48");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    system("gmsysview -q V\\$DB_SERVER");
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 0", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    system("gmsysview -q V\\$DB_SERVER");
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 1", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$DB_SERVER");
    TestWaitRsmRecoverFinish();
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 1", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 1", "RECOVERY_FINISHED: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, g_lablePk, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，RsmKeyRange配置项错误
TEST_F(RsmConfigurationItem, warmboot_008_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    AW_FUN_Log(LOG_STEP, "保留内存,RsmKeyRange配置项错误");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmKeyRange=1,10");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmKeyRange=2,4");
    system("${TEST_HOME}/tools/start.sh");
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmKeyRange=3,2");
    system("${TEST_HOME}/tools/start.sh");
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，RsmKeyRange配置项配置正确
TEST_F(RsmConfigurationItem, warmboot_008_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmKeyRange=0,5");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    system("gmsysview -q V\\$DB_SERVER");
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 0", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    system("gmsysview -q V\\$DB_SERVER");
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 1", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$DB_SERVER");
    TestWaitRsmRecoverFinish();
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 1", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 1", "RECOVERY_FINISHED: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, g_lablePk, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，表级配置is_support_reserved_memory使用默认值
TEST_F(RsmConfigurationItem, warmboot_008_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    system("gmsysview -q V\\$DB_SERVER");
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 0", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelConfig[] = "{\"max_record_count\":1000000, \"auto_increment\":1}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    ret = TestCheckViewFieldResult(
        viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1", "\"tablespace_name\": \"public\"", "IS_USE_RSM: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$DB_SERVER");
    TestWaitRsmRecoverFinish();
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 1", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 0", "RECOVERY_LABEL_CNT: 0", "RECOVERY_FINISHED: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCheckViewFieldResult(
        viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1", "\"tablespace_name\": \"public\"", "IS_USE_RSM: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, g_lablePk, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, vertexCount);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，表级配置is_support_reserved_memory配置为true,rsm_tablespace_name使用默认值
TEST_F(RsmConfigurationItem, warmboot_008_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    system("gmsysview -q V\\$DB_SERVER");
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 0", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Warmboot08Vertex1");
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    ret = TestCheckViewFieldResult(viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1",
        "\"tablespace_name\": \"public\"", "\"rsm_tablespace_name\": \"reservedMemPublic\"", "IS_USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$DB_SERVER");
    TestWaitRsmRecoverFinish();
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 1", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 1", "RECOVERY_FINISHED: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Warmboot08Vertex1");
    ret = TestCheckViewFieldResult(viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1",
        "\"tablespace_name\": \"public\"", "\"rsm_tablespace_name\": \"reservedMemPublic\"", "IS_USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, g_lablePk, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，isUseRsm=0，表级配置is_support_reserved_memory配置为true,rsm_tablespace_name使用默认值
TEST_F(RsmConfigurationItem, warmboot_008_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=0");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$DB_SERVER");
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 0", "USE_RSM: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    ret = TestCheckViewFieldResult(
        viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1", "\"tablespace_name\": \"public\"", "IS_USE_RSM: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，不创建rsmTspA，表级配置rsm_tablespace_name指定为rsmTspA
TEST_F(RsmConfigurationItem, warmboot_008_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    system("gmsysview -q V\\$DB_SERVER");
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 0", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelConfig[] = "{\"max_record_count\":1000000, \"auto_increment\":1, \"is_support_reserved_memory\":true, "
                         "\"rsm_tablespace_name\": \"rsmTspA\"}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    ret = TestCheckViewFieldResult(viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1",
        "\"tablespace_name\": \"public\"", "\"rsm_tablespace_name\": \"rsmTspA\"", "IS_USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *viewTspInfo = "V\\$CATA_TABLESPACE_INFO";
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    ret =
        TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=rsmTspA", "TABLESPACE_NAME: rsmTspA", "IS_RSM_SPACE: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$DB_SERVER");
    TestWaitRsmRecoverFinish();
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 1", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 1", "RECOVERY_FINISHED: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCheckViewFieldResult(viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1",
        "\"tablespace_name\": \"public\"", "\"rsm_tablespace_name\": \"rsmTspA\"", "IS_USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    ret =
        TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=rsmTspA", "TABLESPACE_NAME: rsmTspA", "IS_RSM_SPACE: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, g_lablePk, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，创建rsmTspA，表级配置rsm_tablespace_name指定为rsmTspA
TEST_F(RsmConfigurationItem, warmboot_008_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelConfig[] = "{\"max_record_count\":1000000, \"auto_increment\":1, "
                         "\"is_support_reserved_memory\":true, \"rsm_tablespace_name\": \"rsmTspA\"}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    ret = TestCheckViewFieldResult(viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1",
        "\"tablespace_name\": \"public\"", "\"rsm_tablespace_name\": \"rsmTspA\"", "IS_USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *viewTspInfo = "V\\$CATA_TABLESPACE_INFO";
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    ret =
        TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=rsmTspA", "TABLESPACE_NAME: rsmTspA", "IS_RSM_SPACE: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    TestWaitRsmRecoverFinish();
    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 1", "RECOVERY_FINISHED: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCheckViewFieldResult(viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1",
        "\"tablespace_name\": \"public\"", "\"rsm_tablespace_name\": \"rsmTspA\"", "IS_USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    ret =
        TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=rsmTspA", "TABLESPACE_NAME: rsmTspA", "IS_RSM_SPACE: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, g_lablePk, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，重启前创建rsmTsp,再建表，重启后直接创建表
TEST_F(RsmConfigurationItem, warmboot_008_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsmTspA",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelConfig[] = "{\"max_record_count\":1000000, \"auto_increment\":1, \"is_support_reserved_memory\":true, "
                         "\"rsm_tablespace_name\": \"rsmTspA\"}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    ret = TestCheckViewFieldResult(viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1",
        "\"tablespace_name\": \"public\"", "\"rsm_tablespace_name\": \"rsmTspA\"", "IS_USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *viewTspInfo = "V\\$CATA_TABLESPACE_INFO";
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    ret =
        TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=rsmTspA", "TABLESPACE_NAME: rsmTspA", "IS_RSM_SPACE: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = GmcCreateRsmTablespace(g_stmtSync, &tspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestWaitRsmRecoverFinish();

    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 1", "RECOVERY_FINISHED: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCheckViewFieldResult(viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1",
        "\"tablespace_name\": \"public\"", "\"rsm_tablespace_name\": \"rsmTspA\"", "IS_USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    ret =
        TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=rsmTspA", "TABLESPACE_NAME: rsmTspA", "IS_RSM_SPACE: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, g_lablePk, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    // Truncate、删表和tsp
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，表级配置rsm_tablespace_name,tablespace_nam同时指定
TEST_F(RsmConfigurationItem, warmboot_008_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    char labelConfig[] = "{\"max_record_count\":1000000, \"auto_increment\":1, \"is_support_reserved_memory\":true, "
                         "\"rsm_tablespace_name\": \"rsmTspA\", \"tablespace_name\": \"tspB\"}";
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Warmboot08Vertex1");
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    ret = TestCheckViewFieldResult(viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1",
        "\"tablespace_name\": \"tspB\"", "\"rsm_tablespace_name\": \"rsmTspA\"", "IS_USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *viewTspInfo = "V\\$CATA_TABLESPACE_INFO";
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    ret =
        TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=rsmTspA", "TABLESPACE_NAME: rsmTspA", "IS_RSM_SPACE: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=tspB", "TABLESPACE_NAME: tspB", "IS_RSM_SPACE: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    TestWaitRsmRecoverFinish();
    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 1", "RECOVERY_FINISHED: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Warmboot08Vertex1");
    ret = TestCheckViewFieldResult(viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1",
        "\"tablespace_name\": \"tspB\"", "\"rsm_tablespace_name\": \"rsmTspA\"", "IS_USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    ret =
        TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=rsmTspA", "TABLESPACE_NAME: rsmTspA", "IS_RSM_SPACE: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=tspB", "TABLESPACE_NAME: tspB", "IS_RSM_SPACE: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, g_lablePk, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "tspB");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，表级配置优先级 gmconfig > gmjson/config
TEST_F(RsmConfigurationItem, warmboot_008_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    TestPrepareEnvAndConn();
    const char *schemaPath = "schemaFile/AllTypeSchemaV1WithConfig.gmjson";
    char labelConfig[] = "{\"max_record_count\":1000000, \"auto_increment\":1, \"is_support_reserved_memory\":true, "
                         "\"rsm_tablespace_name\": \"rsmTspA\", \"tablespace_name\": \"tspB\"}";
    ret = testGmcCreateVertexLabel(g_stmtSync, schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Warmboot08Vertex1");
    const char *viewVertexInfo = "V\\$CATA_VERTEX_LABEL_INFO ";
    ret = TestCheckViewFieldResult(viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1",
        "\"tablespace_name\": \"tspB\"", "\"rsm_tablespace_name\": \"rsmTspA\"", "IS_USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *viewTspInfo = "V\\$CATA_TABLESPACE_INFO";
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    ret =
        TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=rsmTspA", "TABLESPACE_NAME: rsmTspA", "IS_RSM_SPACE: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=tspB", "TABLESPACE_NAME: tspB", "IS_RSM_SPACE: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    TestCleanEnvAndConn();

    system("${TEST_HOME}/tools/stop.sh -f");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();

    TestPrepareEnvAndConn();
    ret = testGmcCreateVertexLabel(g_stmtSync, schemaPath, g_labelName, 0, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_LABEL_RECOVERY_INFO");
    TestWaitRsmRecoverFinish();
    const char *viewRsmInfo = "V\\$CATA_LABEL_RECOVERY_INFO";
    ret = TestCheckViewFieldResult(
        viewRsmInfo, NULL, "TOTAL_LABEL_CNT: 1", "RECOVERY_LABEL_CNT: 1", "RECOVERY_FINISHED: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=Warmboot08Vertex1");
    ret = TestCheckViewFieldResult(viewVertexInfo, "VERTEX_LABEL_NAME=Warmboot08Vertex1",
        "\"tablespace_name\": \"tspB\"", "\"rsm_tablespace_name\": \"rsmTspA\"", "IS_USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
    ret =
        TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=rsmTspA", "TABLESPACE_NAME: rsmTspA", "IS_RSM_SPACE: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestCheckViewFieldResult(viewTspInfo, "TABLESPACE_NAME=tspB", "TABLESPACE_NAME: tspB", "IS_RSM_SPACE: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t vertexCount = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, g_lablePk, &vertexCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount, vertexCount);
    ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < g_recordCount; i++) {
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "rsmTspA");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropTablespace(g_stmtSync, "tspB");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestCleanEnvAndConn();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 保留内存，修改与保留内存有关配置项（deviceSize、pageSize、enableCluster），导致带-rb reboot失败
TEST_F(RsmConfigurationItem, warmboot_008_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$DB_SERVER");
    const char *viewDbServer = "V\\$DB_SERVER";
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "WARM_REBOOT: 0", "USE_RSM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSingleWrite(0, g_recordCount, 0);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh deviceSize=8");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh pageSize=16");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmBlockSize=64");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh recover");
    system("${TEST_HOME}/tools/modifyCfg.sh isUseRsm=1");
    system("${TEST_HOME}/tools/modifyCfg.sh RsmKeyRange=0,2");
    AW_FUN_Log(LOG_STEP, "-rb reboot服务");
    TestStartDbWarmReboot();
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    ret = TestCheckViewFieldResult(viewDbServer, NULL, "ErrorCode: 1002002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
