/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: RsmCommon.h
 * Author: gwx620465
 * Create: 2024-07-13
 */
#ifndef RSM_COMMON_H
#define RSM_COMMON_H

#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024
#define FILED_FIX_SIZE 5
#define FILED_STRING_SIZE 10
#define FILED_BYTES_SIZE 10
#define FILED_BITMAP_SIZE 16
#define MAX_NAME_LENGTH 128
#define PARTITION_NUM 10
#define EXPECT_STRING_SIZE 40
#define GMCONFIG_PATH "/usr/local/file/gmserver.ini"

uint32_t g_recordCount = 1000;
bool g_isBlock = false;
pthread_barrier_t g_barrier;
GmcConnT *g_connSync = NULL;
GmcStmtT *g_stmtSync = NULL;
GmcConnT *g_connAsync = NULL;
GmcStmtT *g_stmtAsync = NULL;

const char *g_schemaPath = "schemaFile/AllTypeSchemaV1.gmjson";
const char *g_labelName = "Warmboot08Vertex1";
char g_labelConfig[] = "{\"max_record_count\":1000000, \"auto_increment\":1, "
                       "\"is_support_reserved_memory\":true}";
char g_lablePk[] = "pk";
static const char *g_labelSchemaJson =
    R"([{
        "type":"record",
        "name":"ResVertexLabel002",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"ResVertexLabel002",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

const char *g_clusterPath = "schemaFile/VertexCluster.gmjson";
const char *g_clusterNotuniquePath = "schemaFile/VertexClusterNotunique.gmjson";
const char *g_heapPath = "schemaFile/VertexHeap.gmjson";
const char *g_heapNotuniquePath = "schemaFile/VertexHeapNotunique.gmjson";
const char *g_clusterName = "Warmboot08ClusterVertex";
const char *g_clusterNameNotUnique = "VertexClusterNotunique";
const char *g_heapName = "Warmboot08HeapVertex";
const char *g_heapNameNotUnique = "VertexHeapNotunique";
char g_clusterConfig[] =
    "{\"max_record_count\":1000000, \"is_support_reserved_memory\":true, \"rsm_tablespace_name\": \"rsmTspA\"}";
char g_clusterPk[] = "Cluster_pk";
char g_clusterLocalhash[] = "Cluster_localhash";
char g_clusterLocal[] = "Cluster_local";
char g_clusterHashcluster[] = "Cluster_hashcluster";
char g_clusterLpm4[] = "Cluster_lpm4";
char g_clusterLpm6[] = "Cluster_lpm6";
char g_heapPk[] = "Heap_pk";
char g_heapLocalhash[] = "Heap_localhash";
char g_heapLocal[] = "Heap_local";
char g_heapHashcluster[] = "Heap_hashcluster";
char g_heapLpm4[] = "Heap_lpm4";
char g_heapLpm6[] = "Heap_lpm6";
char g_heapMemkey[] = "Heap_memkey";

int32_t testGetCmdResult(const char *cmd, char *result, uint32_t len)
{
    if (result == NULL) {
        return -1;
    }
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", cmd);
        return -1;
    }

    char *cmdOutput = result;
    while (fgets(cmdOutput, len, pf) != NULL) {
    }
    for (uint32_t i = 0; i < len; i++) {
        if (cmdOutput[i] == '\n') {
            cmdOutput[i] = '\0';
        }
        if (cmdOutput[i] == '\0') {
            break;
        }
    }
    pclose(pf);

    return 0;
}

void TestStartDbWarmReboot()
{
    system("${TEST_HOME}/tools/start.sh -f -rb");
}

// 获取subInfo数量
int TestGetViewCount(const char *viewName, const char *grepStr)
{
    int ret = 0;
    char cmdRet[1024] = {0};
    char cmd[1024] = {0};

    (void)sprintf_s(cmd, sizeof(cmd), "gmsysview -q %s | grep '%s' | wc -l", viewName, grepStr);
    ret = testGetCmdResult(cmd, cmdRet, sizeof(cmdRet));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return atoi(cmdRet);
}

int TestCheckViewFieldResult(const char *viewName, const char *filterStr, const char *v1 = NULL, const char *v2 = NULL,
    const char *v3 = NULL, const char *v4 = NULL)
{
    char cmd[MAX_CMD_SIZE];
    if (filterStr == NULL) {
        (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, viewName);
    } else {
        (void)snprintf(cmd, MAX_CMD_SIZE, "%s/gmsysview -q %s -f %s", g_toolPath, viewName, filterStr);
    }

    int ret = executeCommand(cmd, v1, v2, v3, v4);
    return ret;
}

void TestPrepareEnvAndConn()
{
    int ret;
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_connAsync = NULL;
    g_stmtAsync = NULL;
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestCleanEnvAndConn()
{
    int ret;
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestSetVertexPkProperty(GmcStmtT *stmt, int pk)
{
    int ret;
    uint32_t value7 = pk;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestSetVertexOtherProperty(GmcStmtT *stmt, int pk, int addVal = 0)
{
    int ret;
    const char *string = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwx";
    AW_MACRO_EXPECT_EQ_INT(50, strlen(string));
    char teststr0 = string[(pk + addVal) % strlen(string)];
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char teststr1 = string[(pk + addVal) % strlen(string)];
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value2 = (1 + pk + addVal);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t value3 = (10 + pk + addVal);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value4 = (100 + pk + addVal);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value5 = (1000 + pk + addVal);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value6 = pk + addVal;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool value8 = (pk + addVal) % 2;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value9 = 1000 + pk + addVal;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk + addVal;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)(pk + addVal);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value12 = 10.86 + pk + addVal;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk + addVal;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr14[FILED_STRING_SIZE + 1] = {0};
    ret = snprintf_s(teststr14, FILED_STRING_SIZE + 1, FILED_STRING_SIZE, "d%09d", (pk + addVal) % 10);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value17 = (1000 + pk + addVal);
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t value18 = (1000 + pk) % PARTITION_NUM;
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_PARTITION, &value18, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value20 = (1000 + pk) % 1024;
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_BITFIELD16, &value20, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t f21Bits[2] = {0xfe, 0xff};
    GmcBitMapT value21 = {0};
    value21.beginPos = 0;
    value21.endPos = FILED_BITMAP_SIZE - 1;
    value21.bits = f21Bits;
    ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_BITMAP, &value21, sizeof(value21));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestCheckVertexOtherProperty(GmcStmtT *stmt, int pk, int addVal = 0)
{
    int ret;
    const char *string = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwx";
    char teststr0 = string[(pk + addVal) % strlen(string)];
    unsigned char teststr1 = string[(pk + addVal) % strlen(string)];
    int8_t value2 = (1 + pk + addVal);
    uint8_t value3 = (10 + pk + addVal);
    int16_t value4 = (100 + pk + addVal);
    uint16_t value5 = (1000 + pk + addVal);
    int32_t value6 = pk + addVal;
    bool value8 = (pk + addVal) % 2;
    int64_t value9 = 1000 + pk + addVal;
    uint64_t value10 = 1000 + pk + addVal;
    float value11 = (float)1.2 + (float)(pk + addVal);
    double value12 = 10.86 + pk + addVal;
    uint64_t value13 = 1000 + pk + addVal;
    char teststr14[FILED_STRING_SIZE + 1] = {0};
    ret = snprintf_s(teststr14, FILED_STRING_SIZE + 1, FILED_STRING_SIZE, "d%09d", (pk + addVal) % 10);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    uint32_t value17 = (1000 + pk + addVal);

    char teststr15[10] = "bytes";
    char teststr16[6] = "fixed";
    uint8_t value18 = (1000 + pk) % PARTITION_NUM;
    uint16_t value20 = (1000 + pk) % 1024;
    uint8_t f21Bits[2] = {0xfe, 0xff};
    GmcBitMapT value21 = {0};
    value21.beginPos = 0;
    value21.endPos = FILED_BITMAP_SIZE - 1;
    value21.bits = f21Bits;

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr14);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, teststr15);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, teststr16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &value17);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_PARTITION, &value18);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_BITFIELD16, &value20);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int TestCheckAffectRows(GmcStmtT *stmt, int32_t expect)
{
    int32_t affect;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affect, sizeof(affect));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (affect != expect) {
        AW_FUN_Log(LOG_INFO, "checke effect rows failed, expect is %d, actual is %d", expect, affect);
        return -1;
    }
    return ret;
}

void TestScanAndCheckVertexProperty(GmcStmtT *stmt, const char *label, const char *lablePk, int pk, int addVal = 0)
{
    int ret;

    uint32_t value7 = pk;  // F7是pk
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, lablePk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        TestCheckVertexOtherProperty(stmt, value7, addVal);
    }
}

void TestSingleWrite(uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName = g_labelName)
{
    int ret = 0;
    for (int i = begin; i < end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, labelName, g_lablePk, i);
    }
}

void TestSingleUpdate(uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName = g_labelName)
{
    int ret = 0;
    for (int i = begin; i < end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, labelName, g_lablePk, i, addVal);
    }
}

void TestSingleDelete(uint32_t begin, uint32_t end, const char *labelName = g_labelName)
{
    int ret = 0;
    for (int i = begin; i < end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 批量replace
void TestBatchReplaceAsync(
    GmcConnT *conn, GmcStmtT *stmt, uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName = g_labelName)
{
    uint32_t ret = 0;
    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;
    GmcBatchRetT batch_ret;
    GmcBatchOptionT batch_option;
    uint32_t suc_num = 0, total_num = 0;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int loop = begin; loop < end; loop++) {
        TestSetVertexPkProperty(stmt, loop);
        TestSetVertexOtherProperty(stmt, loop, addVal);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcResetStmt(stmt);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(end - begin, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(end - begin, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

// 批量merge
void TestBatchMergeAsync(
    GmcConnT *conn, GmcStmtT *stmt, uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName = g_labelName)
{
    uint32_t ret = 0;
    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;
    GmcBatchRetT batch_ret;
    GmcBatchOptionT batch_option;
    uint32_t suc_num = 0, total_num = 0;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int loop = begin; loop < end; loop++) {
        TestSetVertexOtherProperty(stmt, loop, addVal);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcResetStmt(stmt);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(end - begin, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(end - begin, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

// 批量DELETE
void TestBatchDeleteAsync(
    GmcConnT *conn, GmcStmtT *stmt, uint32_t begin, uint32_t end, const char *labelName = g_labelName)
{
    uint32_t ret = 0;
    AsyncUserDataT data = {0};
    GmcBatchT *batch = NULL;
    GmcBatchRetT batch_ret;
    GmcBatchOptionT batch_option;
    uint32_t suc_num = 0, total_num = 0;
    ret = GmcBatchOptionInit(&batch_option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batch_option, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batch_option, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batch_option, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int loop = begin; loop < end; loop++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &loop, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcResetStmt(stmt);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(end - begin, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(end - begin, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void TestSetBigObjAllProperty(GmcStmtT *stmt, uint32_t indexVal, uint32_t addVal, bool isSetAllField = true)
{
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &indexVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f1Val[31 * 1024 + 1] = {0};
    (void)snprintf_s(f1Val, 31745, 31744, "d%031743d", indexVal + addVal);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, f1Val, 31744);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isSetAllField) {
        char stringVal[65536] = {0};
        (void)snprintf_s(stringVal, 65536, 65535, "d%065534d", indexVal + addVal);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_STRING, stringVal, 65535);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char f16Val[32769] = {0};
        (void)snprintf_s(f16Val, 32769, 32768, "d%032767d", indexVal + addVal);
        ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_STRING, f16Val, 32768);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// 单写大对象
int TestInsertBigObject(
    GmcStmtT *stmt, const char *labelName, uint32_t indexVal, uint32_t addVal, bool isSetAllField = true)
{
    int ret = 0;
    uint32_t index = indexVal;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSetBigObjAllProperty(stmt, index, addVal, isSetAllField);
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "record index: %d.", index);
        testGmcGetLastError();
        return ret;
    } else {
        ret = TestCheckAffectRows(stmt, 1);
        return ret;
    }
}

void TestSetKV(GmcStmtT *stmt, const char *kvName, uint32_t kvCount, int32_t addVal = 0)
{
    // 写数据
    int ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t value = 0;
    char key[MAX_NAME_LENGTH];
    int32_t getValue = 0;
    uint32_t valueLen = 4;
    for (int32_t i = 0; i < kvCount; i++) {
        (void)sprintf_s(key, sizeof(key), "Warmboot08KvKey%d", i);
        value = i + addVal;
        ret = GmcKvSet(stmt, key, sizeof(key), &value, sizeof(int32_t));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "set kv  ret is %d, kv key is %d.", ret, i);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcKvGet(stmt, key, sizeof(key), &getValue, &valueLen);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "get kv ret is %d, kv key is %d.", ret, i);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(valueLen, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(getValue, i + addVal);
    }

    return;
}

void TestScanKV(GmcStmtT *stmt, const char *kvName, uint32_t kvCountExp, int32_t addVal = 0)
{
    // 写数据
    int ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t value = 0;
    uint32_t valueLen = 0;
    char key[MAX_NAME_LENGTH];
    ret = GmcKvScan(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t actScanCount = 0;
    bool isFinish = true;
    while (true) {
        (void)sprintf_s(key, sizeof(key), "Warmboot08KvKey%d", actScanCount);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        char *fetchKey = NULL;
        uint32_t fetchKeyLen = 0;
        char *fetchValue = NULL;
        uint32_t fetchValueLen = 0;
        ret = GmcKvGetFromStmt(stmt, (void **)&fetchKey, &fetchKeyLen, (void **)&fetchValue, &fetchValueLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = strncmp(key, fetchKey, sizeof(key));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(actScanCount + addVal, *(uint32_t *)fetchValue);
        AW_MACRO_EXPECT_EQ_INT(sizeof(key), fetchKeyLen);
        AW_MACRO_EXPECT_EQ_INT(sizeof(uint32_t), fetchValueLen);
        actScanCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(kvCountExp, actScanCount);

    return;
}

void TestRemoveKV(GmcStmtT *stmt, const char *kvName, uint32_t kvCount)
{
    // 写数据
    int ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char key[MAX_NAME_LENGTH];
    for (int32_t i = 0; i < kvCount; i++) {
        (void)sprintf_s(key, sizeof(key), "Warmboot08KvKey%d", i);
        ret = GmcKvRemove(stmt, &key, sizeof(key));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "remove kv  ret is %d, kv key is %d.", ret, i);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    return;
}

void TestSetKvAsync(GmcStmtT *stmt, const char *kvName, uint32_t kvCount, int32_t addVal = 0)
{
    // 写数据
    int ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t value = 0;
    char key[MAX_NAME_LENGTH];
    GmcKvTupleT kvInfo = {0};
    AsyncUserDataT data = {0};
    for (int32_t i = 0; i < kvCount; i++) {
        (void)sprintf_s(key, sizeof(key), "Warmboot08KvKey%d", i);
        value = i + addVal;
        kvInfo.key = key;
        kvInfo.keyLen = sizeof(key);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(uint32_t);
        ret = GmcKvSetAsync(stmt, &kvInfo, set_kv_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    return;
}

void TestRemoveKvAsync(GmcStmtT *stmt, const char *kvName, uint32_t kvCount)
{
    // 写数据
    int ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    char key[MAX_NAME_LENGTH];
    for (int32_t i = 0; i < kvCount; i++) {
        (void)sprintf_s(key, sizeof(key), "Warmboot08KvKey%d", i);
        ret = GmcKvRemoveAsync(stmt, key, sizeof(key), delete_kv_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    return;
}

void TestBatchSetKV(GmcConnT *conn, GmcStmtT *stmt, const char *kvName, uint32_t kvCount, int32_t addVal = 0)
{
    // 写数据
    int ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchRetT batchRet;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t value = 0;
    char key[MAX_NAME_LENGTH];
    for (int32_t i = 0; i < kvCount; i++) {
        (void)sprintf_s(key, sizeof(key), "Warmboot08KvKey%d", i);
        value = i + addVal;
        ret = GmcKvInputToStmt(stmt, key, sizeof(key), &value, sizeof(int32_t));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "input kv stmt  ret is %d, kv key is %d.", ret, i);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, kvCount);
    AW_MACRO_EXPECT_EQ_INT(successNum, kvCount);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return;
}

void TestBatchDeleteKV(GmcConnT *conn, GmcStmtT *stmt, const char *kvName, uint32_t kvCount, int32_t addVal = 0)
{
    // 写数据
    int ret = GmcKvPrepareStmtByLabelName(stmt, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchRetT batchRet;
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t value = 0;
    char key[MAX_NAME_LENGTH];
    for (int32_t i = 0; i < kvCount; i++) {
        (void)sprintf_s(key, sizeof(key), "Warmboot08KvKey%d", i);
        value = i + addVal;
        ret = GmcKvInputToStmt(stmt, key, sizeof(key), NULL, 0);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "input kv stmt  ret is %d, kv key is %d.", ret, i);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, kvCount);
    AW_MACRO_EXPECT_EQ_INT(successNum, kvCount);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return;
}

void TestCreateLabelAsync(GmcStmtT *stmt, const char *labelName, const char *schemaPath, const char *labelConfig)
{
    char *schemaJson = NULL;
    readJanssonFile(schemaPath, &schemaJson);
    EXPECT_NE((void *)NULL, schemaJson);
    AsyncUserDataT data = {0};
    int ret = GmcDropVertexLabelAsync(stmt, labelName, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_UNDEFINED_TABLE) {
        data.status = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcAsyncRequestDoneContextT context;
    context.createVertexWithNameCb = create_vertex_label_callback;
    context.userData = &data;
    ret = GmcCreateVertexLabelWithNameAsync(stmt, schemaJson, labelConfig, labelName, &context);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(schemaJson);
}

void TestDropLabelAsync(GmcStmtT *stmt, const char *labelName)
{
    int ret;
    AsyncUserDataT data = {0};
    ret = GmcDropVertexLabelAsync(stmt, labelName, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data.status = (data.status == GMERR_UNDEFINED_TABLE ? GMERR_OK : data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

// 导入用户配置白名单
void TestImportGroupAndUser(const char *userFlie = "./userFile/groupAndUser.gmuser")
{
    int ret;
    char cmd[MAX_CMD_SIZE];
    const char *allowListFile = userFlie;
    (void)snprintf(
        cmd, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(cmd, "successfully.", "success: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));
    return;
}

// 删除全局配置白名单
void TestRemoveGroupAndUser(const char *userFlie = "./userFile/groupAndUser.gmuser")
{
    int ret;
    char cmd[MAX_CMD_SIZE];
    const char *allowListFile = userFlie;
    (void)snprintf(
        cmd, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s -d", g_toolPath, allowListFile, g_connServer);
    ret = executeCommand(cmd, "successfully.", "remove", "success: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));
    return;
}

// 导入权限
void TestImportPolicy(const char *policyFile = "./policyFile/groupAndUserPolicy.gmpolicy")
{
    int ret;
    char cmd[MAX_CMD_SIZE];
    const char *sysPolicyFile = policyFile;
    (void)snprintf(
        cmd, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s -d", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(
        cmd, "successfully.", "success: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));
    return;
}

// 删除权限
void TestRemovePolicy(const char *policyFile = "./policyFile/groupAndUserPolicy.gmpolicy")
{
    int ret;
    char cmd[MAX_CMD_SIZE];
    const char *sysPolicyFile = policyFile;
    (void)snprintf(
        cmd, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s -d", g_toolPath, sysPolicyFile, g_connServer);
    ret = executeCommand(
        cmd, "successfully.", "revoke policy", "success: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));
    return;
}

// 公共变量
int g_affectRows = 0;
void TestGmcGetStmtAttr(GmcStmtT *stmt, int expectAffectRows, uint32_t cycleNum)
{
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
    if (ret == GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAffectRows, g_affectRows);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("GmcGetStmtAttr Fail, cycleNum = %d.\n", cycleNum);
    }
}

void TestGmcSetVertexPropertyPk(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = GmcSetVertexProperty(stmt, "PK", GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetVertexNodeT1V(GmcStmtT *stmt, uint32_t addVal)
{
    int ret = 0;
    GmcNodeT *root = NULL;
    GmcNodeT *t1V = NULL;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1_V", &t1V);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *item = NULL;
    for (uint32_t j = 0 + addVal; j < 10 + addVal; j++) {
        ret = GmcNodeAppendElement(t1V, &item);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t value = j;
        ret = GmcNodeSetPropertyByName(item, (char *)"V1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        TEST_ASSERT_INT32(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(item, (char *)"V2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        TEST_ASSERT_INT32(GMERR_OK, ret);
    }
}

void TestGmcSetVertexProperty(GmcStmtT *stmt, uint32_t i, uint32_t addVal, const char *labelName)
{
    int ret = 0;
    uint32_t value = i + addVal;

    uint32_t f1Value = value;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f2Value = value;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3Value = value;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f5Value = 1;
    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT32, &f5Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f6Value = 1;
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_UINT32, &f6Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (strcmp(labelName, g_clusterName) == 0) {
        uint32_t f7Value = ((i + 2) << 8);
        ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_UINT32, &f7Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f8Value = ((24) & 0xff);
        ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_UINT8, &f8Value, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (strcmp(labelName, g_heapName) == 0) {
        uint32_t f7Value = ((i + 2) << 8);
        ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_UINT32, &f7Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f8Value = ((24) & 0xff);
        ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_UINT8, &f8Value, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcSetVertexNodeT1V(stmt, addVal);
    } else if (strcmp(labelName, g_heapNameNotUnique) == 0) {
        char fixVal[17] = {0};
        (void)snprintf_s(fixVal, 17, 16, "ffffffff%08d", i);
        ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_FIXED, fixVal, strlen(fixVal));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f8Value = 128;
        ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_UINT8, &f8Value, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcSetVertexNodeT1V(stmt, addVal);
    } else {
        char fixVal[17] = {0};
        (void)snprintf_s(fixVal, 17, 16, "ffffffff%08d", i);
        ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_FIXED, fixVal, strlen(fixVal));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f8Value = 128;
        ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_UINT8, &f8Value, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGmcSetVertexPropertyP0(GmcStmtT *stmt)
{
    int ret = 0;

    // 写string数据
    uint32_t superSize = 400;
    char *superValue = (char *)malloc(superSize);
    if (superValue == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc superValue unsucc.");
        return;
    }
    memset(superValue, 'B', superSize);

    ret = GmcSetVertexProperty(stmt, "P0", GMC_DATATYPE_FIXED, superValue, superSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(superValue);
}

void TestGmcGetVertexPropertyByName(GmcStmtT *stmt, uint32_t i, uint32_t addVal)
{
    int ret = 0;
    uint32_t value = i + addVal;
    bool isNull;

    uint32_t f1Value;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1Value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(value, f1Value);

    uint32_t f2Value;
    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2Value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(value, f2Value);

    uint32_t f3Value;
    ret = GmcGetVertexPropertyByName(stmt, "F3", &f3Value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(value, f3Value);
}

// Insert
void TestInsertVertexLabel(GmcStmtT *stmt, uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName)
{
    int ret = 0;
    uint32_t i;

    // insert vertex
    for (i = begin; i < end; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // set pk
        TestGmcSetVertexPropertyPk(stmt, i);

        // set Property
        TestGmcSetVertexProperty(stmt, i, addVal, labelName);
        TestGmcSetVertexPropertyP0(stmt);

        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            printf("ret : %d, index : %d\n", ret, i);
            return;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// Update
void TestUpdateVertexLabelByKey(
    GmcStmtT *stmt, const char *keyName, uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName)
{
    int ret = 0;
    uint32_t i;

    // update vertex
    for (i = begin; i < end; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcSetVertexProperty(stmt, i, addVal, labelName);

        // update
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// Replace
void TestReplaceVertexLabel(GmcStmtT *stmt, uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName)
{
    int ret = 0;
    uint32_t i;

    // Replace vertex
    for (i = begin; i < end; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // set pk
        TestGmcSetVertexPropertyPk(stmt, i);

        // set Property
        TestGmcSetVertexProperty(stmt, i, addVal, labelName);
        TestGmcSetVertexPropertyP0(stmt);

        // Replace
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// delete
void TestDeleteLabelByKey(
    GmcStmtT *stmt, const char *keyName, uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName)
{
    int ret = 0;
    uint32_t i;
    uint32_t value;

    // delete vertex
    for (i = begin; i < end; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置Filter
        value = ((strcmp(keyName, g_clusterPk) == 0) || (strcmp(keyName, g_heapPk) == 0)) ? i : (i + addVal);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删除
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestScanLabelByKey(
    GmcStmtT *stmt, const char *keyName, uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName)
{
    int ret = 0;
    uint32_t keyvalue;

    int cnt = 0;
    for (int i = begin; i < end; i++) {
        // scan vertex
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        keyvalue = ((strcmp(keyName, g_clusterPk) == 0) || (strcmp(keyName, g_heapPk) == 0)) ? i : (i + addVal);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;

        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            if (GMERR_OK != ret) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = testGmcGetLastError();
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                break;
            }
            if (isFinish) {
                break;
            }

            TestGmcGetVertexPropertyByName(stmt, cnt, addVal);
            cnt++;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(g_recordCount, cnt);
    AW_FUN_Log(LOG_INFO, "Index scan[%s] is complete, count is %d.", keyName, cnt);
}

// local索引范围查询
void TestRangeScanLabelByLocal(
    GmcStmtT *stmt, const char *keyName, uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName)
{
    int ret = 0;
    uint32_t i;
    uint32_t value;
    GmcPropValueT local_left_value;
    GmcPropValueT local_right_value;
    GmcRangeItemT local_items;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t left_value1 = begin + addVal;
    uint32_t right_value1 = end + addVal;
    local_left_value.type = GMC_DATATYPE_UINT32;
    local_left_value.value = &left_value1;
    local_left_value.size = sizeof(GMC_DATATYPE_UINT32);
    local_right_value.type = GMC_DATATYPE_UINT32;
    local_right_value.value = &right_value1;
    local_right_value.size = sizeof(GMC_DATATYPE_UINT32);
    local_items.lValue = &local_left_value;
    local_items.rValue = &local_right_value;
    local_items.lFlag = GMC_COMPARE_RANGE_CLOSED;
    local_items.rFlag = GMC_COMPARE_RANGE_OPEN;
    local_items.order = GMC_ORDER_DESC;
    ret = GmcSetKeyRange(stmt, &local_items, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }

        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(g_recordCount, cnt);
    AW_FUN_Log(LOG_INFO, "local range scan is complete, count is %d.", cnt);
}

// local索引范围删除
void TestRangeDeleteLabelByLocal(
    GmcStmtT *stmt, const char *keyName, uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName)
{
    uint32_t ret = 0;

    GmcPropValueT local_left_value;
    GmcPropValueT local_right_value;
    GmcRangeItemT local_items;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t left_value1 = begin + addVal;
    uint32_t right_value1 = end + addVal;
    local_left_value.type = GMC_DATATYPE_UINT32;
    local_left_value.value = &left_value1;
    local_left_value.size = sizeof(GMC_DATATYPE_UINT32);
    local_right_value.type = GMC_DATATYPE_UINT32;
    local_right_value.value = &right_value1;
    local_right_value.size = sizeof(GMC_DATATYPE_UINT32);
    local_items.lValue = &local_left_value;
    local_items.rValue = &local_right_value;
    local_items.lFlag = GMC_COMPARE_RANGE_CLOSED;
    local_items.rFlag = GMC_COMPARE_RANGE_CLOSED;
    local_items.order = GMC_ORDER_DESC;

    ret = GmcSetKeyRange(stmt, &local_items, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// lpm单点查询
void TestScanLabelByLpm(
    GmcStmtT *stmt, const char *keyName, uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName)
{
    uint32_t ret = 0;
    int cnt = 0;
    for (uint32_t loop = begin; loop < end; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f5Value = 1;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f5Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f6Value = 1;
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &f6Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f7Value;
        uint8_t f8Value;
        char fixVal[17];
        if ((strcmp(keyName, g_clusterLpm4) == 0) || (strcmp(keyName, g_heapLpm4) == 0)) {
            f7Value = ((loop + 2) << 8);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &f7Value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            f8Value = ((24) & 0xff);
            ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &f8Value, sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            (void)snprintf_s(fixVal, 17, 16, "ffffffff%08d", loop);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, fixVal, strlen(fixVal));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            f8Value = 128;
            ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &f8Value, sizeof(uint8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            if (GMERR_OK != ret) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = testGmcGetLastError();
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                break;
            }
            if (isFinish) {
                break;
            }

            TestGmcGetVertexPropertyByName(stmt, cnt, addVal);
            cnt++;
        }
    }
}

// Lpm索引范围查询
void TestRangeScanLabelByLpm(
    GmcStmtT *stmt, const char *keyName, uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName)
{
    int ret = 0;
    uint32_t i;
    uint32_t value;
    GmcPropValueT local_left_value[4];
    GmcPropValueT local_right_value[4];
    GmcRangeItemT local_items[4];
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t left_value1 = 1;
    uint32_t right_value1 = 1;
    local_left_value[0].type = GMC_DATATYPE_UINT32;
    local_left_value[0].value = &left_value1;
    local_left_value[0].size = sizeof(GMC_DATATYPE_UINT32);
    local_right_value[0].type = GMC_DATATYPE_UINT32;
    local_right_value[0].value = &right_value1;
    local_right_value[0].size = sizeof(GMC_DATATYPE_UINT32);
    local_items[0].lValue = &local_left_value[0];
    local_items[0].rValue = &local_right_value[0];
    local_items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    local_items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    local_items[0].order = GMC_ORDER_DESC;

    local_left_value[1].type = GMC_DATATYPE_UINT32;
    local_left_value[1].value = &left_value1;
    local_left_value[1].size = sizeof(GMC_DATATYPE_UINT32);
    local_right_value[1].type = GMC_DATATYPE_UINT32;
    local_right_value[1].value = &right_value1;
    local_right_value[1].size = sizeof(GMC_DATATYPE_UINT32);
    local_items[1].lValue = &local_left_value[1];
    local_items[1].rValue = &local_right_value[1];
    local_items[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    local_items[1].rFlag = GMC_COMPARE_RANGE_CLOSED;
    local_items[1].order = GMC_ORDER_DESC;

    uint32_t left_value2;
    uint32_t right_value2;
    uint8_t left_value3;
    uint8_t right_value3;
    char fixValLeft[17] = {0};
    char fixValRight[17] = {0};
    if ((strcmp(keyName, g_clusterLpm4) == 0) || (strcmp(keyName, g_heapLpm4) == 0)) {
        left_value2 = (uint32_t)((begin + 2) << 8);
        right_value2 = (uint32_t)((end + 2) << 8);
        printf("left_value2 = %u, right_value2 = %u\n", left_value2, right_value2);
        local_left_value[2].type = GMC_DATATYPE_UINT32;
        local_left_value[2].value = &left_value2;
        local_left_value[2].size = sizeof(GMC_DATATYPE_UINT32);
        local_right_value[2].type = GMC_DATATYPE_UINT32;
        local_right_value[2].value = &right_value2;
        local_right_value[2].size = sizeof(GMC_DATATYPE_UINT32);

        left_value3 = ((24) & 0xff);
        right_value3 = ((24) & 0xff);
        local_left_value[3].type = GMC_DATATYPE_UINT8;
        local_left_value[3].value = &left_value3;
        local_left_value[3].size = 1;
        local_right_value[3].type = GMC_DATATYPE_UINT8;
        local_right_value[3].value = &right_value3;
        local_right_value[3].size = 1;
    } else {
        (void)snprintf_s(fixValLeft, 17, 16, "ffffffff%08d", begin);
        (void)snprintf_s(fixValRight, 17, 16, "ffffffff%08d", end);
        local_left_value[2].type = GMC_DATATYPE_FIXED;
        local_left_value[2].value = fixValLeft;
        local_left_value[2].size = strlen(fixValLeft);
        local_right_value[2].type = GMC_DATATYPE_FIXED;
        local_right_value[2].value = fixValRight;
        local_right_value[2].size = strlen(fixValRight);

        left_value3 = 128;
        right_value3 = 128;
        local_left_value[3].type = GMC_DATATYPE_UINT8;
        local_left_value[3].value = &left_value3;
        local_left_value[3].size = 1;
        local_right_value[3].type = GMC_DATATYPE_UINT8;
        local_right_value[3].value = &right_value3;
        local_right_value[3].size = 1;
    }
    local_items[2].lValue = &local_left_value[2];
    local_items[2].rValue = &local_right_value[2];
    local_items[2].lFlag = GMC_COMPARE_RANGE_CLOSED;
    local_items[2].rFlag = GMC_COMPARE_RANGE_CLOSED;
    local_items[2].order = GMC_ORDER_DESC;

    local_items[3].lValue = &local_left_value[3];
    local_items[3].rValue = &local_right_value[3];
    local_items[3].lFlag = GMC_COMPARE_RANGE_CLOSED;
    local_items[3].rFlag = GMC_COMPARE_RANGE_CLOSED;
    local_items[3].order = GMC_ORDER_DESC;

    ret = GmcSetKeyRange(stmt, local_items, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }

        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(g_recordCount, cnt);
    AW_FUN_Log(LOG_INFO, "%s range scan is complete, count is %d.", keyName, cnt);
}

#endif
