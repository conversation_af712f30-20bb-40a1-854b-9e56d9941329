/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: rsm_extend.h
 * Author: lushiguang
 * Create: 2025-07-3
 */
#ifndef RSM_EXTEND_H
#define RSM_EXTEND_H


#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum OperateType {
    T_RSM_INSERT,
    T_RSM_REPLACE,
    T_RSM_UPDATE,
    T_RSM_DELETE,
    T_RSM_BATCH_INSERT,
    T_RSM_BATCH_UPDATE,
    T_RSM_BATCH_DELETE
} TdmlTypeE;

#define MAX_TEST_TABLE_NAME_LEN 50
typedef struct {
    GmcConnT *conn;
    GmcStmtT *stmt;
    char tableName[MAX_TEST_TABLE_NAME_LEN];
    uint32_t startIndex;
    uint32_t count;
    uint32_t versionId;
    bool with<PERSON><PERSON><PERSON><PERSON>;
    TdmlTypeE type;
    bool isFixedTable;
} DmlArgsT;

uint32_t g_version0 = 0;
uint32_t g_version1 = 1;
uint32_t g_version2 = 2;
uint32_t g_version3 = 3;
uint32_t g_version4 = 4;
uint32_t g_version5 = 5;
uint32_t g_version6 = 6;
uint32_t g_version7 = 7;

uint32_t g_insertCount = 10000;
bool g_indexConflict = false;

uint32_t g_fullIndex = 0;
uint32_t g_asyncOperateCount = 0;
uint32_t g_asyncInsertCount = 0;
uint32_t g_asyncReplaceCount = 0;
uint32_t g_asyncUpdateCount = 0;
uint32_t g_asyncDeleteCount = 0;

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

char g_indexName[] = "PrimaryKey";
char *g_cond = (char *)"A0(int32)=%i{0}";

#define MAX_THREAD_COUNT 20

pthread_t g_thread[MAX_THREAD_COUNT];
int g_curUsedThCount = 0;
DmlArgsT g_dmlArg[MAX_THREAD_COUNT];

// 异步等待1ms
uint32_t g_asyncWaitTime = 1000;

const char *g_config = (char *)R"(
    {
        "max_record_count": 10000000,
        "isFastReadUncommitted": true,
        "is_support_reserved_memory": true,
        "defragmentation": true
    }
)";

const char *g_cwConfig = (char *)R"(
    {
        "max_record_count": 10000000,
        "isFastReadUncommitted": true,
        "is_support_reserved_memory": true,
        "defragmentation": true,
        "use_write_cache": true
    }
)";

const char *g_dwConfig = (char *)R"(
    {
        "max_record_count": 10000000,
        "isFastReadUncommitted": true,
        "is_support_reserved_memory": true,
        "direct_write": true,
        "defragmentation": true
    }
)";

char *g_varTableName = (char *)"rsm_var_vertex";
char *g_varTableNameDw = (char *)"rsm_var_vertex_dw";
char *g_varTableNameCw = (char *)"rsm_var_vertex_cw";
char *g_fixedTableName = (char *)"rsm_fixed_vertex";
char *g_fixedTableNameDw = (char *)"rsm_fixed_vertex_dw";
char *g_fixedTableNameCw = (char *)"rsm_fixed_vertex_cw";
bool g_withNewField = true;

int GenRandomNum(int min, int max)
{
    (void)srand(time(NULL));
    int random = rand() % (max - min + 1) + min;
    AW_FUN_Log(LOG_STEP, "GenRandomNum : %d", random);
    return random;
}

int ExecuteAndExpect(char *cmd, const char *expectText = NULL)
{
    int ret = T_FAILED;
#if defined RUN_DATACOM_HPE
    printf("[ExecuteAndExpect] popen can not run in hpe env\n");
    return 0;
#else
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", cmd);
        return T_FAILED;
    }
    char cmdOutput[1024] = {0};
    (void)memset(cmdOutput, '0', sizeof(cmdOutput));
    const char *temp = (expectText == NULL) ? "" : expectText;
    printf("[Execute]: %s\n[Expect]: %s\n[Result]:\n", cmd, temp);
    while (NULL != fgets(cmdOutput, sizeof(cmdOutput), pf)) {
        printf("%s", cmdOutput);
        if (strstr(cmdOutput, temp)) {
            ret = T_OK;
            break;
        }
        (void)memset(cmdOutput, '0', sizeof(cmdOutput));
    }
    if (pclose(pf) == -1) {
        perror("pclose fail");
    }
    pf = NULL;
    return ret;
#endif
}

int UpgradeSchema(const char *tableName, const char *schemaFile)
{
    char tempCmd[512] = {0};
    (void)sprintf(tempCmd, "gmddl -c alter -f %s -t %s  -u online", schemaFile, tableName);
    int ret = ExecuteAndExpect(tempCmd, (char *)"Alter schema upgrade successfully");
    return ret;
}

int DowngradeSchemaSync(const char *tableName, uint32_t dstVersion)
{
    char tempCmd[512] = {0};
    (void)sprintf(tempCmd, "gmddl -c alter -v %d -t %s -d sync", dstVersion, tableName);
    int ret = ExecuteAndExpect(tempCmd, (char *)"Alter schema degrade successfully");
    return ret;
}

int DowngradeSchemaAsync(const char *tableName, uint32_t dstVersion)
{
    char tempCmd[512] = {0};
    (void)sprintf(tempCmd, "gmddl -c alter -v %d -t %s -d async", dstVersion, tableName);
    int ret = ExecuteAndExpect(tempCmd, (char *)"Alter schema degrade successfully");
    return ret;
}

int CommonCreateTable(GmcStmtT *stmt, const char *lableName, const char *schemaFilePath, const char *config)
{
    char *schema = NULL;
    // 简单表
    readJanssonFile(schemaFilePath, &schema);
    if (schema == NULL) {
        return T_FAILED;
    }
    int ret = GmcCreateVertexLabelWithName(stmt, schema, config, lableName);
    free(schema);
    schema = NULL;
    return ret;
}

int CommonFixedTbIns(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t insertCount, bool withNewField, GmcOperationTypeE operateType)
{
    int ret = T_OK;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, versionId, operateType);
    RETURN_IFERR(ret);
    for (uint32_t i = startIndex; i < insertCount + startIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 1;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a2 = i + 2;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_INT64, &a2, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a3 = i + 3;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_INT64, &a3, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a4 = i + 4;
        ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_INT64, &a4, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a5 = i + 5;
        ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_INT64, &a5, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a6 = i + 6;
        ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_INT64, &a6, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a7 = i + 7;
        ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_INT64, &a7, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a8 = i + 8;
        ret = GmcSetVertexProperty(stmt, "A8", GMC_DATATYPE_INT64, &a8, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a9 = i + 9;
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_INT64, &a9, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t add1;
        int64_t add2;
        int64_t add3;
        int64_t add4;
        int64_t add5;
        int64_t add6;
        int64_t add7;

        if (withNewField) {
            if (versionId > 0) {
                add1 = i + 10;
                ret = GmcSetVertexProperty(stmt, "add1", GMC_DATATYPE_INT64, &add1, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 1) {
                add2 = i + 11;
                ret = GmcSetVertexProperty(stmt, "add2", GMC_DATATYPE_INT64, &add2, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 2) {
                add3 = i + 12;
                ret = GmcSetVertexProperty(stmt, "add3", GMC_DATATYPE_INT64, &add3, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 3) {
                add4 = i + 13;
                ret = GmcSetVertexProperty(stmt, "add4", GMC_DATATYPE_INT64, &add4, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 4) {
                add5 = i + 14;
                ret = GmcSetVertexProperty(stmt, "add5", GMC_DATATYPE_INT64, &add5, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 5) {
                add6 = i + 15;
                ret = GmcSetVertexProperty(stmt, "add6", GMC_DATATYPE_INT64, &add6, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 6) {
                add7 = i + 16;
                ret = GmcSetVertexProperty(stmt, "add7", GMC_DATATYPE_INT64, &add7, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
        }

        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            g_fullIndex = i;
        }
        if (ret != T_OK) {
            AW_FUN_Log(LOG_STEP, "Exit when insert record, index:%u status: %d", i, ret);
            return ret;
        }
        g_asyncOperateCount++;
        if (operateType == GMC_OPERATION_INSERT) {
            g_asyncInsertCount++;
        } else {
            g_asyncReplaceCount++;
        }
    }
    return ret;
}

int CommonVarTbIns(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex, uint32_t insertCount,
    bool withNewField, GmcOperationTypeE operateType)
{
    int ret = T_OK;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, versionId, operateType);
    RETURN_IFERR(ret);
    char add1[50] = {0};
    char add2[50] = {0};
    char add3[50] = {0};
    char add4[50] = {0};
    char add5[50] = {0};
    char add6[50] = {0};
    char add7[50] = {0};
    for (uint32_t i = startIndex; i < insertCount + startIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 1;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a2 = i + 2;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_INT64, &a2, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a3 = i + 3;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_INT64, &a3, sizeof(int64_t));
        RETURN_IFERR(ret);
        char s1[50] = {0};
        (void)snprintf(s1, sizeof(s1), "aaaaaaaaaaaaaaaaaaaaaaaaaa_%d", i);
        ret = GmcSetVertexProperty(stmt, "S1", GMC_DATATYPE_STRING, s1, strlen(s1));
        RETURN_IFERR(ret);
        char s2[50] = {0};
        (void)snprintf(s2, sizeof(s2), "bbbbbbbbbbbbbbbbbbbbbbbbbb_%d", i);
        ret = GmcSetVertexProperty(stmt, "S2", GMC_DATATYPE_STRING, s2, strlen(s2));
        RETURN_IFERR(ret);
        if (withNewField) {
            if (versionId > 0) {
                (void)snprintf(add1, sizeof(add1), "cccccccccccccccccccccccccc_%d", i);
                ret = GmcSetVertexProperty(stmt, "add1", GMC_DATATYPE_STRING, add1, strlen(add1));
                RETURN_IFERR(ret);
            }
            if (versionId > 1) {
                (void)snprintf(add2, sizeof(add2), "dddddddddddddddddddddddddd_%d", i);
                ret = GmcSetVertexProperty(stmt, "add2", GMC_DATATYPE_STRING, add2, strlen(add2));
                RETURN_IFERR(ret);
            }
            if (versionId > 2) {
                (void)snprintf(add3, sizeof(add3), "eeeeeeeeeeeeeeeeeeeeeeeeee_%d", i);
                ret = GmcSetVertexProperty(stmt, "add3", GMC_DATATYPE_STRING, add3, strlen(add3));
                RETURN_IFERR(ret);
            }
            if (versionId > 3) {
                (void)snprintf(add4, sizeof(add4), "ffffffffffffffffffffffffff_%d", i);
                ret = GmcSetVertexProperty(stmt, "add4", GMC_DATATYPE_STRING, add4, strlen(add4));
                RETURN_IFERR(ret);
            }
            if (versionId > 4) {
                (void)snprintf(add5, sizeof(add5), "gggggggggggggggggggggggggg_%d", i);
                ret = GmcSetVertexProperty(stmt, "add5", GMC_DATATYPE_STRING, add5, strlen(add5));
                RETURN_IFERR(ret);
            }
            if (versionId > 5) {
                (void)snprintf(add6, sizeof(add6), "hhhhhhhhhhhhhhhhhhhhhhhhhh_%d", i);
                ret = GmcSetVertexProperty(stmt, "add6", GMC_DATATYPE_STRING, add6, strlen(add6));
                RETURN_IFERR(ret);
            }
            if (versionId > 6) {
                (void)snprintf(add7, sizeof(add7), "iiiiiiiiiiiiiiiiiiiiiiiiii_%d", i);
                ret = GmcSetVertexProperty(stmt, "add7", GMC_DATATYPE_STRING, add7, strlen(add7));
                RETURN_IFERR(ret);
            }
        }
        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            g_fullIndex = i;
        }
        if (ret != T_OK) {
            AW_FUN_Log(LOG_STEP, "Exit when insert record, index:%u status: %d", i, ret);
            return ret;
        }
        g_asyncOperateCount++;
        if (operateType == GMC_OPERATION_INSERT) {
            g_asyncInsertCount++;
        } else {
            g_asyncReplaceCount++;
        }
    }
    return ret;
}

int CommonReplaceFixedTb(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t insertCount, bool withNewField)
{
    int ret =
        CommonFixedTbIns(stmt, lableName, versionId, startIndex, insertCount, withNewField, GMC_OPERATION_REPLACE);
    return ret;
}

int CommonInsertFixedTb(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t insertCount, bool withNewField)
{
    int ret = CommonFixedTbIns(stmt, lableName, versionId, startIndex, insertCount, withNewField, GMC_OPERATION_INSERT);
    return ret;
}

int CommonReplaceVarTb(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t insertCount, bool withNewField)
{
    int ret = CommonVarTbIns(stmt, lableName, versionId, startIndex, insertCount, withNewField, GMC_OPERATION_REPLACE);
    return ret;
}

int CommonInsertVarTb(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t insertCount, bool withNewField)
{
    int ret = CommonVarTbIns(stmt, lableName, versionId, startIndex, insertCount, withNewField, GMC_OPERATION_INSERT);
    return ret;
}

int CommonBatchReplaceVarTb(GmcConnT *conn, GmcStmtT *stmt, const char *lableName, uint32_t versionId,
    uint32_t startIndex, uint32_t insertCount)
{
    int ret;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, versionId, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);
    char s1[50] = {0};
    char s2[50] = {0};
    for (uint32_t i = startIndex; i < insertCount + startIndex; i++) {
        g_asyncOperateCount++;
        int32_t a0 = i;
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 1;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a2 = i + 2;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_INT64, &a2, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a3 = i + 3;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_INT64, &a3, sizeof(int64_t));
        RETURN_IFERR(ret);
        (void)snprintf(s1, sizeof(s1), "aaaaaaaaaaaaaaaaaaaaaaaaaa_%d", i);
        ret = GmcSetVertexProperty(stmt, "S1", GMC_DATATYPE_STRING, s1, strlen(s1));
        RETURN_IFERR(ret);
        (void)snprintf(s2, sizeof(s2), "bbbbbbbbbbbbbbbbbbbbbbbbbb_%d", i);
        ret = GmcSetVertexProperty(stmt, "S2", GMC_DATATYPE_STRING, s2, strlen(s2));
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        if (ret == GMERR_BATCH_BUFFER_FULL) {
            i--;
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != T_OK) {
                AW_FUN_Log(LOG_STEP, "Exit when Batch insert record, index:%u status: %d", i, ret);
                (void)GmcBatchDestroy(batch);
                return ret;
            }
            ret = GmcBatchDestroy(batch);
            RETURN_IFERR(ret);
            ret = GmcBatchPrepare(conn, &batchOption, &batch);
            RETURN_IFERR(ret);
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    ret = GmcBatchDestroy(batch);
    return ret;
}

int CommonBatchReplaceFixedTb(GmcConnT *conn, GmcStmtT *stmt, const char *lableName, uint32_t versionId,
    uint32_t startIndex, uint32_t insertCount)
{
    int ret;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, versionId, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);
    for (uint32_t i = startIndex; i < insertCount + startIndex; i++) {
        g_asyncOperateCount++;
        int32_t a0 = i;
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 1;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a2 = i + 2;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_INT64, &a2, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a3 = i + 3;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_INT64, &a3, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a4 = i + 4;
        ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_INT64, &a4, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a5 = i + 5;
        ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_INT64, &a5, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a6 = i + 6;
        ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_INT64, &a6, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a7 = i + 7;
        ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_INT64, &a7, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a8 = i + 8;
        ret = GmcSetVertexProperty(stmt, "A8", GMC_DATATYPE_INT64, &a8, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a9 = i + 9;
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_INT64, &a9, sizeof(int64_t));
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        if (ret == GMERR_BATCH_BUFFER_FULL) {
            i--;
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != T_OK) {
                AW_FUN_Log(LOG_STEP, "Exit when Batch insert record, index:%u status: %d", i, ret);
                (void)GmcBatchDestroy(batch);
                return ret;
            }
            ret = GmcBatchDestroy(batch);
            RETURN_IFERR(ret);
            ret = GmcBatchPrepare(conn, &batchOption, &batch);
            RETURN_IFERR(ret);
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    ret = GmcBatchDestroy(batch);
    return ret;
}

int CommonBatchDelete(GmcConnT *conn, GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t insertCount)
{
    int ret;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, versionId, GMC_OPERATION_DELETE);
    RETURN_IFERR(ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);
    for (uint32_t i = startIndex; i < insertCount + startIndex; i++) {
        g_asyncOperateCount++;
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_indexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        if (ret == GMERR_BATCH_BUFFER_FULL) {
            i--;
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != T_OK) {
                AW_FUN_Log(LOG_STEP, "Exit when Batch delete record, index:%u status: %d", i, ret);
                (void)GmcBatchDestroy(batch);
                return ret;
            }
            ret = GmcBatchDestroy(batch);
            RETURN_IFERR(ret);
            ret = GmcBatchPrepare(conn, &batchOption, &batch);
            RETURN_IFERR(ret);
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    ret = GmcBatchDestroy(batch);
    return ret;
}

int CommonBatchUpdateVarTb(GmcConnT *conn, GmcStmtT *stmt, const char *lableName, uint32_t versionId,
    uint32_t startIndex, uint32_t insertCount)
{
    int ret;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, versionId, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);
    char s1[50] = {0};
    char s2[50] = {0};
    for (uint32_t i = startIndex; i < insertCount + startIndex; i++) {
        g_asyncOperateCount++;
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_indexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 1000000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a2 = i + 20;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_INT64, &a2, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a3 = i + 30;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_INT64, &a3, sizeof(int64_t));
        RETURN_IFERR(ret);
        (void)snprintf(s1, sizeof(s1), "aaaaaaaaaaaaaaaaaaaaaaaaaa_%d", i);
        ret = GmcSetVertexProperty(stmt, "S1", GMC_DATATYPE_STRING, s1, strlen(s1));
        RETURN_IFERR(ret);
        (void)snprintf(s2, sizeof(s2), "bbbbbbbbbbbbbbbbbbbbbbbbbb_%d", i);
        ret = GmcSetVertexProperty(stmt, "S2", GMC_DATATYPE_STRING, s2, strlen(s2));
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        if (ret == GMERR_BATCH_BUFFER_FULL) {
            i--;
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != T_OK) {
                AW_FUN_Log(LOG_STEP, "Exit when Batch update record, index:%u status: %d", i, ret);
                (void)GmcBatchDestroy(batch);
                return ret;
            }
            ret = GmcBatchDestroy(batch);
            RETURN_IFERR(ret);
            ret = GmcBatchPrepare(conn, &batchOption, &batch);
            RETURN_IFERR(ret);
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    ret = GmcBatchDestroy(batch);
    return ret;
}

int CommonBatchUpdateFixedTb(GmcConnT *conn, GmcStmtT *stmt, const char *lableName, uint32_t versionId,
    uint32_t startIndex, uint32_t insertCount)
{
    int ret;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, versionId, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    RETURN_IFERR(ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    RETURN_IFERR(ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    RETURN_IFERR(ret);
    for (uint32_t i = startIndex; i < insertCount + startIndex; i++) {
        g_asyncOperateCount++;
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_indexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 1000000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a2 = i + 20;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_INT64, &a2, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a3 = i + 30;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_INT64, &a3, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a4 = i + 40;
        ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_INT64, &a4, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a5 = i + 50;
        ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_INT64, &a5, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a6 = i + 60;
        ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_INT64, &a6, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a7 = i + 70;
        ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_INT64, &a7, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a8 = i + 80;
        ret = GmcSetVertexProperty(stmt, "A8", GMC_DATATYPE_INT64, &a8, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a9 = i + 90;
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_INT64, &a9, sizeof(int64_t));
        RETURN_IFERR(ret);
        ret = GmcBatchAddDML(batch, stmt);
        if (ret == GMERR_BATCH_BUFFER_FULL) {
            i--;
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != T_OK) {
                AW_FUN_Log(LOG_STEP, "Exit when Batch update record, index:%u status: %d", i, ret);
                (void)GmcBatchDestroy(batch);
                return ret;
            }
            ret = GmcBatchDestroy(batch);
            RETURN_IFERR(ret);
            ret = GmcBatchPrepare(conn, &batchOption, &batch);
            RETURN_IFERR(ret);
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    RETURN_IFERR(ret);
    ret = GmcBatchDestroy(batch);
    return ret;
}

int CommonUpdateFixedTb(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t insertCount, bool withNewField)
{
    int ret = T_OK;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, versionId, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    for (uint32_t i = startIndex; i < insertCount + startIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_indexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = (g_indexConflict) ? 1 : i + 1000000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a2 = i + 20;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_INT64, &a2, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a3 = i + 30;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_INT64, &a3, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a4 = i + 40;
        ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_INT64, &a4, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a5 = i + 50;
        ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_INT64, &a5, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a6 = i + 60;
        ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_INT64, &a6, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a7 = i + 70;
        ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_INT64, &a7, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a8 = i + 80;
        ret = GmcSetVertexProperty(stmt, "A8", GMC_DATATYPE_INT64, &a8, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a9 = i + 90;
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_INT64, &a9, sizeof(int64_t));
        RETURN_IFERR(ret);
        if (withNewField) {
            if (versionId > 0) {
                int64_t add1 = i + 100;
                ret = GmcSetVertexProperty(stmt, "add1", GMC_DATATYPE_INT64, &add1, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 1) {
                int64_t add2 = i + 110;
                ret = GmcSetVertexProperty(stmt, "add2", GMC_DATATYPE_INT64, &add2, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 2) {
                int64_t add3 = i + 120;
                ret = GmcSetVertexProperty(stmt, "add3", GMC_DATATYPE_INT64, &add3, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 3) {
                int64_t add4 = i + 130;
                ret = GmcSetVertexProperty(stmt, "add4", GMC_DATATYPE_INT64, &add4, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 4) {
                int64_t add5 = i + 140;
                ret = GmcSetVertexProperty(stmt, "add5", GMC_DATATYPE_INT64, &add5, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 5) {
                int64_t add6 = i + 150;
                ret = GmcSetVertexProperty(stmt, "add6", GMC_DATATYPE_INT64, &add6, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 6) {
                int64_t add7 = i + 160;
                ret = GmcSetVertexProperty(stmt, "add7", GMC_DATATYPE_INT64, &add7, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
        }

        ret = GmcExecute(stmt);
        if (g_indexConflict && ret == GMERR_UNIQUE_VIOLATION) {
            ret = T_OK;
        }
        if (ret != T_OK) {
            AW_FUN_Log(LOG_STEP, "Exit when update record, index:%u status: %d", i, ret);
            return ret;
        }
        g_asyncOperateCount++;
        g_asyncUpdateCount++;
    }
    return ret;
}

int CommonUpdateVarTb(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t insertCount, bool withNewField)
{
    int ret = T_OK;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, versionId, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    char add1[50] = {0};
    char add2[50] = {0};
    char add3[50] = {0};
    char add4[50] = {0};
    char add5[50] = {0};
    char add6[50] = {0};
    char add7[50] = {0};
    for (uint32_t i = startIndex; i < insertCount + startIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_indexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = (g_indexConflict) ? 1 : i + 1000000;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a2 = i + 20;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_INT64, &a2, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a3 = i + 30;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_INT64, &a3, sizeof(int64_t));
        RETURN_IFERR(ret);
        char s1[50] = {0};
        (void)snprintf(s1, sizeof(s1), "aaaaaaaaaaaaaaaaaaaaaaaaaa_%d", i);
        ret = GmcSetVertexProperty(stmt, "S1", GMC_DATATYPE_STRING, s1, strlen(s1));
        RETURN_IFERR(ret);
        char s2[50] = {0};
        (void)snprintf(s2, sizeof(s2), "bbbbbbbbbbbbbbbbbbbbbbbbbb_%d", i);
        ret = GmcSetVertexProperty(stmt, "S2", GMC_DATATYPE_STRING, s2, strlen(s2));
        RETURN_IFERR(ret);
        if (withNewField) {
            if (versionId > 0) {
                (void)snprintf(add1, sizeof(add1), "cccccccccccccccccccccccccc_%d", i);
                ret = GmcSetVertexProperty(stmt, "add1", GMC_DATATYPE_STRING, add1, strlen(add1));
                RETURN_IFERR(ret);
            }
            if (versionId > 1) {
                (void)snprintf(add2, sizeof(add2), "dddddddddddddddddddddddddd_%d", i);
                ret = GmcSetVertexProperty(stmt, "add2", GMC_DATATYPE_STRING, add2, strlen(add2));
                RETURN_IFERR(ret);
            }
            if (versionId > 2) {
                (void)snprintf(add3, sizeof(add3), "eeeeeeeeeeeeeeeeeeeeeeeeee_%d", i);
                ret = GmcSetVertexProperty(stmt, "add3", GMC_DATATYPE_STRING, add3, strlen(add3));
                RETURN_IFERR(ret);
            }
            if (versionId > 3) {
                (void)snprintf(add4, sizeof(add4), "ffffffffffffffffffffffffff_%d", i);
                ret = GmcSetVertexProperty(stmt, "add4", GMC_DATATYPE_STRING, add4, strlen(add4));
                RETURN_IFERR(ret);
            }
            if (versionId > 4) {
                (void)snprintf(add5, sizeof(add5), "gggggggggggggggggggggggggg_%d", i);
                ret = GmcSetVertexProperty(stmt, "add5", GMC_DATATYPE_STRING, add5, strlen(add5));
                RETURN_IFERR(ret);
            }
            if (versionId > 5) {
                (void)snprintf(add6, sizeof(add6), "hhhhhhhhhhhhhhhhhhhhhhhhhh_%d", i);
                ret = GmcSetVertexProperty(stmt, "add6", GMC_DATATYPE_STRING, add6, strlen(add6));
                RETURN_IFERR(ret);
            }
            if (versionId > 6) {
                (void)snprintf(add7, sizeof(add7), "iiiiiiiiiiiiiiiiiiiiiiiiii_%d", i);
                ret = GmcSetVertexProperty(stmt, "add7", GMC_DATATYPE_STRING, add7, strlen(add7));
                RETURN_IFERR(ret);
            }
        } else {
            if (versionId > 0) {
                ret = GmcSetVertexProperty(stmt, "add1", GMC_DATATYPE_STRING, "", 0);
                RETURN_IFERR(ret);
            }
            if (versionId > 1) {
                ret = GmcSetVertexProperty(stmt, "add2", GMC_DATATYPE_STRING, "", 0);
                RETURN_IFERR(ret);
            }
            if (versionId > 2) {
                ret = GmcSetVertexProperty(stmt, "add3", GMC_DATATYPE_STRING, "", 0);
                RETURN_IFERR(ret);
            }
            if (versionId > 3) {
                ret = GmcSetVertexProperty(stmt, "add4", GMC_DATATYPE_STRING, "", 0);
                RETURN_IFERR(ret);
            }
            if (versionId > 4) {
                ret = GmcSetVertexProperty(stmt, "add5", GMC_DATATYPE_STRING, "", 0);
                RETURN_IFERR(ret);
            }
            if (versionId > 5) {
                ret = GmcSetVertexProperty(stmt, "add6", GMC_DATATYPE_STRING, "", 0);
                RETURN_IFERR(ret);
            }
            if (versionId > 6) {
                ret = GmcSetVertexProperty(stmt, "add7", GMC_DATATYPE_STRING, "", 0);
                RETURN_IFERR(ret);
            }
        }
        ret = GmcExecute(stmt);
        if (g_indexConflict && ret == GMERR_UNIQUE_VIOLATION) {
            ret = T_OK;
        }
        if (ret != T_OK) {
            AW_FUN_Log(LOG_STEP, "Exit when update record, index:%u status: %d", i, ret);
            return ret;
        }
        g_asyncOperateCount++;
        g_asyncUpdateCount++;
    }
    return ret;
}

int CommonDelete(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex, uint32_t insertCount)
{
    int ret = T_OK;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, versionId, GMC_OPERATION_DELETE);
    RETURN_IFERR(ret);
    for (uint32_t i = startIndex; i < insertCount + startIndex; i++) {
        int32_t a0 = i;
        ret = GmcSetIndexKeyName(stmt, g_indexName);
        RETURN_IFERR(ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        if (ret != T_OK) {
            AW_FUN_Log(LOG_STEP, "Exit when delete record, index:%u status: %d", i, ret);
            return ret;
        }
        g_asyncOperateCount++;
        g_asyncDeleteCount++;
    }
    return ret;
}

int ReplaceVarTbUntilPagesFull(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t fullPageCount,
    uint32_t *actCount, bool withNewField)
{
    int ret;
    int checkDstPageCount = fullPageCount + 1;
    char cmd[200] = {0};
    (void)snprintf(cmd, sizeof(cmd),
        "gmsysview -q 'V$STORAGE_HEAP_STAT' -F 'LABEL_NAME=%s'|grep PAGE_COUNT|awk '{print $NF}'|grep '^%d$'",
        lableName, checkDstPageCount);
    int count = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, versionId, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    char add1[50] = {0};
    char add2[50] = {0};
    char add3[50] = {0};
    char add4[50] = {0};
    char add5[50] = {0};
    char add6[50] = {0};
    char add7[50] = {0};
    for (uint32_t i = 0; i < 0xFFFFFFFF; i++) {
        int32_t a0 = i;
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 1;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a2 = i + 2;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_INT64, &a2, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a3 = i + 3;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_INT64, &a3, sizeof(int64_t));
        RETURN_IFERR(ret);
        char s1[50] = {0};
        (void)snprintf(s1, sizeof(s1), "aaaaaaaaaaaaaaaaaaaaaaaaaa_%d", i);
        ret = GmcSetVertexProperty(stmt, "S1", GMC_DATATYPE_STRING, s1, strlen(s1));
        RETURN_IFERR(ret);
        char s2[50] = {0};
        (void)snprintf(s2, sizeof(s2), "bbbbbbbbbbbbbbbbbbbbbbbbbb_%d", i);
        ret = GmcSetVertexProperty(stmt, "S2", GMC_DATATYPE_STRING, s2, strlen(s2));
        RETURN_IFERR(ret);
        if (withNewField) {
            if (versionId > 0) {
                (void)snprintf(add1, sizeof(add1), "cccccccccccccccccccccccccc_%d", i);
                ret = GmcSetVertexProperty(stmt, "add1", GMC_DATATYPE_STRING, add1, strlen(add1));
                RETURN_IFERR(ret);
            }
            if (versionId > 1) {
                (void)snprintf(add2, sizeof(add2), "dddddddddddddddddddddddddd_%d", i);
                ret = GmcSetVertexProperty(stmt, "add2", GMC_DATATYPE_STRING, add2, strlen(add2));
                RETURN_IFERR(ret);
            }
            if (versionId > 2) {
                (void)snprintf(add3, sizeof(add3), "eeeeeeeeeeeeeeeeeeeeeeeeee_%d", i);
                ret = GmcSetVertexProperty(stmt, "add3", GMC_DATATYPE_STRING, add3, strlen(add3));
                RETURN_IFERR(ret);
            }
            if (versionId > 3) {
                (void)snprintf(add4, sizeof(add4), "ffffffffffffffffffffffffff_%d", i);
                ret = GmcSetVertexProperty(stmt, "add4", GMC_DATATYPE_STRING, add4, strlen(add4));
                RETURN_IFERR(ret);
            }
            if (versionId > 4) {
                (void)snprintf(add5, sizeof(add5), "gggggggggggggggggggggggggg_%d", i);
                ret = GmcSetVertexProperty(stmt, "add5", GMC_DATATYPE_STRING, add5, strlen(add5));
                RETURN_IFERR(ret);
            }
            if (versionId > 5) {
                (void)snprintf(add6, sizeof(add6), "hhhhhhhhhhhhhhhhhhhhhhhhhh_%d", i);
                ret = GmcSetVertexProperty(stmt, "add6", GMC_DATATYPE_STRING, add6, strlen(add6));
                RETURN_IFERR(ret);
            }
            if (versionId > 6) {
                (void)snprintf(add7, sizeof(add7), "iiiiiiiiiiiiiiiiiiiiiiiiii_%d", i);
                ret = GmcSetVertexProperty(stmt, "add7", GMC_DATATYPE_STRING, add7, strlen(add7));
                RETURN_IFERR(ret);
            }
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        if (i % 100 == 0) {
            ret = GtExecSystemCmd(cmd);
            if (ret == GMERR_OK) {
                count = i;
                break;
            }
        }
    }
    *actCount = count;
    return T_OK;
}

int ReplaceFixedTbUntilPagesFull(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t fullPageCount,
    uint32_t *actCount, bool withNewField)
{
    int ret;
    int checkDstPageCount = fullPageCount + 1;
    char cmd[200] = {0};
    (void)snprintf(cmd, sizeof(cmd),
        "gmsysview -q 'V$STORAGE_HEAP_STAT' -F 'LABEL_NAME=%s'|grep PAGE_COUNT|awk '{print $NF}'|grep '^%d$'",
        lableName, checkDstPageCount);
    int count = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, lableName, versionId, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    for (uint32_t i = 0; i < 0xFFFFFFFF; i++) {
        int32_t a0 = i;
        ret = GmcSetVertexProperty(stmt, "A0", GMC_DATATYPE_INT32, &a0, sizeof(int32_t));
        RETURN_IFERR(ret);
        int64_t a1 = i + 1;
        ret = GmcSetVertexProperty(stmt, "A1", GMC_DATATYPE_INT64, &a1, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a2 = i + 2;
        ret = GmcSetVertexProperty(stmt, "A2", GMC_DATATYPE_INT64, &a2, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a3 = i + 3;
        ret = GmcSetVertexProperty(stmt, "A3", GMC_DATATYPE_INT64, &a3, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a4 = i + 4;
        ret = GmcSetVertexProperty(stmt, "A4", GMC_DATATYPE_INT64, &a4, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a5 = i + 5;
        ret = GmcSetVertexProperty(stmt, "A5", GMC_DATATYPE_INT64, &a5, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a6 = i + 6;
        ret = GmcSetVertexProperty(stmt, "A6", GMC_DATATYPE_INT64, &a6, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a7 = i + 7;
        ret = GmcSetVertexProperty(stmt, "A7", GMC_DATATYPE_INT64, &a7, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a8 = i + 8;
        ret = GmcSetVertexProperty(stmt, "A8", GMC_DATATYPE_INT64, &a8, sizeof(int64_t));
        RETURN_IFERR(ret);
        int64_t a9 = i + 9;
        ret = GmcSetVertexProperty(stmt, "A9", GMC_DATATYPE_INT64, &a9, sizeof(int64_t));
        RETURN_IFERR(ret);
        if (withNewField) {
            if (versionId > 0) {
                int64_t add1 = i + 10;
                ret = GmcSetVertexProperty(stmt, "add1", GMC_DATATYPE_INT64, &add1, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 1) {
                int64_t add2 = i + 11;
                ret = GmcSetVertexProperty(stmt, "add2", GMC_DATATYPE_INT64, &add2, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 2) {
                int64_t add3 = i + 12;
                ret = GmcSetVertexProperty(stmt, "add3", GMC_DATATYPE_INT64, &add3, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 3) {
                int64_t add4 = i + 13;
                ret = GmcSetVertexProperty(stmt, "add4", GMC_DATATYPE_INT64, &add4, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 4) {
                int64_t add5 = i + 14;
                ret = GmcSetVertexProperty(stmt, "add5", GMC_DATATYPE_INT64, &add5, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 5) {
                int64_t add6 = i + 15;
                ret = GmcSetVertexProperty(stmt, "add6", GMC_DATATYPE_INT64, &add6, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
            if (versionId > 6) {
                int64_t add7 = i + 16;
                ret = GmcSetVertexProperty(stmt, "add7", GMC_DATATYPE_INT64, &add7, sizeof(int64_t));
                RETURN_IFERR(ret);
            }
        }

        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        if (i % 100 == 0) {
            ret = GtExecSystemCmd(cmd);
            if (ret == GMERR_OK) {
                count = i;
                break;
            }
        }
    }
    *actCount = count;
    return T_OK;
}

void WarmReboot()
{
    (void)system("sh $TEST_HOME/tools/stop.sh -f");
    (void)system("sh $TEST_HOME/tools/start.sh -f -rb");
}

int UpgradeVersion(const char *lableName, char *schemaPathPrefix, uint32_t versionId)
{
    char schemaPath[100] = {0};
    (void)snprintf(schemaPath, sizeof(schemaPath), "./schema/%s_%d.gmjson", schemaPathPrefix, versionId);
    int ret = UpgradeSchema(lableName, schemaPath);
    return ret;
}

int UpgradeMultiVersion(const char *lableName, char *schemaPathPrefix, uint32_t maxVersionId)
{
    int ret = T_OK;
    char schemaPath[100] = {0};
    for (uint32_t i = 1; i <= maxVersionId; i++) {
        (void)snprintf(schemaPath, sizeof(schemaPath), "./schema/%s_%d.gmjson", schemaPathPrefix, i);
        ret = UpgradeSchema(lableName, schemaPath);
        RETURN_IFERR(ret);
    }
    return ret;
}

int VarTbUpgrade2Version(const char *lableName)
{
    int ret = T_OK;
    uint32_t maxVersionId = 2;
    ret = UpgradeMultiVersion(lableName, (char *)"var_vertex", maxVersionId);
    return ret;
}

int VarTbUpgrade3Version(const char *lableName)
{
    int ret = T_OK;
    uint32_t maxVersionId = 3;
    ret = UpgradeMultiVersion(lableName, (char *)"var_vertex", maxVersionId);
    return ret;
}

int VarTbUpgrade4Version(const char *lableName)
{
    int ret = T_OK;
    uint32_t maxVersionId = 4;
    ret = UpgradeMultiVersion(lableName, (char *)"var_vertex", maxVersionId);
    return ret;
}

int VarTbUpgrade5Version(const char *lableName)
{
    int ret = T_OK;
    uint32_t maxVersionId = 5;
    ret = UpgradeMultiVersion(lableName, (char *)"var_vertex", maxVersionId);
    return ret;
}

int VarTbUpgrade7Version(const char *lableName)
{
    int ret = T_OK;
    uint32_t maxVersionId = 7;
    ret = UpgradeMultiVersion(lableName, (char *)"var_vertex", maxVersionId);
    return ret;
}

int FixedTbUpgrade3Version(const char *lableName)
{
    int ret = T_OK;
    uint32_t maxVersionId = 3;
    ret = UpgradeMultiVersion(lableName, (char *)"fixed_vertex", maxVersionId);
    return ret;
}

int FixedTbUpgrade4Version(const char *lableName)
{
    int ret = T_OK;
    uint32_t maxVersionId = 4;
    ret = UpgradeMultiVersion(lableName, (char *)"fixed_vertex", maxVersionId);
    return ret;
}

int FixedTbUpgrade7Version(const char *lableName)
{
    int ret = T_OK;
    uint32_t maxVersionId = 7;
    ret = UpgradeMultiVersion(lableName, (char *)"fixed_vertex", maxVersionId);
    return ret;
}

int CommonCheckVarTb(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex, uint32_t endIndex,
    bool withNewField)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format, "A0(int32)=%i{0};A1(int64)=%i{1};A2(int64)=%i{2};A3(int64)=%i{3};S1(string)=%f{26}a_%i{0};S2("
        "string)=%f{26}b_%i{0}");
    if (withNewField) {
        if (versionId > 0) {
            (void)strcat(format, ";add1(string)=%f{26}c_%i{0}");
        }
        if (versionId > 1) {
            (void)strcat(format, ";add2(string)=%f{26}d_%i{0}");
        }
        if (versionId > 2) {
            (void)strcat(format, ";add3(string)=%f{26}e_%i{0}");
        }
        if (versionId > 3) {
            (void)strcat(format, ";add4(string)=%f{26}f_%i{0}");
        }
        if (versionId > 4) {
            (void)strcat(format, ";add5(string)=%f{26}g_%i{0}");
        }
        if (versionId > 5) {
            (void)strcat(format, ";add6(string)=%f{26}h_%i{0}");
        }
        if (versionId > 6) {
            (void)strcat(format, ";add7(string)=%f{26}i_%i{0}");
        }
    }

    ret = TestSelVertexRecord(stmt, lableName, g_indexName, g_cond, format, startIndex, endIndex, versionId);
    return ret;
}

int CommonCheckFixedTb(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t endIndex, bool withNewField)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format, "A0(int32)=%i{0};A1(int64)=%i{1};A2(int64)=%i{2};A3(int64)=%i{3};A4(int64)=%i{4};A5(int64)=%i{"
        "5};A6(int64)=%i{6};A7(int64)=%i{7};A8(int64)=%i{8};A9(int64)=%i{9}");
    if (withNewField) {
        if (versionId > 0) {
            (void)strcat(format, ";add1(int64)=%i{10}");
        }
        if (versionId > 1) {
            (void)strcat(format, ";add2(int64)=%i{11}");
        }
        if (versionId > 2) {
            (void)strcat(format, ";add3(int64)=%i{12}");
        }
        if (versionId > 3) {
            (void)strcat(format, ";add4(int64)=%i{13}");
        }
        if (versionId > 4) {
            (void)strcat(format, ";add5(int64)=%i{14}");
        }
        if (versionId > 5) {
            (void)strcat(format, ";add6(int64)=%i{15}");
        }
        if (versionId > 6) {
            (void)strcat(format, ";add7(int64)=%i{16}");
        }
    }
    ret = TestSelVertexRecord(stmt, lableName, g_indexName, g_cond, format, startIndex, endIndex, versionId);
    return ret;
}

int CommonCheckUpdateVarTb(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t endIndex, bool withNewField = false)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{1000000};A2(int64)=%i{20};A3(int64)=%i{30};S1(string)=%f{26}a_%i{0};S2("
        "string)=%f{26}b_%i{0}");
    if (withNewField) {
        if (versionId > 0) {
            (void)strcat(format, ";add1(string)=%f{26}c_%i{0}");
        }
        if (versionId > 1) {
            (void)strcat(format, ";add2(string)=%f{26}d_%i{0}");
        }
        if (versionId > 2) {
            (void)strcat(format, ";add3(string)=%f{26}e_%i{0}");
        }
        if (versionId > 3) {
            (void)strcat(format, ";add4(string)=%f{26}f_%i{0}");
        }
        if (versionId > 4) {
            (void)strcat(format, ";add5(string)=%f{26}g_%i{0}");
        }
        if (versionId > 5) {
            (void)strcat(format, ";add6(string)=%f{26}h_%i{0}");
        }
        if (versionId > 6) {
            (void)strcat(format, ";add7(string)=%f{26}i_%i{0}");
        }
    }

    ret = TestSelVertexRecord(stmt, lableName, g_indexName, g_cond, format, startIndex, endIndex, versionId);
    return ret;
}

int CommonCheckUpdateFixedTb(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t endIndex, bool withNewField = false)
{
    int ret = T_OK;
    char format[1024] = {0};
    (void)strcat(format,
        "A0(int32)=%i{0};A1(int64)=%i{1000000};A2(int64)=%i{20};A3(int64)=%i{30};A4(int64)=%i{40};A5(int64)=%i{"
        "50};A6(int64)=%i{60};A7(int64)=%i{70};A8(int64)=%i{80};A9(int64)=%i{90}");
    if (withNewField) {
        if (versionId > 0) {
            (void)strcat(format, ";add1(int64)=%i{100}");
        }
        if (versionId > 1) {
            (void)strcat(format, ";add2(int64)=%i{110}");
        }
        if (versionId > 2) {
            (void)strcat(format, ";add3(int64)=%i{120}");
        }
        if (versionId > 3) {
            (void)strcat(format, ";add4(int64)=%i{130}");
        }
        if (versionId > 4) {
            (void)strcat(format, ";add5(int64)=%i{140}");
        }
        if (versionId > 5) {
            (void)strcat(format, ";add6(int64)=%i{150}");
        }
        if (versionId > 6) {
            (void)strcat(format, ";add7(int64)=%i{160}");
        }
    }
    ret = TestSelVertexRecord(stmt, lableName, g_indexName, g_cond, format, startIndex, endIndex, versionId);
    return ret;
}

int CommonRecordCount(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex, uint32_t endIndex,
    uint32_t expectCount)
{
    int ret = T_OK;
    ret = TestSelVertexCount(stmt, lableName, g_indexName, g_cond, expectCount, startIndex, endIndex, versionId);
    return ret;
}

int InitRsmCfg()
{
    int ret = GtExecSystemCmd("sed -i '/isUseRsm/d' %s", g_sysGMDBCfg);
    RETURN_IFERR(ret);
    ret = GtExecSystemCmd("echo 'isUseRsm=0' >> %s", g_sysGMDBCfg);
    RETURN_IFERR(ret);

    ret = GtExecSystemCmd("sed -i '/RsmBlockSize/d' %s", g_sysGMDBCfg);
    RETURN_IFERR(ret);
    ret = GtExecSystemCmd("echo 'RsmBlockSize=128' >> %s", g_sysGMDBCfg);
    RETURN_IFERR(ret);

    ret = GtExecSystemCmd("sed -i '/RsmKeyRange/d' %s", g_sysGMDBCfg);
    RETURN_IFERR(ret);
    ret = GtExecSystemCmd("echo 'RsmKeyRange=0,9' >> %s", g_sysGMDBCfg);
    RETURN_IFERR(ret);

    ret = ChangeGmserverCfg((char *)"isUseRsm", (char *)"1");
    return ret;
}

int CleanAndReConn(GmcConnT **conn, GmcStmtT **stmt)
{
    (void)testGmcDisconnect(*conn, *stmt);
    int ret = close_epoll_thread();
    RETURN_IFERR(ret);
    testEnvClean();
    ret = testEnvInit();
    RETURN_IFERR(ret);
    ret = create_epoll_thread();
    RETURN_IFERR(ret);
    ret = testGmcConnect(conn, stmt);
    return ret;
}

int WaitRsmRecover(int tableCount, int timeout = 30)
{
    char cmd1[200] = {0};
    char cmd2[200] = {0};
    int ret;
    (void)snprintf(cmd1, sizeof(cmd1), "gmsysview -q 'V$CATA_LABEL_RECOVERY_INFO' |grep 'RECOVERY_LABEL_CNT: %d$'",
        tableCount);
    (void)snprintf(cmd2, sizeof(cmd2), "gmsysview -q 'V$CATA_LABEL_RECOVERY_INFO' |grep 'RECOVERY_FINISHED: 1$'",
        tableCount);
    for (int i = 0; i < timeout; i++) {
        ret = GtExecSystemCmd(cmd2);
        if (ret == T_OK) {
            ret = GtExecSystemCmd(cmd1);
            return ret;
        }
        sleep(1);
    }
    AW_FUN_Log(LOG_STEP, "CheckRsmRecover time out.");
    return T_FAILED;
}

void *ThreadDml(void *arg)
{
    DmlArgsT *dmlArg = (DmlArgsT *)arg;
    int ret;
    AW_FUN_Log(LOG_STEP, "ThreadDml table: %s, versionId: %u, startIndex: %u, count: %u, withNewField: %d",
        dmlArg->tableName, dmlArg->versionId, dmlArg->startIndex, dmlArg->count, dmlArg->withNewField);
    switch (dmlArg->type) {
        case T_RSM_REPLACE: {
            if (dmlArg->isFixedTable) {
                ret = CommonReplaceFixedTb(dmlArg->stmt, dmlArg->tableName, dmlArg->versionId, dmlArg->startIndex,
                    dmlArg->count, dmlArg->withNewField);
            } else {
                ret = CommonReplaceVarTb(dmlArg->stmt, dmlArg->tableName, dmlArg->versionId, dmlArg->startIndex,
                    dmlArg->count, dmlArg->withNewField);
            }
            AW_FUN_Log(LOG_STEP, "Replace %s version: %u status: %d", dmlArg->tableName, dmlArg->versionId, ret);
            break;
        }
        case T_RSM_INSERT: {
            if (dmlArg->isFixedTable) {
                ret = CommonInsertFixedTb(dmlArg->stmt, dmlArg->tableName, dmlArg->versionId, dmlArg->startIndex,
                    dmlArg->count, dmlArg->withNewField);
            } else {
                ret = CommonInsertVarTb(dmlArg->stmt, dmlArg->tableName, dmlArg->versionId, dmlArg->startIndex,
                    dmlArg->count, dmlArg->withNewField);
            }
            AW_FUN_Log(LOG_STEP, "Insert %s version: %u status: %d", dmlArg->tableName, dmlArg->versionId, ret);
            break;
        }
        case T_RSM_UPDATE: {
            if (dmlArg->isFixedTable) {
                ret = CommonUpdateFixedTb(dmlArg->stmt, dmlArg->tableName, dmlArg->versionId, dmlArg->startIndex,
                    dmlArg->count, dmlArg->withNewField);
            } else {
                ret = CommonUpdateVarTb(dmlArg->stmt, dmlArg->tableName, dmlArg->versionId, dmlArg->startIndex,
                    dmlArg->count, dmlArg->withNewField);
            }

            AW_FUN_Log(LOG_STEP, "Update %s version: %u status: %d", dmlArg->tableName, dmlArg->versionId, ret);
            break;
        }
        case T_RSM_DELETE: {
            ret = CommonDelete(dmlArg->stmt, dmlArg->tableName, dmlArg->versionId, dmlArg->startIndex, dmlArg->count);
            AW_FUN_Log(LOG_STEP, "Delete %s version: %u status: %d", dmlArg->tableName, dmlArg->versionId, ret);
            break;
        }
        case T_RSM_BATCH_INSERT: {
            if (dmlArg->isFixedTable) {
                ret = CommonBatchReplaceFixedTb(dmlArg->conn, dmlArg->stmt, dmlArg->tableName, dmlArg->versionId,
                    dmlArg->startIndex, dmlArg->count);
            } else {
                ret = CommonBatchReplaceVarTb(dmlArg->conn, dmlArg->stmt, dmlArg->tableName, dmlArg->versionId,
                    dmlArg->startIndex, dmlArg->count);
            }
            AW_FUN_Log(LOG_STEP, "Batch Insert %s version: %u status: %d", dmlArg->tableName, dmlArg->versionId, ret);
            break;
        }
        case T_RSM_BATCH_UPDATE: {
            if (dmlArg->isFixedTable) {
                ret = CommonBatchUpdateFixedTb(dmlArg->conn, dmlArg->stmt, dmlArg->tableName, dmlArg->versionId,
                    dmlArg->startIndex, dmlArg->count);
            } else {
                ret = CommonBatchUpdateVarTb(dmlArg->conn, dmlArg->stmt, dmlArg->tableName, dmlArg->versionId,
                    dmlArg->startIndex, dmlArg->count);
            }
            AW_FUN_Log(LOG_STEP, "Batch Update %s version: %u status: %d", dmlArg->tableName, dmlArg->versionId, ret);
            break;
        }
        case T_RSM_BATCH_DELETE: {
            ret = CommonBatchDelete(dmlArg->conn, dmlArg->stmt, dmlArg->tableName, dmlArg->versionId,
                dmlArg->startIndex, dmlArg->count);
            AW_FUN_Log(LOG_STEP, "Batch Delete %s version: %u status: %d", dmlArg->tableName, dmlArg->versionId, ret);
            break;
        }
        default: {
            break;
        }
    }
}

int AsyncVarTbOperate(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t insertCount, bool withNewField, TdmlTypeE type)
{
    if (g_curUsedThCount > MAX_THREAD_COUNT) {
        AW_FUN_Log(LOG_STEP, "The number of threads exceeds the maximum.");
        return T_FAILED;
    }
    g_curUsedThCount++;
    (void)strncpy(g_dmlArg[g_curUsedThCount - 1].tableName, lableName, MAX_TEST_TABLE_NAME_LEN);
    g_dmlArg[g_curUsedThCount - 1].stmt = stmt;
    g_dmlArg[g_curUsedThCount - 1].conn = NULL;
    g_dmlArg[g_curUsedThCount - 1].versionId = versionId;
    g_dmlArg[g_curUsedThCount - 1].startIndex = startIndex;
    g_dmlArg[g_curUsedThCount - 1].count = insertCount;
    g_dmlArg[g_curUsedThCount - 1].withNewField = withNewField;
    g_dmlArg[g_curUsedThCount - 1].isFixedTable = false;
    g_dmlArg[g_curUsedThCount - 1].type = type;
    int ret = pthread_create(&g_thread[g_curUsedThCount - 1], NULL, ThreadDml, &g_dmlArg[g_curUsedThCount - 1]);
    return ret;
}

int AsyncFixedTbOperate(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t startIndex,
    uint32_t insertCount, bool withNewField, TdmlTypeE type)
{
    if (g_curUsedThCount > MAX_THREAD_COUNT) {
        AW_FUN_Log(LOG_STEP, "The number of threads exceeds the maximum.");
        return T_FAILED;
    }
    g_curUsedThCount++;
    (void)strncpy(g_dmlArg[g_curUsedThCount - 1].tableName, lableName, MAX_TEST_TABLE_NAME_LEN);
    g_dmlArg[g_curUsedThCount - 1].stmt = stmt;
    g_dmlArg[g_curUsedThCount - 1].conn = NULL;
    g_dmlArg[g_curUsedThCount - 1].versionId = versionId;
    g_dmlArg[g_curUsedThCount - 1].startIndex = startIndex;
    g_dmlArg[g_curUsedThCount - 1].count = insertCount;
    g_dmlArg[g_curUsedThCount - 1].withNewField = withNewField;
    g_dmlArg[g_curUsedThCount - 1].isFixedTable = true;
    g_dmlArg[g_curUsedThCount - 1].type = type;
    int ret = pthread_create(&g_thread[g_curUsedThCount - 1], NULL, ThreadDml, &g_dmlArg[g_curUsedThCount - 1]);
    return ret;
}

int AsyncVarTbBatchOperate(GmcConnT *conn, GmcStmtT *stmt, const char *lableName, uint32_t versionId,
    uint32_t startIndex, uint32_t insertCount, TdmlTypeE type)
{
    if (g_curUsedThCount > MAX_THREAD_COUNT) {
        AW_FUN_Log(LOG_STEP, "The number of threads exceeds the maximum.");
        return T_FAILED;
    }
    g_curUsedThCount++;
    (void)strncpy(g_dmlArg[g_curUsedThCount - 1].tableName, lableName, MAX_TEST_TABLE_NAME_LEN);
    g_dmlArg[g_curUsedThCount - 1].stmt = stmt;
    g_dmlArg[g_curUsedThCount - 1].conn = conn;
    g_dmlArg[g_curUsedThCount - 1].versionId = versionId;
    g_dmlArg[g_curUsedThCount - 1].startIndex = startIndex;
    g_dmlArg[g_curUsedThCount - 1].count = insertCount;
    g_dmlArg[g_curUsedThCount - 1].withNewField = false;
    g_dmlArg[g_curUsedThCount - 1].isFixedTable = false;
    g_dmlArg[g_curUsedThCount - 1].type = type;
    int ret = pthread_create(&g_thread[g_curUsedThCount - 1], NULL, ThreadDml, &g_dmlArg[g_curUsedThCount - 1]);
    return ret;
}

int AsyncFixedTbBatchOperate(GmcConnT *conn, GmcStmtT *stmt, const char *lableName, uint32_t versionId,
    uint32_t startIndex, uint32_t insertCount, TdmlTypeE type)
{
    if (g_curUsedThCount > MAX_THREAD_COUNT) {
        AW_FUN_Log(LOG_STEP, "The number of threads exceeds the maximum.");
        return T_FAILED;
    }
    g_curUsedThCount++;
    (void)strncpy(g_dmlArg[g_curUsedThCount - 1].tableName, lableName, MAX_TEST_TABLE_NAME_LEN);
    g_dmlArg[g_curUsedThCount - 1].stmt = stmt;
    g_dmlArg[g_curUsedThCount - 1].conn = conn;
    g_dmlArg[g_curUsedThCount - 1].versionId = versionId;
    g_dmlArg[g_curUsedThCount - 1].startIndex = startIndex;
    g_dmlArg[g_curUsedThCount - 1].count = insertCount;
    g_dmlArg[g_curUsedThCount - 1].withNewField = false;
    g_dmlArg[g_curUsedThCount - 1].isFixedTable = true;
    g_dmlArg[g_curUsedThCount - 1].type = type;
    int ret = pthread_create(&g_thread[g_curUsedThCount - 1], NULL, ThreadDml, &g_dmlArg[g_curUsedThCount - 1]);
    return ret;
}

int JoinAsyncThread()
{
    int ret = T_OK;
    for (int i = 0; i < g_curUsedThCount; i++) {
        ret = pthread_join(g_thread[i], NULL);
        RETURN_IFERR(ret);
    }
    g_curUsedThCount = 0;
    return ret;
}

int InsertFixedTbCust(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t fullPageCount,
    uint32_t *actCount, bool withNewField)
{
    int ret;
    // 如果指定页数，按大概页数来写入,否则根据传入actCount数量写入
    if (fullPageCount != 0) {
        ret = ReplaceFixedTbUntilPagesFull(stmt, lableName, versionId, fullPageCount, actCount, withNewField);
    } else {
        ret = CommonReplaceFixedTb(stmt, lableName, versionId, 0, *actCount, withNewField);
    }
    return ret;
}

int InsertVarTbCust(GmcStmtT *stmt, const char *lableName, uint32_t versionId, uint32_t fullPageCount,
    uint32_t *actCount, bool withNewField)
{
    int ret;
    // 如果指定页数，按大概页数来写入,否则根据传入actCount数量写入
    if (fullPageCount != 0) {
        ret = ReplaceVarTbUntilPagesFull(stmt, lableName, versionId, fullPageCount, actCount, withNewField);
    } else {
        ret = CommonReplaceVarTb(stmt, lableName, versionId, 0, *actCount, withNewField);
    }
    return ret;
}

int WaitActiveScaleInHeap(const char *lableName, int timeout = 10)
{
    int ret = T_OK;
    int waitTime = 0;
    while (waitTime < timeout * 1000) {
        ret = GtExecSystemCmd(
            "gmsysview -q 'V$STORAGE_HEAP_STAT' -f 'LABEL_NAME=%s'|grep 'LAST_DEFRAG_TASK_TIME'|grep 20", lableName);
        if (ret == T_OK) {
            break;
        }
        usleep(1000);
        waitTime++;
    }
    system("gmsysview -q 'V$STORAGE_HEAP_STAT'");
    return ret;
}

int WaitActiveScaleInCluster(const char *lableName, int timeout = 10)
{
    int ret = T_OK;
    int waitTime = 0;
    while (waitTime < timeout * 1000) {
        ret = GtExecSystemCmd("gmsysview -q 'V$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT' -f 'LABEL_NAME=%s'|grep "
            "'SCALE_IN_THREHOLD_MEET_COUNT: 0'",
            lableName);
        if (ret != T_OK) {
            (void)GtExecSystemCmd(
                "gmsysview -q 'V$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT' | grep SCALE_IN_THREHOLD_MEET_COUNT");
            ret = T_OK;
            break;
        }
        usleep(1000);
        waitTime++;
    }
    return ret;
}

void WaitAsyncOperateCount(uint32_t count, int timeout = 10)
{
    int us = timeout * 1000000;
    while (us >= 0) {
        if (g_asyncOperateCount >= count) {
            break;
        }
        usleep(10);
        us = us - 10;
    }
    if (us == 0) {
        AW_FUN_Log(LOG_ERROR, "WaitAsyncOperateCount timeout!!!!!!!!!.");
        AW_MACRO_EXPECT_NE_INT(us, 0);
    }
}

#ifdef __cplusplus
}
#endif

#endif
/* RSM_EXTEND_H */

