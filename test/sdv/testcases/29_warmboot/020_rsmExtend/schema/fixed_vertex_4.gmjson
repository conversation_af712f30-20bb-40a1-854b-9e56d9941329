[{"comment": "定长vertex label", "version": "2.0", "type": "record", "name": "rsm_fixed_vertex", "schema_version": 4, "fields": [{"name": "A0", "type": "int32", "nullable": false}, {"name": "A1", "type": "int64", "nullable": false}, {"name": "A2", "type": "int64", "nullable": false}, {"name": "A3", "type": "int64", "nullable": false}, {"name": "A4", "type": "int64", "nullable": false}, {"name": "A5", "type": "int64", "nullable": false}, {"name": "A6", "type": "int64", "nullable": false}, {"name": "A7", "type": "int64", "nullable": false}, {"name": "A8", "type": "int64", "nullable": false}, {"name": "A9", "type": "int64", "nullable": false}, {"name": "add1", "type": "int64", "nullable": true}, {"name": "add2", "type": "int64", "nullable": true}, {"name": "add3", "type": "int64", "nullable": true}, {"name": "add4", "type": "int64", "nullable": true}], "keys": [{"node": "rsm_fixed_vertex", "name": "<PERSON><PERSON><PERSON>", "index": {"type": "primary"}, "fields": ["A0"], "constraints": {"unique": true}, "comment": "主键索引"}, {"node": "rsm_fixed_vertex", "name": "localhash_key", "fields": ["A1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}, "comment": "localhash索引"}]}]