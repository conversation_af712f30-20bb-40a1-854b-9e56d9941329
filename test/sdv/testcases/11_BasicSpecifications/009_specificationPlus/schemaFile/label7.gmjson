[{"type": "record", "name": "label7", "fields": [{"name": "id", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "T1", "type": "record", "fixed_array": true, "size": 1024, "fields": [{"name": "A1", "type": "int32", "nullable": true}]}], "keys": [{"node": "label7", "name": "primary_key", "fields": ["id"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "label7", "name": "localhash_key", "fields": ["A1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]