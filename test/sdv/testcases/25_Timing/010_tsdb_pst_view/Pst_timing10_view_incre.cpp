/*
/*****************************************************************************
 Description  : 临时表
 Notes        : 
             Timing_010_001 增量持久化配置，创建vl,查询视图 STORAGE_PERSISTENT_STAT
             Timing_010_002 增量持久化配置，创建vl,多线程 并发 查询视图 STORAGE_PERSISTENT_STAT
             Timing_010_003 增量持久化配置，创建vl,写到表空间满 并发 查询视图 STORAGE_PERSISTENT_STAT
             Timing_010_004 增量持久化配置，spaceMaxNum=1025起服务失败

History      :
 Author       : 潘鹏 pwx860460
 Modification :
 Date         : 2024/04/18
*****************************************************************************/

#include "../../06_Other/053_OnDemandPersistence/Persistence_common.h"

#define GM_SYS_NSP "GM_SYS_NSP"
#define GM_SYS_VL "GM_SYS_VL"
#define GM_SYS_NODE "GM_SYS_NODE"
#define GM_SYS_PROP "GM_SYS_PROP"
#define GM_SYS_IDX "GM_SYS_IDX"
#define AW_MACRO_EXPECT_LE_INT(a, b) EXPECT_LE(a, b)

class Timing_pst_view : public testing::Test {
protected:
    static void SetUpTestCase()
    {  
    }

    static void TearDownTestCase()
    {  
    };
public:
    virtual void SetUp();
    virtual void TearDown();
};
char g_errorCode04[MAX_CMD_SIZE] = {0};
char g_errorCode05[MAX_CMD_SIZE] = {0};
void Timing_pst_view::SetUp()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("mkdir -p /data/gmdb/; rm -rf /data/gmdb/*");
    memset(g_command, 0, sizeof(g_command));// 增量配置还得用cp备份然后恢复
    snprintf(g_command, MAX_CMD_SIZE, "\\cp %s ./gmserver.iniback -rf ", g_sysGMDBCfg);
    system(g_command);
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "grep -rn spaceMaxNum %s |grep -v #|wc -l", g_sysGMDBCfg);
    char result[1024] = {0};
    ret = GtExecSystemCmdAndGetResult(result, sizeof(result), g_command);
    if (atoi(result) == 0){
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "echo 'spaceMaxNum = 3' >> %s", g_sysGMDBCfg);//不存在的值需要是默认值
        printf("%s\n",g_command);
        system(g_command);
    }
    ret = ChangeGmserverCfg((char *)"spaceMaxNum", (char *)"1024");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"dbFilesMaxCnt", (char *)"1024");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"maxSeMem", (char *)"32");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(g_errorCode04, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    (void)snprintf(g_errorCode05, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(5, g_errorCode01, g_errorCode02, g_errorCode03, g_errorCode04, g_errorCode05);
    AW_CHECK_LOG_BEGIN();
}

void Timing_pst_view::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    memset(g_command, 0, sizeof(g_command));// 适配用例干扰配置
    snprintf(g_command, MAX_CMD_SIZE, "\\cp ./gmserver.iniback %s -rf", g_sysGMDBCfg);
    system(g_command);
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char expectCmd[100] = {0};
    char *homePath = getenv("HOME");
    (void)snprintf(expectCmd, sizeof(expectCmd), "rm %s/../data/gmdb/* -rf", homePath);
    system(expectCmd);
}
char g_fileName[] = "STORAGE_PERSISTENT_STAT.log";
char g_fileName2[] = "STORAGE_SPACE_INFO.log";
int64_t g_version;
int64_t g_shut_down;
int64_t g_pageCount;
int64_t g_lsn;

int64_t g_max_size;
int64_t g_step_size;
int64_t g_space_file_max_size;
int64_t g_data_file_count;
int64_t g_hwm;
int64_t g_cur_size;
int64_t g_actual_size;
int64_t g_used_size;



int GetViewFieldValueFromFile(const char *fileName, const char *field, int64_t *value)
{
    int maxLen = 50;
    char valueStr[maxLen] = {0};
    char cmd[512] = {0};
    (void)snprintf(cmd, sizeof(cmd), "cat %s |grep '%s:'|awk '{print $2}'", fileName, field);
    GtGetCmdReturn(cmd, valueStr, &maxLen);
    if (!IsDigitStr(valueStr)) {
        return FAILED;
    }
    int64_t temp;
    char *endptr;
    temp = strtol(valueStr, &endptr, 10);
    if (strcmp(endptr, "") != 0) {
        return FAILED;
    }
    *value = temp;
    return GMERR_OK;
}
void GetViewValue1()
{
    int ret = GetViewFieldValueFromFile(g_fileName, "FILE_FORMAT_VERSION", &g_version);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GetViewFieldValueFromFile(g_fileName, "IS_NORMAL_SHUT_DOWN", &g_shut_down);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GetViewFieldValueFromFile(g_fileName, "LSN", &g_lsn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GetViewValue2()
{
    int ret;
    ret = GetViewFieldValueFromFile(g_fileName2, "SPACE_FILE_EXTEND_STEP_SIZE(MB)", &g_step_size);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GetViewFieldValueFromFile(g_fileName2, "SPACE_FILE_MAX_SIZE(KB)", &g_space_file_max_size);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GetViewFieldValueFromFile(g_fileName2, "DATA_FILE_COUNT", &g_data_file_count);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GetViewFieldValueFromFile(g_fileName2, "DATA_FILE_COUNT_HWM", &g_hwm);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GetViewFieldValueFromFile(g_fileName2, "CUR_SIZE(B)", &g_cur_size);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GetViewFieldValueFromFile(g_fileName2, "ACTUAL_SIZE(B)", &g_actual_size);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GetViewFieldValueFromFile(g_fileName2, "USED_SIZE(B)", &g_used_size);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 查看系统视图
TEST_F(Timing_pst_view, Timing_010_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    int ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    ASSERT_EQ(GMERR_OK, ret);
    // 重新视图STORAGE_PERSISTENT_STAT
    ret = GtExecSystemCmd("gmsysview -q 'V$STORAGE_PERSISTENT_STAT' > %s", g_fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("cat %s", g_fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GetViewValue1();
    AW_MACRO_EXPECT_LE_INT(1, g_version);
    AW_MACRO_EXPECT_LE_INT(0, g_shut_down);
    AW_MACRO_EXPECT_LE_INT(0, g_lsn);
    char type_list[3][30]={"SPACE_TYPE_SYSTEM", "SPACE_TYPE_UNDO", "SPACE_TYPE_USER_DEFAULT"};
    for (int i = 0; i < 3; i++){
        memset(g_command, 0, sizeof(g_command));
        sprintf(g_command, "gmsysview -q V\\$STORAGE_SPACE_INFO -f TYPE=%s > %s",type_list[i] ,g_fileName2);
        system(g_command);
        ret = GtExecSystemCmd("cat %s", g_fileName2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GetViewValue2();
        AW_MACRO_EXPECT_LE_INT(4, g_step_size);
        AW_MACRO_EXPECT_LE_INT(1, g_space_file_max_size);
        AW_MACRO_EXPECT_LE_INT(1, g_data_file_count);
        AW_MACRO_EXPECT_LE_INT(1, g_hwm);
        AW_MACRO_EXPECT_LE_INT(1, g_cur_size);
        AW_MACRO_EXPECT_LE_INT(1, g_actual_size);
        AW_MACRO_EXPECT_LE_INT(1, g_used_size);
    }
}


void *systableview(void *arg)
{
    char temp_command[1024];
    int ret = 0;
    char type_list[3][30]={"SPACE_TYPE_SYSTEM", "SPACE_TYPE_UNDO", "SPACE_TYPE_USER_DEFAULT"};
    for (int i = 0; i < 3; i++){
        memset(g_command, 0, sizeof(g_command));
        sprintf(g_command, "gmsysview -q V\\$STORAGE_SPACE_INFO -f TYPE=%s > %s",type_list[i] ,g_fileName2);
        system(g_command);
    }

    return (void *)(int64_t)ret;
}

// 多个线程查询同一个视图
TEST_F(Timing_pst_view, Timing_010_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建namespace
    const char *nameSpace = (const char *)"nsp1";
    int ret = GmcCreateNamespace(g_stmt, nameSpace, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t threadNum = 5;

    pthread_t threadId[threadNum];
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_create(&threadId[i], NULL, systableview, NULL);
        ASSERT_EQ(GMERR_OK, ret);
    }

    for (int32_t i = 0; i < threadNum; i++) {
        void *threadRet;
        ret = pthread_join(threadId[i], &threadRet);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, (int)(int64_t)threadRet);
    }
    ret = GtExecSystemCmd("cat %s", g_fileName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GetViewValue2();
    AW_MACRO_EXPECT_LE_INT(4, g_step_size);
    AW_MACRO_EXPECT_LE_INT(1, g_space_file_max_size);
    AW_MACRO_EXPECT_LE_INT(1, g_data_file_count);
    AW_MACRO_EXPECT_LE_INT(1, g_hwm);
    AW_MACRO_EXPECT_LE_INT(1, g_cur_size);
    AW_MACRO_EXPECT_LE_INT(1, g_actual_size);
    AW_MACRO_EXPECT_LE_INT(1, g_used_size);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 构造内存满,重启后，再次校验视图
TEST_F(Timing_pst_view, Timing_010_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *vertex_label_schema = NULL;
    char labelName[]="vertex_01";
    char g_configJson[128] = "{\"max_record_count\" : 10000000}";
    readJanssonFile("./schema/Vertex_01.gmjson", &vertex_label_schema);
    ASSERT_NE((void *)NULL, vertex_label_schema);
    GmcDropVertexLabel(g_stmt, labelName);
    int ret = GmcCreateVertexLabel(g_stmt, vertex_label_schema, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vertex_label_schema);
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);

    ret = PstInsertValueFull(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(3);
    // 重新视图STORAGE_PERSISTENT_STAT
    ret = GtExecSystemCmd("gmsysview -q 'V$STORAGE_PERSISTENT_STAT' > %s", g_fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("cat %s", g_fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GetViewValue1();
    AW_MACRO_EXPECT_LE_INT(1, g_version);
    AW_MACRO_EXPECT_LE_INT(0, g_shut_down);
    AW_MACRO_EXPECT_LE_INT(0, g_lsn);
    char type_list[3][30]={"SPACE_TYPE_SYSTEM", "SPACE_TYPE_UNDO", "SPACE_TYPE_USER_DEFAULT"};
    for (int i = 0; i < 3; i++){
        memset(g_command, 0, sizeof(g_command));
        sprintf(g_command, "gmsysview -q V\\$STORAGE_SPACE_INFO -f TYPE=%s > %s",type_list[i] ,g_fileName2);
        system(g_command);
        ret = GtExecSystemCmd("cat %s", g_fileName2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GetViewValue2();
        AW_MACRO_EXPECT_LE_INT(4, g_step_size);
        AW_MACRO_EXPECT_LE_INT(1, g_space_file_max_size);
        AW_MACRO_EXPECT_LE_INT(1, g_data_file_count);
        AW_MACRO_EXPECT_LE_INT(1, g_hwm);
        AW_MACRO_EXPECT_LE_INT(1, g_cur_size);
        AW_MACRO_EXPECT_LE_INT(1, g_actual_size);
        AW_MACRO_EXPECT_LE_INT(1, g_used_size);
    }

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新视图STORAGE_PERSISTENT_STAT
    ret = GtExecSystemCmd("gmsysview -q 'V$STORAGE_PERSISTENT_STAT' > %s", g_fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("cat %s", g_fileName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GetViewValue1();
    AW_MACRO_EXPECT_LE_INT(1, g_version);
    AW_MACRO_EXPECT_LE_INT(0, g_shut_down);
    AW_MACRO_EXPECT_LE_INT(0, g_lsn);
    for (int i = 0; i < 3; i++){
        memset(g_command, 0, sizeof(g_command));
        sprintf(g_command, "gmsysview -q V\\$STORAGE_SPACE_INFO -f TYPE=%s > %s",type_list[i] ,g_fileName2);
        system(g_command);
        ret = GtExecSystemCmd("cat %s", g_fileName2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GetViewValue2();
        AW_MACRO_EXPECT_LE_INT(4, g_step_size);
        AW_MACRO_EXPECT_LE_INT(1, g_space_file_max_size);
        AW_MACRO_EXPECT_LE_INT(1, g_data_file_count);
        AW_MACRO_EXPECT_LE_INT(1, g_hwm);
        AW_MACRO_EXPECT_LE_INT(1, g_cur_size);
        AW_MACRO_EXPECT_LE_INT(1, g_actual_size);
        AW_MACRO_EXPECT_LE_INT(1, g_used_size);
    }
}

// 增量持久化配置，spaceMaxNum=1025起服务失败
TEST_F(Timing_pst_view, Timing_010_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(2, "GMERR-1002002", "GMERR-1015004");
    int ret;
    system("sh $TEST_HOME/tools/stop.sh -f");
    
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "grep -rn spaceMaxNum %s |wc -l", g_sysGMDBCfg);
    ret = executeCommand(g_command, "0");
    if (GMERR_OK == ret){
        memset(g_command, 0, sizeof(g_command));
        snprintf(g_command, MAX_CMD_SIZE, "echo 'spaceMaxNum = 3' >> %s", g_sysGMDBCfg);
        system(g_command);
    }
    ret = ChangeGmserverCfg((char *)"spaceMaxNum", (char *)"1025");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}


