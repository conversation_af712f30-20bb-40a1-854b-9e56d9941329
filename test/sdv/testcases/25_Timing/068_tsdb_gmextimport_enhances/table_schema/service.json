{"src_file": "Service_Org", "src_table": "t_service", "dst_table": "t_service", "chunk_capacity": 9000, "time_col": "log_time1", "compression": "fast", "interval": "1 hour", "src_def": [{"src_name": "log_id", "src_type": "GMEI_I64_"}, {"src_name": "log_time", "src_type": "GMEI_I64_"}, {"src_name": "log_brief", "src_type": "GMEI_STR_"}, {"src_name": "module_name", "src_type": "GMEI_STR_"}, {"src_name": "log_level", "src_type": "GMEI_I64_"}, {"src_name": "log_category", "src_type": "GMEI_I64_"}, {"src_name": "vsys_id", "src_type": "GMEI_I64_"}, {"src_name": "content_en", "src_type": "GMEI_STR_"}, {"src_name": "content_ch", "src_type": "GMEI_STR_"}], "dst_def": [{"dst_name": "log_id1", "dst_type": "GMEI_I64_", "max_size": 0, "src_data": "log_id"}, {"dst_name": "log_time1", "dst_type": "GMEI_I64_", "max_size": 0, "src_data": "log_time"}, {"dst_name": "log_brief1", "dst_type": "GMEI_STR_", "max_size": 64, "src_data": "log_brief"}, {"dst_name": "module_name", "dst_type": "GMEI_STR_", "max_size": 64, "src_data": "module_name"}, {"dst_name": "log_level", "dst_type": "GMEI_I64_", "max_size": 0, "src_data": "log_level"}, {"dst_name": "log_category", "dst_type": "GMEI_I64_", "max_size": 0, "src_data": "log_category"}, {"dst_name": "vsys_id", "dst_type": "GMEI_I64_", "max_size": 0, "src_data": "vsys_id"}, {"dst_name": "content_en", "dst_type": "GMEI_STR_", "max_size": 64, "src_data": "content_en"}, {"dst_name": "content_ch", "dst_type": "GMEI_STR_", "max_size": 64, "src_data": "content_ch"}]}