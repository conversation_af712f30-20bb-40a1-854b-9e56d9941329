/* ****************************************************************************
 Description  : 告警上报 存储业务数据使用共享内存的告警 告警项测试
 Node      :
   001服务拉起，获取业务内存告警数据。
    002
服务拉起，修改maxSeMem为16M,获取业务内存告警数据；写数据写满内存，获取业务告警数据；删除一半数据，获取告警数据；再将内存写满，然后删除一半数据，获取告警数据；删除全部数据，获取告警数据。
    003 服务拉起，获取业务内存告警数据并记录succTimes和failTimes，创建顶点表后写数据至写满内存，重复10次错误操作，
        获取业务内存告警数据并对比succTimes和failTimes
 Author       : qwx620469
 Modification :
 Date         : 2024/10/31
 node:  服务端上报到告警平台需300秒，delay_s=300，构建会超时
        Hpe不可测，写满内存会重启
**************************************************************************** */
extern "C" {}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gmc_test.h"
#include "gtest/gtest.h"
#include "test_delay_sn.h"
#include "../../common/include/component/t_rd_ts.h"

#define DEBUG_PRINT 0

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
char const *view_name = "V\\$STORAGE_MEMDATA_STAT";

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

GmcAlarmIdE AlarmId = GMC_ALARM_VETEX_EDGE_DATA;
GmcAlarmDataT test_AlarmData;

uint16_t delay_s = 310;  // 时间需要大于300秒

int DropCmTable(char *tableName)
{
    int ret = 0;
    char sqlCmd[256] = {0};
    (void)sprintf(sqlCmd, "drop table %s;", tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(g_stmt, sqlCmd, cmdLen);
    return ret;
}

class tsdb_alarm_vetex_edge_data : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=16\"");
        int ret = 0;
        system("sh $TEST_HOME/tools/stop.sh -ts");
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void tsdb_alarm_vetex_edge_data::SetUp()
{
    int ret = 0;
    // 建立同步连接
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

#if defined ENV_RTOSV2X  // IOT环境写满内存时最高值为0.875
    ret = GmcChangeAlmThreshold(g_stmt, AlarmId, 80, 70);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    ret = GmcChangeAlmThreshold(g_stmt, AlarmId, 10, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(delay_s);
    //  刷新告警状态值
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("failTimes: %d succTimes: %d\n", test_AlarmData.failTimes, test_AlarmData.succTimes);
    printf("activeValue: %6.3f clearedValue:%6.3f \n", test_AlarmData.activeValue, test_AlarmData.clearedValue);
    printf("activeThreshold: %6.3f clearedThreshold:%6.3f \n", test_AlarmData.activeThreshold,
        test_AlarmData.clearedThreshold);
    printf("detail : %s\n", test_AlarmData.detail);

    AW_CHECK_LOG_BEGIN();
}

void tsdb_alarm_vetex_edge_data::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    int ret = 0;
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
class tsdb_alarm_vetex_edge_data_01 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"maxSeMem=16\"");
        system("sh $TEST_HOME/tools/stop.sh -ts");
        TsDefulatDbFileClean();
        system("sh $TEST_HOME/tools/start.sh -ts");
    }

    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    }

public:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
};
int GetPrintBycmd(char *cmd, int *value)
{
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", cmd);
        return -1;
    }
    char cmdOutput[64] = {0};
    while (NULL != fgets(cmdOutput, 64, pf))
        ;
    *value = atoi(cmdOutput);
    pclose(pf);
    return 0;
}
int CheckAlarmData(GmcAlarmDataT *TestAlarmData, const char *detail)
{
    int ret = 0;
    if ((*TestAlarmData).alarmStatus == GMC_ALARM_STATUS) {
        // activeValue大于0，clearedValue为0
        if (!((*TestAlarmData).activeValue > 0))
            ret = -1;
        EXPECT_GT((*TestAlarmData).activeValue, 0);
    }
    if (detail) {
        if (strcmp((*TestAlarmData).detail, detail) != 0) {
            printf("get str: %s\ncheck str: %s\n", (*TestAlarmData).detail, detail);
            ret = -1;
        }
    }

#ifdef DEBUG_PRINT
    printf("failTimes: %d succTimes: %d\n", test_AlarmData.failTimes, test_AlarmData.succTimes);
    printf("activeValue: %6.3f clearedValue:%6.3f \n", test_AlarmData.activeValue, test_AlarmData.clearedValue);
    printf("activeThreshold: %6.3f clearedThreshold:%6.3f \n", test_AlarmData.activeThreshold,
        test_AlarmData.clearedThreshold);
    printf("detail : %s\n", test_AlarmData.detail);
#endif
    return ret;
}

#if defined ENV_RTOSV2X
#define HALF_MEMORY_RECORDNUM 5
#else
#define HALF_MEMORY_RECORDNUM 65000
#endif

// 001服务拉起，获取业务内存告警数据。
TEST_F(tsdb_alarm_vetex_edge_data, Timing_058_005_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char str[1024] = {};
    // 获取告警数据，预期告警状态变更为无告警且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_EMPTY, test_AlarmData.status);
    (void)snprintf(str, sizeof(str), "Slient corruption statis of table space succTimes=%d, failTimes=%d.",
        test_AlarmData.succTimes, test_AlarmData.failTimes);
    ret = CheckAlarmData(&test_AlarmData, str);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
}

void *SystemView(void *args)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *g_conn1 = NULL;
    GmcStmtT *g_stmt1 = NULL;

    // 建立同步连接
    int ret = TestTsGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int i = *((int *)args);
    char tableName[32];

    sprintf(tableName, "testdb%d", i);
    printf("table=%s\n", tableName);

    char ddlCommand[512];
    snprintf_s(ddlCommand, sizeof(ddlCommand), sizeof(ddlCommand),
        "create table %s(name char(64),  id integer, worktime integer, salarys integer, messages1 blob, messages2 "
        "blob, messages3 blob, messages4 blob, messages5 blob, messages6 blob, messages7 blob, messages8 blob, "
        "messages69 blob, messages70 blob, messages71 blob, messages72 blob, messages73 blob, messages74 blob, "
        "messages99 blob, messages100 blob, messages101 blob, messages blob) with "
        "(time_col = 'worktime', interval = '1 hour', compression = 'fast(rapidlz)', ttl = '3 hours',cache_size = "
        "'100');",
        tableName);

    ret = GmcExecDirect(g_stmt1, ddlCommand, 512);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 断开连接
    ret = testGmcDisconnect(g_conn1, g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ((void *)0);
}

// 002服务拉起，修改maxSeMem为16M，获取业务内存告警数据；写数据写满内存，获取业务告警数据；删除一半数据，获取告警数据；再将内存写满，然后删除一半数据，获取告警数据；删除全部数据，获取告警数据。
// 需要开启缩容，插入数据的情况
TEST_F(tsdb_alarm_vetex_edge_data, Timing_058_005_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char str[1024] = {};
    int maxInsertNum = 0;
    int64_t i_max = 1;
    // 获取告警数据，预期告警状态变更为无告警且为非告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_EMPTY, test_AlarmData.status);
    (void)snprintf(str, sizeof(str), "Slient corruption statis of table space succTimes=%d, failTimes=%d.",
        test_AlarmData.succTimes, test_AlarmData.failTimes);
    ret = CheckAlarmData(&test_AlarmData, str);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    int THREAD_NUM = 600;

    pthread_t thr_arr[THREAD_NUM];
    pthread_t thr_arr2[THREAD_NUM];
    int index[1000];
    int conn_id;
    for (conn_id = 0; conn_id < THREAD_NUM; conn_id++) {
        index[conn_id] = conn_id;
        ret = pthread_create(&thr_arr[conn_id], 0, &SystemView, (void *)&index[conn_id]);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    for (conn_id = 0; conn_id < THREAD_NUM; conn_id++) {

        ret = pthread_join(thr_arr[conn_id], 0);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }

    sleep(delay_s);
    //  获取告警数据，预期告警状态变更为告警激活且为告警状态
    memset(&test_AlarmData, 0, sizeof(test_AlarmData));
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    AW_MACRO_EXPECT_EQ_INT(GMC_ALARM_ACTIVE, test_AlarmData.status);
    (void)snprintf(str, sizeof(str),
        "Alarm of table space is active, from server, value=%.2f,"
        " slient corruption statis succTimes=%d, failTimes=%d.",
        test_AlarmData.activeValue, test_AlarmData.succTimes, test_AlarmData.failTimes);
    ret = CheckAlarmData(&test_AlarmData, str);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
}
