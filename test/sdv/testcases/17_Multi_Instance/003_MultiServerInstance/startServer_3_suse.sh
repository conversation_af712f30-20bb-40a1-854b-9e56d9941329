#!/bin/bash

function usage()
{
    echo "usage: start.sh [-f] [-t]"
    echo "-t: use taskset when RunMode is 0 "
    exit 1
}

if [ $# -gt 2 ]; then
    usage
fi
forceFlag=0
taskset=0
if [ $# -eq 1 ]; then
    if [ "x$1" = "x-f" ]; then
        forceFlag=1
    elif [ "x$1" = "x-t" ]; then
        taskset=1
    else
        usage
    fi
elif [ $# -eq 2 ]; then
    if [ "x$1" != "x-f" -a "x$1" = "x-t" ]; then
        usage
    fi
    if [ "x$2" != "x-f" -a "x$2" = "x-t" ]; then
        usage
    fi
    if [ "x$1" = "x-f" -o "x$2" = "x-f" ]; then
        forceFlag=1
    fi
    if [ "x$1" = "x-t" -o "x$2" = "x-t" ]; then
        taskset=1
    fi
fi

isQemu=0
fileName="sysconfig_3_suse.ini"
cfgFile=`grep -w sysGmdbCfg ${fileName}| awk '{print $3}'`
runName="${TEST_HOME}/.runFlag"

EnvType=`grep -w EnvType ${fileName}| awk '{print $3}'`
if [ "x${EnvType}" != "x0" -a "x${EnvType}" != "x1" -a "x${EnvType}" != "x2" -a "x${EnvType}" != "x3" ]; then
    echo "invalid EnvType:${EnvType}, it must been in [0, 1, 2, 3]. Please Check ${TEST_HOME}/cfg/sysconfig.ini"
    exit 2
fi
RunMode=`grep -w RunMode ${fileName}| awk '{print $3}'`
if [ "x${RunMode}" != "x0" -a "x${RunMode}" != "x1" -a "x${RunMode}" != "x2" ]; then
    echo "invalid RunMode:${RunMode}, it must been in [0, 1, 2]. Please Check ${TEST_HOME}/cfg/sysconfig.ini"
    exit 3
fi
toolPath=`grep -w toolPath ${fileName} | awk -F "= " '{print $NF}'`
if [ "x" = "x${toolPath}" ]; then
    toolPath="/usr/local/bin"
fi

self_pid=$$
parent_pid=$PPID
if [ ${EnvType} -eq 0 -o ${EnvType} -eq 1 ]; then
    # SELF=$(ps -ef | grep $self_pid | grep -v $parent_pid | grep -v 'grep' | grep -v "ps \-ef")
    PARENT_CMD=$(ps -ef | grep $parent_pid | grep -v 'grep'  | awk '{ for (i=8; i<=NF; i++) {printf"%s", $i}; printf"\n" }' | grep -v -w "sh" | grep -v -w "\/bin\/bash")
    PARENT_COMMAND="${PARENT_CMD//--/ --}"
    #pppid=$(ps -o ppid= $parent_pid)
    pppid=$(ps -ef | grep $parent_pid | grep -v $self_pid | grep -v 'grep' | grep -v "ps \-www" | awk '{printf $3}')
    PARENT_OF_PARENT_CMD=$(ps -ef | grep $pppid | grep -v 'grep' | grep -v "ps \-ef" | grep autotest_env | grep "bash \-c" | awk '{ for (i=10; i<=NF; i++) {printf"%s ", $i}; printf"\n" }')
else
    # hongmeng command 'ps' is different
    PARENT_CMD=$(ps -l -www | grep $parent_pid  | grep -v 'grep' | awk '{ for (i=11; i<=NF; i++) {printf"%s", $i}; printf"\n" }' | grep -v -w "sh" | grep -v -w "\/bin\/bash")
    PARENT_COMMAND="${PARENT_CMD//--/ --}"
    pppid=$(ps -l -www | grep $parent_pid | grep -v $self_pid | grep -v 'grep' | grep -v "ps \-www" | awk '{printf $4}')
    PARENT_OF_PARENT_CMD=$(ps -l -www | grep $pppid | grep -v 'grep' | grep -v "ps \-www" | grep autotest_env | grep "sh \-c" | awk '{ for (i=13; i<=NF; i++) {printf"%s ", $i}; printf"\n" }')
fi

his_file="${TEST_HOME}/gmdb_sdv_log/start_history.sh" && [ ! -f ${his_file} ] && mkdir -p "${TEST_HOME}/gmdb_sdv_log" && touch ${his_file}
PARENT_OF_PARENT_CMD="${PARENT_OF_PARENT_CMD//--/ --}"
if [[ "X${PARENT_COMMAND}" != "X" ]] && [[ "X${PARENT_OF_PARENT_CMD}" != "X" ]];then
    test_full_command="echo CI@["$(date "+%F %T")"]; ${PARENT_OF_PARENT_CMD} ; "
    echo "$test_full_command" | tee -a ${his_file}
fi

pidOfServer=""
function getPIDOfServer()
{
    if [ ${RunMode} -eq 0 ]; then
        pidOfServer=$(pidof gmserver)
        echo "pidOfServer:${pidOfServer}"
    elif [ ${EnvType} -eq 1 ]; then
        pidOfServer=$(ps -ef | grep hpk | grep -v grep| awk '{print $2}')
    elif [ ${EnvType} -eq 2 ]; then
        pidOfServer=$(ps -l -www | grep hpk | grep /usr/bin/vm.elf | grep -v grep | awk '{print $3}')
    fi
    if [ X"${pidOfServer}" == X ];then        
        isServerStarted=0
    else
        isServerStarted=1
    fi
}

function ac_check_server()
{
    export PATH=/sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin:${PATH}
    export LD_LIBRARY_PATH=/usr/local/lib:${LD_LIBRARY_PATH}
    local ret=0
    for i in `seq 1 15`
    do
        gmsysview -q V\$DB_SERVER | tee _bb_.txt
        ret=`cat _bb_.txt | grep 'fetched all records' | wc -l`
        rm -f _bb_.txt
        if [ $ret -ne 0 ];then
            break;
        fi
        sleep 2
    done
    if [ $ret -ne 0 ];then
        return 0
    fi
    echo "[start.sh] Error: AC server abnormal!"
    return 100
}

function ac_start_db()
{
    local ret=0
    getPIDOfServer
    if [ "x" != "x${pidOfServer}" ]; then
        ac_check_server
        ret=$?
        if [ $ret -eq 0 ];then
            return 0
        else
            echo "[start.sh] Error: AC server abnormal! Need to reboot."
            exit $ret
        fi
    fi
    echo "start hpe ..."
    echo > /run/hpe/log/start_hpe.log

    rm /usr/local/etc/gmjson/*/BUILD /usr/local/etc/gmjson/mqc/mqc_inst.gmjson /usr/local/etc/gmjson/kpi/allkpi_mapping.gmdata
    systemctl start pnf_pre_v8
    systemctl start ftp_server
    systemctl start swxn_main
    systemctl start gmpolicy_extract
    systemctl start kms_crypto_selftest
    systemctl start sswm_version_verify
    systemctl start syspkg_verify   # 拉起这个服务可以解决lbas启动后，设备静止重启的问题。
    systemctl start hpe.service
    systemctl start lbas
    systemctl start hctl.service
    systemctl start litedb_generate_gmconfig.service
    systemctl start gmserver # 3.23新增 解决自动拉起导致单板重启问题
    systemctl start schema_loader # 状态数据库导入表成功

    ac_check_server
    ret=$?
    if [ $ret -eq 0 ];then
        return 0
    fi
    echo "[start.sh] Error: AC server abnormal! Need to reboot."
    exit $ret #启动失败，需要复位
}

function isNeedStart
{
    if [ ${RunMode} -eq 2 ]; then
        # client run in hpe env, do nothing
        return 0
    fi
    if [ ${RunMode} -eq 1 ]; then
        echo 3 > /proc/sys/vm/drop_caches
    fi
    getPIDOfServer
    if [ "x" = "x${pidOfServer}" ]; then
        forceStop="-f"
        return 1
    fi
    if [ ${RunMode} -eq 1 ]; then
        return 0
    fi
    if [ -f ${TEST_HOME}/.updateCfg ]; then
        rm -f ${TEST_HOME}/.updateCfg
        forceStop="-f"
        return 1
    fi
    exeLevel=`grep -w executeLevel ${fileName}| awk '{print $3}'`
    if [ $? -ne 0 -o "x" = "x${exeLevel}" ]; then
        return 1
    fi
    if [ ${exeLevel} -eq 0 ]; then
        return 1
    fi

    if [ ${EnvType} -eq 1 -a ${RunMode} -eq 1 -a "x" != "x${pidOfServer}" ]; then
        # client run in AE9700S(+hpe), do nothing
        ac_check_server
        ret=$?
        if [ $ret -ne 0 ];then
            echo "[start.sh] Error: AC server abnormal! Need to reboot."
            exit $ret #启动失败，需要复位
        fi
    fi

    if [ ${exeLevel} -eq 1 ]; then
        echo "[start.sh] exeLevel=${exeLevel}, do nothing."
        return 0
    fi
    if [ ${forceFlag} -eq 1 ]; then
        return 1
    fi
    runDir=`cat ${runName} | awk '{print $1}'`
    runFile=`cat ${runName} | awk '{print $2}'`
    if [ ${exeLevel} -eq 2 -a "${currDir}" = "${runDir}" ]; then
        echo "[start.sh] exeLevel=${exeLevel}, dir=${currDir}, do nothing."
        return 0
    fi
    if [ ${exeLevel} -eq 3 -a "${currDir}" = "${runDir}" -a "${currProc}" = "${runFile}" ]; then
        echo "[start.sh] exeLevel=${exeLevel}, dir=${currDir}, file=${runFile} do nothing."
        return 0
    fi
    return 1
}

function waitServerAvailable
{
    if [ $isQemu -eq 1 ]; then
        maxWaitTime=120
        sysviewTimeout=30
    else
        maxWaitTime=60
        sysviewTimeout=10
    fi
    t1=`date '+%s'`
    t2=`expr ${t1} + ${maxWaitTime}`
    let try=0
    while [ ${t1} -lt ${t2} ]
    do
        try=`expr ${try} + 1`
        echo "wait for server ready... try time:${try}"
        getPIDOfServer
        if [ "x" = "x${pidOfServer}" ]; then
            echo "Error: start service failure"
            return 1
        fi
        if [ ${RunMode} -eq 0 ]; then
            strTmp=`timeout ${sysviewTimeout} ${toolPath}/gmsysview -q 'V$STORAGE_MEMDATA_STAT' -s 'usocket:/run/verona/unix_emserver_3'`
        elif [ ${EnvType} -eq 1 ]; then
            strTmp=`timeout ${sysviewTimeout} ${toolPath}/gmsysview -q 'V$STORAGE_MEMDATA_STAT' -s 'channel:ctl_channel'`
        elif [ ${EnvType} -eq 2 ]; then
            strTmp=`${toolPath}/gmsysview -q 'V$STORAGE_MEMDATA_STAT' -s 'channel:ctl_channel' & ( sleep ${sysviewTimeout}; kill $! & )`
        fi
        isConnect=`echo ${strTmp} | grep "fetched all records"`
        if [ $? -eq 0 ]; then
            break;
        fi
        sleep 0.5
        t1=`date '+%s'`
    done
    if [ ${t1} -lt ${t2} ]; then
        echo "Info: start service done"
        return 0
    fi
    echo "Warn: start service timeout"
    return 2
}

function dealWithXsanLog()
{
    xsanAbsolute=`echo $1 | awk -F":" '{print $NF}' | awk -F"=" '{print $NF}'`
    xsanPath=`dirname ${xsanAbsolute}`
    xsanName=`basename ${xsanAbsolute}`
    if [ ! -d ${xsanPath} ]; then
        return 0
    fi
    t=`date '+%Y%m%d%H%M%S.%N'`
    fileList=`ls ${xsanPath}/${xsanName}.* 2>/dev/null`
    if [ $? -ne 0 ]; then
        return 0
    fi
    f3=""
    for f1 in ${fileList}
    do
        f2=`basename ${f1}`
        filePid=`echo ${f2} | awk -F"." '{print $NF}'`
        fileSize=`ls -l ${f1} | awk '{printf $5}'`
        if [ -f /proc/${filePid}/exe ]; then
            if [ ${fileSize} -lt 10 ]; then
                 continue   
            fi
            cp ${f1} ${xsanPath}/${t}_${f2}
            echo "" > ${f1}
        else
            mv ${f1} ${xsanPath}/${t}_${f2}
        fi
        f3="${f3} ${t}_${f2}"
    done
    echo "${currDir}  ${currName}  ${f3}" >> ${xsanPath}/xsan.txt
}

function backupXsanLog()
{
    if [ "x" = "x${currDir}" -o "x" = "x${currName}" ]; then
        return 0
    fi
    if [ "x" != "x${UBSAN_OPTIONS}" ]; then
        dealWithXsanLog ${UBSAN_OPTIONS}
        return 0
    fi
    if [ "x" != "x${TSAN_OPTIONS}" ]; then
        dealWithXsanLog ${TSAN_OPTIONS}
        return 0
    fi
    if [ "x" != "x${ASAN_OPTIONS}" ]; then
        dealWithXsanLog ${ASAN_OPTIONS}
        return 0
    fi
    return 0
}

function markStartFlag
{
    echo "${currDir} ${currProc} end" > ${runName}
}

function grantGmsysviewPrivs()
{
    userPolicyMode=`grep -w "^userPolicyMode" ${cfgFile} | awk -F"=" '{print $2}' | tr -d ' ' | tr -d '\r'`
    gmsysview_allow_list=${TEST_HOME}/testcases/10_Security/004_ObjPrivs/schemaFile/allow_list/gmsysview_allow_list.gmuser
    gmsysview_policy=${TEST_HOME}/testcases/10_Security/004_ObjPrivs/schemaFile/policy/gmsysviewPolicy.gmpolicy
    if [ ${userPolicyMode} -eq 2 ]; then
            ${toolPath}/gmrule -c import_allowlist -f ${gmsysview_allow_list} -s 'usocket:/run/verona/unix_emserver_3'
            ${toolPath}/gmrule -c import_policy  -f ${gmsysview_policy} -s 'usocket:/run/verona/unix_emserver_3'
    fi
}

function record_corefile()
{
    # 统计core文件
    local his_file="$1"
    if [ ! -f ${his_file} ];then
        return 0
    fi
    last_command_dir=$(grep "autotest" ${his_file} | awk '/./{line=$0} END{print line}')
    if [ "${last_command_dir}" != "" ];then
        dir_t1=$(echo ${last_command_dir} | awk -F'source' '{print $1}')
        dir_t2=$(echo ${dir_t1} | awk -F'&&' '{print $1}')
        dir_t3=$(echo ${dir_t2} | awk '{print $NF}')
        if [ ${EnvType} -eq 0 ];then
            server_core=$(find ${TEST_HOME} -maxdepth 1 -name "core-*")
            test_core=$(find ${dir_t3} -name "core-*")
        else
            server_core=$(find /opt/vrpv8/home/<USER>"hcoredump*.gz")
            test_core=$(find /opt/vrpv8/home/<USER>"core*.gz")
        fi
        for c in ${server_core}
        do
            c_num=$(cat ${TEST_HOME}/gmdb_sdv_log/__core__.txt | grep $c | wc -l)
            if [ ${c_num} -eq 0 ];then
                echo "${c}" >> ${TEST_HOME}/gmdb_sdv_log/__core__.txt
                echo "#corefile of testsuit: ${dir_t3}; ls ${c}" | tee -a ${his_file}
            fi
        done
        for c in ${test_core}
        do
            c_num=$(cat ${TEST_HOME}/gmdb_sdv_log/__core__.txt | grep $c | wc -l)
            if [ ${c_num} -eq 0 ];then
                echo "${c}" >> ${TEST_HOME}/gmdb_sdv_log/__core__.txt
                echo "#corefile of testsuit: ${dir_t3}; ls ${c}" | tee -a ${his_file}
            fi
        done
    fi
}

self_pid=$$
parent_pid=$PPID
function record_history()
{
    if [ ${EnvType} -eq 0 -o ${EnvType} -eq 1 ]; then
        # SELF=$(ps -ef | grep $self_pid | grep -v $parent_pid | grep -v 'grep' | grep -v "ps \-ef")
        PARENT_CMD=$(ps -ef | grep $parent_pid | grep -v 'grep'  | awk '{ for (i=8; i<=NF; i++) {printf"%s", $i}; printf"\n" }' | grep -v -w "sh" | grep -v -w "\/bin\/bash")
        PARENT_COMMAND="${PARENT_CMD//--/ --}"
        #pppid=$(ps -o ppid= $parent_pid)
        pppid=$(ps -ef | grep $parent_pid | grep -v $self_pid | grep -v 'grep' | grep -v "ps \-ef" | awk '{printf $3}')
        PARENT_OF_PARENT_CMD=$(ps -ef | grep $pppid | grep -v 'grep' | grep -v "ps \-ef" | grep autotest_env | grep "sh \-c" | awk '{ for (i=10; i<=NF; i++) {printf"%s ", $i}; printf"\n" }')
    else
        # hongmeng command 'ps' is different
        PARENT_CMD=$(ps -l -www | grep $parent_pid  | grep -v 'grep' | awk '{ for (i=11; i<=NF; i++) {printf"%s", $i}; printf"\n" }' | grep -v -w "sh" | grep -v -w "\/bin\/bash")
        PARENT_COMMAND="${PARENT_CMD//--/ --}"
        pppid=$(ps -l -www | grep $parent_pid | grep -v $self_pid | grep -v 'grep' | grep -v "ps \-www" | awk '{printf $4}')
        PARENT_OF_PARENT_CMD=$(ps -l -www | grep $pppid | grep -v 'grep' | grep -v "ps \-www" | grep autotest_env | grep "sh \-c" | awk '{ for (i=13; i<=NF; i++) {printf"%s ", $i}; printf"\n" }')
    fi

    his_file="${TEST_HOME}/gmdb_sdv_log/start_history.sh" && [ ! -f ${his_file} ] && mkdir -p "${TEST_HOME}/gmdb_sdv_log" && touch ${his_file}
    PARENT_OF_PARENT_CMD="${PARENT_OF_PARENT_CMD//--/ --}"
    if [[ "X${PARENT_COMMAND}" != "X" ]] && [[ "X${PARENT_OF_PARENT_CMD}" != "X" ]];then
        # 统计core文件
        record_corefile "${his_file}"
        test_full_command="echo CI@["$(date "+%F %T")"]; ${PARENT_OF_PARENT_CMD} ; "
        echo "$test_full_command" | tee -a ${his_file}
    else
        his_file="${TEST_HOME}/gmdb_sdv_log/start_history.sh"
        record_corefile "${his_file}"
    fi
}
record_history

currDir=`pwd`
currProc=`ls -l /proc/${PPID}/exe | awk '{print $NF}' | awk -F"/" '{print $NF}'`
currName=`cat /proc/${PPID}/cmdline | tr -d '\0'`
forceStop=""
backupXsanLog
if [ -d ${TEST_HOME}/log ]; then
    mkdir -p ${TEST_HOME}/logback/$currProc
    cp -r ${TEST_HOME}/log ${TEST_HOME}/logback/$currProc/
fi

isNeedStart
if [ $? -ne 1 ]; then
    exit 4
fi

# 停止服务端进程
# ${TEST_HOME}/tools/stop.sh ${forceStop} > /dev/null 2>&1

if [ ${RunMode} -eq 0 ]; then
    runPath=`grep -w serverLocator ${fileName} | awk -F":" '{print $NF}'`
    lockFile="`dirname ${runPath}`/GMDBV5_INSTANCE_3.lock"
    rm -f ${lockFile}
    if [ -d ${TEST_HOME}/log ]; then
        rm -rf ${TEST_HOME}/log
    fi
    prevPath=`pwd`
    cd ${TEST_HOME}
    # 默认绑定#3号核(taskset的4, 代表 0x4, 即100b, 代表3号核)
    if [ ${taskset} -eq 1 ]; then
        if [ "x" = "x${cfgFile}" ]; then
            taskset 4 gmserver -b > /dev/null 2>&1
        else
            taskset 4 gmserver -p ${cfgFile} -b > /dev/null 2>&1
        fi
    else
        if [ "x" = "x${cfgFile}" ]; then
            gmserver -b > /dev/null 2>&1
        else
            gmserver -p ${cfgFile} -b > /dev/null 2>&1
        fi
    fi
    cd ${prevPath}
    sleep 0.5
elif [ ${EnvType} -eq 1 ]; then
    startType="ae9700"
    ac_start_db
elif [ ${EnvType} -eq 2 ]; then
    echo "[start.sh] Info: IOT do nothing"
fi

grantGmsysviewPrivs
waitServerAvailable

markStartFlag

getPIDOfServer
printf "%s\n" " echo isServerStarted:${isServerStarted},pid:${pidOfServer}; " >> ${his_file}
