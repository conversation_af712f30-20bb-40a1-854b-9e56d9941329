extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

int ret;
unsigned int len;
int data_num = 100;
GmcConnT *g_conn_sync = NULL, *g_conn = NULL, *g_conn_2 = NULL, *g_conn_async = NULL, *g_conn_async_2 = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt = NULL, *g_stmt_2 = NULL, *g_stmt_async = NULL, *g_stmt_async_2 = NULL;
void *g_label = NULL, *g_label_2 = NULL;
char *g_schema = NULL, *g_schema_2 = NULL;
char g_label_config[64] = "{\"max_record_count\":10000}";
int affectRows = 0;

using namespace std;

void normal_insert_layer1(GmcStmtT *stmtVsys, int v1_id)
{
    int ret = GmcSetVertexProperty(stmtVsys, "id", GMC_DATATYPE_INT32, &v1_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtVsys, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

void normal_insert_layer2(GmcStmtT *stmtRule, int v2_vsys_id, int v2_id, void *v2_name)
{
    int ret = GmcSetVertexProperty(stmtRule, "vsys::id", GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "id", GMC_DATATYPE_INT32, &v2_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "name", GMC_DATATYPE_STRING, v2_name, strlen((const char *)v2_name));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtRule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtRule, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

void normal_insert_layer3(GmcStmtT *stmtS_ip, int v3_vsys_id, int v3_rule_id, int v3_ipLen, int v3_masklen)
{
    int ret = GmcSetVertexProperty(stmtS_ip, "rule::vsys::id", GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "rule::id", GMC_DATATYPE_INT32, &v3_rule_id, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "ipLen", GMC_DATATYPE_INT32, &v3_ipLen, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "maskLen", GMC_DATATYPE_INT32, &v3_masklen, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtS_ip, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

void test_tearDown_disconnect()
{
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema);
    printf("test_tearDown_disconnect End.\n");
    g_schema = NULL;
    g_label = NULL;
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_conn_async = NULL;
    g_stmt_async = NULL;
}

void test_close_and_drop_label_async(GmcStmtT *stmt, void *label, char *labelName)
{
    int ret;
    AsyncUserDataT data = {0};
    if (label) {
        label = 0;
    }
    ret = GmcDropVertexLabelAsync(stmt, labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);
}
