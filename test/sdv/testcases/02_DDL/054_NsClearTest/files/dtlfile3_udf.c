/*  版权所有 (c) 华为技术有限公司 2022-2023 */
#include <stdio.h>
#include <string.h>
#include "gm_udf.h"

#pragma pack(1)
typedef struct Func {
    int32_t dtlReservedCount;
    int32_t Fa;
    int32_t Fb;
    int32_t Fc;
} Func;

typedef struct FuncAggIn {
    int32_t dtlReservedCount;
    int32_t Fa;
    int32_t Fb;
} FuncAggIn;

typedef struct FuncAggOut {
    int32_t Fa;
}  FuncAggOut;

#pragma pack(0)
int32_t dtl_ext_func_funcA(void *tuple, GmUdfCtxT *ctx)
{
    int32_t ret = 0;
    // 查找存在的key
    char key[32] = "para1";
    uint32_t keylen = strlen(key) + 1;
    int32_t value = 100;
    uint32_t valuelen = sizeof(int32_t);
    // 需要校验返回的值
    ret = GmUdfGetAccessKV(ctx, key, keylen, &value, &valuelen);
    if (ret != GMERR_OK) {
        return -1;
    }
    if (valuelen != sizeof(uint32_t)) {
        // 不匹配的数据类型
        return GMERR_GET_THIRD_PARTY_FUNCTION_FAILED;
    }
    Func *p = (Func *)tuple;
    p->Fb = p->Fa + 5;

    return GMERR_OK;
}
