/*****************************************************************************
 Description  : DDL batch操作新增接口参数验证
 Notes        :
 History      :
 Author       : houjia hwx390087
 Modification :
*****************************************************************************/

#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>

#include "t_datacom_lite.h"

GmcConnT *conn = NULL;
GmcStmtT *stmt;
char *vertexLabel_schema1, *vertexLabel_schema2, *edgeLabel_schema = NULL;
char *vtxLable_long_name, *edgeLabel_long_name = NULL;
char labelName1[128] = "T39";
char labelName2[128] = "T39_02";
char edgeName[128] = "T39_to_T39_2";
char g_label_config[128] = "{\"max_record_count\":1000}";

int ret;
GmcConnT *g_conn_sync = NULL, *g_conn = NULL, *g_conn_2 = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt = NULL, *g_stmt_2 = NULL;
void *g_label = NULL, *g_label_2 = NULL;
char *g_schema = NULL, *g_schema_2 = NULL;
int res = 0;

GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

class Batch_DDL_interface_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
    static void SetUpTestCase()
    {
        // 重启server
        system("sh $TEST_HOME/tools/start.sh");

        // 读取两个顶点label和一个边label的schema json
        readJanssonFile("./schema_file/DML_insertVertex_test.gmjson", &vertexLabel_schema1);
        ASSERT_NE((void *)NULL, vertexLabel_schema1);

        readJanssonFile("./schema_file/DML_insertVertex_02_test.gmjson", &vertexLabel_schema2);
        ASSERT_NE((void *)NULL, vertexLabel_schema2);

        readJanssonFile("./schema_file/edge_insert_schema.gmjson", &edgeLabel_schema);
        ASSERT_NE((void *)NULL, edgeLabel_schema);

        readJanssonFile("./schema_file/DML_insertVertex_long_name_test.gmjson", &vtxLable_long_name);
        ASSERT_NE((void *)NULL, vtxLable_long_name);

        readJanssonFile("./schema_file/edge_insert_schema_long_name_test.gmjson", &edgeLabel_long_name);
        ASSERT_NE((void *)NULL, edgeLabel_long_name);

        res = testEnvInit();
        EXPECT_EQ(GMERR_OK, res);
        res = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
    };

    static void TearDownTestCase()
    {

        // 释放schema json资源
        free(vertexLabel_schema1);
        free(vertexLabel_schema2);
        free(edgeLabel_schema);
        free(vtxLable_long_name);
        free(edgeLabel_long_name);

        res = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
        testEnvClean();
    };
};

void Batch_DDL_interface_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    // 创建客户端连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcDropEdgeLabel(stmt, "T39_to_T39_2");
    GmcDropVertexLabel(stmt, "T39");
    GmcDropVertexLabel(stmt, "T39_02");
    AW_CHECK_LOG_BEGIN(0);
}

void Batch_DDL_interface_test::TearDown()
{
    printf("\n======================TEST:END========================\n");

    AW_CHECK_LOG_END();
    // 关闭 client connection
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

char deltaStoreJson[] = "{\
	\"version\": \"1.0\",\
	\"delta_stores\": \
	[\
	  {\
		\"name\": \"ccbdp1\",\
		\"init_mem_size\": 32768,\
		\"max_mem_size\": 131072,\
		\"extend_mem_size\": 8192,\
		\"page_size\": 4096\
	  }\
	]\
  }";

char g_configJson[128] = "{\"max_record_count\" : 10000}";

char g_label_name[] = "T20_all_type", g_label_name_2[] = "T20_all_type_2";
char g_lable_PK[] = "T20_PK";

// 01. GmcBatchPrepare接口入参stmt为空
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_001)
{
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    // 入参conn为空
    ret = GmcBatchPrepare(NULL, &batchOption, &batch);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

// 02.GmcBatchAddDDL接口入参batch为空
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_002)
{
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // GmcBatchAddDDL 接口入参batch为空
    // ret = GmcBatchAddDDL(NULL, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, g_configJson);
    ret = GmcBatchAddDDL(NULL, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, g_configJson);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

// 03.GmcBatchAddDDL接口入参opCode分别取值四种DDL枚举值
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    // opCode分别取值四种DDL枚举值
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    // add cmd 批量创建label
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
    EXPECT_EQ(2, totalNum);
    EXPECT_EQ(2, successNum);
#else
    EXPECT_EQ(3, totalNum);
    EXPECT_EQ(3, successNum);
#endif
    AW_FUN_Log(LOG_STEP, "\n[INFO] DDL batch create label status %d, totalNum %d, succNum %d\n",
        ret, totalNum, successNum);

    /*** 批量 drop label ***/
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // add cmd 批量drop label
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_EDGE_LABEL, edgeName, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName1, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName2, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
    EXPECT_EQ(2, totalNum);
    EXPECT_EQ(2, successNum);
#else
    EXPECT_EQ(3, totalNum);
    EXPECT_EQ(3, successNum);
#endif
    AW_FUN_Log(LOG_STEP, "\n[INFO] DDL batch drop label status %d, totalNum %d, succNum %d\n",
        ret, totalNum, successNum);
}

// 04.GmcBatchAddDDL接口入参labelName为空(2021.4.21：labelName取自schema
// json，入参labelname处不做校验，后续考虑优化去掉参数)
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_004)
{
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // 1）入参labelName为空（取自schema json，入参labelname处不做校验）
    // ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, NULL, vertexLabel_schema1, g_configJson);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, NULL, vertexLabel_schema1, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(1, successNum);

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);

    // 2）入参schema json定义中labelName为空
    char *schema = NULL;
    readJanssonFile("./schema_file/DML_insertVertex_NULL_labelName.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // batch prepare
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, "", schema, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    GmcBatchRetT batchRet2;
    ret = GmcBatchExecute(batch, &batchRet2);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchDeparseRet(&batchRet2, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(0, successNum);
}

// 05.GmcBatchAddDDL接口入参labelSchema为空
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_005)
{
    // batch prepare
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // 入参labelSchema
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, NULL, g_configJson);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 06.GmcBatchAddDDL接口入参configJson为空 （configJson允许为空）
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_006)
{
    // batch prepare
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // 入参configJson为空 （configJson允许为空）
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 执行批量操作
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(1, successNum);
    printf("\n[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);

    // drop vertex label
    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 07.GmcBatchExecute接口入参stmt为空
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    // GmcBatchAddDDL接口入参opCode分别取值四种DDL枚举值
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    // GmcBatchExecute接口入参batch为空
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(NULL, &batchRet);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 08.GmcBatchExecute接口入出参totalNum为空
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    // GmcBatchAddDDL接口入参opCode分别取值四种DDL枚举值
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    // GmcBatchExecute接口出参totalNum为空
    uint32_t totalNum = 0;
    uint32_t successNum = 0;

    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchDeparseRet(&batchRet, NULL, &successNum);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // drop label
#ifdef NRELEASE
#else
    ret = GmcDropEdgeLabel(stmt, edgeName);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName2);
    EXPECT_EQ(GMERR_OK, ret);
}

// 09.GmcBatchExecute接口入出参succNum为空
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    GmcDropEdgeLabel(stmt, edgeName);
    GmcDropVertexLabel(stmt, labelName1);
    GmcDropVertexLabel(stmt, labelName2);

    // GmcBatchAddDDL接口入参opCode分别取值四种DDL枚举值
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    // GmcBatchExecute接口出参succNum为空
    uint32_t totalNum = 0;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // drop label
#ifdef NRELEASE
#else
    ret = GmcDropEdgeLabel(stmt, edgeName);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName2);
    EXPECT_EQ(GMERR_OK, ret);
}

// 10.GmcBatchExecuteAsync异步接口入参stmt为空
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    int res = 0;
    // 创建异步连接
    res = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, res);

    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    // GmcBatchAddDDL接口入参opCode分别取值四种DDL枚举值
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#endif

    ret = GmcBatchExecuteAsync(NULL, NULL, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 11.GmcBatchAddDDL接口，opcode = GMC_CMD_CREATE_VERTEX_LABEL，入参vertexLabelName长度超长
// 2020.11.23 labelname长度128字节规格约束取消
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_011)
{
    // prepare
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // add cmd
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL,
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        vtxLable_long_name, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // batch execute
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    // ret = GmcBatchExecute(stmt, &totalNum, &successNum);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(0, successNum);
    printf("\n[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);

    // batch drop label
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL,
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        vtxLable_long_name, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);

    // batch execute
    unsigned int totalNum1 = 0;
    unsigned int successNum1 = 0;
    // ret = GmcBatchExecute(stmt, &totalNum1, &successNum1);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_NAME_TOO_LONG, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchDeparseRet(&batchRet, &totalNum1, &successNum1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum1);
    EXPECT_EQ(0, successNum1);
    printf("\n[INFO] DDL batch [drop] status %d, totalNum1 is %d, succNum is %d\n", ret, totalNum1, successNum1);
}

// 12.GmcBatchAddDDL接口，opcode = GMC_CMD_CREATE_EDGE_LABEL，入参edgeLabelName长度超长
// 2020.11.23 labelName长度128字节规格校验放开，取消校验
// 2021.5.17  增加edgeLabelName长度128字节校验；
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_012)
{
    AW_FUN_Log(LOG_STEP, "START\n");
    int ret = 0;
    // prepare
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    // add cmd
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

#ifdef NRELEASE
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    testGmcGetLastError(NULL);

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName2);
    EXPECT_EQ(GMERR_OK, ret);
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL,
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        edgeLabel_long_name, vertexLabel_config);

    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName2);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    AW_FUN_Log(LOG_STEP, "END\n");
}

// 13.GmcBatchAddDDL接口入参labelJson 报文长度超过1M
// old: STATUS_CLT_BATCH_NOT_PREPARED -> GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE， 现实际：GMERR_INVALID_PROPERTY
// 2021.9.16 一次batch execute最大2M
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_013)
{
    // prepare
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // 2M schema json
    char *schema_2M = NULL;
    readJanssonFile("./schema_file/large_schema_2M.gmjson", &schema_2M);
    ASSERT_NE((void *)NULL, schema_2M);

    // add cmd
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, "schema_2M", schema_2M, g_configJson);
    EXPECT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // batch execute
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    // ret = GmcBatchExecute(stmt, &totalNum, &successNum);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);  // STATUS_CLT_BATCH_NOT_PREPARED
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    EXPECT_EQ(0, totalNum);
    EXPECT_EQ(0, successNum);
    printf("\n[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);
    free(schema_2M);
}

// 14.异步batch execute回调函数 GmcBatchRecvRespAsync出参totalNum为空
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    int res = 0;
    // 创建异步连接
    res = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, res);

    // 预准备
    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    // DDL add cmd: 2 vetex label, 1 edge label
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#endif

    // 执行异步批量操作
    uint32_t totalNum = 0;
    uint32_t successNum = 0;

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    res = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, res);
    EXPECT_EQ(GMERR_OK, data.status);
#ifdef NRELEASE
    EXPECT_EQ(2, data.totalNum);
    EXPECT_EQ(2, data.succNum);
#else
    EXPECT_EQ(3, data.totalNum);
    EXPECT_EQ(3, data.succNum);
#endif
    AW_FUN_Log(LOG_STEP, "\n[INFO][CLIENT] DDL batch execute callback status %d, totalNum is %d, succNum is %d\n",
        data.status, data.totalNum, data.succNum);

    // drop edgeLabel, vtxLabel
#ifdef NRELEASE
#else
    ret = GmcDropEdgeLabel(stmt, edgeName);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName2);
    EXPECT_EQ(GMERR_OK, ret);

    // 断开异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 15.异步batch execute回调函数 GmcGetBatchExectueRespAsync出参successNum为空
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    int res = 0;
    // 创建异步连接
    res = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, res);

    // 预准备
    AsyncUserDataT data = {0};
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";

    // DDL add cmd: 2 vetex label, 1 edge label
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName2, vertexLabel_schema2, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef NRELEASE
#else
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_EDGE_LABEL, edgeName, edgeLabel_schema, vertexLabel_config);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    // 执行异步批量操作
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    res = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, res);
    EXPECT_EQ(GMERR_OK, data.status);
#ifdef NRELEASE
    EXPECT_EQ(2, data.totalNum);
    EXPECT_EQ(2, data.succNum);
#else
    EXPECT_EQ(3, data.totalNum);
    EXPECT_EQ(3, data.succNum);
#endif
    AW_FUN_Log(LOG_STEP, "\n[INFO][CLIENT] DDL batch execute callback status %d, totalNum is %d, succNum is %d\n",
        data.status, data.totalNum, data.succNum);

    // drop edgeLabel, vtxLabel
#ifdef NRELEASE
#else
    ret = GmcDropEdgeLabel(stmt, edgeName);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName2);
    EXPECT_EQ(GMERR_OK, ret);

    // async disconn
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 16. add cmd 配置deltaS
TEST_F(Batch_DDL_interface_test, DDL_012_Batch_DDL_interface_test_016)
{
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    char configJson[1024] = "{\"max_record_count\" : 1000}";
    ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName1, vertexLabel_schema1, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("[INFO] GmcBatchAddDDL with deltaS status is %d \n", ret);

    // 执行批量操作
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(1, successNum);
    printf("\n[INFO] DDL batch execute status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);

    // drop vertexLabel
    ret = GmcDropVertexLabel(stmt, labelName1);
    EXPECT_EQ(GMERR_OK, ret);
}
