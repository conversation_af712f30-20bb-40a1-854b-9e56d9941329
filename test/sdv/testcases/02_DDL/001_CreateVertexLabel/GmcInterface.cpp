/*****************************************************************************
 Description  : GmcCreateVertexLabel 创建顶点label接口测试
 Notes        : 01.创建、查询 VertexLabel 基本功能场景
                02.GmcCreateVertexLabel接口入参labelName需要与schema定义的name字段值保持一致；(2020.11 以schema
json中的定义为准) 03.Vertexlabel schema定义bitmap	迭代一不支持bitmap，返回GMERR_SYNTAX_ERROR_DATA_TYPE
EXPECT_EQ(GMERR_OK, ret);

                04.
 History      :
 Author       : houjia hwx390087
 Modification : [2020.11.18] 02.GmcCreateVertexLabel入参
*****************************************************************************/

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "gtest/gtest.h"

#include "t_datacom_lite.h"

#define LABELNAME_MAX_LENGTH 128
#define SCHEMA_JSON_SIZE 1024

GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;
int32_t ret;
int res = 0;

class GmcCreateVertexLabel_interface_test : public testing::Test {
public:
    static void SetUpTestCase()
    {
        // 重启server
        system("sh $TEST_HOME/tools/start.sh ");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    };

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    };
    virtual void SetUp();
    virtual void TearDown();
};

void GmcCreateVertexLabel_interface_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    // 创建同步客户端连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN(1);
}

void GmcCreateVertexLabel_interface_test::TearDown()
{
    printf("\n======================TEST:END========================\n");

    AW_CHECK_LOG_END();
    // 关闭 client connection
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

char g_label_schema[SCHEMA_JSON_SIZE] =
    "[{\"type\":\"record\", \"name\":\"T39\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
    "{\"name\":\"F1\", \"type\":\"int32\", \"nullable\" : false , \"default\" : 1},{\"name\":\"F2\", "
    "\"type\":\"int\"},{\"name\":\"F3\", \"type\":\"int32\"}],"
    "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
    "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";

char g_configJson[128] = "{\"max_record_count\" : 10000}";
char g_labelName[LABELNAME_MAX_LENGTH] = "T39";

// 01.创建、查询 VertexLabel 基本功能场景
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;

    // 创建VertexLabel
    GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 02.GmcCreateVertexLabel接口入参labelName需要与schema定义的name字段值保持一致；
// [2020.11.18] 取消接口入参labelName长度128字节校验，改为校验schema json中的name，后续接口入参可能删除；
// 创建vertexlabel labelname取自schema json
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel(schema json中定义T39，调用接口传入T39_11，两边不一致create返回70000)
    char labelName[LABELNAME_MAX_LENGTH] = "T39_11";
    GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // drop vtxLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 03. Vertexlabel schema定义bitmap	迭代一不支持bitmap，返回GMERR_SYNTAX_ERROR_DATA_TYPE
// 2021.3.26 支持bitmap，建表预期成功
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char bitmap_schema[SCHEMA_JSON_SIZE] =
        "[{\"type\":\"record\", \"name\":\"T39\",\"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"bitmap\", \"size\" : 16 "
        "},{\"name\":\"F3\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"T39\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";

    // 创建VertexLabel
    char labelName[LABELNAME_MAX_LENGTH] = "T39";
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, bitmap_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 04. schema定义config：check_validity、max vertex num
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/V3_schema_compatible.gmjson", &labelJson);
    char labelName[LABELNAME_MAX_LENGTH] = "V3_schema_compatible";
    ret = GmcCreateVertexLabel(stmt, labelJson, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);  // GMERR_SYNTAX_ERROR
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(labelJson);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 05.GmcCreateVertexLabel接口入参connect为NULL；
// 原预期：STATUS_CLT_NULL_PTR	==> 客户端内部空指针         GMERR_UNEXPECTED_NULL_VALUE
//                              ==> 客户端写入读取报文异常   GMERR_DATA_EXCEPTION
//                              ==> 非法参数值               GMERR_INVALID_PARAMETER_VALUE
// GMERR_UNDEFINED_TABLE
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel
    ret = GmcCreateVertexLabel(NULL, g_label_schema, g_configJson);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);  // STATUS_CLT_NULL_PTR
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, ret);  // STATUS_CATA_INVALID_VERTEX_LABEL_NAME
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

// 06.GmcCreateVertexLabel接口入参labelName为NULL；(已取消)
// schema json定义label name为空串""，建表可成功；【待】  DTS2021062504NYBOP1G00 错误码映射不全
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/vertexLabelName_NULL.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    free(schema_json);
}

// 07.GmcCreateVertexLabel接口入参labelName为空串；
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel，schema json name 为空串
    char schema[SCHEMA_JSON_SIZE] =
        "[{\"type\":\"record\", \"name\":\"\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\", \"nullable\" : false , \"default\" : 1},{\"name\":\"F2\", "
        "\"type\":\"int\"},{\"name\":\"F3\", \"type\":\"int32\"}],"
        "\"keys\":[{\"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    ret = GmcCreateVertexLabel(stmt, schema, g_configJson);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

// 08.GmcCreateVertexLabel接口入参labelName长度127；
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // label_name长度127
    char label_schema[SCHEMA_JSON_SIZE] =
        "[{\"type\":\"record\", "
        "\"name\":"
        "\"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
        "aaaaaaaaaaaaaaaaaaa\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
        "\"type\":\"int32\"}],"
        "\"keys\":[{\"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";

    // 创建VertexLabel
    uint32_t value_len = 127;
    char *long_labelname = (char *)malloc(value_len + 1);
    for (uint32_t i = 0; i < value_len; i++) {
        long_labelname[i] = 'a';
    }
    long_labelname[value_len] = '\0';
    ret = GmcCreateVertexLabel(stmt, label_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, long_labelname);
    ASSERT_EQ(GMERR_OK, ret);
    free(long_labelname);
}

// 09. vertexlabel_name 字符串长度 [1,128] 单位/字节，包含字符串结束符\0
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel
    uint32_t value_len = 127;
    char *long_labelname = (char *)malloc(value_len + 1);
    for (uint32_t i = 0; i < value_len; i++) {
        long_labelname[i] = 'a';
    }
    long_labelname[value_len] = '\0';

    // 读取schema json
    char *schema_json = NULL;
    readJanssonFile("./schema_file/vertexLabel_longLabelName_128.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, long_labelname);
    ASSERT_EQ(GMERR_OK, ret);
    free(long_labelname);
}

// 10.GmcCreateVertexLabel接口入参labelName非法值以V$或SV_开头
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char label_schema[SCHEMA_JSON_SIZE] =
        "[{\"type\":\"record\", \"name\":\"V$vertexLabel_006\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
        "\"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"V$vertexLabel_006\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";

    char label_schema1[SCHEMA_JSON_SIZE] =
        "[{\"type\":\"record\", \"name\":\"SV_vertexLabel_006\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},{\"name\":\"F2\", \"type\":\"int32\"},{\"name\":\"F3\", "
        "\"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"SV_vertexLabel_006\", \"name\":\"T39_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";

    // labelName非法值以V$或SV_开头
    char labelName1[LABELNAME_MAX_LENGTH] = "V$vertexLabel_006";
    ret = GmcCreateVertexLabel(stmt, label_schema, g_configJson);
    printf("[INFO] GmcCreateVertexLabel status is %d \n", ret);
    ASSERT_EQ(GMERR_INVALID_NAME, ret);  // 以V$ 开头 迭代一无校验，迭代三已加校验
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    char labelName2[LABELNAME_MAX_LENGTH] = "SV_vertexLabel_006";
    ret = GmcCreateVertexLabel(stmt, label_schema1, g_configJson);
    printf("[INFO] GmcCreateVertexLabel status is %d \n", ret);
    ASSERT_EQ(GMERR_OK, ret);  // 以SV 开头 迭代一无校验

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, labelName2);
    ASSERT_EQ(GMERR_OK, ret);
}

// 11. GmcCreateVertexLabel接口入参schema为NULL；
// STATUS_CLT_STRING_EMPTY
// 参数传入空字符串         GMERR_INVALID_PARAMETER_VALUE
// 内部结构数据空字符串     GMERR_DATA_EXCEPTION
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel，入参 schema 为NULL
    char labelName[LABELNAME_MAX_LENGTH] = "vertexLabel_007";
    ret = GmcCreateVertexLabel(stmt, NULL, g_configJson);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

// 12. 10kb schema（反序列化后小于32k）
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_012_1)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel，schema大小为31k
    char labelName[LABELNAME_MAX_LENGTH] = "test_100_fields_schema";
    char *labelJson = NULL;
    readJanssonFile("./schema_file/test_100_fields_schema.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson);
}

// 12.【9.2 open 60005】GmcCreateVertexLabel接口入参schema大小超过最大值；（原规格1M）8.31 迭代一暂时实现为32k
// 挂单：DTS202009030MDKYTP1H00  廖美丰
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel，schema大小为31k
    char labelName[LABELNAME_MAX_LENGTH] = "large_schema_31k";
    char *labelJson = NULL;
    readJanssonFile("./schema_file/large_schema_31k.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("[INFO] GmcDropVertexLabel status is %d \n", ret);
}

// 13.schema大小为32k，预期vertexlabel创建失败【迭代三优化】
// 迭代三： schema json大小取消32kb的约束，统一修改为请求报文最大1M；(原32kb schema json可建表成功)
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    /** 创建VertexLabel，schema大小为32k **/
    char *labelJson = NULL;
    char labelName[LABELNAME_MAX_LENGTH] = "large_schema_32k";
    readJanssonFile("./schema_file/large_schema_32k.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(stmt, labelJson, g_configJson);
    printf("\n[INFO] 32k schema GmcCreateVertexLabel status is %d \n", ret);
    ASSERT_EQ(GMERR_OK, ret);
    free(labelJson);

    // drop vtxLabel
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 14.GmcCreateVertexLabel接口入参schema非法值 (primary key unique = false)
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/wrong_schema.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);

    ret = GmcCreateVertexLabel(stmt, labelJson, g_configJson);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);  // STATUS_QUERY_INVALID_UINT32
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(labelJson);
}

// 15.GmcCreateVertexLabel接口入参configJson为NULL
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel，入参configJson允许为NULL
    GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, NULL);  // g_configJson
    ASSERT_EQ(GMERR_OK, ret);
    printf("[INFO] g_configJson is NULL, GmcCreateVertexLabel status is %d \n", ret);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 16. 入参configJson中delta_store_name指定的cache不存在（未创建） 【9.16 err_code = 1】
// STATUS_CATA_INVALID_DELTASTORE_NAME
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建VertexLabel
    char configJson[128] = "{\"max_record_count\":1000}";

    ret = GmcCreateVertexLabel(stmt, g_label_schema, configJson);
    ASSERT_EQ(GMERR_OK, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 17.GmcCreateVertexLabel接口入参configJson非法值；
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建VertexLabel， error config json with invalid max_record_count
    char configJson_err[128] = "{\"max_record_count_abc\":1000}";

    GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, configJson_err);
    printf("[INFO][g_configJson: max_vertex_num invalid] GmcCreateVertexLabel status is %d \n", ret);
    ASSERT_EQ(GMERR_OK, ret);  // 匹配不到max_record_count认为用户未配置，max num取默认值

    // drop vtxLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 18.GmcCreateVertexLabel接口入参configJson配置项maxLabelNum边界值，取最小值场景
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建VertexLabel，configJson 中的max_record_count取最小值
    char g_configJson[128] = "{\"max_record_count\" : 1}";
    GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 19.GmcCreateVertexLabel接口入参configJson配置项maxLabelNum小于下边界值（非法属性值）
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel
    char g_configJson[128] = "{\"max_record_count\": 0}";
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_configJson);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);  // STATUS_QUERY_INVALID_UINT64
    res = testGmcGetLastError(NULL);
    ASSERT_EQ(GMERR_OK, res);
}

// 20.GmcCreateVertexLabel接口入参configJson配置项边界值 = MAX value （8.27 max暂时无规格）
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcDropVertexLabel(stmt, g_labelName);
    // 创建VertexLabel
    char g_configJson[128] = "{\"max_record_count\" : 9223372036854775807}";
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 21.GmcCreateVertexLabel 接口入参configJson配置项边界值 > MAX value （8.27 max暂时无规格）
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    GmcDropVertexLabel(stmt, g_labelName);
    // 创建VertexLabel
    char configJson[128] = "{\"max_record_count\" : 9223372036854775808}";
    ret = GmcCreateVertexLabel(stmt, g_label_schema, configJson);
    ASSERT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

// 27.schema新增数据类型 char、uchar
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 读取schema json
    char *schema_json = NULL;
    readJanssonFile("./schema_file/char_unchar_schema.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // 创建VertexLabel
    char labelName[128] = "test_char_unchar";
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    free(schema_json);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 28.创建vertexLabel成功后，再次创建同名label  [CIDA]
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 读取schema json
    char *schema_json = NULL;
    readJanssonFile("./schema_file/schema_datatype.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // 创建VertexLabel
    char labelName[128] = "schema_datatype";
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    printf("[INFO] GmcCreateVertexLabel[1] status is %d \n", ret);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建同名已存在的VertexLabel
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_DUPLICATE_TABLE, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
}

// 29. GmcCloseVertexLabel 入参stmt为空 -- 接口已删除
// 2021.8.2 验证新增GmcSetIndexKeyName接口参数
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel
    char labelName[128] = "T39";
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_configJson);
    printf("[INFO] GmcCreateVertexLabel status is %d \n", ret);
    ASSERT_EQ(GMERR_OK, ret);

    // 入参stmt为NULL
    ret = GmcSetIndexKeyName(NULL, "T39_K0");
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 30. GmcCloseVertexLabel 入参vertexLabel为空
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel
    char labelName[128] = "T39";
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_configJson);
    printf("[INFO] GmcCreateVertexLabel status is %d \n", ret);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    // 入参keyName为NULL 设置成功
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // drop顶点label
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 31.char、uchar配置默认值报错50019  (DTS用例 DTS202009070LHT7JP1F00)
// uchar默认值数据类型变更（int->string）DTS2020091105N0I8P1400
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 读取schema json
    char *schema_json = NULL;
    readJanssonFile("./schema_file/char_unchar_defaultValue_schema.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // 创建VertexLabel
    char labelName[128] = "test_char_unchar";
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 32.schema定义中int8 int16配置default属性  (创建label失败,DTS: DTS202009010E0P9WP1100)
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 读取schema json
    char *schema_json = NULL;
    readJanssonFile("./schema_file/int8_int16_defaultValue_schema.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    // 创建VertexLabel
    char labelName[128] = "int8_int16_defaultValue";
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // drop顶点label
    ret = GmcDropVertexLabel(stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 33. labelname长度无128字节规格约束，验证drop label
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    // 创建VertexLabel
    uint32_t value_len = 132;
    char *long_labelname = (char *)malloc(value_len + 1);
    for (uint32_t i = 0; i < value_len + 1; i++) {
        long_labelname[i] = 'a';
    }
    long_labelname[value_len] = '\0';

    printf("longname: %s \n", long_labelname);

    // 读取schema json
    char *schema_json = NULL;
    readJanssonFile("./schema_file/vertexLabel_longLabelName.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, long_labelname);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    printf("\n[INFO] createVtxLabel name over 128 status %d \n", ret);
    // ASSERT_EQ(GMERR_OK, ret);

    free(schema_json);
    // drop顶点label
    ret = GmcDropVertexLabel(stmt, long_labelname);
    // ASSERT_EQ(GMERR_OK, ret);
    free(long_labelname);
    printf("\n[INFO] GmcDropVertexLabel name over 128 status %d \n", ret);
}

// 34. schema json定义两个PK，预期建表报错
TEST_F(GmcCreateVertexLabel_interface_test, DDL_001_GmcCreateVertexLabel_interface_test_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_2pk.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);

    char labelName[LABELNAME_MAX_LENGTH] = "ip4forward";
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_DUPLICATE_OBJECT, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
    printf("[INFO] schema json with 2 primary key, GmcCreateVertexLabel status is %d \n", ret);
    free(schema_json);
}
