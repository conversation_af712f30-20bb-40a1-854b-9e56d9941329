[{"type": "record", "name": "T39_all_type", "fields": [{"name": "F7", "type": "uint32", "nullable": false}, {"name": "F0", "type": "char", "nullable": false}, {"name": "F1", "type": "uchar", "nullable": false}, {"name": "F2", "type": "int8", "nullable": false}, {"name": "F3", "type": "uint8", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": false}, {"name": "F6", "type": "int32", "nullable": false}, {"name": "F8", "type": "boolean", "nullable": false}, {"name": "F9", "type": "int64", "nullable": false}, {"name": "F10", "type": "uint64", "nullable": false}, {"name": "F11", "type": "float", "nullable": false}, {"name": "F12", "type": "double", "nullable": false}, {"name": "F13", "type": "time", "nullable": false}, {"name": "F14", "type": "string", "nullable": false, "size": 100}, {"name": "F15", "type": "bytes", "size": 12}, {"name": "F16", "type": "fixed", "size": 12}], "keys": [{"node": "T39_all_type", "name": "T39_K0", "fields": ["F7"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]