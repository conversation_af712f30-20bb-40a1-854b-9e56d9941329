/*****************************************************************************
 Description  : 同步批量Replace功能测试
 Notes        : DDL_016_BatchReplaceVertexSync_001	001.同步批量Replace 100个顶点，读数据
                DDL_016_BatchReplaceVertexSync_002	002.同步批量Replace 多个vertexLabel的顶点
                DDL_016_BatchReplaceVertexSync_003	003.同步批量Replace 1025个顶点，执行失败
                DDL_016_BatchReplaceVertexSync_004	004.同一vertexLabel并发同步批量Replace100个顶点，读数据
                DDL_016_BatchReplaceVertexSync_005	005.不同vertexLabel并发同步批量Replace ,读数据
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2020/11/17
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
int affectRows;
unsigned int len;
char *normal_vertexlabel_schema = NULL;
char *simple_vertexlabel_schema = NULL;
const char *normal_config_json = R"(
    {
        "max_record_count":10000
    }
)";
const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_simple_vertexlabel_name = "T39_simple";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";
const char *g_simple_pk_name = g_normal_pk_name;
const char *g_simple_sk_name = g_normal_sk_name;

void set_VertexProperty_PK(GmcStmtT *stmt, int i);
void set_VertexProperty_SK(GmcStmtT *stmt, int i);
void set_VertexProperty(GmcStmtT *stmt, int i);
void query_VertexProperty(GmcStmtT *stmt, int i);

class BatchReplaceSyn : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
        readJanssonFile("schemaFile/SimpleVertexLabel.gmjson", &simple_vertexlabel_schema);
        ASSERT_NE((void *)NULL, simple_vertexlabel_schema);
        int ret = 0;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        free(normal_vertexlabel_schema);
        free(simple_vertexlabel_schema);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void BatchReplaceSyn::SetUp()
{
    printf("[INFO] BatchReplaceSyn Start.\n");
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void BatchReplaceSyn::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("[INFO] BatchReplaceSyn End.\n");
}

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty_SK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F14Value[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    EXPECT_EQ(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
}

void query_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    // Get F0
    char F0Value = i;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned char F1Value = i;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F2
    int8_t F2Value = i;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F3
    uint8_t F3Value = i;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F4
    int16_t F4Value = i;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F5
    uint16_t F5Value = i;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F6
    int32_t F6Value = i;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F8
    bool F8Value = false;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F10
    uint64_t F10Value = i;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F11
    float F11Value = i;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F12
    double F12Value = i;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F13
    uint64_t F13Value = i;
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F14
    char F14Value[] = "testver";
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F15
    char F15Value[12] = "12";
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, F15Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F16
    char F16Value[12] = "13";
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, F16Value);
    EXPECT_EQ(GMERR_OK, ret);
}

// 001.同步批量replace 100个顶点，读数据
TEST_F(BatchReplaceSyn, DDL_016_BatchReplaceVertexSync_001)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t start_num = 0;
    uint32_t end_num = 100;

    // batch replace Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);

    // query replace Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        query_VertexProperty(g_stmt, i);
        uint64_t SKValue = i;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.同步批量replace 多个vertexLabel的顶点
TEST_F(BatchReplaceSyn, DDL_016_BatchReplaceVertexSync_002)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    void *vertexLabel2 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, simple_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t start_num = 0;
    uint32_t end_num = 100;

    // batch replace Vertex
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_simple_vertexlabel_name, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num * 2, totalNum);
    EXPECT_EQ(end_num * 2, successNum);

    // query replace Vertex
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // Query VertexLabel1
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);

        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        query_VertexProperty(g_stmt, i);
        uint64_t SKValue = i;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_simple_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // Query VertexLabel2
        ret = GmcSetIndexKeyName(g_stmt, g_simple_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, i);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    // free
    GmcFreeIndexKey(g_stmt);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simple_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.同步批量replace 1025个顶点，执行失败
TEST_F(BatchReplaceSyn, DDL_016_BatchReplaceVertexSync_003)
{
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_BATCH_BUFFER_FULL);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t start_num = 0;
    uint32_t end_num = 1025;

    // batch replace Vertex 1025
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        if (i == 1024) {
            EXPECT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    // batch replace Vertex 1024
    end_num = 1024;
    ret = GmcBatchReset(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

void *batchReplaceVertexNormal(void *args)
{
    void *vertexLabel;
    GmcConnT *conn_t;
    GmcStmtT *stmt_t;
    int ret = testGmcConnect(&conn_t, &stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 100;
    uint32_t val = 1000;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_t, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        set_VertexProperty_PK(stmt_t, i);
        set_VertexProperty_SK(stmt_t, i + 100);
        set_VertexProperty(stmt_t, val);
        ret = GmcBatchAddDML(batch, stmt_t);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *batchReplaceVertexSimple(void *args)
{
    void *vertexLabel;
    GmcConnT *conn_t;
    GmcStmtT *stmt_t;
    int ret = testGmcConnect(&conn_t, &stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t, g_simple_vertexlabel_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 100;
    uint32_t val = 1000;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_t, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        set_VertexProperty_PK(stmt_t, i);
        set_VertexProperty_SK(stmt_t, i + 100);
        set_VertexProperty(stmt_t, val);
        ret = GmcBatchAddDML(batch, stmt_t);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn_t, stmt_t);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 004.同一vertexLabel并发同步批量replace100个顶点，读数据
TEST_F(BatchReplaceSyn, DDL_016_BatchReplaceVertexSync_004)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t start_num = 0;
    uint32_t end_num = 100;

    // batch insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num, totalNum);
    EXPECT_EQ(end_num, successNum);

    // multi threads replace
    int tdNum = 8;
    int err = 0;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&sameNameth[i], NULL, batchReplaceVertexNormal, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    // query replace Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int val = 1000;
    for (uint32_t i = start_num; i < end_num; i++) {
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);

        query_VertexProperty(g_stmt, val);
        uint64_t SKValue = i + 100;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.不同vertexLabel并发同步批量replace ,读数据
TEST_F(BatchReplaceSyn, DDL_016_BatchReplaceVertexSync_005)
{
    int ret = 0;
    void *vertexLabel1 = NULL;
    void *vertexLabel2 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, simple_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t start_num = 0;
    uint32_t end_num = 100;

    // batch insert Vertex
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_simple_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(end_num * 2, totalNum);
    EXPECT_EQ(end_num * 2, successNum);

    // multi threads replace
    int err = 0;
    pthread_t sameNameth[2];
    err = pthread_create(&sameNameth[0], NULL, batchReplaceVertexNormal, NULL);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&sameNameth[1], NULL, batchReplaceVertexSimple, NULL);
    EXPECT_EQ(GMERR_OK, err);
    pthread_join(sameNameth[0], NULL);
    pthread_join(sameNameth[1], NULL);

    // query
    int val = 1000;
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // Query VertexLabel1
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, val);
        uint64_t SKValue = i + 100;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_simple_vertexlabel_name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // Query VertexLabel2
        ret = GmcSetIndexKeyName(g_stmt, g_simple_pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simple_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}
