/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-22 09:32:49
 * @FilePath: \GMDBV5\test\sdv\gmdb_td\testcases\001_stream\015_support_printf_format\03_support_printf_format.cpp
 * @Description: 
 * @LastEditors: tian<PERSON><PERSON> 
 * @LastEditTime: 2024-11-15 11:51:24
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "rd_feature_stream.h"
#include "stream_table_struct.h"



class PrintfFormat : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *PrintfFormat::conn = NULL;
GmcStmtT *PrintfFormat::stmt = NULL;

void PrintfFormat::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void PrintfFormat::TearDownTestCase()
{
    int32_t ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void PrintfFormat::SetUp()
{
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void PrintfFormat::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// format函数使用特殊打印格式%x，且后面参数数量相同，类型为字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %x', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %x', dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %x, name is %x', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%X，且后面参数数量相同，类型为字符串类型 预期：失败
TEST_F(PrintfFormat, STREAM_015_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %X', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %X', dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %X, name is %X', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%s，且后面参数数量相同，类型为integer 预期：失败
TEST_F(PrintfFormat, STREAM_015_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %s', id) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %s', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %s, name is %s', id, age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数使用特殊打印格式%s和%d，且后面参数数量相同，类型没有对应 预期：失败
TEST_F(PrintfFormat, STREAM_015_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %d, name is %s', name, age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %d, name is %s, id is %d, address is %s', name, age, dept, id) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_DATATYPE_MISMATCH
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数占位符数量与参数数量不匹配 预期：失败
TEST_F(PrintfFormat, STREAM_015_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %d, name is %d', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %d, name is %d', age, id ,id) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %d, name is %s, id is %d, address is %s', name, age, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is %d, name is %s, id is %d, address is %s',id, name, age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 关键字FORMAT拼写错误 预期：失败
TEST_F(PrintfFormat, STREAM_015_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, formatt('My age is %s', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
        {
            "create stream sink sink1 as SELECT id, name, age, fformat('My name is %s', dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
        {
            "create stream sink sink1 as SELECT id, name, age, foormat('My age is %s, name is %s', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 关键字FORMAT缺失 预期：失败
TEST_F(PrintfFormat, STREAM_015_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, ('My age is %s', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR
        },
        {
            "create stream sink sink1 as SELECT id, name, age, ('My name is %d', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR
        },
        {
            "create stream sink sink1 as SELECT id, name, age, ('My age is %s, name is %s', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 使用FORMAT以外的关键字 预期：失败
TEST_F(PrintfFormat, STREAM_015_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, printff('My age is %s', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
        {
            "create stream sink sink1 as SELECT id, name, age, printtf('My name is %d', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
        {
            "create stream sink sink1 as SELECT id, name, age, printtf('My age is %s, name is %s', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_FEATURE_NOT_SUPPORTED
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 关键字FORMAT同时包含大小写字母 预期：成功
TEST_F(PrintfFormat, STREAM_015_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, Format('My age is %ld', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK
        },
         {
            "create stream sink sink2 as SELECT id, name, age, foRmaT('My age is %ld', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK
        },
         {
            "create stream sink sink3 as SELECT id, name, age, FORMAT('My age is %ld', age) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream sink sink2;"},
        {"drop stream sink sink3;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数语句占位符缺失 预期：失败
TEST_F(PrintfFormat, STREAM_015_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is , name is ', age, name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is , name is , id is , address is ', id, name, age, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_INVALID_PARAMETER_VALUE
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数语句只存在参数 预期：失败
TEST_F(PrintfFormat, STREAM_015_051_1)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format(id, name, age, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数语句参数在表中不存在 预期：失败
TEST_F(PrintfFormat, STREAM_015_051_2)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('%s', address) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_UNDEFINE_COLUMN
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('%d', weight) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_UNDEFINE_COLUMN
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数不包含占位符和变量 预期：失败
TEST_F(PrintfFormat, STREAM_015_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('My age is 18, name is lisi') "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数语句括号缺失/冗余  预期：失败
TEST_F(PrintfFormat, STREAM_015_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format(('My age is %s', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %s', dept)) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format(('My age is %s, name is %s', name, dept)) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_SEMANTIC_ERROR
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format 'My age is %s, name is %s', name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format ('My age is %s, name is %s', name, dept "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数语句引号缺失  预期：失败
TEST_F(PrintfFormat, STREAM_015_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format(My age is %s', name) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format('My name is %s, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
        {
            "create stream sink sink1 as SELECT id, name, age, format(My age is %s, name is %s, name, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_SYNTAX_ERROR
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数设置最大单个转换后的format字符串长度为256  预期：成功
TEST_F(PrintfFormat, STREAM_015_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {"create stream sink sink1 as SELECT id, name, age, format('%256d', age) from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    int64_t insertVal = 1;
    uint32_t rowNum = 200;
    RdStructWriteStreamTable1(stmt, vertexLabel, rowNum, insertVal, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    (void)sprintf(expectDept,"%256d", insertVal);
    RdCheckDataInTSTableOfStreamTable2(stmt, selectTsName, expectRowsCount, expectColsCount, insertVal, expectDept);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数设置最大单个转换后的format字符串长度为257  预期：成功
TEST_F(PrintfFormat, STREAM_015_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {"create stream sink sink1 as SELECT id, name, age, format('%0257s', name) from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    int64_t insertVal = 1;
    uint32_t rowNum = 200;
    RdStructWriteStreamTable1(stmt, vertexLabel, rowNum, insertVal, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
    (void)sprintf(expectDept,"%0257s", "name_1");
    RdCheckDataInTSTableOfStreamTable2(stmt, selectTsName, expectRowsCount, expectColsCount, insertVal, expectDept);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数设置输出的打印结果长度为1024  预期：成功
TEST_F(PrintfFormat, STREAM_015_056_1)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(1030), age integer, dept char(1030)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(1030), age integer, dept char(1030));"},
        {"create stream view view1 as select * from stream1;"},
        {"create stream sink sink1 as SELECT id, name, age, format('this is the information:%256d, %256s, %256d, %225s', id, name, age, dept) from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    int64_t insertVal = 1;
    uint32_t rowNum = 200;
    RdStructWriteStreamTable2(stmt, vertexLabel, rowNum, insertVal, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T2_NAME_SIZE] = {0};
    (void)sprintf(expectDept,"this is the information:%256d, %256s, %256d, %225s", insertVal, "name_1", insertVal, "dept_1");
    RdCheckDataInTSTableOfStreamTable2(stmt, selectTsName, expectRowsCount, expectColsCount, insertVal, expectDept);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// format函数设置输出的打印结果长度为1025  预期：成功
TEST_F(PrintfFormat, STREAM_015_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(1030), age integer, dept char(1030)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(1030), age integer, dept char(1030));"},
        {"create stream view view1 as select * from stream1;"},
        {"create stream sink sink1 as SELECT id, name, age, format('this is the information:%256d, %256s, %256d, %227s', id, name, age, dept) from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    int64_t insertVal = 1;
    uint32_t rowNum = 200;
    RdStructWriteStreamTable2(stmt, vertexLabel, rowNum, insertVal, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T2_NAME_SIZE] = {0};
    (void)sprintf(expectDept,"this is the information:%256d, %256s, %256d, %225s", insertVal, "name_1", insertVal, "dept");
    RdCheckDataInTSTableOfStreamTable2(stmt, selectTsName, expectRowsCount, expectColsCount, insertVal, expectDept);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 时序表字符串字段长度 大于 format输出长度 预期：成功
TEST_F(PrintfFormat, STREAM_015_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {"create stream sink sink1 as SELECT id, name, age, format('this is the information:%100d, %50s, %50d, %30s', id, name, age, dept) from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK},
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    int64_t insertVal = 1;
    uint32_t rowNum = 200;
    RdStructWriteStreamTable1(stmt, vertexLabel, rowNum, insertVal, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T2_NAME_SIZE] = {0};
    (void)sprintf(expectDept,"this is the information:%100d, %50s, %50d, %30s", insertVal, "name_1", insertVal, "dept_1");
    RdCheckDataInTSTableOfStreamTable1(stmt, selectTsName, expectRowsCount, expectColsCount, insertVal, expectDept);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 时序表字符串字段长度 小于 format输出长度 预期：报错
TEST_F(PrintfFormat, STREAM_015_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    int32_t ret;

    // 创建时序表、流表和SINK
    RdStreamExecDescT creates[] = {
        {"create table ts1 (id integer, name char(300), age integer, dept char(300)) with (time_col = 'id', interval= '1 hour', compression = 'no');", GMC_MODEL_TS},
        {"create stream table stream1 (id integer, name char(300), age integer, dept char(300));"},
        {"create stream view view1 as select * from stream1;"},
        {
            "create stream sink sink1 as SELECT id, name, age, format('this is the information:%100d, %100s, %50d, %50s', id, name, age, dept) "
            "from view1 into tsdb (ts1) with (batch_window_size = 1);", GMC_MODEL_STREAM, GMERR_OK
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    int64_t insertVal = 1;
    uint32_t rowNum = 200;
    RdStructWriteStreamTable1(stmt, vertexLabel, rowNum, insertVal, 0);

    // 查询ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = rowNum;
    uint32_t expectColsCount = 4;
    char expectDept[RD_STREAM_TABLE_T2_NAME_SIZE] = {0};
    (void)sprintf(expectDept,"this is the information:%100d, %100s, %50d, %50s", insertVal, "name_1", insertVal, "dept_1");
    RdCheckDataOfFormat(stmt, selectTsName, expectRowsCount, expectColsCount, insertVal, expectDept);

    // 释放资源
    RdStreamExecDescT drops[] = {
        {"drop table ts1;", GMC_MODEL_TS},
        {"drop stream sink sink1;"},
        {"drop stream view view1;"},
        {"drop stream table stream1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}






