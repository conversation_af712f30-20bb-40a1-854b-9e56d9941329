/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-03-24 10:54:24
 * @FilePath: \GMDBV5\test\sdv\testcases\34_stream\039_support_select_as\01_support_select_as_basic.cpp
 * @Description: 
 * @LastEditors: t<PERSON><PERSON><PERSON> 
 * @LastEditTime: 2025-03-24 16:49:30
 */
#include "gtest/gtest.h"
#include "securec.h"
#include "gmc_sql.h"
#include "t_rd_sn.h"
#include "rd_feature_stream.h"
#include "rd_feature_ts.h"

#include "as_util.h"


class SupportAvg : public testing::Test {
public:
    static GmcConnT *conn;
    static GmcStmtT *stmt;

    bool hasFinishTest = false;
    void RdFinishTest();

    static void SetUpTestCase();
    static void TearDownTestCase();
    virtual void SetUp();
    virtual void TearDown();
};

GmcConnT *SupportAvg::conn = NULL;
GmcStmtT *SupportAvg::stmt = NULL;

void SupportAvg::SetUpTestCase()
{
    int32_t ret;
    ret = RdStreamEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportAvg::TearDownTestCase()
{
    int32_t ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = RdStreamEnvClean();
    EXPECT_EQ(GMERR_OK, ret);
}

void SupportAvg::SetUp()
{   
    // 创建客户端连接
    int32_t ret = RdGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void SupportAvg::TearDown()
{
    int32_t ret = RdGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// as重命名的名字，[a-zA-Z]开头，只包含大小写字母 预期：成功
TEST_F(SupportAvg, STREAM_039_BASIC_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
         {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select 12 as idCol, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select idCol, name, time, age, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 19;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAge[i], val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// as重命名的名字，_开头，包含大小写字母、_和数字 预期：成功
TEST_F(SupportAvg, STREAM_039_BASIC_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
         {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select 12 as _idCol, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select _idCol, name, time, age, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 19;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAge[i], val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// as重命名的名字，包含中文 预期：成功
TEST_F(SupportAvg, STREAM_039_BASIC_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
         {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select 12 as 字段1, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select 字段1, name, time, age, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 19;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(12, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAge[i], val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// selsect数值常量INT64_MAX并as重命名 预期：成功
TEST_F(SupportAvg, STREAM_039_BASIC_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
         {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select 9223372036854775807 as idCol, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select idCol, name, time, age, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 19;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(9223372036854775807, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAge[i], val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// selsect数值常量0并as重命名 预期：成功
TEST_F(SupportAvg, STREAM_039_BASIC_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
         {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select 0 as idCol, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select idCol, name, time, age, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 19;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAge[i], val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// selsect数值常量INT64_MAX + 1并as重命名 预期：成功
TEST_F(SupportAvg, STREAM_039_BASIC_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int32_t ret;

    // 创建相关表
    RdStreamExecDescT creates[] = {
        {
           "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
            "with (time_col = 'id', interval = '1 hour');"
        },
         {
            "create stream table if not exists t1 (id integer, name text, time integer, age integer, address char(50));"
        },
        {
            "create stream view v1 as select 9223372036854775808 as idCol, name, time, age, address from t1 with (tuple_buffer_size = 1);"
        },
        {
            "CREATE STREAM SINK s1 AS select idCol, name, time, age, address FROM v1 "
            "INTO tsdb(ts1) with "
            "(batch_window_size = '1', tuple_buffer_size = '1');"
        },
    };
    ret = RdStreamExecSql(stmt, creates, sizeof(creates));
    EXPECT_EQ(GMERR_OK, ret);

    // 解析建表SQL，生成结构化schema
    RdVertexLabelT *vertexLabel = RdStreamParseTableSchema(creates[1].sql);
    ASSERT_NE((RdVertexLabelT *)NULL, vertexLabel);

    // 结构化写入数据
    uint16_t len = 7;
    uint16_t fixedLen = 10;
    uint32_t rowNum = 19;
    int64_t id[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41}; 
    int64_t time[rowNum] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t age[rowNum] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};
    RdStructWriteStreamTableByArr(stmt, vertexLabel, rowNum, id, time, age, len, fixedLen);

    // 校验ts表中数据
    char *selectTsName = (char *)"ts1";
    uint32_t expectRowsCount = 19;
    uint32_t expectColsCount = 5;
    int64_t expectId[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectTime[expectRowsCount] = {1, 3, 5, 10, 11, 13, 15, 20, 7, 21, 7, 24, 25, 30, 8, 35, 22, 24, 41};
    int64_t expectAge[expectRowsCount] = {10, 11, 12, 10, 13, 12, 16, 10, 10, 12, 11, 11, 12, 10, 10, 11, 11, 10, 10};

    char *selectCmd = (char *) "SELECT * FROM ts1;";
    ret = GmcExecDirect(stmt, selectCmd, strlen(selectCmd));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)8, size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(9223372036854775807, val);

        // check name
        ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        char name[size] = {0};
        char expectName[len] = {0};
        (void)snprintf_s(expectName, len, len, "name%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(strlen(name) + 1, size);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectName, name));

        // check time
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectTime[i], val);

        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAge[i], val);

        // check address
        size = RD_STREAM_TABLE_T1_NAME_SIZE;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, fixedLen, fixedLen, "address%0ld", expectId[i]);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &address, &size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, strcmp(expectAddress, address));
        
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(expectRowsCount, i);
    
    // 删除相关表
    RdStreamExecDescT drops[] = {
        {"drop stream sink s1;"},
        {"drop stream view v1;"},
        {"drop stream table t1;"},
        {"drop table ts1;"},
    };
    ret = RdStreamExecSql(stmt, drops, sizeof(drops));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    RdStreamFreeTableSchema(vertexLabel);

    AW_FUN_Log(LOG_STEP, "test end.");
}

