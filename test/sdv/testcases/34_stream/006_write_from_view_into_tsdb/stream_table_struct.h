/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 流表t1
 * Author: t<PERSON><PERSON><PERSON>
 * Create: 2024-09-10
 */
#ifndef STREAM_TABLE_STRUCT_H
#define STREAM_TABLE_STRUCT_H 1
#include "rd_feature_stream.h"

#define RD_STREAM_TABLE_T1_NAME_SIZE 50

/**
 * @brief 定义结构化表数据
 * @attention 定义结构体必须满足既定约定，否则行为未定义
 *  1、通过#pragma pack(1)控制结构体按1字节对齐
 *  2、变长字段的上一个成员是该字段的长度，长度必须使用uint16定义
 *  3、定长字段不需要定义长度
 *  4、fixed类型（即流表中的CHAR类型）不需要定义长度，但必须使用数组，且数组长度和表定义的大小一致
 *  5、结构体必须和表定义中的列一一对应，否则行为未定义
 *  6、变长字段申请的内存必须在写入数据后手动释放，否则将导致内存泄露
 */
#pragma pack(1)
typedef struct {
    int64_t id;
    char name[RD_STREAM_TABLE_T1_NAME_SIZE]; // 定长类型，使用定长数组，不需要单独定义长度
} RdStreamTable1DataT;
#pragma pack()

#pragma pack(1)
typedef struct {
    int64_t id;
    char name[RD_STREAM_TABLE_T1_NAME_SIZE]; // 定长类型，使用定长数组，不需要单独定义长度
    int64_t age;
    char address[RD_STREAM_TABLE_T1_NAME_SIZE];
} RdStreamTable2DataT;
#pragma pack()

#pragma pack(1)
typedef struct {
    int64_t id;
    char name[RD_STREAM_TABLE_T1_NAME_SIZE]; // 定长类型，使用定长数组，不需要单独定义长度
    char address[RD_STREAM_TABLE_T1_NAME_SIZE];
} RdStreamTable3DataT;
#pragma pack()

#pragma pack(1)
typedef struct {
    RdVertexLabelT *vertexLabel;
    int64_t rowNum;
    int64_t id;
    int64_t seed;
} RdThreadWriteT;
#pragma pack(1)

RdStreamTable1DataT *RdStreamT1PrepareData(int64_t id, int64_t seed)
{
    RdStreamTable1DataT *t1 = (RdStreamTable1DataT *)malloc(sizeof(RdStreamTable1DataT));
    if (t1 == NULL) {
        RD_ERROR("Unable to malloc stream data.");
        return NULL;
    }
    (void)memset_s(t1, sizeof(RdStreamTable1DataT), 0x0, sizeof(RdStreamTable1DataT));

    *t1 = (RdStreamTable1DataT) {
        .id = id,
        .name = {0},
    };
    (void)snprintf_s(t1->name, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "test_%0ld", id + seed);
    return t1;
}

RdStreamTable2DataT *RdStreamT2PrepareData(int64_t id, int64_t seed)
{
    RdStreamTable2DataT *t1 = (RdStreamTable2DataT *)malloc(sizeof(RdStreamTable2DataT));
    if (t1 == NULL) {
        RD_ERROR("Unable to malloc stream data.");
        return NULL;
    }
    (void)memset_s(t1, sizeof(RdStreamTable2DataT), 0x0, sizeof(RdStreamTable2DataT));

    *t1 = (RdStreamTable2DataT) {
        .id = id,
        .name = {0},
        .age = id,
        .address = {0},
    };
    (void)snprintf_s(t1->name, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "test_%0ld", id + seed);
    (void)snprintf_s(t1->address, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "test_%0ld", id + seed);
    return t1;
}

RdStreamTable3DataT *RdStreamT3PrepareData(int64_t id, int64_t seed)
{
    RdStreamTable3DataT *t1 = (RdStreamTable3DataT *)malloc(sizeof(RdStreamTable3DataT));
    if (t1 == NULL) {
        RD_ERROR("Unable to malloc stream data.");
        return NULL;
    }
    (void)memset_s(t1, sizeof(RdStreamTable3DataT), 0x0, sizeof(RdStreamTable3DataT));

    *t1 = (RdStreamTable3DataT) {
        .id = id,
        .name = {0},
        .address = {0},
    };
    (void)snprintf_s(t1->name, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "test_%0ld", id + seed);
    (void)snprintf_s(t1->address, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "test_%0ld", id + seed);
    return t1;
}


// 结构化写入数据StreamTable1
void  RdStructWriteStreamTable(GmcStmtT *stmt, RdVertexLabelT *vertexLabel, int64_t rowNum, int64_t id, int64_t seed)
{
    uint32_t modelType = GMC_MODEL_STREAM;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamTable1DataT *t1 = NULL;
    for (uint32_t i = 0; i < rowNum; i++) {
        t1 = RdStreamT1PrepareData(id, seed);
        if (t1 == NULL) {
            RD_ERROR("Unable to prepare data, i = %d.", i);
            ret = RD_FAILED;
            break;
        }
        ret = RdStreamSetVertexWithBuf(stmt, vertexLabel, t1);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable to prepare data, i = %d.", i);
        ret = GmcExecute(stmt);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable execute, i = %d.", i);
        free(t1);
        t1 = NULL;
    }
    if (t1 != NULL) {
        free(t1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 结构化写入数据StreamTable2
void  RdStructWriteStreamTable2(GmcStmtT *stmt, RdVertexLabelT *vertexLabel, int64_t rowNum, int64_t id, int64_t seed)
{
    uint32_t modelType = GMC_MODEL_STREAM;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamTable2DataT *t1 = NULL;
    for (uint32_t i = 0; i < rowNum; i++) {
        t1 = RdStreamT2PrepareData(id, seed);
        if (t1 == NULL) {
            RD_ERROR("Unable to prepare data, i = %d.", i);
            ret = RD_FAILED;
            break;
        }
        ret = RdStreamSetVertexWithBuf(stmt, vertexLabel, t1);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable to prepare data, i = %d.", i);
        ret = GmcExecute(stmt);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable execute, i = %d.", i);
        free(t1);
        t1 = NULL;
    }
    if (t1 != NULL) {
        free(t1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 结构化写入数据StreamTable3
void  RdStructWriteStreamTable3(GmcStmtT *stmt, RdVertexLabelT *vertexLabel, int64_t rowNum, int64_t id, int64_t seed)
{
    uint32_t modelType = GMC_MODEL_STREAM;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamTable3DataT *t1 = NULL;
    for (uint32_t i = 0; i < rowNum; i++) {
        t1 = RdStreamT3PrepareData(id, seed);
        if (t1 == NULL) {
            RD_ERROR("Unable to prepare data, i = %d.", i);
            ret = RD_FAILED;
            break;
        }
        ret = RdStreamSetVertexWithBuf(stmt, vertexLabel, t1);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable to prepare data, i = %d.", i);
        ret = GmcExecute(stmt);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable execute, i = %d.", i);
        free(t1);
        t1 = NULL;
    }
    if (t1 != NULL) {
        free(t1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void RdCheckDataInTSTableOfStreamTable1(GmcStmtT *stmt, char *tsTableName, uint32_t expectRowsCount,
                                        uint32_t expectColsCount)
{
    
    // 查询ts1表中数据
    uint32_t modelType = GMC_MODEL_TS;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    ASSERT_EQ(GMERR_OK, ret);

    char selectCmd[1024];
    (void)sprintf(selectCmd, "SELECT * FROM %s;", tsTableName);
    uint32_t selectCmdLen = strlen(selectCmd);
    ret = GmcExecDirect(stmt, selectCmd, selectCmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(1u, val);

        // check name
        size = 50;
        char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "test_%0ld", 1 + 0);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
        
        i++;
    }
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, i);
}

void RdCheckDataInTSTableOfStreamTable2(GmcStmtT *stmt, char *tsTableName, uint32_t expectRowsCount,
                                        uint32_t expectColsCount)
{
    
    // 查询ts1表中数据
    uint32_t modelType = GMC_MODEL_TS;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    ASSERT_EQ(GMERR_OK, ret);

    char selectCmd[1024];
    (void)sprintf(selectCmd, "SELECT * FROM %s;", tsTableName);
    uint32_t selectCmdLen = strlen(selectCmd);
    ret = GmcExecDirect(stmt, selectCmd, selectCmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(1u, val);

        // check name
        size = 50;
        char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "test_%0ld", 1 + 0);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
        
        // check age
        size = 8;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(1u, val);
        
        // check address
        size = 50;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "test_%0ld", 1 + 0);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &address, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectAddress, address));
        i++;
    }
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, i);
}

void RdCheckDataInTSTableOfStreamTable3(GmcStmtT *stmt, char *tsTableName, uint32_t expectRowsCount,
                                        uint32_t expectColsCount)
{
    
    // 查询ts1表中数据
    uint32_t modelType = GMC_MODEL_TS;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    ASSERT_EQ(GMERR_OK, ret);

    char selectCmd[1024];
    (void)sprintf(selectCmd, "SELECT * FROM %s;", tsTableName);
    uint32_t selectCmdLen = strlen(selectCmd);
    ret = GmcExecDirect(stmt, selectCmd, selectCmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(1u, val);

        // check name
        size = 50;
        char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "test_%0ld", 1 + 0);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));

        // check address
        size = 50;
        char address[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectAddress[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectAddress, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "test_%0ld", 1 + 0);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &address, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectAddress, address));
        i++;
    }
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, i);
}

void RdCheckDataInTSTableOfTest11(GmcStmtT *stmt, char *tsTableName, uint32_t expectRowsCount, uint32_t expectColsCount)
{
    
    // 查询ts1表中数据
    uint32_t modelType = GMC_MODEL_TS;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    ASSERT_EQ(GMERR_OK, ret);

    char selectCmd[1024];
    (void)sprintf(selectCmd, "SELECT * FROM %s;", tsTableName);
    uint32_t selectCmdLen = strlen(selectCmd);
    ret = GmcExecDirect(stmt, selectCmd, selectCmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, rowsCount);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(1u, val);

        // check age
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(1u, val);

        i++;
    }
    AW_MACRO_ASSERT_EQ_INT(expectRowsCount, i);
}

// 多线程结构化写入数据
void RdStructWriteStreamTableThread(GmcStmtT *stmt, RdVertexLabelT *vertexLabel, int64_t rowNum, int64_t id,
                                    int64_t seed)
{
    uint32_t modelType = GMC_MODEL_STREAM;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, vertexLabel->topRecordName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    RdStreamTable1DataT *t1 = NULL;
    for (uint32_t i = 0; i < rowNum; i++) {
        t1 = RdStreamT1PrepareData(id, seed);
        if (t1 == NULL) {
            RD_ERROR("Unable to prepare data, i = %d.", i);
            ret = RD_FAILED;
            break;
        }

        ret = RdStreamSetVertexWithBuf(stmt, vertexLabel, t1);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable to prepare data, i = %d.", i);
        ret = GmcExecute(stmt);
        RD_LOG_AND_BREAK_IF_ERROR(ret, "Unable execute, i = %d.", i);
        free(t1);
        t1 = NULL;
    }
    if (t1 != NULL) {
        free(t1);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 多线程写入数据
void *RdInsertDataThread(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    testGmcConnect(&conn, &stmt);
    RdThreadWriteT *writeInfo =  (RdThreadWriteT *)args;
    
    RdStructWriteStreamTableThread(stmt, writeInfo->vertexLabel, writeInfo->rowNum, writeInfo->id, writeInfo->seed);

    // 关闭同步客户端连接
    testGmcDisconnect(conn, stmt);
    return nullptr;
}

// 检查多线程写入的数据
void RdCheckDataInTSTableOfConcurrency(GmcStmtT *stmt, char *tsTableName, uint32_t expectRowsCount,
                                       uint32_t expectColsCount)
{
    
    // 查询ts1表中数据
    uint32_t modelType = GMC_MODEL_TS;
    uint32_t ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType));
    ASSERT_EQ(GMERR_OK, ret);

    char selectCmd[1024];
    (void)sprintf(selectCmd, "SELECT * FROM %s;", tsTableName);
    uint32_t selectCmdLen = strlen(selectCmd);
    ret = GmcExecDirect(stmt, selectCmd, selectCmdLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 检查记录数
    uint32_t rowsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &rowsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_LE(expectRowsCount, rowsCount + 1000);

    // 检查字段数
    uint32_t colsCount = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &colsCount, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(expectColsCount, colsCount);

    uint32_t i = 0;
    bool eof = false;
    int64_t val = 0;
    uint32_t size = 0;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        AW_MACRO_ASSERT_EQ_INT((uint32_t)8, size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        
        // check id
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(1u, val);

        // check name
        size = 50;
        char name[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        char expectName[RD_STREAM_TABLE_T1_NAME_SIZE] = {0};
        (void)snprintf_s(expectName, RD_STREAM_TABLE_T1_NAME_SIZE, RD_STREAM_TABLE_T1_NAME_SIZE, "test_%0ld", 1 + 0);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &name, &size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(0, strcmp(expectName, name));
        
        i++;
    }
    ASSERT_LE(expectRowsCount, i + 1000);

    
}

#endif /* end of STREAM_TABLE_STRUCT_H */
