[{"version": "2.0", "type": "record", "name": "T0", "fields": [{"name": "F1", "type": "int8"}, {"name": "F2", "type": "uint8"}, {"name": "F3", "type": "int16"}, {"name": "F4", "type": "uint16"}, {"name": "F5", "type": "int32"}, {"name": "F6", "type": "uint32"}, {"name": "F7", "type": "int64"}, {"name": "F8", "type": "uint64"}, {"name": "F9", "type": "int"}, {"name": "F10", "type": "int64"}, {"name": "F11", "type": "float"}, {"name": "F12", "type": "double"}, {"name": "F13", "type": "boolean"}, {"name": "F14", "type": "int64"}, {"name": "F15", "type": "string", "size": 6}, {"name": "F16", "type": "string"}, {"name": "F17", "type": "bytes", "size": 6}, {"name": "F18", "type": "bytes"}, {"name": "F19", "type": "fixed", "size": 6}], "keys": [{"name": "pk", "node": "T0", "fields": ["F1"], "index": {"type": "primary"}, "constraints": {"unique": false}}]}]