{"object_json": [{"type": "record", "name": "simpleLabel", "fields": [{"name": "F0", "type": "int32"}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "int32", "nullable": true}], "keys": [{"name": "ip4_key", "index": {"type": "primary"}, "node": "simpleLabel", "fields": ["F0"], "constraints": {"unique": true}}, {"name": "ip4_hashcluster", "index": {"type": "hashcluster"}, "node": "simpleLabel", "fields": ["F1"]}, {"name": "ip4_key_local", "index": {"type": "local"}, "node": "simpleLabel", "fields": ["F2"]}]}, {"comment": "前缀表，对应7#表", "version": "2.0", "type": "record", "name": "general", "max_record_count": 2001, "fields": [{"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "dest_ip_addr", "type": "uint32", "comment": "目的地址"}, {"name": "mask_len", "type": "partition", "nullable": false, "comment": "标识Nhp或NhpG"}, {"name": "nhp_group_flag", "type": "uint8", "comment": "掩码长度"}, {"name": "qos_profile_id", "type": "uint16", "comment": "QosID"}, {"name": "primary_label", "type": "uint32", "comment": "标签"}, {"name": "attribute_id", "type": "uint32", "comment": "属性ID"}, {"name": "nhp_group_id", "type": "uint32", "comment": "下一跳索引还是下一跳组索引，根据nhp_group_flag决定"}, {"name": "path_flags", "type": "uint32", "comment": "path标记"}, {"name": "flags", "type": "uint32", "comment": "标志(path完备性)"}, {"name": "status_high_prio", "type": "uint8"}, {"name": "status_normal_prio", "type": "uint8"}, {"name": "errcode_high_prio", "type": "uint8"}, {"name": "errcode_normal_prio", "type": "uint8", "default": 1}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 34, "default": "ffffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的svcCtx"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 34, "default": "ffffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的svcCtx"}, {"name": "app_source_id", "type": "uint32"}, {"name": "table_smooth_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}, {"name": "trace", "type": "uint64"}, {"name": "route_flags", "type": "uint16", "comment": "路由标记"}, {"name": "reserved", "type": "uint16", "comment": "预留"}, {"name": "test_str", "type": "string", "size": 100}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "general", "fields": ["vr_id", "vrf_index", "dest_ip_addr"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "general", "fields": ["qos_profile_id", "nhp_group_id"], "constraints": {"unique": false}, "comment": "根据nhp_group_id + vrid索引"}, {"name": "localhash_key_2", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "general", "fields": ["primary_label", "attribute_id"], "constraints": {"unique": true}}, {"name": "localhash_key_3", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "general", "fields": ["qos_profile_id", "svc_ctx_normal_prio"], "constraints": {"unique": false}}, {"name": "localhash_key_new", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "general", "fields": ["qos_profile_id", "status_high_prio"], "constraints": {"unique": false}}, {"name": "hashcluster_key", "index": {"type": "hashcluster"}, "node": "general", "fields": ["qos_profile_id", "nhp_group_id"], "constraints": {"unique": false}}, {"name": "hashcluster_key_str", "index": {"type": "hashcluster"}, "node": "general", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "svc_ctx_high_prio"], "constraints": {"unique": false}}, {"name": "local_key", "index": {"type": "local"}, "node": "general", "fields": ["primary_label", "qos_profile_id"], "constraints": {"unique": false}}]}, {"type": "record", "name": "special", "special_complex": true, "max_record_count": 2001, "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "partition", "type": "partition", "nullable": false, "comment": "标识Nhp或NhpG"}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "uint8", "nullable": false}, {"name": "F7", "type": "time", "nullable": true}, {"name": "F8", "type": "fixed", "nullable": false, "size": 9, "default": "0xffffffffffffffffff"}, {"name": "F9", "type": "uint8: 5", "nullable": false, "default": "0x1f"}, {"name": "F10", "type": "uint16: 10", "nullable": false, "default": "0x3ff"}, {"name": "F11", "type": "uint32", "nullable": false}, {"name": "F12", "type": "uint32", "nullable": false}, {"name": "F13", "type": "uint8: 4", "nullable": true}, {"name": "F14", "type": "bytes", "nullable": true, "size": 256}, {"name": "T1V", "type": "record", "vector": true, "size": 64, "fields": [{"name": "V1", "type": "uint32"}, {"name": "V2", "type": "uint32"}, {"name": "V3", "type": "bitmap", "size": 16}, {"name": "V4", "type": "string", "size": 16}, {"name": "T2V", "type": "record", "vector": true, "size": 64, "fields": [{"name": "V1", "type": "uint32"}, {"name": "V2", "type": "uint32"}, {"name": "V3", "type": "bitmap", "size": 16}, {"name": "V4", "type": "string", "size": 16}]}]}], "keys": [{"node": "special", "name": "primary_key", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "special", "name": "hashcluster_unique_key", "index": {"type": "hashcluster"}, "fields": ["F1", "F2"], "constraints": {"unique": true}}, {"node": "special", "name": "localhash_key", "fields": ["F4", "F5"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "special", "name": "local_key", "fields": ["F3"], "index": {"type": "local"}, "constraints": {"unique": false}}, {"node": "special", "name": "lpm4_key", "fields": ["F3", "F11", "F12", "F6"], "index": {"type": "lpm4_tree_bitmap"}, "constraints": {"unique": true}}, {"name": "mem_key", "index": {"type": "none"}, "node": "T1V", "fields": ["V1"]}]}]}