{"int8": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": -10, "min_value": -99, "step": 11}}, "uint8": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": 100, "min_value": 11, "step": 11}}, "int16": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": -10, "min_value": -99, "step": 11}}, "uint16": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": 100, "min_value": 11, "step": 11}}, "int32": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": -10, "min_value": -99, "step": 11}}, "uint32": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": 100, "min_value": 11, "step": 11}}, "int64": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": -10, "min_value": -99, "step": 11}}, "uint64": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": 100, "min_value": 11, "step": 11}}, "float": {"generate_rule": "automatic", "type": "sequential", "properties": {"max_value": 10.2, "min_value": 1.1, "step": 1.1}}, "string": {"generate_rule": "automatic", "type": "random", "properties": {"lower_case": true, "upper_case": true, "number": true, "special_char": true, "custom_char": "!@#$%^&_", "max_value": 20, "min_value": 10}}, "time": {"generate_rule": "automatic", "type": "sequential", "format": "%Y-%m-%d %H:%M:%S", "properties": {"max_value": "2021-12-31 23:59:59", "min_value": "2021-01-01 00:00:00", "step": 10}}, "byte": {"generate_rule": "automatic", "type": "random", "properties": {"max_value": 20, "min_value": 10}}, "boolean": {"generate_rule": "automatic", "type": "fix_value", "properties": {"fix_value": true}}}