[{"type": "record", "name": "T10_002_003", "fields": [{"name": "F7", "type": "uint64", "nullable": false}, {"name": "F0", "type": "string", "nullable": false, "size": 1024}, {"name": "F1", "type": "string", "nullable": false, "size": 1024}, {"name": "F2", "type": "string", "nullable": false, "size": 1024}, {"name": "F3", "type": "string", "nullable": false, "size": 1024}, {"name": "F4", "type": "string", "nullable": false, "size": 1024}, {"name": "F5", "type": "string", "nullable": false, "size": 1024}, {"name": "F6", "type": "string", "nullable": false, "size": 1024}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "int64", "nullable": true}], "keys": [{"node": "T10_002_003", "name": "T20_PK", "fields": ["F7"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]