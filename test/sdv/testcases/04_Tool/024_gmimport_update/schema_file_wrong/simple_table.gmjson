{"type": "record", "name": "simple_table_test", "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "int32", "nullable": false}, {"name": "F4", "type": "int32", "nullable": false}], "keys": [{"node": "simple_table_test", "name": "Test_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}