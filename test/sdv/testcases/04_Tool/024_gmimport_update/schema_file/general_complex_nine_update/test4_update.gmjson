[{"type": "record", "name": "test4", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": true}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "int16", "nullable": true}, {"name": "F5", "type": "uint16", "nullable": true}, {"name": "F6", "type": "int8", "nullable": true}, {"name": "F7", "type": "uint8", "nullable": true}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "float", "nullable": true}, {"name": "F10", "type": "double", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F14", "type": "fixed", "size": 16, "nullable": true}, {"name": "F15", "type": "partition", "nullable": false}, {"name": "F16", "type": "string", "size": 100, "nullable": true}, {"name": "F17", "type": "bytes", "size": 100, "nullable": true}, {"name": "vr_id", "type": "uint32", "comment": "Vs索引", "nullable": false}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引", "nullable": false}, {"name": "dest_ip_addr_lpm4", "type": "uint32", "comment": "目的地址", "nullable": false}, {"name": "mask_len", "type": "uint8", "comment": "掩码长度", "nullable": false}, {"name": "T1", "type": "record", "fields": [{"name": "P0", "type": "int64", "nullable": true}, {"name": "P1", "type": "uint64", "nullable": true}, {"name": "P2", "type": "int32", "nullable": true}, {"name": "P3", "type": "uint32", "nullable": true}, {"name": "P4", "type": "int16", "nullable": true}, {"name": "P5", "type": "uint16", "nullable": true}, {"name": "P6", "type": "int8", "nullable": true}, {"name": "P7", "type": "uint8", "nullable": true}, {"name": "P8", "type": "boolean", "nullable": true}, {"name": "P9", "type": "float", "nullable": true}, {"name": "P10", "type": "double", "nullable": true}, {"name": "P11", "type": "time", "nullable": true}, {"name": "P12", "type": "char", "nullable": true}, {"name": "P13", "type": "uchar", "nullable": true}, {"name": "P14", "type": "fixed", "size": 16, "nullable": true}, {"name": "P15", "type": "string", "size": 100, "nullable": true}, {"name": "P16", "type": "bytes", "size": 100, "nullable": true}]}, {"name": "T2", "type": "record", "array": true, "size": 1024, "fields": [{"name": "A0", "type": "int64", "nullable": true}, {"name": "A1", "type": "uint64", "nullable": true}, {"name": "A2", "type": "int32", "nullable": true}, {"name": "A3", "type": "uint32", "nullable": true}, {"name": "A4", "type": "int16", "nullable": true}, {"name": "A5", "type": "uint16", "nullable": true}, {"name": "A6", "type": "int8", "nullable": true}, {"name": "A7", "type": "uint8", "nullable": true}, {"name": "A8", "type": "boolean", "nullable": true}, {"name": "A9", "type": "float", "nullable": true}, {"name": "A10", "type": "double", "nullable": true}, {"name": "A11", "type": "time", "nullable": true}, {"name": "A12", "type": "char", "nullable": true}, {"name": "A13", "type": "uchar", "nullable": true}, {"name": "A14", "type": "fixed", "size": 16, "nullable": true}, {"name": "A15", "type": "string", "size": 13312, "nullable": true}, {"name": "A16", "type": "string", "size": 13312, "nullable": true}, {"name": "A17", "type": "string", "size": 13312, "nullable": true}, {"name": "A18", "type": "string", "size": 13312, "nullable": true}, {"name": "A19", "type": "string", "size": 13312, "nullable": true}, {"name": "A20", "type": "string", "size": 13312, "nullable": true}, {"name": "A21", "type": "string", "size": 13312, "nullable": true}, {"name": "A22", "type": "string", "size": 13312, "nullable": true}, {"name": "A23", "type": "string", "size": 13312, "nullable": true}, {"name": "A24", "type": "string", "size": 13312, "nullable": true}, {"name": "A25", "type": "string", "size": 13312, "nullable": true}, {"name": "A26", "type": "string", "size": 13312, "nullable": true}, {"name": "A27", "type": "string", "size": 13312, "nullable": true}, {"name": "A28", "type": "string", "size": 13312, "nullable": true}, {"name": "A29", "type": "string", "size": 13312, "nullable": true}, {"name": "A30", "type": "string", "size": 13312, "nullable": true}, {"name": "A31", "type": "string", "size": 13312, "nullable": true}, {"name": "A32", "type": "string", "size": 100, "nullable": true}, {"name": "A33", "type": "bytes", "size": 100, "nullable": true}]}, {"name": "T3", "type": "record", "vector": true, "size": 1024, "fields": [{"name": "V0", "type": "int64", "nullable": true}, {"name": "V1", "type": "uint64", "nullable": true}, {"name": "V2", "type": "int32", "nullable": true}, {"name": "V3", "type": "uint32", "nullable": true}, {"name": "V4", "type": "int16", "nullable": true}, {"name": "V5", "type": "uint16", "nullable": true}, {"name": "V6", "type": "int8", "nullable": true}, {"name": "V7", "type": "uint8", "nullable": true}, {"name": "V8", "type": "boolean", "nullable": true}, {"name": "V9", "type": "float", "nullable": true}, {"name": "V10", "type": "double", "nullable": true}, {"name": "V11", "type": "time", "nullable": true}, {"name": "V12", "type": "char", "nullable": true}, {"name": "V13", "type": "uchar", "nullable": true}, {"name": "V14", "type": "fixed", "size": 16, "nullable": true}, {"name": "V15", "type": "string", "size": 100, "nullable": true}, {"name": "V16", "type": "bytes", "size": 100, "nullable": true}]}], "keys": [{"node": "test4", "name": "TEST_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "test4", "name": "uniq_localhash", "fields": ["F3"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}, {"node": "test4", "name": "nonuniq_localhash", "fields": ["F2"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "test4", "name": "nonuniq_hashcluster", "fields": ["F2"], "index": {"type": "hashcluster"}, "constraints": {"unique": false}}, {"node": "test4", "name": "local", "fields": ["F2"], "index": {"type": "local"}, "constraints": {"unique": false}}, {"node": "test4", "name": "lpm4", "index": {"type": "lpm4_tree_bitmap"}, "fields": ["vr_id", "vrf_index", "dest_ip_addr_lpm4", "mask_len"], "constraints": {"unique": true}}]}]