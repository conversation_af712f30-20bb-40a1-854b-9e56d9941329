{"type": "record", "name": "test1", "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "int32", "nullable": false}], "super_fields": [{"name": "superfield1", "comment": "test", "fields": ["F0", "F1"]}, {"name": "superfield2", "comment": "test", "fields": ["F2", "F3"]}], "keys": [{"node": "Test", "name": "Test_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}