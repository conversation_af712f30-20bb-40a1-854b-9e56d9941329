#include "PublicFunc.h"

int main()
{
    int ret;
    uint32_t ulDbId;
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_Init());

    // 创建db
    TestDBCreateDB("DB2", &ulDbId);

    // 创建表
    DB_REL_DEF_STRU stRelDef = {0};
    uint16_t usRelId = 0;
    ret = TestDB_CreateTbl(ulDbId, "schema_file/label2.json", &usRelId, &stRelDef);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);

    sleep(10);

    // 删除DB
    TestFreeRelDef(&stRelDef);
    ret = DB_CloseDB(ulDbId);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    ret = DB_DropDB((VOS_UINT8 *)"DB2", 0);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, ret);
    V1_AW_MACRO_EXPECT_EQ_INT(DB_SUCCESS_V1, TestDB_UnInit());
}
