/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: 通用工具类
 * Author: guopanpan
 * Create: 2021-04-26
 * History:
 */
#ifndef _BIG_OBJECT_UTIL_H
#define _BIG_OBJECT_UTIL_H 1

#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <stdarg.h>
#include <regex.h>
#include <signal.h>
#include <pthread.h>
#include <sys/sem.h>
#include <sys/wait.h>
#include <sys/time.h>

#include "gtest/gtest.h"
#include "t_datacom_lite.h"

/******************************************************* 宏定义 *******************************************************/
#define MAX_NAME_LENGTH 128

#define ALLOW_PRINT_SCAN_RESULT 0

#if !ENABLE_INFO
#define TEST_INFO(log, args...)                                                \
    do {                                                                       \
        fprintf(stdout, "Info: %s:%d: " log "\n", __FILE__, __LINE__, ##args); \
    } while (0)
#else
#define TEST_INFO(log, args...)
#endif

#define TEST_ERROR(log, args...)                                               \
    do {                                                                       \
        fprintf(stdout, "Error: %s:%d " log "\n", __FILE__, __LINE__, ##args); \
    } while (0)

// 仅用于调试
#define LOG_IFERR(ret, log, args...)                                                                     \
    do {                                                                                                 \
        if ((ret) != GMERR_OK) {                                                                         \
            fprintf(stdout, "Error: %s:%d " log ", " #ret " = %d\n", __FILE__, __LINE__, ##args, (ret)); \
        }                                                                                                \
    } while (0)

// 仅用于调试
#define LOG_LAST_ERROR_IFERR(ret, conn)                                    \
    do {                                                                   \
        if ((ret) != GMERR_OK) {                                           \
            const char *_lastError_ = "";                                  \
            _lastError_ = GmcGetLastError();                           \
            TEST_ERROR("ret = %d, last error = \"%s\"", ret, _lastError_); \
        }                                                                  \
    } while (0)

const char *gConfigJsonPath = "./schema/ConfigForVertexLabel.json";

/****************************************************** 工具函数 ******************************************************/
// 暂停进程, 用于调试用例
void GtPause(const char *notice = "")
{
    printf("Info: [%s] pause, enter anything to continue...\n", notice);
    getchar();
}

// 执行系统调用并获取执行结果
// [out] result: 执行系统调用的结果, 使用结束后必须调用free()释放内存
int GtExecSystemCmd(char **result, const char *format, ...)
{
    int ret = 0;
    va_list args;
    va_start(args, format);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), format, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d.", ret);
        va_end(args);
        return FAILED;
    }
    va_end(args);

    TEST_INFO("cmd = \"%s\"", cmd);
    FILE *fd = popen(cmd, "r");
    if (fd == NULL) {
        TEST_ERROR("popen failed, errno = %d.", errno);
        return FAILED;
    }

    int size = 1024 * 100;
    char *tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        TEST_ERROR("malloc failed, errno = %d.", errno);
        return FAILED;
    }
    memset(tmpResult, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat(tmpResult, buf);
    }

    ret = pclose(fd);
    if (ret == -1) {
        TEST_ERROR("pclose failed, errno = %d.", errno);
        free(tmpResult);
        return FAILED;
    }
    *result = tmpResult;
    return GMERR_OK;
}

// 拼接字符串
int GtStrcat(char *dest, size_t dest_max, const char *src, ...)
{
    int ret;
    errno = 0;
    char *tmpSrc = (char *)malloc(dest_max);

    va_list args;
    va_start(args, src);
    ret = vsnprintf(tmpSrc, dest_max, src, args);
    if (ret <= 0) {
        TEST_ERROR("call vsnprintf failed, ret = %d, errno = %d, %s\n", ret, errno, strerror(errno));
        va_end(args);
        free(tmpSrc);
        return ret;
    }
    va_end(args);

    strncat(dest, tmpSrc, dest_max);
    if (errno != GMERR_OK) {
        TEST_ERROR("call strncat failed, errno = %d, %s", errno, strerror(errno));
        free(tmpSrc);
        return errno;
    }
    free(tmpSrc);
    return GMERR_OK;
}

typedef enum tagGtTimeUnit {
    GT_TIME_UNIT_SEC = 0,   // 秒
    GT_TIME_UNIT_MSEC = 1,  // 毫秒
    GT_TIME_UNIT_USEC = 2   // 微秒
} GtTimeUnitE;

// 获取当前时间戳
double GtGetCurrentTime(GtTimeUnitE unit = GT_TIME_UNIT_MSEC)
{
    struct timeval now;
    gettimeofday(&now, NULL);
    uint64_t usec = (uint64_t)(now.tv_sec * 1000 * 1000) + (uint64_t)now.tv_usec;

    switch (unit) {
        case GT_TIME_UNIT_SEC:
            return (double)usec / 1000 / 1000;
        case GT_TIME_UNIT_MSEC:
            return (double)usec / 1000;
        case GT_TIME_UNIT_USEC:
            return (double)usec;
        default:
            TEST_ERROR("invalid unit = %d", unit);
            abort();
    }

    return -1;
}

// 获取当前的格式化时间
char *GtGetCurrentFormatTime(const char *format = "%Y-%m-%d %H:%M:%S")
{
    time_t t = time(NULL);
    static char tmpBuf[128];
    strftime(tmpBuf, sizeof(tmpBuf), format, localtime(&t));
    return tmpBuf;
}

// 检查字符串
int GtCheckString(const char *expect, const char *actual, bool isFullMatch = false)
{
    if (isFullMatch) {
        if (strcmp(expect, actual) != 0) {
            printf(
                "Error: failed to check string for full match:\n  expect: \"%s\"\n  actual: \"%s\"\n", expect, actual);
            return FAILED;
        }
    } else {
        if (strstr(actual, expect) == NULL) {
            printf("Error: failed to check string for partial match:\n  expect: \"%s\"\n  actual: \"%s\"\n", expect,
                actual);
            return FAILED;
        }
    }
    return GMERR_OK;
}

/***************************************************** DB通用函数 *****************************************************/
// sysconfig.ini -> 0:EULER, 1:docker+DOPRA(lite), 2:docker(yang)
typedef enum tagRunMode { GT_RUN_MODE_EULER = 0, GT_RUN_MODE_DOPRA = 1, GT_RUN_MODE_YANG = 2 } GtRunModeE;

// 检查DML操作影响的行数
int GtCheckAffectRows(GmcStmtT *stmt, int32_t expect)
{
    int32_t affect;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affect, sizeof(affect));
    RETURN_IFERR(ret);
    if (affect != expect) {
        TEST_ERROR("checke effect rows failed, expect is %d, actual is %d", expect, affect);
        return FAILED;
    }
    return GMERR_OK;
}

// 执行gmsysview工具并获取查询结果
// [out] result: 执行系统调用的结果, 使用结束后必须调用free()释放内存
int GtExecSysviewCmd(char **result, const char *viewName, const char *filter = "", ...)
{
    int ret;
    va_list args;
    va_start(args, filter);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), filter, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d.", ret);
        va_end(args);
        return FAILED;
    }
    va_end(args);

    char *buf = NULL;
    ret = GtExecSystemCmd(&buf, "%s/gmsysview -s %s -q '%s' %s", g_toolPath, g_connServer, viewName, cmd);
    if (ret != GMERR_OK) {
        TEST_ERROR("exec system cmd failed, ret = %d.", ret);
        free(buf);
        buf = NULL;
        return ret;
    }

    buf[strlen(buf) - 1] = '\0';
    TEST_INFO("sysview result = \"%s\"", buf);
    *result = buf;
    return GMERR_OK;
}

// 执行gmsysview工具
int GtExecSysviewCmd(const char *viewName, const char *filter = "", ...)
{
    int ret;
    va_list args;
    va_start(args, filter);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), filter, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d.", ret);
        va_end(args);
        return FAILED;
    }
    va_end(args);

    return GtExecSystemCmd(
        "%s/gmsysview -s %s -q '%s' %s", g_toolPath, g_connServer, viewName, cmd);
}

// 执行导入工具
int GtExecImportCmd(const char *cmd, ...)
{
    va_list args;
    va_start(args, cmd);
    char tmpCmd[1024] = {0};
    int ret = vsnprintf(tmpCmd, sizeof(tmpCmd), cmd, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d.", ret);
        va_end(args);
        return FAILED;
    }
    va_end(args);

    // cmd 示例： -c respool -f schema_file/ResourcePool.gmrespool
    return GtExecSystemCmd("%s/gmimport -s %s %s", g_toolPath, g_connServer, tmpCmd);
}

// 执行导入工具并获取执行结果
int GtExecImportCmd(char **result, const char *cmd, ...)
{
    va_list args;
    va_start(args, cmd);
    char tmpCmd[1024] = {0};
    int ret = vsnprintf(tmpCmd, sizeof(tmpCmd), cmd, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d.", ret);
        va_end(args);
        return FAILED;
    }
    va_end(args);

    // cmd 示例： -c respool -f schema_file/ResourcePool.gmrespool
    return GtExecSystemCmd(
        result, "%s/gmimport -s %s %s", g_toolPath, g_connServer, tmpCmd);
}

// 执行导出工具
int GtExecExportCmd(const char *cmd, ...)
{
    va_list args;
    va_start(args, cmd);
    char tmpCmd[1024] = {0};
    int ret = vsnprintf(tmpCmd, sizeof(tmpCmd), cmd, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d.", ret);
        va_end(args);
        return FAILED;
    }
    va_end(args);

    // cmd 示例： -c vschema -t VertexLabel
    return GtExecSystemCmd("%s/gmexport -s %s %s", g_toolPath, g_connServer, tmpCmd);
}

// 创建vertex label
int GtCreateVertexLabel(GmcStmtT *stmt, const char *labelJsonPath, const char *configJsonPath)
{
    char *labelJson = NULL;
    long len = readJanssonFile(labelJsonPath, &labelJson);
    if (labelJson == NULL) {
        TEST_ERROR("read label json failed, labelJsonPath = %s, labelJson = %p", labelJsonPath, labelJson);
        return FAILED;
    }
    if (len <= 0) {
        TEST_ERROR("read label json failed, labelJsonPath = %s, len = %d", labelJsonPath, len);
        free(labelJson);
        return FAILED;
    }

    char *configJson = NULL;
    len = readJanssonFile(configJsonPath, &configJson);
    if (configJson == NULL) {
        TEST_ERROR("read config json failed, configJsonPath = %s, labelJson = %p", labelJsonPath, configJson);
        return FAILED;
    }
    if (len <= 0) {
        TEST_ERROR("read config json failed, configJsonPath = %s, len = %d", labelJsonPath, len);
        free(labelJson);
        return FAILED;
    }

    int ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    free(labelJson);
    free(configJson);
    return ret;
}

// 创建订阅关系
int GtSubscribe(GmcStmtT *stmt, GmcConnT *channel, const char *subName, const char *subInfoJsonPath,
    GmcSubCallbackT userCb, void *userData)
{
    char *subInfoJson = NULL;
    long len = readJanssonFile(subInfoJsonPath, &subInfoJson);
    if (subInfoJson == NULL) {
        TEST_ERROR("read label json failed, subInfoJsonPath = %s, subInfoJson = %p", subInfoJsonPath, subInfoJson);
        return FAILED;
    }
    if (len <= 0) {
        TEST_ERROR("read label json failed, subInfoJsonPath = %s, len = %d", subInfoJsonPath, len);
        free(subInfoJson);
        return FAILED;
    }

    GmcSubConfigT subConfig;
    subConfig.configJson = subInfoJson;
    subConfig.subsName = subName;
    return GmcSubscribe(stmt, &subConfig, channel, userCb, userData);
}

int GtCreateResPool(GmcStmtT *stmt, const char *resPoolJsonPath)
{
    char *resPoolJson = NULL;
    long len = readJanssonFile(resPoolJsonPath, &resPoolJson);
    if (resPoolJson == NULL) {
        TEST_ERROR("read label json failed, resPoolJsonPath = %s, resPoolJson = %p", resPoolJsonPath, resPoolJson);
        return FAILED;
    }
    if (len <= 0) {
        TEST_ERROR("read label json failed, resPoolJsonPath = %s, len = %d", resPoolJsonPath, len);
        free(resPoolJson);
        return FAILED;
    }

    return GmcCreateResPool(stmt, resPoolJson);
}

// 填充uint8数组
void GtFillBytes(uint8_t target[], uint32_t len, char ch, bool isEof)
{
    errno = 0;
    if (len < 2) {
        TEST_ERROR("len must greater than %d, actual is %d", 8, len);
    }

    (void)memset(target, ch, len);
    if (errno != GMERR_OK) {
        TEST_ERROR("memset failed, errno = %d, errmsg = %s", errno, strerror(errno));
    }

    if (isEof) {
        target[len - 1] = 0;
    }
}

// 写入字节数组数据至kv表
int GtKvSetBytesArray(GmcStmtT *stmt, const char *kvTableName, int64_t key, char byte, uint32_t valLen)
{
    errno = 0;
    int ret = GmcKvPrepareStmtByLabelName(stmt, kvTableName);
    RETURN_IFERR(ret);

    uint8_t *val = (uint8_t *)malloc(valLen * sizeof(uint8_t));
    if (val == NULL) {
        TEST_ERROR("malloc val failed, errno = %d, errmsg = %s", errno, strerror(errno));
        return FAILED;
    }
    GtFillBytes(val, valLen, byte, true);

    GmcKvTupleT kvTuple;
    kvTuple.key = &key;
    kvTuple.keyLen = sizeof(key);
    kvTuple.value = val;
    kvTuple.valueLen = valLen;
    // 释放val后直接返回ret即可, 通过上层判断正确性
    ret = GmcKvSet(stmt, &key, sizeof(key), val, valLen);
    free(val);
    return ret;
}

// 读取kv表中字节数组数据
int GtKvGetBytesArray(GmcStmtT *stmt, const char *kvTableName, int64_t key, char expectByte, uint32_t valLen)
{
    errno = 0;
    int ret = GmcKvPrepareStmtByLabelName(stmt, kvTableName);
    RETURN_IFERR(ret);

    uint8_t *val = (uint8_t *)malloc(valLen * sizeof(uint8_t));
    if (val == NULL) {
        TEST_ERROR("malloc val failed, errno = %d, errmsg = %s", errno, strerror(errno));
        return FAILED;
    }
    GtFillBytes(val, valLen, expectByte, true);

    char *outputVal = (char *)malloc(valLen * sizeof(char));
    if (outputVal == NULL) {
        TEST_ERROR("malloc outputVal failed, errno = %d, errmsg = %s", errno, strerror(errno));
        free(val);
        return FAILED;
    }

    uint32_t outputValLen;
    do {
        ret = GmcKvGet(stmt, &key, sizeof(key), outputVal, &outputValLen);
        // 数据不存在的情况不打印报错信息, 直接返回错误码, 通过上层判断正确性
        if (ret == GMERR_DATA_EXCEPTION) {
            return ret;
        }
        BREAK_IFERR(ret);
        if (valLen != outputValLen) {
            TEST_ERROR("expect valLen == outputValLen, actual valLen = %d, outputValLen = %d", valLen, outputValLen);
            ret = FAILED;
            break;
        }
        ret = memcmp(val, outputVal, valLen);
        BREAK_IFERR(ret);
#if ALLOW_PRINT_SCAN_RESULT
        printf("kv get: %ld = %s\n", key, outputVal);
#endif
    } while (0);

    free(val);
    free(outputVal);
    return ret;
}

char *GtByteToStr(uint8_t *src, uint32_t size)
{
    static char byteToStr[10240] = {0};
    memcpy(byteToStr, src, size);
    byteToStr[size] = 0;
    return byteToStr;
}

// 等待老化任务完成
void GtWaitAgeFinish(int32_t maxSeconds = 30)
{
    for (int32_t i = 0; i < maxSeconds; i++) {
        char *result = NULL;
        int ret = GtExecSysviewCmd(&result, "V$QRY_AGE_TASK");
        if (ret != GMERR_OK) {
            free(result);
            TEST_ERROR("get age task status failed");
        }
        if (strstr(result, "AGING") == NULL) {
            free(result);
            break;
        }
        free(result);
        sleep(1);
    }
}

// 多线程并发场景获取错误码
int GtThreadGetExecuteStatus(int ret, GmcOperationTypeE operType = GMC_OPERATION_BUTT)
{
    int32_t ignoreStatus[] = {GMERR_DATA_EXCEPTION, GMERR_PRIMARY_KEY_VIOLATION, GMERR_INTERNAL_ERROR,
        GMERR_OUT_OF_MEMORY};  // GMERR_PROGRAM_LIMIT_EXCEEDED

    for (int32_t i = 0; i < sizeof(ignoreStatus) / sizeof(ignoreStatus[0]); i++) {
        if (ret == ignoreStatus[i]) {
            return GMERR_OK;
        }
    }

    return ret;
}

#endif /* _BIG_OBJECT_UTIL_H */
