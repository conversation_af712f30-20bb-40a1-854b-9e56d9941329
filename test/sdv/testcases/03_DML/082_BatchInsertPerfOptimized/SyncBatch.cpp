extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "batch_insert_common.h"

using namespace std;
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

class SyncVertexBatchInsert : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh ");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        g_conn_sync = NULL;
        g_stmt_sync = NULL;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SyncVertexBatchInsert::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}
void SyncVertexBatchInsert::TearDown()
{
    AW_CHECK_LOG_END();
}

//设置开启优化，批量接口单表插入执行1025条记录批量插入完成后，调用解析返回接口验证；
TEST_F(SyncVertexBatchInsert, DML_082_02_001)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_BATCH_BUFFER_FULL);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt_sync, batch_label_name);
    ret = GmcBatchAddDDL(
        batch, GMC_OPERATION_CREATE_VERTEX_LABEL, batch_label_name, string_schemal_json, batch_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, batch_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t insertRecord = 1025;
    for (uint32_t i = 0; i < insertRecord; i++) {
        int32_t val = i + 65535;
        ret = GmcSetVertexProperty(g_stmt_sync, "F0", GMC_DATATYPE_INT32, &val, sizeof(val));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F1", GMC_DATATYPE_INT32, &val, sizeof(val));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F2", GMC_DATATYPE_INT32, &val, sizeof(val));
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcSetVertexProperty(g_stmt_sync, "F3", GMC_DATATYPE_INT32, &val, sizeof(val));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        if (i >= 1024) {
            EXPECT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        } else
            EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1U, totalNum);
    EXPECT_EQ(1U, successNum);

    ret = GmcDropVertexLabel(g_stmt_sync, batch_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

void *batch_insert_scan_pthread1(void *args);
void *batch_insert_scan_pthread3(void *args);
//同步连接下开启优化并开启事务，事务操作合并执行优化，对不同的普通表的执行批量插入
TEST_F(SyncVertexBatchInsert, DML_082_02_012)  //由于有问题单失败用例，为上架连跑调整用例位置
{
    char *test_schema1 = NULL;
    char *test_schema2 = NULL;
    const char *strBatchLabelName1 = "KEY_S0";
    const char *strBatchLabelVertexPK1 = "KEY_PK";
    const char *strBatchLabelName2 = "TBL_K0";
    const char *strBatchLabelVertexPK2 = "TBL_PK";
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    GmcDropVertexLabel(g_stmt_sync, strBatchLabelName1);
    readJanssonFile("schema_file/batch_insert_dml_general.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema1, Label_config);
    ASSERT_EQ(true, (ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE));
    free(test_schema1);
    GmcDropVertexLabel(g_stmt_sync, strBatchLabelName2);
    readJanssonFile("schema_file/batch_insert_dst_general.gmjson", &test_schema2);
    ASSERT_NE((void *)NULL, test_schema2);
    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema2, Label_config);
    ASSERT_EQ(true, (ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE));
    free(test_schema2);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    int start_num = 0, end_num = 512;
    for (int i = start_num; i < end_num; i++) {

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        TestSetVertexPKPropertyValue(g_stmt_sync, i);
        TestSetVertexPropertyValue(g_stmt_sync, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName2, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        TestSetVertexPKPropertyValue(g_stmt_sync, i);
        TestSetVertexPropertyValue(g_stmt_sync, i, 0, (char *)"string");

        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }

    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(2 * end_num, totalNum);
    ASSERT_EQ(2 * end_num, successNum);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, strBatchLabelVertexPK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        TestQueryVertexProperty(g_stmt_sync, i, 0, (char *)"string");
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, strBatchLabelVertexPK2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        TestQueryVertexProperty(g_stmt_sync, i, 0, (char *)"string");
    }

    ret = GmcDropVertexLabel(g_stmt_sync, strBatchLabelName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, strBatchLabelName2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

//同步连接下开启优化建多张表后，默认回收大小和内存上限，批量选项设置GMC_BATCH_BREAK，多线程按照顺序合并操作(GMC_BATCH_ORDER_SEMI)
TEST_F(SyncVertexBatchInsert, DML_082_02_013)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    void *ptread_ret[24] = {0};
    char *test_schema1 = NULL;
    char *test_schema2 = NULL;
    const char *strBatchLabelName1 = "KEY_S0";
    const char *strBatchLabelVertexPK1 = "KEY_PK";
    const char *strBatchLabelName2 = "TBL_K0";
    const char *strBatchLabelVertexPK2 = "TBL_PK";
    GmcDropVertexLabel(g_stmt_sync, strBatchLabelName1);
    readJanssonFile("schema_file/batch_insert_dml_general.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema1, g_label_config);
    ASSERT_EQ(true, (ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE));
    free(test_schema1);
    GmcDropVertexLabel(g_stmt_sync, strBatchLabelName2);
    readJanssonFile("schema_file/batch_insert_dst_general.gmjson", &test_schema2);
    ASSERT_NE((void *)NULL, test_schema2);
    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema2, g_label_config);
    ASSERT_EQ(true, (ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE));
    free(test_schema2);
    pthread_t pid1, client_thr_03;
    for (int i = 0; i < 1; i++) {
        ret = pthread_create(&pid1, NULL, batch_insert_scan_pthread1, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        ret = pthread_create(&client_thr_03, NULL, batch_insert_scan_pthread3, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        pthread_join(pid1, &ptread_ret[1]);
        pthread_join(client_thr_03, &ptread_ret[3]);
    }

    ret = GmcDropVertexLabel(g_stmt_sync, strBatchLabelName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, strBatchLabelName2);
    ASSERT_EQ(GMERR_OK, ret);
}

//设置开启优化,调用BatchAddDML接口对同一张表批量插入数据
TEST_F(SyncVertexBatchInsert, DML_082_02_002)
{
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;
    int i, start_num = 0, end_num = 1024;

    readJanssonFile("./schema_file/batch_insert_data_general.gmjson", &gBatchLabelSchema);
    ASSERT_NE((void *)NULL, gBatchLabelSchema);
    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    ret = GmcCreateVertexLabel(g_stmt_sync, gBatchLabelSchema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(gBatchLabelSchema);
    gBatchLabelSchema = NULL;

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        TestSetVertexPropertyGeneral(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(1, successNum);

    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

//设置开启优化,设置内存上限和回收大小后调用BatchAddDML接口执行插入、删除等对单表执行混合DML操作
TEST_F(SyncVertexBatchInsert, DML_082_02_003)
{
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;
    int i, start_num = 0, end_num = 1024;

    readJanssonFile("./schema_file/batch_insert_data_general.gmjson", &gBatchLabelSchema);
    ASSERT_NE((void *)NULL, gBatchLabelSchema);
    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    ret = GmcCreateVertexLabel(g_stmt_sync, gBatchLabelSchema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(gBatchLabelSchema);
    gBatchLabelSchema = NULL;

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        TestSetVertexPropertyGeneral(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(1, successNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        TestSetVertexPropertyUpdateGeneral(g_stmt_sync, i + end_num);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_primary_key);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_primary_key);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
}

//开启事务，事务操作合并执行优化，设置内存上限和回收大小对同一张普通表的操作批量插入合并执行
TEST_F(SyncVertexBatchInsert, DML_082_02_004)
{
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;
    int i, start_num = 0, end_num = 1024;
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    readJanssonFile("./schema_file/batch_insert_data_general.gmjson", &gBatchLabelSchema);
    ASSERT_NE((void *)NULL, gBatchLabelSchema);
    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    ret = GmcCreateVertexLabel(g_stmt_sync, gBatchLabelSchema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(gBatchLabelSchema);
    gBatchLabelSchema = NULL;

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;

    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        TestSetVertexPropertyGeneral(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(1, successNum);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

void *batch_insert_scan_pthread1(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *strBatchLabelName1 = "KEY_S0";
    const char *strBatchLabelVertexPK1 = "KEY_PK";
    ret = testGmcPrepareStmtByLabelName(stmt, strBatchLabelName1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption = {};
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    int start_num = 0, end_num = 100;
    for (int i = start_num; i < end_num; i++) {
        TestSetVertexPKPropertyValue(stmt, i);
        TestSetVertexPropertyValue(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(1, successNum);

    ret = testGmcPrepareStmtByLabelName(stmt, strBatchLabelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, strBatchLabelVertexPK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        EXPECT_EQ(GMERR_OK, ret);
        TestQueryVertexProperty(stmt, i, 0, (char *)"string");
    }
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *batch_insert_scan_pthread2(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *BatchLabelName1 = "KEY_S0";
    const char *BatchLabelVertexPK1 = "KEY_PK";
    GmcBatchT *pBatch = NULL;
    GmcBatchRetT pBatchRet = {};
    GmcBatchOptionT pBatchOption = {};
    ret = GmcBatchOptionInit(&pBatchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&pBatchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&pBatchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&pBatchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &pBatchOption, &pBatch);
    EXPECT_EQ(GMERR_OK, ret);
    int start_num = 0, end_num = 100;
    ret = testGmcPrepareStmtByLabelName(stmt, BatchLabelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = end_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, BatchLabelVertexPK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &f0_value);
        EXPECT_EQ(GMERR_OK, ret);
        TestQueryVertexProperty(stmt, i, 0, (char *)"string");
    }
    ret = GmcBatchDestroy(pBatch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

//同步连接下建单张表后，默认回收大小和内存上限，批量选项设置GMC_BATCH_BREAK，多线程执行合并操作
TEST_F(SyncVertexBatchInsert, DML_082_02_005)
{
    char *test_schema1 = NULL;
    const char *batchLabelName1 = "KEY_S0";
    GmcDropVertexLabel(g_stmt_sync, batchLabelName1);
    readJanssonFile("schema_file/batch_insert_dml_general.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema1, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema1);

    pthread_t pid1, pid2;
    void *ptread_ret[24] = {0};

    for (int i = 0; i < 1; i++) {
        ret = pthread_create(&pid1, NULL, batch_insert_scan_pthread1, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        usleep(1000);
        ret = pthread_create(&pid2, NULL, batch_insert_scan_pthread2, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        pthread_join(pid1, &ptread_ret[1]);
        pthread_join(pid2, &ptread_ret[2]);
    }

    ret = GmcDropVertexLabel(g_stmt_sync, batchLabelName1);
    ASSERT_EQ(GMERR_OK, ret);
}

//同步连接下建单张表后，默认回收大小和内存上限，批量选项设置GMC_BATCH_BREAK，按照顺序合并操作(GMC_BATCH_ORDER_SEMI)
TEST_F(SyncVertexBatchInsert, DML_082_02_006)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;
    int i, start_num = 0, end_num = 1024;
    char Label_config[] = "{\"max_record_num\":1000000, \"isFastReadUncommitted\":0}";

    readJanssonFile("./schema_file/batch_insert_data_general.gmjson", &gBatchLabelSchema);
    ASSERT_NE((void *)NULL, gBatchLabelSchema);
    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    ret = GmcCreateVertexLabel(g_stmt_sync, gBatchLabelSchema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(gBatchLabelSchema);
    gBatchLabelSchema = NULL;

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;

    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef ONOFF_BATCH_ERR_CTRL_DEFINE
    uint32_t batchErrCtl = GMC_BATCH_ERR_BREAK;
    ret = GmcBatchOptionSetErrCtrl(&batchOption, batchErrCtl);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        TestSetVertexPropertyGeneral(g_stmt_sync, i);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(true, (ret == GMERR_OK || ret == GMERR_PROGRAM_LIMIT_EXCEEDED));
    }
    if (ret != GMERR_OK) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);  //只取决于是否开启优化，开启优化批量一次就为1
    EXPECT_EQ(1, successNum);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

/**************************************************************/
//同步连接下开启优化，设置批量缓存最大进行100条批量插入并对插入记录进行读取校验
TEST_F(SyncVertexBatchInsert, DML_082_02_007)
{
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;
    char *test_schema1 = NULL;
    GmcDropVertexLabel(g_stmt_sync, labelNameT);
    readJanssonFile("schema_file/batch_insert_dml_general.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema1, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema1);
    test_schema1 = NULL;

    int start_num = 0, end_num = 100;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef ONOFF_BATCH_ERR_CTRL_DEFINE
    uint32_t batchErrCtl = GMC_BATCH_ERR_BREAK;
    ret = GmcBatchOptionSetErrCtrl(&batchOption, batchErrCtl);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelNameT, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        TestSetVertexPKPropertyValue(g_stmt_sync, i);
        TestSetVertexPropertyValue(g_stmt_sync, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, totalNum);
    ASSERT_EQ(1, successNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelNameT, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, pkLabelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        TestQueryVertexProperty(g_stmt_sync, i, 0, (char *)"string");
    }

    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelNameT);
    EXPECT_EQ(GMERR_OK, ret);
}

//同步连接下开启优化，批量插入并解析和读取数据，然后销毁再重复进行
TEST_F(SyncVertexBatchInsert, DML_082_02_008)
{
    char *test_schema1 = NULL;
    GmcDropVertexLabel(g_stmt_sync, labelNameT);
    readJanssonFile("schema_file/batch_insert_dml_general.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema1, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema1);
    test_schema1 = NULL;

    int repeat, start_num = 0, end_num = 1024;
    for (repeat = 0; repeat < 100; repeat++) {
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet = {};
        GmcBatchOptionT batchOption = {};
        ret = GmcBatchOptionInit(&batchOption);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        EXPECT_EQ(GMERR_OK, ret);
#ifdef ONOFF_BATCH_ERR_CTRL_DEFINE
        uint32_t batchErrCtl = GMC_BATCH_ERR_BREAK;
        ret = GmcBatchOptionSetErrCtrl(&batchOption, batchErrCtl);
        EXPECT_EQ(GMERR_OK, ret);
#endif
        ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelNameT, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        for (int i = start_num; i < end_num; i++) {
            i += (repeat * end_num);  //数据不能重复相同
            TestSetVertexPKPropertyValue(g_stmt_sync, i);
            TestSetVertexPropertyValue(g_stmt_sync, i, 0, (char *)"string");
            ret = GmcBatchAddDML(batch, g_stmt_sync);
            ASSERT_EQ(GMERR_OK, ret);
        }
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ret = GmcBatchExecute(batch, &batchRet);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, totalNum);
        ASSERT_EQ(1, successNum);
        ret = GmcBatchDestroy(batch);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelNameT, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, pkLabelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_INT64, &f0_value);
        ASSERT_EQ(GMERR_OK, ret);
        TestQueryVertexProperty(g_stmt_sync, i, 0, (char *)"string");
    }

    ret = GmcDropVertexLabel(g_stmt_sync, labelNameT);
    EXPECT_EQ(GMERR_OK, ret);
}

//开启批量优化GmcBatchAddDDL批量创建最大1024张表，批量执行后解析然后删表
TEST_F(SyncVertexBatchInsert, DML_082_02_009)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_BATCH_BUFFER_FULL);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    char g_errorCode03[1024] = {0};
    (void)snprintf(g_errorCode03, 1024, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;
    int record = 1024;
    char labelName[64] = {};
    char schema_path[64] = {};
    CreateVertexLabelPdeal();  // 1024
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef ONOFF_BATCH_ERR_CTRL_DEFINE
    uint32_t batchErrCtl = GMC_BATCH_ERR_BREAK;
    ret = GmcBatchOptionSetErrCtrl(&batchOption, batchErrCtl);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= record; i++) {

        char *schema_json = NULL;
        sprintf(labelName, "batch_set_ds_cfg%d", i);
        sprintf(schema_path, "./batch_file/%s.gmjson", labelName);
        readJanssonFile(schema_path, &schema_json);
        ASSERT_NE((void *)NULL, schema_json);

        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, schema_json, g_label_config);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            printf("GmcBatchAddDDL:i=%d, ret=%d ,schema_path=%s\n", i, ret, schema_path);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            free(schema_json);
            schema_json = NULL;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_json);
        schema_json = NULL;
    }

    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 1; i <= successNum; i++) {
        sprintf(labelName, "batch_set_ds_cfg%d", i);
        ret = GmcDropVertexLabel(g_stmt_sync, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

//同步连接下,设置开启优化,对多张表批量插入数据然后对多表分别读取数据进行校验
TEST_F(SyncVertexBatchInsert, DML_082_02_010)
{
    char *test_schema1 = NULL;
    char *test_schema2 = NULL;
    const char *strBatchLabelName1 = "KEY_S0";
    const char *strBatchLabelVertexPK1 = "KEY_PK";
    const char *strBatchLabelName2 = "TBL_K0";
    const char *strBatchLabelVertexPK2 = "TBL_PK";
    GmcDropVertexLabel(g_stmt_sync, strBatchLabelName1);
    readJanssonFile("schema_file/batch_insert_dml_general.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema1, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema1);
    GmcDropVertexLabel(g_stmt_sync, strBatchLabelName2);
    readJanssonFile("schema_file/batch_insert_dst_general.gmjson", &test_schema2);
    ASSERT_NE((void *)NULL, test_schema2);
    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema2, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(test_schema2);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
#ifdef ONOFF_BATCH_ERR_CTRL_DEFINE
    uint32_t batchErrCtl = GMC_BATCH_ERR_BREAK;
    ret = GmcBatchOptionSetErrCtrl(&batchOption, batchErrCtl);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    int start_num = 0, end_num = 100;
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName1, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        TestSetVertexPKPropertyValue(g_stmt_sync, i);
        TestSetVertexPropertyValue(g_stmt_sync, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName2, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        TestSetVertexPKPropertyValue(g_stmt_sync, i);
        TestSetVertexPropertyValue(g_stmt_sync, i, 0, (char *)"string");

        ret = GmcBatchAddDML(batch, g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // GmcBatchReset
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(2 * end_num, totalNum);
    ASSERT_EQ(2 * end_num, successNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName1, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, strBatchLabelVertexPK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        TestQueryVertexProperty(g_stmt_sync, i, 0, (char *)"string");
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, strBatchLabelVertexPK2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt_sync, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        TestQueryVertexProperty(g_stmt_sync, i, 0, (char *)"string");
    }

    ret = GmcDropVertexLabel(g_stmt_sync, strBatchLabelName1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, strBatchLabelName2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}


/**************************************************************/
//同步连接下开启优化，设置内存上限和回收大小后批量创建最大1024张kv表，创建后解析然后再删除kv表
TEST_F(SyncVertexBatchInsert, DML_082_02_014)
{
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    char labelName[128];
    for (int i = 0; i < 1024; i++) {
        sprintf(labelName, "KvTabel_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_KV_TABLE, labelName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, 1024);
    EXPECT_EQ(successNum, 1024);

    for (int i = 0; i < 1024; i++) {
        sprintf(labelName, "KvTabel_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_KV_TABLE, labelName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, 1024);
    EXPECT_EQ(successNum, 1024);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

//同步连接下开启优化，设置内存上限和回收大小后建kv表批量插入多条数据然后批量删除;
TEST_F(SyncVertexBatchInsert, DML_082_02_015)
{
    void *tableLabel = NULL;
    char kvTableName[] = "BatchKV";
    int ret = GmcKvCreateTable(g_stmt_sync, kvTableName, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmt_sync, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char key_set[1024];
    for (int32_t i = 0; i < 1000; i++) {
        sprintf(key_set, "BatchKVSet%d", i);
        int32_t value1 = i;
        kvInfo1.key = key_set;
        kvInfo1.keyLen = strlen(key_set);
        kvInfo1.value = &value1;
        kvInfo1.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt_sync, key_set, strlen(key_set), &value1, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt_sync, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
    }

    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(totalNum, (uint32_t)1000);
    ASSERT_EQ(successNum, (uint32_t)1000);

    char key_delete[1024];
    for (int32_t i = 0; i < 1000; i++) {
        sprintf(key_delete, "BatchKVSet%d", i);
        kvInfo1.key = key_delete;
        kvInfo1.keyLen = strlen(key_delete);
        kvInfo1.valueLen = 0;
        ret = GmcKvInputToStmt(g_stmt_sync, key_delete, strlen(key_delete), NULL, 0);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt_sync, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(totalNum, (uint32_t)1000);
    ASSERT_EQ(successNum, (uint32_t)1000);

    ret = GmcKvDropTable(g_stmt_sync, kvTableName);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
}

/**************************************************************/
//同步连接下开启优化，设置内存上限和回收大小后创建资源池批量连续插入数据然后删除资源字段记录并检查释放
TEST_F(SyncVertexBatchInsert, DML_082_02_016)
{
    int32_t ret = 0;
    uint64_t count = 10;
    int start_num = 0;
    int end_num = 200 / count;
    char *test_schema2 = NULL;
    const char *gResPoolTest =
        R"({
        "name" : "resource_pool_test",
        "pool_id" : 0,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *strBatchLabelVertexPK2 = "TBL_PK";
    const char *resPoolTestName = "resource_pool_test";
    ret = GmcCreateResPool(g_stmt_sync, gResPoolTest);
    EXPECT_EQ(GMERR_OK, ret);

    const char *strBatchLabelName2 = "VT_F0";
    readJanssonFile("schema_file/batch_insert_dml_resource.gmjson", &test_schema2);
    ASSERT_NE((void *)NULL, test_schema2);
    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema2, NULL);
    ASSERT_EQ(true, (ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE));

    ret = GmcBindResPoolToLabel(g_stmt_sync, resPoolTestName, strBatchLabelName2);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName2, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchT *batch = NULL;
        GmcBatchRetT batchRet = {};
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
        EXPECT_EQ(GMERR_OK, ret);
        for (int i = start_num; i < end_num; i++) {
            TestSetVertexPKPropertyValue(g_stmt_sync, i);
            TestSetVertexNodesProperty(g_stmt_sync, i, 0, (char *)"string", 0xFFFF, count, 0xFFFFFFFF);
            ret = GmcBatchAddDML(batch, g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
        }
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, totalNum);
        ASSERT_EQ(1, successNum);
        ret = GmcBatchDestroy(batch);
        EXPECT_EQ(GMERR_OK, ret);

        // 删除顶点
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName2, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchT *batch2 = NULL;
        GmcBatchRetT batchRet2 = {};
        GmcBatchOptionT batchOption2;
        ret = GmcBatchOptionInit(&batchOption2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption2, 2048);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption2, GMC_BATCH_ORDER_SEMI);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchPrepare(g_conn_sync, &batchOption2, &batch2);
        EXPECT_EQ(GMERR_OK, ret);

        for (int i = start_num; i < end_num; i++) {
            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt_sync, strBatchLabelVertexPK2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch2, g_stmt_sync);
            EXPECT_EQ(GMERR_OK, ret);
        }
        totalNum = 0;
        successNum = 0;
        ret = GmcBatchExecute(batch2, &batchRet2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet2, &totalNum, &successNum);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, totalNum);
        ASSERT_EQ(1, successNum);
        ret = GmcBatchDestroy(batch2);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName2, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, strBatchLabelVertexPK2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, strBatchLabelName2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt_sync, resPoolTestName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync, strBatchLabelName2);
    free(test_schema2);
}

//同步连接下开启优化，设置内存上限和回收大小后对资源表进行DML批量插入输入直至写满返回然后再回收资源
TEST_F(SyncVertexBatchInsert, DML_082_02_017)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    const char *gResPoolTest =
        R"({
        "name" : "resource_pool_test",
        "pool_id" : 0,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    char *test_schema2 = NULL;
    const char *strBatchLabelName2 = "OP_RESPOOL";
    const char *strBatchLabelVertexPK1 = "KEY_PK";
    const char *resPoolTestName = "resource_pool_test";
    int32_t ret = 0;
    uint64_t count = 10;
    int start_num = 0, end_num = 200 / count + 1;

    ret = GmcCreateResPool(g_stmt_sync, gResPoolTest);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/batch_insert_full_mem.gmjson", &test_schema2);
    ASSERT_NE((void *)NULL, test_schema2);
    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema2, NULL);
    ASSERT_EQ(true, (ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE));
    if (ret != GMERR_OK) {
        const char *expect = NULL;
        ret = testGmcGetLastError(expect);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBindResPoolToLabel(g_stmt_sync, resPoolTestName, strBatchLabelName2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption = {};
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        TestSetVertexPKPropertyValue(g_stmt_sync, i);
        TestSetVertexNodesProperty(g_stmt_sync, i, 0, (char *)"string", 0xFFFF, count, 0xFFFFFFFF);
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_RESOURCE_POOL_NOT_ENOUGH, ret);
    const char *expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, totalNum);
    EXPECT_EQ(0, successNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, strBatchLabelName2, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, strBatchLabelVertexPK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, strBatchLabelName2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt_sync, resPoolTestName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync, strBatchLabelName2);
    free(test_schema2);
}
