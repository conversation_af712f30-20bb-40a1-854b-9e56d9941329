[{"type": "record", "name": "T39", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "string", "size": 10, "nullable": true}, {"name": "F2", "type": "uint32"}, {"name": "F3", "type": "uint32"}], "keys": [{"node": "T39", "name": "T39_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T39", "name": "T39_hash", "fields": ["F2", "F3"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]