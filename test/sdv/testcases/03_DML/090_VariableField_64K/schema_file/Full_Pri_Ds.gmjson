[{"version": "2.0", "type": "record", "name": "Full_Pri_Ds", "comment": "主要验证primarykey和localhashkey和变长交互", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "Default_StrSize", "type": "string", "nullable": true}, {"name": "Default_StrSize2", "type": "string", "nullable": true}, {"name": "Default_BytSize", "type": "bytes", "nullable": true}, {"name": "Default_BytSize2", "type": "bytes", "nullable": true}, {"name": "Eq_Str8K", "type": "string", "size": 8192, "nullable": true}, {"name": "Eq_Byt8K", "type": "bytes", "size": 8192, "nullable": true}, {"name": "Eq_Str8K2", "type": "string", "size": 8192, "nullable": true}, {"name": "Eq_Byt8K2", "type": "bytes", "size": 8192, "nullable": true}, {"name": "Eq_Str256B", "type": "string", "size": 256, "nullable": true}, {"name": "Eq_Byt256B", "type": "bytes", "size": 256, "nullable": true}, {"name": "Eq_Str256B2", "type": "string", "size": 256, "nullable": true}, {"name": "Eq_Byt256B2", "type": "bytes", "size": 256, "nullable": true}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "Full_Pri_Ds", "fields": ["F0"], "constraints": {"unique": true}, "comment": "主键索引"}, {"name": "localhash_keys", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "Full_Pri_Ds", "fields": ["Default_StrSize2"], "constraints": {"unique": false}, "comment": "localhash索引"}, {"name": "localhash_keyb", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "Full_Pri_Ds", "fields": ["Default_BytSize2"], "constraints": {"unique": false}, "comment": "localhash索引"}, {"name": "localhash_keys8k", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "Full_Pri_Ds", "fields": ["Eq_Str8K"], "constraints": {"unique": false}, "comment": "localhash索引"}, {"name": "localhash_keyb8k", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "Full_Pri_Ds", "fields": ["Eq_Byt8K"], "constraints": {"unique": false}, "comment": "localhash索引"}]}]