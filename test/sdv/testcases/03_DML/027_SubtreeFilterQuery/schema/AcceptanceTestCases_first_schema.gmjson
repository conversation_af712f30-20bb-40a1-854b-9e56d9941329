[{"name": "T36", "version": "2.0", "type": "record", "fixed_array": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "uint32"}, {"name": "F3", "type": "uint32"}, {"name": "F4", "type": "string", "size": 100}], "keys": [{"node": "T36", "name": "T36_K0", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T36", "name": "T36_H0", "fields": ["F1"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}, {"node": "T36", "name": "T36_H1", "fields": ["F2"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}]}]