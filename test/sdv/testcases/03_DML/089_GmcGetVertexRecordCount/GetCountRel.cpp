/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: 089_GmcGetVertexRecordCount
 * Author: hanyang
 * Create: 2022-5-30
 */
#include "GetCount_test.h"

const char *MS_VLabel_Name_01_Memory = "vertex_01_Memory";
const char *MS_VLabel_Key_Name_01_Memory = "vertex_01_Memory_key";

class GetCountRel_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GetCountRel_test::SetUpTestCase()
{
}

void GetCountRel_test::TearDownTestCase()
{
}

void GetCountRel_test::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void GetCountRel_test::TearDown()
{
    AW_CHECK_LOG_END();
}

/*****************************************************************************
 Description  : Rel_001.获取表记录数前，连接中断，重新建立连接后，获取记录数正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(GetCountRel_test, DML_089_GetCountRel_001)
{
    int ret;

    // 启动服务
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建Vertex表
    testCreateLabel(g_stmt);

    uint64_t count;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // 查询表记录数
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 中断连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 继续执行报错
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    // 重建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询表记录数
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 全表扫描
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, count);

    // 主键查询
    ret = GmcSetIndexKeyName(g_stmt, Vertex_KeyName);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t KeyValue = 3;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);

    // local唯一索引
    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalName_u);
    EXPECT_EQ(GMERR_OK, ret);
    KeyValue = 3;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);

    // hashcluster非唯一索引
    ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
    EXPECT_EQ(GMERR_OK, ret);
    KeyValue = 3;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, count);

    // 删除Vertex表
    testDropLabel(g_stmt, Vertex_Name);

    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

/*****************************************************************************
 Description  : Rel_002.获取表记录数前，服务异常退出，重启服务后，获取记录数正常。
 Author       : hanyang
*****************************************************************************/
TEST_F(GetCountRel_test, DML_089_GetCountRel_002)
{
    int ret;

    // 启动服务
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建Vertex表
    testCreateLabel(g_stmt);

    uint64_t count;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // 查询表记录数
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 服务异常退出
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("ipcrm -a");

    // 继续执行报错
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    // 重启服务
    system("sh $TEST_HOME/tools/start.sh -f");
    sleep(1);
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);

    // 重建连接
    g_conn = NULL;
    g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建Vertex表
    testCreateLabel(g_stmt);

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // 查询表记录数
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 全表扫描
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, count);

    // 主键查询
    ret = GmcSetIndexKeyName(g_stmt, Vertex_KeyName);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t KeyValue = 3;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);

    // local唯一索引
    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalName_u);
    EXPECT_EQ(GMERR_OK, ret);
    KeyValue = 3;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);

    // hashcluster非唯一索引
    ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
    EXPECT_EQ(GMERR_OK, ret);
    KeyValue = 3;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, count);

    // 删除Vertex表
    testDropLabel(g_stmt, Vertex_Name);

    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 清理环境
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

/*****************************************************************************
 Description  : Rel_003.使用多种索引类型，获取表记录数，循环执行10000次
 Author       : hanyang
*****************************************************************************/
TEST_F(GetCountRel_test, DML_089_GetCountRel_003)
{
    int ret;

    // 启动服务
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);

    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建Vertex表
    testCreateLabel(g_stmt);

    uint64_t count;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    for (uint32_t i = 0; i < RECORD_NUM_003; i++) {
        // 查询表记录数
        ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);

        // 全表扫描
        ret = GmcGetVertexRecordCount(g_stmt, &count);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(100, count);

        // 主键查询
        ret = GmcSetIndexKeyName(g_stmt, Vertex_KeyName);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t KeyValue = 3;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexRecordCount(g_stmt, &count);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, count);

        // local唯一索引
        ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalName_u);
        EXPECT_EQ(GMERR_OK, ret);
        KeyValue = 3;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexRecordCount(g_stmt, &count);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, count);

        // hashcluster非唯一索引
        ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
        EXPECT_EQ(GMERR_OK, ret);
        KeyValue = 3;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexRecordCount(g_stmt, &count);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(10, count);
    }

    // 删除Vertex表
    testDropLabel(g_stmt, Vertex_Name);

    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}


/*****************************************************************************
 Description  : Rel_004.连接满场景下，使用多种索引类型，获取表记录数
 Author       : hanyang
*****************************************************************************/
TEST_F(GetCountRel_test, DML_089_GetCountRel_004)
{
    int ret;

    // 启动服务
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);

    GmcConnT *g_conn;
    GmcStmtT *g_stmt;
    GmcConnT *conn[MAX_CONN_SIZE] = {NULL};
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    ASSERT_EQ(0, ret);

    for (int i = 0; i < (MAX_CONN_SIZE - 3 - existConnNum); i++) {
        printf("testGmcConnect:%d\n", i);
        ret = testGmcConnect(&conn[i]);
        if (ret != GMERR_OK) {
            printf("i:%d ret:%d\n", i, ret);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    /******************************begin ************************************************/
    // 创建Vertex表
    testCreateLabel(g_stmt);

    uint64_t count;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);

    // 查询表记录数
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 全表扫描
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, count);

    // 主键查询
    ret = GmcSetIndexKeyName(g_stmt, Vertex_KeyName);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t KeyValue = 3;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);

    // local唯一索引
    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalName_u);
    EXPECT_EQ(GMERR_OK, ret);
    KeyValue = 3;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);

    // hashcluster非唯一索引
    ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
    EXPECT_EQ(GMERR_OK, ret);
    KeyValue = 3;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, count);

    // 删除Vertex表
    testDropLabel(g_stmt, Vertex_Name);
    /******************************end ************************************************/

    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < (MAX_CONN_SIZE - 3 - existConnNum); i++) {
        ret = testGmcDisconnect(conn[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 清理环境
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}


/*****************************************************************************
 Description  : Rel_005.内存满场景下，使用多种索引类型，获取表记录数
 Author       : hanyang
*****************************************************************************/
TEST_F(GetCountRel_test, DML_089_GetCountRel_005)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    if (g_envType != 0) {
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/stop.sh");                        // 修改配置，先停服务
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");  // 内存大小改小，减少单个用例执行时间
        system("sh $TEST_HOME/tools/start.sh -f");
    }
    sleep(1);
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);

    /******************************先插入数据 begin***********************************/
    // 建立连接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建Vertex表
    testCreateLabel(g_stmt);

    uint64_t count;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name);
    /******************************先插入数据 end***********************************/

    /******************************写数据到内存满 begin***********************************/
    ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // create label
    char *MS_VLabel_01_schema = NULL;
    readJanssonFile("schema_file/Vertex_01_Memory.gmjson", &MS_VLabel_01_schema);
    ASSERT_NE((void *)NULL, MS_VLabel_01_schema);
    ret = GmcCreateVertexLabel(stmt, MS_VLabel_01_schema, MS_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(MS_VLabel_01_schema);

    // 打开Vertex Label
    // 插入顶点
    uint32_t i = 0;
    uint32_t insertvalue = 0;
    while (ret == 0) {
        ret = testGmcPrepareStmtByLabelName(stmt, MS_VLabel_Name_01_Memory, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        // 写数据
        insertvalue = i;
        ret = GmcSetVertexProperty(stmt, "PK", GMC_DATATYPE_UINT32, &insertvalue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &insertvalue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &insertvalue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        // 写string数据
        uint32_t SuperSize = 1024;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'B', (SuperSize - 1));
        SuperValue[SuperSize - 1] = '\0';

        ret = GmcSetVertexProperty(stmt, "P0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        EXPECT_EQ(GMERR_OK, ret);

        free(SuperValue);

        ret = GmcExecute(stmt);

        i++;
        if ((i % 50000) == 0) {
            printf("till now:insert records %d\n", i);
        }
    }
    printf("actul insert records is:%d\n", i);
    EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
    ret = testGmcGetLastError();
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetStmt(stmt);
    /******************************写数据到内存满 end***********************************/

    /******************************begin ************************************************/
    // 查询表记录数
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // 全表扫描
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, count);

    // 主键查询
    ret = GmcSetIndexKeyName(g_stmt, Vertex_KeyName);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t KeyValue = 3;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);

    // local唯一索引
    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalName_u);
    EXPECT_EQ(GMERR_OK, ret);
    KeyValue = 3;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, count);

    // hashcluster非唯一索引
    ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
    EXPECT_EQ(GMERR_OK, ret);
    KeyValue = 3;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &KeyValue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(g_stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(10, count);

    // 删除Vertex表
    testDropLabel(g_stmt, Vertex_Name);

    // 释放连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    /******************************end ************************************************/

    // dorp Vlabel
    ret = GmcDropVertexLabel(stmt, MS_VLabel_Name_01_Memory);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    sleep(1);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    if (g_envType == 0) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    }
}

