/*****************************************************************************
 Description  : LPM4索引场景测试
 History      :
 Author       : houjia hwx390087
 Modification :
 Date         : 2021/3/23
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "tools.h"


using namespace std;

char g_configJson[128] = "{\"max_record_count\" : 999999}";
GmcStmtT *stmt_sn_sync;
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";
GmcConnT *subChan;
char *vtxLabelJson7 = NULL, *vtxLabelJson8 = NULL, *vtxLabelJson9 = NULL;
char *vtxLabelJson10 = NULL, *vtxLabelJson42 = NULL;
char *edgeLabelJson78 = NULL, *edgeLabelJson79 = NULL, *edgeLabelJson89 = NULL, *edgeLabelJson910 = NULL,
     *edgeLabelJson1042 = NULL;
const char *vtxLabelName7 = "ip4forward";
const char *vtxLabelName8 = "nhp_group";
const char *vtxLabelName9 = "nhp_group_node";
const char *vtxLabelName10 = "nhp";
const char *vtxLabelName42 = "nhp_std";
const char *edgeLabelNameF78 = (char *)"from_7_to_8";
const char *edgeLabelNameF79 = (char *)"from_7_to_9";
const char *edgeLabelNameF89 = (char *)"from_8_to_9";
const char *edgeLabelNameF910 = (char *)"from_9_to_10";
const char *edgeLabelNameF1042 = (char *)"from_10_to_42";

GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

class LPM4_scene_test : public testing::Test {
public:
    static void SetUpTestCase()
    {
        // 重启server
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh modifyCfg.sh \"enableClusterHash=1\"");
        system("sh modifyCfg.sh \"isFastReadUncommitted=1\"");
        system("sh $TEST_HOME/tools/start.sh -f");

        res = testEnvInit();
        EXPECT_EQ(GMERR_OK, res);
        res = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
    }

    static void TearDownTestCase()
    {

        res = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\""); 
    };

    virtual void SetUp();
    virtual void TearDown();
};

void LPM4_scene_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    conn = NULL;
    stmt = NULL;
    vertexLabel = NULL;
    // 创建同步连接
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN(1);
}

void LPM4_scene_test::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();

    // 断开客户端连接
    GmcDropVertexLabel(stmt, "ip4forward");
    GmcDropVertexLabel(stmt, "ip4forward_1");
    GmcDropVertexLabel(stmt, "ip4forward_2");
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(LPM4_scene_test, DML_050_LPM4_scene_001_insert)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // 写入数据
    int oper_nums = 128;
    test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, 24);
}

// 01.schema定义新增key type：lpm4索引，gmimport工具导入建表
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcDropVertexLabel(stmt, g_labelName);
    // gmimport 导入vertexLabel -c: dstore | vschema | vdata | eschema | edata | cache
    char schema_file[512] = "./schema_file/ip4forward_lpm4.gmjson";
    char cmd[512];
    if (g_envType != 0) {
        snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -ns %s -s %s", g_toolPath, schema_file, g_testNameSpace,
            g_connServer);
    } else {
        snprintf(cmd, 512, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, schema_file, g_connServer);
    }
    testGmcGetLastError(NULL);
    system(cmd);

    // 导入创建成功后，调用建表接口创建同名表，预期表已存在
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema_json);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 02.schema定义新增key type：lpm4索引，调用batch DDL接口批量建表、删表
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_002)
{
    // 准备1024个1kb 大小的schema json文件
    system("sh create_multi_label_1k.sh 127");

    char configJson[512] = "{\"max_record_count\" : 1000}";

    // 批量建表预准备
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    // 添加批量执行操作：create vertexLabel 512
    char *schema_json = NULL;
    char labelName[512];
    char schema_path[512];
    for (int i = 1; i <= 127; i++) {

        schema_json = NULL;
        sprintf(labelName, "T39_%d", i);
        sprintf(schema_path, "./labelSchema_1k/schemaSize_1k_%d.gmjson", i);
        readJanssonFile(schema_path, &schema_json);
        ASSERT_NE((void *)NULL, schema_json);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, schema_json, configJson);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_json);
    }
    
    // 执行批量操作
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(127, totalNum);
    EXPECT_EQ(127, successNum);
    printf("\n[INFO] batch create vertexLabel status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);
    GmcBatchDestroy(batch);

    // 批量drop vertexLabel
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i <= 127; i++) {
        schema_json = NULL;
        sprintf(labelName, "T39_%d", i);
        ret = GmcBatchAddDDL(batch, GMC_OPERATION_DROP_VERTEX_LABEL, labelName, NULL, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(127, totalNum);
    EXPECT_EQ(127, successNum);
    printf("\n[INFO] batch drop vertexLabel status %d, totalNum is %d, succNum is %d\n", ret, totalNum, successNum);
}

// 03.建表写入数据后，调用GmcTruncateVertexLabel清空表中数据；
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_003)
{
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // 写入数据
    int oper_nums = 128;
    test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, 24);

    // 清空数据
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // 清空数据后，再次写入相同值，预期写入成功
    test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, 24);
    free(schema_json);
}

// 04.更新lpm4索引的前两个字段值为与该表原有前两个字段值不同的数据；
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_004)
{
    int ret = -1;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int oper_nums = 128;
    ret = test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, 24);
    ASSERT_EQ(GMERR_OK, ret);

    // TEST POINT: 更新lpm4索引的前两个字段值为与该表原有前两个字段值不同的数据
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 24;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    unsigned int up_val = 2222;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &up_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &up_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    ret = GmcExecute(stmt);  // 主键更新lpm4前两个字段值为与该表原有前两个字段值不同的数据
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 05.根据lpm4索引同步更新
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_005)
{
    int isPrint = 1, ret = -1;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据  192.168.[loop].0  |  24
    int oper_nums = 128;
    test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, 24);

    // lpm4索引同步更新
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 18;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4forward_lpm");  // lpm4索引不支持更新操作
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));  // 【18】
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char up_uint8 = 22;
    unsigned short up_qos_profile_id = 2222;
    char up_fixed[35] = "update";
    unsigned char up_nhp_group_flag = 22;

    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, &up_fixed, 34);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcUpdateVertexByIndexKey(stmt, "ip4forward_lpm");  // lpm4索引不支持更新操作
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
    printf("[INFO] update data with lpm4 index, status is %d \n", ret);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    printf("\n[INFO] [ lpm4 update ] affect row: %d \n\n", affectRows);

    // lpm4索引扫描
    unsigned char exp_mask_len = 24;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &up_uint8, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = test_index_scan_ip4forward(stmt, "ip4forward_lpm", &exp_mask_len, thread_id, "lpm4 scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 06.lpm4索引不支持更新操作，预期根据lpm4索引删除数据失败
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_006)
{
    int ret = -1;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int isPrint = 1;
    int oper_nums = 128;
    test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, 24);

    // 根据lpm4索引删除，预期失败
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 24;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4forward_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcDeleteVertexByIndexKey(stmt, vertexLabel, "ip4forward_lpm");
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);
    printf("\n[INFO] [ lpm4 delete ] affect row: %d \n\n", affectRows);

    // lpm4索引扫描
    unsigned char exp_mask_len = 24;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = test_index_scan_ip4forward(stmt, "ip4forward_lpm", &exp_mask_len, thread_id, "lpm4 scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 07.根据lpm4索引进行扫描，符合路由最长匹配原则，4位全匹配场景
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_007)
{
    int isPrint = 1;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    const char *ip_addr_str = "***********";
    ret = test_insert_vertex_ip4forward_lpm4_single(stmt, vr_id, vrf_index, ip_addr_str, 16, isPrint);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_insert_vertex_ip4forward_lpm4_single(stmt, vr_id, vrf_index, ip_addr_str, 24, isPrint);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_insert_vertex_ip4forward_lpm4_single(stmt, vr_id, vrf_index, ip_addr_str, 32, isPrint);
    ASSERT_EQ(GMERR_OK, ret);

    // TEST POINT: 根据lpm4索引前4个字段扫描(实际底层只根据前3个字段解析)，预期扫描到的数据符合最长匹配原则
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 8;  // set index时即使mask_len = 8，预期扫描*********** 时 4位全匹配，应扫描到 mask_len = 32
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t exp_mask_len = 32;
    ret = test_index_scan_ip4forward(stmt, "ip4forward_lpm", &exp_mask_len, thread_id, "lpm4 scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 08.根据lpm4索引进行扫描，符合路由最长匹配原则，前3位匹配场景
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_008)
{
    int isPrint = 1;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    const char *ip_addr_str = "***********";
    ret = test_insert_vertex_ip4forward_lpm4_single(stmt, vr_id, vrf_index, ip_addr_str, 16, isPrint);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_insert_vertex_ip4forward_lpm4_single(stmt, vr_id, vrf_index, ip_addr_str, 24, isPrint);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_insert_vertex_ip4forward_lpm4_single(stmt, vr_id, vrf_index, ip_addr_str, 32, isPrint);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // TEST POINT: 根据lpm4索引前4个字段扫描(实际底层只根据前3个字段解析)，预期扫描到的数据符合最长匹配原则
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 8;  // set index时即使mask_len = 8，预期扫描*********** 时 4位全匹配，应扫描到 mask_len = 32
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t exp_mask_len = 24;

    ret = GmcSetIndexKeyName(stmt, "ip4forward_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = test_index_scan_ip4forward(stmt, "ip4forward_lpm", &exp_mask_len, thread_id, "lpm4 scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 09.根据lpm4索引进行扫描，符合路由最长匹配原则，前2位匹配场景
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_009)
{
    int isPrint = 1;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    const char *ip_addr_str = "***********";
    ret = test_insert_vertex_ip4forward_lpm4_single(stmt, vr_id, vrf_index, ip_addr_str, 16, isPrint);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_insert_vertex_ip4forward_lpm4_single(stmt, vr_id, vrf_index, ip_addr_str, 24, isPrint);
    ASSERT_EQ(GMERR_OK, ret);
    ret = test_insert_vertex_ip4forward_lpm4_single(stmt, vr_id, vrf_index, ip_addr_str, 32, isPrint);
    ASSERT_EQ(GMERR_OK, ret);

    // TEST POINT: 根据lpm4索引前4个字段扫描(实际底层只根据前3个字段解析)，预期扫描到的数据符合最长匹配原则
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 8;  // set index时即使mask_len = 8，预期扫描*********** 时 4位全匹配，应扫描到 mask_len = 32
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t exp_mask_len = 16;
    ret = test_index_scan_ip4forward(stmt, "ip4forward_lpm", &exp_mask_len, thread_id, "lpm4 scan", isPrint);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
}

// 10.根据pk更新lpm4索引字段值为数据库中已存在的数据；
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_010)
{
    int ret = -1;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int oper_nums = 128;
    test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, 24);

    // TEST POINT: 更新lpm4索引字段值为数据库中已存在的数据
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 24;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    // uint32_t up_vr_id = 0;
    // uint32_t up_vrf_index = 0;
    // TEST POINT：根据pk更新dest_ip_addr字段值为数据库中已存在的数据，预期更新失败
    uint32_t up_dest_ip_addr = trans_ip("*************");  // 数据库中已有数据 *********** -> *************
    // ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &up_vr_id, sizeof(uint32_t));
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &up_vrf_index, sizeof(uint32_t));
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &up_dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    // ret = GmcUpdateVertexByIndexKey(stmt, "primary_key");
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    printf("\n[INFO] [ lpm4 update exist data ] affect row: %d \n\n", affectRows);
}

// 11.写入数据lpm4索引前两个字段值，与数据库中已有数据前两个字段值不同
// 2021.7.23 迭代五二级索引增强特性：允许多对vrid和vrfid组合
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_011)
{
    int isPrint = 1;
    int ret = -1;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    const char *ip_addr_str = "***********";
    ret = test_insert_vertex_ip4forward_lpm4_single(stmt, 0, 0, ip_addr_str, 24, isPrint);
    ASSERT_EQ(GMERR_OK, ret);

    // TEST POINT: 写入lpm4索引前两个字段值，与数据库中已有数据前两个字段值不同，预期写入报错
    ret = test_insert_vertex_ip4forward_lpm4_single(stmt, 1, 1, ip_addr_str, 24, isPrint);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = testGmcGetLastError(NULL);
    // EXPECT_EQ(GMERR_OK, ret);
}

// 12.更新数据库中已有数据lpm4索引的前两个字段值为与该表原有前两个字段值不同的数据
// 2021.7.23 迭代五二级索引增强特性：允许多对vrid和vrfid组合
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_012)
{
    int ret = -1;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int oper_nums = 128;
    test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, 24);

    // TEST POINT: 更新已有数据lpm4索引的前两个字段值为与该表原有前两个字段值不同的数据
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 24;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t up_vr_id = 1;  // 原vr_id = 0, vrf_index = 0，修改为vr_id = 1, vrf_index = 1
    uint32_t up_vrf_index = 1;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &up_vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &up_vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    // ret = GmcUpdateVertexByIndexKey(stmt, "primary_key");
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // ret = testGmcGetLastError(NULL);
    // EXPECT_EQ(GMERR_OK, ret);
}

// 13.调用batch DML批量接口同步写入、更新、删除
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_013)
{
    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 同步 batch 写入数据
    int operNum = 10;
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint8_t mask_len = 18;

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t loop = 0; loop < operNum; loop++) {
        sprintf(ip_addr, "192.168.%d.0", loop);
        uint32_t trans_val = trans_ip(ip_addr);
        set_vtxLabel_pk_field_ip4forward(stmt, vr_id, vrf_index, trans_val, 24);
        set_vtxLabel_field_ip4forward(stmt, loop);
        // ret = GmcBatchAddVertexDML(stmt,GMC_CMD_INSERT_VERTEX);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
        // ret = GmcInsertVertex(stmt);
        // EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(operNum, totalNum);
    EXPECT_EQ(operNum, successNum);
    printf("\n[INFO][ batch write lpm4 ] affect row: %d \n", successNum);
    GmcBatchDestroy(batch);

    // 同步 batch 更新
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t loop = 0; loop < operNum; loop++) {

        sprintf(ip_addr, "192.168.%d.0", loop);
        uint32_t trans_val = trans_ip(ip_addr);

        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &trans_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));  // 【18】
        EXPECT_EQ(GMERR_OK, ret);

        unsigned char up_uint8 = 22;
        unsigned short up_qos_profile_id = 2222;
        char up_fixed[36] = "update";
        unsigned char up_nhp_group_flag = 22;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
        EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
        ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, &up_fixed, 34);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchAddVertexDML(stmt, GMC_CMD_UPDATE_VERTEX);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(operNum, totalNum);
    EXPECT_EQ(10, successNum);
    printf("\n[INFO][ lpm4 sync batch update] affect row: %d \n\n", successNum);
    GmcBatchDestroy(batch);

    // 同步 batch 删除
    ret = GmcBatchPrepare(conn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t loop = 0; loop < operNum; loop++) {

        sprintf(ip_addr, "192.168.%d.0", loop);
        uint32_t trans_val = trans_ip(ip_addr);

        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &trans_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));  // 【18】
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchAddVertexDML(stmt,GMC_CMD_DELETE_VERTEX);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(operNum, totalNum);
    EXPECT_EQ(operNum, successNum);
    printf("[INFO][ lpm4 sync batch delete] affect row: %d \n\n", successNum);
    GmcBatchDestroy(batch);

    // Drop edgeLabel, vtxLabel
    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 14.调用batch DML批量接口异步写入、更新、删除
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_014)
{
    // 创建异步连接
    res = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, res);

    // 异步创建vertexLabel
    AsyncUserDataT data = {0};
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema_json, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);
    res = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, res);
    ASSERT_EQ(GMERR_OK, data.status);

    // 异步 batch 写入数据
    int operNum = 10;
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint8_t mask_len = 18;

    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t loop = 0; loop < operNum; loop++) {

        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        sprintf(ip_addr, "192.168.%d.0", loop);
        uint32_t trans_val = trans_ip(ip_addr);
        set_vtxLabel_pk_field_ip4forward(g_stmt_async, vr_id, vrf_index, trans_val, 24);
        set_vtxLabel_field_ip4forward(g_stmt_async, loop);
        // ret = GmcBatchAddVertexDML(g_stmt_async,GMC_CMD_INSERT_VERTEX);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    res = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, res);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(operNum, data.totalNum);
    EXPECT_EQ(operNum, data.succNum);
    printf("\n[INFO] batch write lpm4 callback status %d, totalNum is %d, succNum is %d\n", data.status, data.totalNum,
        data.succNum);
    // GmcBatchDestroy(batch);

    // 异步 batch 更新
    // ret = GmcBatchPrepare(g_stmt_async,NULL, &batch);
    // ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t loop = 0; loop < operNum; loop++) {

        sprintf(ip_addr, "192.168.%d.0", loop);
        uint32_t trans_val = trans_ip(ip_addr);
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 2, GMC_DATATYPE_UINT32, &trans_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));  // 【18】
        EXPECT_EQ(GMERR_OK, ret);

        unsigned char up_uint8 = 22;
        unsigned short up_qos_profile_id = 2222;
        char up_fixed[36] = "update";
        unsigned char up_nhp_group_flag = 22;
        ret = GmcSetVertexProperty(g_stmt_async, "mask_len", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
        EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "nhp_group_flag", GMC_DATATYPE_UINT8, &up_uint8, sizeof(up_uint8));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt_async, "qos_profile_id", GMC_DATATYPE_UINT16, &up_qos_profile_id, sizeof(up_qos_profile_id));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, &up_fixed, 34);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_async, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchAddVertexDML(g_stmt_async,GMC_CMD_UPDATE_VERTEX);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    res = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, res);
    EXPECT_EQ(0, data.status);
    EXPECT_EQ(operNum, data.totalNum);
    EXPECT_EQ(10, data.succNum);
    printf("\n[INFO] lpm4 batch update callback status %d, totalNum is %d, succNum is %d\n", data.status, data.totalNum,
        data.succNum);
    // GmcBatchDestroy(batch);

    // 异步 batch 删除
    // ret = GmcBatchPrepare(g_stmt_async);
    // ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t loop = 0; loop < operNum; loop++) {

        sprintf(ip_addr, "192.168.%d.0", loop);
        uint32_t trans_val = trans_ip(ip_addr);
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 2, GMC_DATATYPE_UINT32, &trans_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));  // 【18】
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_async, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcBatchAddVertexDML(g_stmt_async, GMC_CMD_DELETE_VERTEX);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    res = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, res);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(operNum, data.totalNum);
    EXPECT_EQ(operNum, data.succNum);
    printf("\n[INFO] lpm4 batch delete callback status %d, totalNum is %d, succNum is %d\n", data.status, data.totalNum,
        data.succNum);
    GmcBatchDestroy(batch);
}

// 15.条件过滤更新lpm索引所在字段值
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_015)
{
#if 0
    // 创建vertexLabel
    char *schema_json = NULL;	
    readJanssonFile("./schema_file/TreeModelSchema.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    
    // 写入数据
    int isPrint = 1;
    uint32_t start_num = 0;
    uint32_t end_num = 1;
    int array_num = 3;
    TestGmcInsertVertex_lpm(stmt, false, (char *)"testve", start_num, end_num, array_num, "OP_T0");
    
    // 条件过滤更新lpm4索引所在字段值   
    ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_UPDATE);
	EXPECT_EQ(GMERR_OK, ret);
    const char *cond=(const char *)"F17=0";   
    ret = GmcSetFilter(stmt, cond);
    uint8_t up_F20 = 32;   
    TestGmcNodeSetPropertyByName_lpm4(stmt, 0, "***********", up_F20);
    // ret = GmcUpdateVertexByCond(stmt, cond);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, affectRows);    
   
    // lpm4 索引扫描
    uint32_t F17_val = 0;
    uint32_t F18_val = 0;
    uint32_t F19_val = trans_ip("***********");
    ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_SCAN);
	EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "lpm_test");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F17_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &F18_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &F19_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &up_F20, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    TEST_ASSERT_EQ(0, ret);   
    int cnt = 0;
    ret = GmcFetch(stmt, &isFinish);   
    if(isFinish == true || ret != 0){		
        printf("fetch times: %d, status is %d \n", cnt, ret);
        scan_end = cnt;
        ret = GMERR_OK;
    }
    TEST_SCAN_DEL_RES(0, ret, cnt);
    
    unsigned int sc_F17_val;
    ret = queryFieldValueAndCompare(stmt, "F17", &sc_F17_val, &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(4, valueSize);
    EXPECT_EQ(F17_val, sc_F17_val);
    TEST_INFO("after filter update, lpm4 scan", "F17", sc_F17_val, thread_id, isPrint, 0);
    
    unsigned int sc_F18_val;
    ret = queryFieldValueAndCompare(stmt, "F18", &sc_F18_val, &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(4, valueSize);
    EXPECT_EQ(F18_val, sc_F18_val);
    TEST_INFO("after filter update, lpm4 scan", "F18", sc_F18_val, thread_id, isPrint, 0);
    
    unsigned int sc_F19_val;
    ret = queryFieldValueAndCompare(stmt, "F19", &sc_F19_val, &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(4, valueSize);
    EXPECT_EQ(F19_val, sc_F19_val);
    TEST_INFO("after filter update, lpm4 scan", "F19", sc_F19_val, thread_id, isPrint, 0);
    
    unsigned char sc_F20_val;
    ret = queryFieldValueAndCompare(stmt, "F20", &sc_F20_val, &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, valueSize);
    EXPECT_EQ(up_F20, sc_F20_val);
    TEST_INFO("after filter update, lpm4 scan", "F20", sc_F20_val, thread_id, isPrint, 0);
		
    GmcResetStmt(stmt);
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    ASSERT_EQ(GMERR_OK, ret);
#endif
}

// 16.条件过滤删除条件设置在lpm4索引所在字段
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_016)
{
#if 0
    // 创建vertexLabel
    char *schema_json = NULL;	
    readJanssonFile("./schema_file/TreeModelSchema.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    
    // 写入数据
    int isPrint = 1;
    uint32_t start_num = 0;
    uint32_t end_num = 1;
    int array_num = 3;
    TestGmcInsertVertex_lpm(stmt, false, (char *)"testve", start_num, end_num, array_num, "OP_T0");
    
    // 条件过滤删除，过滤条件在lpm4索引所在字段
    ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_DELETE);
	EXPECT_EQ(GMERR_OK, ret);   
    const char *cond = (const char *)"F17=0";
    // ret = GmcDeleteVertexByCond(stmt, vertexLabel, cond);   
    ret = GmcSetFilter(stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret); 
    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret); 
    ASSERT_EQ(1, affectRows);    
   
    // lpm4 索引扫描
    uint32_t F17_val = 0;
    uint32_t F18_val = 0;
    uint8_t up_F20 = 32;
    uint32_t F19_val = trans_ip("***********");
    ret = testGmcPrepareStmtByLabelName(stmt, "OP_T0", GMC_OPERATION_SCAN);
	EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "lpm_test");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F17_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &F18_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &F19_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &up_F20, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);   
    // ret = GmcExecScanVertex(stmt, vertexLabel, "lpm_test");
    ret = GmcExecute(stmt);
    TEST_ASSERT_EQ(0, ret);   
    int cnt = 0;
    ret = GmcFetch(stmt, &isFinish);   
    TEST_SCAN_DEL_RES(0, ret, cnt);
    if(isFinish == true || ret != 0){		
        printf("fetch times: %d, status is %d \n", cnt, ret);
        scan_end = cnt;
        ret = GMERR_OK;
    }
    else{
        unsigned int sc_F17_val;
        ret = queryFieldValueAndCompare(stmt, "F17", &sc_F17_val, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        EXPECT_EQ(F17_val, sc_F17_val);
        TEST_INFO("after filter update, lpm4 scan", "F17", sc_F17_val, thread_id, isPrint, 0);
        
        unsigned int sc_F18_val;
        ret = queryFieldValueAndCompare(stmt, "F18", &sc_F18_val, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        EXPECT_EQ(F18_val, sc_F18_val);
        TEST_INFO("after filter update, lpm4 scan", "F18", sc_F18_val, thread_id, isPrint, 0);
        
        unsigned int sc_F19_val;
        ret = queryFieldValueAndCompare(stmt, "F19", &sc_F19_val, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(4, valueSize);
        EXPECT_EQ(F19_val, sc_F19_val);
        TEST_INFO("after filter update, lpm4 scan", "F19", sc_F19_val, thread_id, isPrint, 0);
        
        unsigned char sc_F20_val;
        ret = queryFieldValueAndCompare(stmt, "F20", &sc_F20_val, &valueSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, valueSize);
        EXPECT_EQ(up_F20, sc_F20_val);
        TEST_INFO("after filter update, lpm4 scan", "F20", sc_F20_val, thread_id, isPrint, 0);		
    }    
    GmcResetStmt(stmt);   
    ret = GmcDropVertexLabel(stmt, "OP_T0");
    ASSERT_EQ(GMERR_OK, ret);
#endif
}

// 17.调用GmcGetVertexCount接口，根据lpm4索引获取查询记录数
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_017)
{
    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int oper_nums = 128;
    ret = test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, 24);
    EXPECT_EQ(GMERR_OK, ret);

    // get vertex count
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 24;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_labelName, "ip4forward_lpm", &count);
    ASSERT_EQ(128, count);
    printf("[INFO] get count: %lu \n", count);
}

// 18.与事务交互。开启显示事务接口，根据主键更新lpm4索引所在字段值数据（异常场景），提交事务后验证数据回滚结果
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_018)
{
    int ret = -1;
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    char Label_config[] = "{\"max_record_count\":1000000, \"isFastReadUncommitted\":0}";

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, Label_config);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int oper_nums = 128;
    test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, 24);

    // TEST POINT: 与显示事务接口交互
    // 开启事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    ASSERT_EQ(GMERR_OK, ret);

    // 根据主键更新lpm4索引所在字段值为db中已存在的数据
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 24;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t up_dest_ip_addr = trans_ip("*************");  // 111.0数据库中已有数据 *********** -> *************
    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &up_dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    // ret = GmcUpdateVertexByIndexKey(stmt, "primary_key");
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    // get affect row
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);
    printf("\n[INFO] [ lpm4 update exist data ] affect row: %d \n\n", affectRows);

    // 事务 rollback
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 提交事务
    // ret = GmcTransCommit(conn);
    // ASSERT_EQ(GMERR_OK, ret);

    // lpm索引扫描，验证update失败，数据已回滚
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4forward_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = test_index_scan_ip4forward(stmt, "ip4forward_lpm", &mask_len, thread_id, "lpm4 scan", 1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 19.多线程并发根据主键更新lpm4索引所在字段值；
#define THR_NUM 2
GmcConnT *g_conn_tht[THR_NUM * 2];
GmcStmtT *g_stmt_tht[THR_NUM * 2];
void *g_vertexLabel_tht[THR_NUM * 2];
int oper_nums = 1000;  // 指定每个线程的operNum
unsigned char up_uint8 = 22;
unsigned short up_qos_profile_id = 2222;
char up_fixed[36] = "update";
unsigned char up_nhp_group_flag = 22;
int g_affectRows[THR_NUM] = {0};
void *thread_pk_update_lpm4_fields_test(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);

    // 写入数据
    int oper_nums = 128;
    test_insert_vertex_ip4forward_lpm4(g_stmt_tht[conn_id], g_labelName, 0, 0, oper_nums, 24 + conn_id);

    // TEST POINT: 更新已有数据lpm4索引的前两个字段值为与该表原有前两个字段值不同的数据
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 24 + conn_id;
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    // uint32_t up_vr_id = 1;  // 原vr_id = 0, vrf_index = 0，修改为vr_id = 1, vrf_index = 1
    // uint32_t up_vrf_index = 1;
    uint8_t up_mask_len = 20 + conn_id;
    ret = GmcSetVertexProperty(g_stmt_tht[conn_id], "mask_len", GMC_DATATYPE_UINT8, &up_mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    // ret = GmcSetVertexProperty(g_stmt_tht[conn_id], "vrf_index", GMC_DATATYPE_UINT32, &up_vrf_index,
    // sizeof(uint32_t)); EXPECT_EQ(GMERR_OK, ret); ret = GmcUpdateVertexByIndexKey(g_stmt_tht[conn_id], "primary_key");
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_019)
{
    // 创建vertexLabel
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 多线程并发非唯一hash索引更新
    pthread_t thr_arr[32];
    void *thr_ret[32];
    int index[32] = {0};
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < THR_NUM; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_pk_update_lpm4_fields_test, (void *)&index[i]);
        sleep(1);
    }

    for (int i = 0; i < THR_NUM; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
}

// 20.多表并发根据lpm4索引扫描；
GmcConnT *g_conn_tht2[THR_NUM * 2];
GmcStmtT *g_stmt_tht2[THR_NUM * 2];
void *g_vertexLabel_tht2[THR_NUM * 2];
void *thread_dml_diff_label_test(void *args)
{
    // 创建连接
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht2[conn_id], &g_stmt_tht2[conn_id]);
    EXPECT_EQ(GMERR_OK, res);

    // 创建顶点label
    char labelName[512];
    sprintf(labelName, "ip4forward_%d", conn_id + 1);
    // ret = GmcDropVertexLabel(g_stmt_tht2[conn_id], labelName);

    // char schema_path[512];
    // sprintf(schema_path, "./schema_file/ip4forward_lpm4_%d.gmjson", conn_id+1);
    // printf("schema path : %s ,label name : %s \n", schema_path, labelName);

    // char *schema_json = NULL;
    // readJanssonFile(schema_path, &schema_json);
    // ret = GmcCreateVertexLabel(g_stmt_tht2[conn_id], schema_json, g_configJson);
    // EXPECT_EQ(GMERR_OK, ret);
    // free(schema_json);

    // 写入数据
    int oper_nums = 100;
    test_insert_vertex_ip4forward_lpm4(g_stmt_tht2[conn_id], labelName, conn_id, conn_id, oper_nums, 24 + conn_id);

    // lpm4索引最长匹配扫描
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht2[conn_id], labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_tht2[conn_id], "ip4forward_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 24 + conn_id;
    ret = GmcSetIndexKeyValue(g_stmt_tht2[conn_id], 0, GMC_DATATYPE_UINT32, &conn_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht2[conn_id], 1, GMC_DATATYPE_UINT32, &conn_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht2[conn_id], 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht2[conn_id], 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht2[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t exp_mask_len = 24 + conn_id;
    ret = test_index_scan_ip4forward(g_stmt_tht2[conn_id], "ip4forward_lpm", &exp_mask_len, thread_id, "lpm4 scan", 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭 client connection
    ret = testGmcDisconnect(g_conn_tht2[conn_id], g_stmt_tht2[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_020)
{
#if defined ENV_EULER
    // 多线程： [1] 全表扫描  [2] 全表扫描过程中drop表
    GmcConnT *conn_t[32] = {0};
    pthread_t thr_arr[32];
    pthread_t thr_arr2[32];
    void *thr_ret[32];
    int index[3] = {0};
    int index2[3] = {0};
    memset(g_conn_tht2, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < THR_NUM; i++) {
        // 创建顶点label
        char labelName[512];
        sprintf(labelName, "ip4forward_%d", i + 1);
        ret = GmcDropVertexLabel(stmt, labelName);

        char schema_path[512];
        sprintf(schema_path, "./schema_file/ip4forward_lpm4_%d.gmjson", i + 1);
        AW_FUN_Log(LOG_STEP, "schema path : %s ,label name : %s \n", schema_path, labelName);

        char *schema_json = NULL;
        readJanssonFile(schema_path, &schema_json);
        ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_json);

        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_dml_diff_label_test, (void *)&index[i]);
        usleep(8000);
    }

    for (int i = 0; i < THR_NUM; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
#else
    GmcConnT *conn_t[3] = {0};
    pthread_t thr_arr[3];
    pthread_t thr_arr2[3];
    void *thr_ret[3];
    int index[3] = {0};
    int index2[3] = {0};
    memset(g_conn_tht2, 0, sizeof(void *) * THR_NUM * 2);

    // 创建顶点labelcd 
    char labelName[512] = "ip4forward_1";
    GmcDropVertexLabel(stmt, labelName);
    char schema_path[512] = "./schema_file/ip4forward_lpm4_1.gmjson";
    char *schema_json = NULL;
    readJanssonFile(schema_path, &schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);

    pthread_create(&thr_arr[1], NULL, thread_dml_diff_label_test, (void *)&index[1]);
    usleep(8000);

    pthread_join(thr_arr[1], &thr_ret[1]);
#endif
}

// 21.同步建表，唯一lpm4索引多线程读取同一条数据及扫描同张表数据  【验收用例场景】
void *thread_lpm4_scan_test1(void *args)
{
    // 创建连接
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht2[conn_id], &g_stmt_tht2[conn_id]);
    EXPECT_EQ(GMERR_OK, res);

    // lpm4索引最长匹配扫描
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht2[conn_id], g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_tht2[conn_id], "ip4forward_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 24 + conn_id;
    ret = GmcSetIndexKeyValue(g_stmt_tht2[conn_id], 0, GMC_DATATYPE_UINT32, &conn_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht2[conn_id], 1, GMC_DATATYPE_UINT32, &conn_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht2[conn_id], 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_tht2[conn_id], 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht2[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t exp_mask_len = 24;
    ret = test_index_scan_ip4forward(g_stmt_tht2[conn_id], "ip4forward_lpm", &exp_mask_len, thread_id, "lpm4 scan", 1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2, scan_end);
    return NULL;
}
void *thread_fullLabel_scan_test(void *args)
{
    // 创建连接
    int conn_id = *((int *)args);
    res = testGmcConnect(&g_conn_tht2[conn_id], &g_stmt_tht2[conn_id]);
    EXPECT_EQ(GMERR_OK, res);

    // 全表扫描
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht2[conn_id], g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcSetIndexKeyName(g_stmt_tht2[conn_id], NULL);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht2[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t exp_mask_len = 24;
    ret = test_index_scan_ip4forward(g_stmt_tht2[conn_id], NULL, &exp_mask_len, thread_id, "full scan", 0);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(129, scan_end);

    // 断开连接
    ret = testGmcDisconnect(g_conn_tht2[conn_id], g_stmt_tht2[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return ((void *)0);
}
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_021)
{
    // 同步建表
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 写入数据
    int oper_nums = 128;
    test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, 24);
    // test_insert_vertex_ip4forward_lpm4(stmt, g_labelName, 0, 0, oper_nums, 25);

    // 多线程： [1] 唯一lpm索引扫描  [2] 全表扫描
    GmcConnT *conn_t[32] = {0};
    pthread_t thr_arr[32];
    pthread_t thr_arr2[32];
    void *thr_ret[32];
    int index[3] = {0};
    int index2[3] = {0};
    memset(g_conn_tht2, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < 1; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_lpm4_scan_test1, (void *)&index[i]);
    }
    sleep(1);
    for (int i = 0; i < 1; i++) {
        index2[i] = i;
        pthread_create(&thr_arr2[i], NULL, thread_fullLabel_scan_test, (void *)&index2[i]);
    }
    for (int i = 0; i < 1; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    for (int i = 0; i < 1; i++) {
        pthread_join(thr_arr2[i], &thr_ret[i]);
    }
}

// 22.同步建表，主键修改一张表的记录，唯一lpm4索引多线程扫描同张表数据  【验收用例场景】
void *thread_lpm4_scan_test(void *args)
{
    // 创建连接
    int conn_id = *((int *)args);
    GmcConnT *conn;
    GmcStmtT *stmt;
    int res = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, res);

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    // lpm4索引最长匹配扫描
    uint32_t dest_ip_addr = trans_ip("***********");
    uint8_t mask_len = 24 + conn_id;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &conn_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &conn_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "ip4forward_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t exp_mask_len = 24 + conn_id;
    ret = test_index_scan_ip4forward(stmt, "ip4forward_lpm", &exp_mask_len, thread_id, "lpm4 scan", 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_022)
{
    // 同步建表
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_lpm4.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 多线程： [1] 主键修改数据  [2] lpm4索引扫描
    GmcConnT *conn_t[32] = {0};
    pthread_t thr_arr[32];
    pthread_t thr_arr2[32];
    void *thr_ret[32];
    int index[3] = {0};
    int index2[3] = {0};
    memset(g_conn_tht2, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < 1; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_pk_update_lpm4_fields_test, (void *)&index[i]);
    }

    for (int i = 0; i < 1; i++) {
        index2[i] = i;
        pthread_create(&thr_arr2[i], NULL, thread_lpm4_scan_test, (void *)&index2[i]);
    }
    sleep(4);
    for (int i = 0; i < 1; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    sleep(3);
    for (int i = 0; i < 1; i++) {
        pthread_join(thr_arr2[i], &thr_ret[i]);
    }
}

struct fibData {
    const char *ip;
    uint8_t mastLen;
};

// lpm索引读 AW_FUN_Log(LOG_INFO, ">>lpm read \n");
void lpmRead(GmcStmtT *stmt)
{
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint32_t dest_ip_addr = trans_ip("*********");
    uint8_t mask_len = 0;

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "ip4forward_lpm");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void asyncBatchInsert(GmcStmtT *stmtAsync, GmcStmtT *resdStmt, fibData dataArr[], int nDataSize, int loopStart, int loopEnd)
{
    int operNum = 0;
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint8_t mask_len = 0;
    AsyncUserDataT data = {0};

    int nDataSum = nDataSize / sizeof(fibData);
    ASSERT_LE(loopEnd, nDataSum);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_async,  &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    operNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmtAsync, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t loop = loopStart; loop < loopEnd; loop++) {
        uint32_t trans_val = trans_ip(dataArr[loop].ip);
        mask_len = dataArr[loop].mastLen;

        set_vtxLabel_pk_field_ip4forward(stmtAsync, vr_id, vrf_index, trans_val, mask_len);
        ret = GmcBatchAddDML(batch, stmtAsync);
        ASSERT_EQ(GMERR_OK, ret);
        operNum++;
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    lpmRead(resdStmt);  // lpm read
    res = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, res);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(1, data.totalNum);
    EXPECT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);
}


void asyncBatchDelete(GmcStmtT *stmtAsync, GmcStmtT *resdStmt, fibData dataArr[], int nDataSize, int loopStart, int loopEnd)
{
    int operNum = 0;
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint8_t mask_len = 0;
    AsyncUserDataT data = {0};

    int nDataSum = nDataSize / sizeof(fibData);
    ASSERT_LE(loopEnd, nDataSum);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);

    operNum = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t loop = loopStart; loop < loopEnd; loop++) {
        uint32_t trans_val = trans_ip(dataArr[loop].ip);
        mask_len = dataArr[loop].mastLen;

        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 2, GMC_DATATYPE_UINT32, &trans_val, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));   
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_async, "primary_key");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_async);
        ASSERT_EQ(GMERR_OK, ret);
        operNum++;
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    lpmRead(resdStmt);  // lpm read
    res = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, res);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(1, data.totalNum);
    EXPECT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);
}

void asyncInsert(GmcStmtT *stmtAsync, GmcStmtT *resdStmt, fibData dataFib)
{
    AsyncUserDataT data = {0};
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint8_t mask_len = 0;

    int ret = testGmcPrepareStmtByLabelName(stmtAsync, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t trans_val = trans_ip(dataFib.ip);
    mask_len = dataFib.mastLen;
    set_vtxLabel_pk_field_ip4forward(stmtAsync, vr_id, vrf_index, trans_val, mask_len);
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &data;
    ret = GmcExecuteAsync(stmtAsync, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    lpmRead(resdStmt);  // lpm read
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(1, data.affectRows);
}

void asyncDelete(GmcStmtT *stmtAsync, GmcStmtT *resdStmt, fibData dataFib)
{
    AsyncUserDataT data = {0};
    uint32_t vr_id = 0;
    uint32_t vrf_index = 0;
    uint8_t mask_len = 0;

    ret = testGmcPrepareStmtByLabelName(stmtAsync, g_labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t trans_val = trans_ip(dataFib.ip);
    mask_len = dataFib.mastLen;
    ret = GmcSetIndexKeyValue(stmtAsync, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtAsync, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtAsync, 2, GMC_DATATYPE_UINT32, &trans_val, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtAsync, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));   
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtAsync, "primary_key");
    EXPECT_EQ(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.insertCb = delete_vertex_callback;
    deleteRequestCtx.userData = &data;
    ret = GmcExecuteAsync(stmtAsync, &deleteRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    lpmRead(resdStmt);  // lpm read
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(1, data.affectRows);
}

// 23. 模拟下游lpm流程 core掉问题
TEST_F(LPM4_scene_test, DML_050_LPM4_scene_023)
{
    // 创建异步连接
    res = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, res);

    // 异步创建vertexLabel
    AsyncUserDataT data = {0};
    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward_r21_1.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, schema_json, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_json);
    res = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, res);
    ASSERT_EQ(GMERR_OK, data.status);

    int nCycle = 0;
    while (nCycle < 10) {
        AW_FUN_Log(LOG_INFO, "\n---------  cycle: %d\n", nCycle++);

        // 异步批量写入数据 1  printf("\n\n---------  batch write 1\n");
        fibData dataArr[] = {
            {"80.0.0.0", 8},
            {"**********", 24},
            {"***********", 32},
            {"************", 32},
            {"*********", 8},
            {"127.0.0.1", 32},
            {"***************", 32},
            {"***************", 32},
            {"**********", 24},
            {"**********", 32},
            {"************", 32},
            {"14.11.12.0", 24},
            {"14.11.12.1", 32},
            {"14.11.12.255", 32},
            {"15.11.11.0", 24},
            {"15.11.11.1", 32},
            {"15.11.11.255", 32}
        };

        asyncBatchInsert(g_stmt_async, stmt, dataArr, sizeof(dataArr), 0, 8);
        asyncBatchInsert(g_stmt_async, stmt, dataArr, sizeof(dataArr), 8, 11);
        asyncBatchInsert(g_stmt_async, stmt, dataArr, sizeof(dataArr), 11, 17);

        // 异步批量删除数据 1 printf("\n\n---------  batch delete 1\n");
        fibData dataArr2[] = {
            {"15.11.11.0", 24},
            {"15.11.11.1", 32},
            {"15.11.11.255", 32},
            {"14.11.12.0", 24},
            {"14.11.12.1", 32},
            {"14.11.12.255", 32},
            {"**********", 24},
            {"**********", 32},
            {"************", 32}
        };

        asyncBatchDelete(g_stmt_async, stmt, dataArr2, sizeof(dataArr2), 0, 3);
        asyncBatchDelete(g_stmt_async, stmt, dataArr2, sizeof(dataArr2), 3, 6);
        asyncBatchDelete(g_stmt_async, stmt, dataArr2, sizeof(dataArr2), 6, 9);

        // 异步单写 printf("\n\n---------  signal write 1\n");
        fibData dataFib = {"1.1.1.1", 32};
        asyncInsert(g_stmt_async, stmt, dataFib);

        // 异步批量写入数据 2 printf("\n\n---------  batch write 2\n");
        fibData dataArr3[] = {
            {"200.1.1.0", 24},
            {"200.1.1.1", 32},
            {"200.1.1.255", 32},
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32},
            {"201.1.1.0", 24},
            {"201.1.1.1", 32},
            {"*********55", 32},
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32} 
        };
        asyncBatchInsert(g_stmt_async, stmt, dataArr3, sizeof(dataArr3), 0, 3);
        asyncBatchInsert(g_stmt_async, stmt, dataArr3, sizeof(dataArr3), 3, 6);
        asyncBatchInsert(g_stmt_async, stmt, dataArr3, sizeof(dataArr3), 6, 9);
        asyncBatchInsert(g_stmt_async, stmt, dataArr3, sizeof(dataArr3), 9, 12);

        // 异步批量删除数据 2 printf("\n\n---------  batch delete 2\n");
        fibData dataArr4[] = {
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32},
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32}
        };
        asyncBatchDelete(g_stmt_async, stmt, dataArr4, sizeof(dataArr4), 0, 6);

        // 异步批量写入数据 3 printf("\n\n---------  batch write 3\n");
        fibData dataArr5[] = {
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32},
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32}
        };
        asyncBatchInsert(g_stmt_async, stmt, dataArr5, sizeof(dataArr5), 0, 6);

        // 异步单删除 1 printf("\n\n---------  signal delete 1\n");
        asyncDelete(g_stmt_async, stmt, dataFib);
    
        // 异步批量删除数据 3 printf("\n\n---------  batch delete 3\n");
        fibData dataArr6[] = {
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32},
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32},
        };
        asyncBatchDelete(g_stmt_async, stmt, dataArr6, sizeof(dataArr6), 0, 6);

        // 异步单写 2 printf("\n\n---------  signal write 2\n");
        asyncInsert(g_stmt_async, stmt, dataFib);

        // 异步批量写入数据 4 printf("\n\n---------  batch write 4\n");
        fibData dataArr7[] = {
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32},
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32}
        };
        asyncBatchInsert(g_stmt_async, stmt, dataArr7, sizeof(dataArr7), 0, 6);

        // 异步单删除 2 printf("\n\n---------  signal delete 2\n");
        asyncDelete(g_stmt_async, stmt, dataFib);

        // 异步批量删除数据 4 printf("\n\n---------  batch delete 4\n");
        fibData dataArr8[] = {
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32},
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32},
            {"201.1.1.0", 24},
            {"201.1.1.1", 32},
            {"*********55", 32},
            {"200.1.1.0", 24},
            {"200.1.1.1", 32},
            {"200.1.1.255", 32}
        };
        asyncBatchDelete(g_stmt_async, stmt, dataArr8, sizeof(dataArr8), 0, 6);
        asyncBatchDelete(g_stmt_async, stmt, dataArr8, sizeof(dataArr8), 6, 9);
        asyncBatchDelete(g_stmt_async, stmt, dataArr8, sizeof(dataArr8), 9, 12);

        // 异步单写 3 printf("\n\n---------  signal write 3\n");
        asyncInsert(g_stmt_async, stmt, dataFib);

        // 异步批量写入数据 5 printf("\n\n---------  batch write 5\n");
        fibData dataArr9[] = {
            {"200.1.1.0", 24},
            {"200.1.1.1", 32},
            {"200.1.1.255", 32},
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32},
            {"201.1.1.0", 24},
            {"201.1.1.1", 32},
            {"*********55", 32},
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32}
        };
        asyncBatchInsert(g_stmt_async, stmt, dataArr9, sizeof(dataArr9), 0, 3);
        asyncBatchInsert(g_stmt_async, stmt, dataArr9, sizeof(dataArr9), 3, 6);
        asyncBatchInsert(g_stmt_async, stmt, dataArr9, sizeof(dataArr9), 6, 9);
        asyncBatchInsert(g_stmt_async, stmt, dataArr9, sizeof(dataArr9), 9, 12);

        // 异步批量删除数据 5 printf("\n\n---------  batch delete 5\n");
        fibData dataArr10[] = {
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32}
        };
        asyncBatchDelete(g_stmt_async, stmt, dataArr10, sizeof(dataArr10), 0, 3);

        // 异步批量写入数据 6 printf("\n\n---------  batch write 6\n");
        fibData dataArr11[] = {
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32}
        };
        asyncBatchInsert(g_stmt_async, stmt, dataArr11, sizeof(dataArr11), 0, 3);

        // 异步批量删除数据 6 printf("\n\n---------  batch delete 6\n");
        fibData dataArr12[] = {
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32},
            {"201.1.1.0", 24},
            {"201.1.1.1", 32},
            {"*********55", 32},
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32},
            {"200.1.1.0", 24},
            {"200.1.1.1", 32},
            {"200.1.1.255", 32}
        };
        asyncBatchDelete(g_stmt_async, stmt, dataArr12, sizeof(dataArr12), 0, 3);
        asyncBatchDelete(g_stmt_async, stmt, dataArr12, sizeof(dataArr12), 3, 6);
        asyncBatchDelete(g_stmt_async, stmt, dataArr12, sizeof(dataArr12), 6, 9);
        asyncBatchDelete(g_stmt_async, stmt, dataArr12, sizeof(dataArr12), 9, 12);

        // 异步单删除 3 printf("\n\n---------  signal delete 3\n");
        asyncDelete(g_stmt_async, stmt, dataFib);

        // 异步单写 4 printf("\n\n---------  signal write 4\n");
        asyncInsert(g_stmt_async, stmt, dataFib);

        // 异步批量写入数据 7 printf("\n\n---------  batch write 7\n");
        fibData dataArr13[] = {
            {"200.1.1.0", 24},
            {"200.1.1.1", 32},
            {"200.1.1.255", 32},
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32},
            {"201.1.1.0", 24},
            {"201.1.1.1", 32},
            {"*********55", 32},
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32}
        };
        asyncBatchInsert(g_stmt_async, stmt, dataArr13, sizeof(dataArr13), 0, 3);
        asyncBatchInsert(g_stmt_async, stmt, dataArr13, sizeof(dataArr13), 3, 6);
        asyncBatchInsert(g_stmt_async, stmt, dataArr13, sizeof(dataArr13), 6, 9);
        asyncBatchInsert(g_stmt_async, stmt, dataArr13, sizeof(dataArr13), 9, 12);

        // 异步批量删除数据 7 printf("\n\n---------  batch delete 7\n");
        fibData dataArr14[] = {
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32}
        };
        asyncBatchDelete(g_stmt_async, stmt, dataArr14, sizeof(dataArr14), 0, 3);

        // 异步批量写入数据 8 printf("\n\n---------  batch write 8\n");
        fibData dataArr15[] = {
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32}
        };
        asyncBatchInsert(g_stmt_async, stmt, dataArr15, sizeof(dataArr15), 0, 3);

        // 异步批量删除数据 8 printf("\n\n---------  batch delete 8\n");
        fibData dataArr16[] = {
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32},
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32},
            {"201.1.1.0", 24},
            {"201.1.1.1", 32},
            {"*********55", 32},
            {"200.1.1.0", 24},
            {"200.1.1.1", 32},
            {"200.1.1.255", 32}
        };
        asyncBatchDelete(g_stmt_async, stmt, dataArr16, sizeof(dataArr16), 0, 3);
        asyncBatchDelete(g_stmt_async, stmt, dataArr16, sizeof(dataArr16), 3, 6);
        asyncBatchDelete(g_stmt_async, stmt, dataArr16, sizeof(dataArr16), 6, 9);
        asyncBatchDelete(g_stmt_async, stmt, dataArr16, sizeof(dataArr16), 9, 12);

        // 异步单删除 4 printf("\n\n---------  signal delete 4\n");
        asyncDelete(g_stmt_async, stmt, dataFib);

        // 异步单写 5 printf("\n\n---------  signal write 5\n");
        asyncInsert(g_stmt_async, stmt, dataFib);

        // 异步批量写入数据 9 printf("\n\n---------  batch write 9\n");
        fibData dataArr17[] = {
            {"200.1.1.0", 24},
            {"200.1.1.1", 32},
            {"200.1.1.255", 32},
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32},
            {"201.1.1.0", 24},
            {"201.1.1.1", 32},
            {"*********55", 32},
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32}
        };
        asyncBatchInsert(g_stmt_async, stmt, dataArr17, sizeof(dataArr17), 0, 3);
        asyncBatchInsert(g_stmt_async, stmt, dataArr17, sizeof(dataArr17), 3, 6);
        asyncBatchInsert(g_stmt_async, stmt, dataArr17, sizeof(dataArr17), 6, 9);
        asyncBatchInsert(g_stmt_async, stmt, dataArr17, sizeof(dataArr17), 9, 12);

        // 异步批量删除数据 9 printf("\n\n---------  batch delete 9\n");
        fibData dataArr18[] = {
            {"101.1.1.0", 24},
            {"101.1.1.1", 32},
            {"101.1.1.255", 32},
            {"201.1.1.0", 24},
            {"201.1.1.1", 32},
            {"*********55", 32},
            {"100.1.1.0", 24},
            {"100.1.1.1", 32},
            {"100.1.1.255", 32}
        };
        asyncBatchDelete(g_stmt_async, stmt, dataArr18, sizeof(dataArr18), 0, 3);
        asyncBatchDelete(g_stmt_async, stmt, dataArr18, sizeof(dataArr18), 3, 6);
        asyncBatchDelete(g_stmt_async, stmt, dataArr18, sizeof(dataArr18), 6, 9);

        system("gmsysview count");

        // 清空数据
        ret = GmcTruncateVertexLabel(stmt, g_labelName);
        ASSERT_EQ(GMERR_OK, ret);
    
        sleep(2);
    }

    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);
}

