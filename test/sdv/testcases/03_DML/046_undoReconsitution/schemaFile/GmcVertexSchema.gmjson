[{"type": "record", "name": "T39", "fields": [{"name": "F7", "type": "int64", "nullable": false}, {"name": "F0", "type": "int8", "nullable": false}, {"name": "F1", "type": "uint8", "nullable": false}, {"name": "F2", "type": "int16", "nullable": false}, {"name": "F3", "type": "uint16", "nullable": false}, {"name": "F4", "type": "int32", "nullable": false}, {"name": "F5", "type": "uint32", "nullable": false}, {"name": "F6", "type": "boolean", "nullable": false}, {"name": "F8", "type": "uint64", "nullable": false}, {"name": "F9", "type": "float", "nullable": false}, {"name": "F10", "type": "double", "nullable": false}, {"name": "F11", "type": "time", "nullable": false}, {"name": "F12", "type": "string", "nullable": false, "size": 8100}, {"name": "F13", "type": "char", "nullable": true}, {"name": "F14", "type": "uchar", "nullable": true}, {"name": "F15", "type": "bytes", "size": 7, "nullable": true}, {"name": "F16", "type": "fixed", "size": 7, "nullable": true}], "keys": [{"node": "T39", "name": "T39_K0", "fields": ["F7"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T39", "name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F5"], "constraints": {"unique": true}}]}]