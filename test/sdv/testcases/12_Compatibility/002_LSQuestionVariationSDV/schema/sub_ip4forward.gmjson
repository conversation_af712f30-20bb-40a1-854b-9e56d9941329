[{"comment": "前缀表，对应7#表", "version": "2.0", "type": "record", "name": "sub_ip4forward", "config": {"check_validity": true}, "max_record_count": 4000000, "fields": [{"name": "vr_id", "type": "uint32", "nullable": false, "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "nullable": false, "comment": "VpnInstace索引"}, {"name": "dest_ip_addr", "type": "uint32", "nullable": false, "comment": "目的地址"}, {"name": "mask_len", "type": "uint8", "nullable": false, "comment": "掩码长度"}, {"name": "nhp_group_flag", "type": "uint8", "nullable": false, "comment": "标识Nhp或NhpG"}, {"name": "qos_profile_id", "type": "uint16", "nullable": false, "comment": "QosID"}, {"name": "primary_label", "type": "uint32", "nullable": false, "comment": "标签"}, {"name": "attribute_id", "type": "uint32", "nullable": false, "comment": "属性ID"}, {"name": "nhp_group_id", "type": "uint32", "nullable": false, "comment": "下一跳索引还是下一跳组索引，根据nhp_group_flag决定"}, {"name": "route_flags", "type": "uint32", "nullable": false, "comment": "路由标记"}, {"name": "flags", "type": "uint32", "nullable": false, "comment": "标志(path完备性)"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "sub_ip4forward", "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "nhpgroupid_localhash_key", "index": {"type": "hashcluster"}, "node": "sub_ip4forward", "fields": ["nhp_group_id"], "constraints": {"unique": false}, "comment": "根据nhp_group_id索引"}, {"name": "primary_label_localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "sub_ip4forward", "fields": ["primary_label"], "constraints": {"unique": false}, "comment": "根据primary_label索引"}, {"name": "local_key", "index": {"type": "local"}, "node": "sub_ip4forward", "fields": ["attribute_id", "primary_label"], "comment": "根据local类型key索引"}]}]