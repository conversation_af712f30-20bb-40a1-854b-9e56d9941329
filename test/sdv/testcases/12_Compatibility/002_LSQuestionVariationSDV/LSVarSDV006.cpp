/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :DTS2022042513337问题单转换SDV用例
 RU模式下，带有排序索引的表，插入2K数据。开启3个线程按key多次扫描，
 其中开启一个线程，随机修改数据，直到扫描线程结束。
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2022/07/12
**************************************************************************** */

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"

#include "test.h"

class LSQuestionVariationSDV006 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void LSQuestionVariationSDV006::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh 'isFastReadUncommitted=1'");
    system("sh $TEST_HOME/tools/modifyCfg.sh 'enableTableLock=0'");
    system("sh $TEST_HOME/tools/modifyCfg.sh 'workerHungThreshold=6,200,300'");
    system("sh $TEST_HOME/tools/start.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void LSQuestionVariationSDV006::TearDownTestCase()
{
    int ret = close_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    testEnvClean();
}

void LSQuestionVariationSDV006::SetUp()
{
    // 创表
    int ret = CreateLabelMiniLpm();
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void LSQuestionVariationSDV006::TearDown()
{
    AW_CHECK_LOG_END();
    TestDropVertexLabel(g_labelNameIp4fowardMiniLpm);
}

/* ****************************************************************************
 Description  : DTS2022042513337问题单转换SDV用例
 RU模式下，带有排序索引的表，插入2K数据。开启3个线程按key多次扫描，
 其中开启一个线程，随机修改数据，直到扫描线程结束
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(LSQuestionVariationSDV006, Compatibility_002_006)
{
    pthread_t graphMinThr1, graphMinThr2, graphMinThr3, graphMinThr4;
    // 预制数据
    TestLpmPrepareData(0, MINI_LABEL_PREPARE_DATA);
    int ret = pthread_create(&graphMinThr1, NULL, ReadThreadIp4fowardMiniLpm, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_create(&graphMinThr2, NULL, GetRecordLocalhashMiniLpm, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_create(&graphMinThr3, NULL, UpdateThreadIp4fowardMiniLpm, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_create(&graphMinThr4, NULL, LocalhashScanMiniLpm, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    ret = pthread_join(graphMinThr1, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_join(graphMinThr2, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_join(graphMinThr3, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_join(graphMinThr4, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}
