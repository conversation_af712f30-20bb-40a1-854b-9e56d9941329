[{"type": "list", "name": "list_deep_#(0)#", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "uint32", "nullable": true, "default": 1}, {"name": "F3", "type": "uint32", "nullable": true, "default": 2}], "keys": [{"name": "pk", "fields": ["PID", "F1"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]