[{"name": "root_to_list_1", "source_vertex_label": "root", "dest_vertex_label": "list_1", "source_node_path": "/con_1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_list_2", "source_vertex_label": "root", "dest_vertex_label": "list_2", "source_node_path": "/con_2", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_list_3", "source_vertex_label": "root", "dest_vertex_label": "list_3", "source_node_path": "/con_3", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_list_4", "source_vertex_label": "root", "dest_vertex_label": "list_4", "source_node_path": "/con_4", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_leaflist_1", "source_vertex_label": "root", "dest_vertex_label": "leaflist_1", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "root_to_leaflist_2", "source_vertex_label": "root", "dest_vertex_label": "leaflist_2", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]