[{"type": "container", "name": "Con_root", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "string", "pattern": ["[ -~]*"]}, {"name": "F1", "type": "string", "pattern": ["([^?]*)"]}, {"name": "F2", "type": "string", "pattern": ["\\*|[^\\*].*"]}, {"name": "F3", "type": "string", "pattern": ["$0$.*|$1$[a-zA-Z0-9./]{1,8}$[a-zA-Z0-9./]{22}|$5$(rounds=\\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{43}|$6$(rounds=\\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{86}", "$0$.*|$1$[a-zA-Z0-9./]{1,8}$[a-zA-Z0-9./]{22}|$5$(rounds=\\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{43}|$6$(rounds=\\d+$)?[a-zA-Z0-9./]{1,16}$[a-zA-Z0-9./]{86}|$3b$.*"]}, {"name": "F4", "type": "string", "pattern": ["([01]?[0-9]|2[0-3]):[0-5][0-9]-([01]?[0-9]|2[0-3]):[0-5][0-9]|all"]}, {"name": "F5", "type": "string", "pattern": ["([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]-([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]"]}, {"name": "F6", "type": "string", "pattern": ["(([0-1](\\.[1-3]?[0-9]))|(2\\.(0|([1-9]\\d*))))(\\.(0|([1-9]\\d*)))*"]}, {"name": "F7", "type": "string", "pattern": ["([0-7](-[0-7])?(,[0-7](-[0-7])?)*)|any"]}, {"name": "F8", "type": "string", "pattern": ["(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(%[\\p{N}\\p{L}]+)?", "[0-9\\.]*"]}, {"name": "F9", "type": "string", "pattern": ["(([0-9]|[0-5][0-9]|[6][0-3])(-([0-9]|[0-5][0-9]|[6][0-3]))?(,([0-9]|[0-5][0-9]|[6][0-3])(-([0-9]|[0-5][0-9]|[6][0-3]))?)*)|any"]}, {"name": "F10", "type": "string", "pattern": ["(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])/(([0-9])|([1-2][0-9])|(3[0-2]))|((:|[0-9a-fA-F]{0,4}):)([0-9a-fA-F]{0,4}:){0,5}((([0-9a-fA-F]{0,4}:)?(:|[0-9a-fA-F]{0,4}))|(((25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])))(/(([0-9])|([0-9]{2})|(1[0-1][0-9])|(12[0-8])))|(([^:]+:){6}(([^:]+:[^:]+)|(.*\\..*)))|((([^:]+:)*[^:]+)?::(([^:]+:)*[^:]+)?)(/.+)"]}, {"name": "F11", "type": "string", "pattern": ["(([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(%[\\p{N}\\p{L}]+)?|((:|[0-9a-fA-F]{0,4}):)([0-9a-fA-F]{0,4}:){0,5}((([0-9a-fA-F]{0,4}:)?(:|[0-9a-fA-F]{0,4}))|(((25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])))(%[\\p{N}\\p{L}]+)?|(([^:]+:){6}(([^:]+:[^:]+)|(.*\\..*)))|((([^:]+:)*[^:]+)?::(([^:]+:)*[^:]+)?)(%.+)?|((([a-zA-Z0-9_]([a-zA-Z0-9\\-_]){0,61})?[a-zA-Z0-9]\\.)*([a-zA-Z0-9_]([a-zA-Z0-9\\-_]){0,61})?[a-zA-Z0-9]\\.?)|\\."]}, {"name": "F12", "type": "string", "pattern": ["([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\.([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])){3}|(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:))"]}, {"name": "F13", "type": "string", "pattern": ["([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-3][0-9][0-9][0-9]|40[0-8][0-9]|409[0-5])([,-]([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-3][0-9][0-9][0-9]|40[0-8][0-9]|409[0-5]))*"]}, {"name": "F14", "type": "string", "pattern": ["((:|[0-9a-fA-F]{0,4}):)([0-9a-fA-F]{0,4}:){0,5}((([0-9a-fA-F]{0,4}:)?(:|[0-9a-fA-F]{0,4}))|(((25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])))(/(([0-9])|([0-9]{2})|(1[0-1][0-9])|(12[0-8])))", "(([^:]+:){6}(([^:]+:[^:]+)|(.*\\..*)))|((([^:]+:)*[^:]+)?::(([^:]+:)*[^:]+)?)(/.+)"]}, {"name": "F15", "type": "string", "pattern": ["((:|[0-9a-fA-F]{0,4}):)([0-9a-fA-F]{0,4}:){0,5}((([0-9a-fA-F]{0,4}:)?(:|[0-9a-fA-F]{0,4}))|(((25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9]?[0-9])))(%[\\p{N}\\p{L}]+)?", "(([^:]+:){6}(([^:]+:[^:]+)|(.*\\..*)))|((([^:]+:)*[^:]+)?::(([^:]+:)*[^:]+)?)(%.+)?"]}, {"name": "F16", "type": "string", "pattern": ["([0-9a-fA-F]{2}(:[0-9a-fA-F]{2})*)?"]}, {"name": "F17", "type": "string", "pattern": ["0x[A-Fa-f0-9]{4}|any|\\d+"]}, {"name": "F18", "type": "string", "pattern": ["([1-9][0-9]{0,3}(-[1-9][0-9]{0,3})?(,[1-9][0-9]{0,3}(-[1-9][0-9]{0,3})?)*)"]}, {"name": "F19", "type": "string", "pattern": ["(([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-3][0-9][0-9][0-9]|40[0-8][0-9]|409[0-4])([,-]([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-3][0-9][0-9][0-9]|40[0-8][0-9]|409[0-4]))*)?|vlan-id-is-a-parameter|any|priority-tagged"]}, {"name": "F20", "type": "string", "pattern": ["((([a-zA-Z0-9_]([a-zA-Z0-9\\-_]){0,61})?[a-zA-Z0-9]\\.)*([a-zA-Z0-9_]([a-zA-Z0-9\\-_]){0,61})?[a-zA-Z0-9]\\.?)|\\."]}, {"name": "F21", "type": "string", "pattern": ["[a-zA-Z]{4}[0-9a-fA-F]{8}"]}, {"name": "F22", "type": "string", "pattern": ["\\d{1,2}:\\d{1,2}:\\d{1,2}", "(([0-1][0-9])|(2[0-3])|[0-9]):([0-5][0-9]|[0-9]):([0-5][0-9]|[0-9])"]}, {"name": "F23", "type": "string", "pattern": ["\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?(Z|[\\+\\-]\\d{2}:\\d{2})"]}, {"name": "F24", "type": "string", "pattern": ["LoopBack([1-2]{0,1}[0-9]|3[0-1])"]}, {"name": "F25", "type": "string", "pattern": ["((mon(,tues)?(,wed)?(,thu)?(,fri)?(,sat)?(,sun)?)|(tues(,wed)?(,thu)?(,fri)?(,sat)?(,sun)?)|(wed(,thu)?(,fri)?(,sat)?(,sun)?)|(thu(,fri)?(,sat)?(,sun)?)|(fri(,sat)?(,sun)?)|(sat(,sun))|(sun))|off-day|daily|working-day"]}, {"name": "F26", "type": "string", "pattern": ["Vlanif(([1-9][0-9]{0,2})|([1-3][0-9]{3})|(40[0-8][0-9])|(409[0-3]))"]}, {"name": "F27", "type": "string", "pattern": ["[a-zA-Z]{4}[0-9a-fA-F]{8}"]}, {"name": "F28", "type": "string", "pattern": ["[a-zA-Z]{4}[0-9a-fA-F]{8}"]}, {"name": "F29", "type": "string", "pattern": ["[a-zA-Z]{4}[0-9a-fA-F]{8}"]}, {"name": "F30", "type": "string", "pattern": ["[a-zA-Z]{4}[0-9a-fA-F]{8}"]}, {"name": "F31", "type": "string", "pattern": ["[a-zA-Z]{4}[0-9a-fA-F]{8}"]}, {"name": "F32", "type": "string", "pattern": ["[a-zA-Z]{4}[0-9a-fA-F]{8}"]}, {"name": "Con_1", "type": "container", "fields": [{"name": "A1", "type": "uint32"}]}, {"name": "Choice_1", "type": "choice", "fields": [{"name": "Case_2", "type": "case", "fields": [{"name": "B1", "type": "string"}]}]}], "keys": [{"node": "Con_root", "name": "CON_0_PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "LP", "type": "uint32", "nullable": false}, {"name": "L1", "type": "string"}], "keys": [{"node": "list_1", "name": "list_1_PK", "fields": ["PID", "LP"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]