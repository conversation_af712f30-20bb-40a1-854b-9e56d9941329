alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.alias_ListOne:update[(pri<PERSON>ey(PID:1,F0:101), preKey(PID:1,F0:100)),(pri<PERSON>ey(PID:1,F0:101), preKey(PID:1,F0:100))]
alias_ListOne.Listchoice:update
Listchoice.ListchoiceCaseTwo:create
ListchoiceCaseTwo.F0:create(301)
ListchoiceCaseTwo.F1:create(456)
ListchoiceCaseTwo.F4:create(NIL:8)
alias_ContainerOne.alias_ListOne:update[(pri<PERSON>ey(PID:1,F0:102), preKey(PID:1,F0:101)),(pri<PERSON>ey(PID:1,F0:102), preKey(PID:1,F0:101))]
alias_ListOne.Listchoice:create
Listchoice.ListchoiceCaseTwo:create
ListchoiceCaseTwo.F0:create(301)
ListchoiceCaseTwo.F1:create(456)
ListchoiceCaseTwo.F4:create(NIL:8)
alias_ContainerOne.alias_ListOne:create[(priKey(PID:1,F0:103), preKey(PID:1,F0:102)),(NULL)]
alias_ListOne.Listchoice:create
Listchoice.ListchoiceCaseTwo:create
ListchoiceCaseTwo.F0:create(301)
ListchoiceCaseTwo.F1:create(456)
ListchoiceCaseTwo.F4:create(NIL:8)
