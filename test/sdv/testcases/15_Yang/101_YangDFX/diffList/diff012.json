alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.F0:update(101,100)
alias_ContainerOne.F1:update(101,100)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:100)),(priKey(PID:1,F0:100))]
alias_ListOne.F1:update(101,100)
alias_ListOne.F2:update(101,234)
alias_ListOne.Listchoice:update
Listchoice.ListchoiceCase:update
ListchoiceCase.F0:update(101,100)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:101), preKey(PID:1,F0:100)),(priKey(PID:1,F0:101), preKey(PID:1,F0:100))]
alias_ListOne.F1:update(102,101)
alias_ListOne.F2:update(102,234)
alias_ListOne.Listchoice:update
Listchoice.ListchoiceCase:remove
ListchoiceCase.F0:remove(101)
ListchoiceCase.F1:remove(456)
ListchoiceCase.F4:remove(NIL:8)
Listchoice.ListchoiceCaseTwo:create
ListchoiceCaseTwo.F0:create(102)
ListchoiceCaseTwo.F1:create(456)
ListchoiceCaseTwo.F4:create(NIL:8)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:102), preKey(PID:1,F0:101)),(priKey(PID:1,F0:102), preKey(PID:1,F0:101))]
alias_ListOne.F1:update(103,102)
alias_ListOne.F2:update(103,234)
alias_ListOne.Listchoice:create
Listchoice.ListchoiceCase:create
ListchoiceCase.F0:create(103)
ListchoiceCase.F1:create(456)
ListchoiceCase.F4:create(NIL:8)
