[{"name": "ContainerListB", "source_vertex_label": "ContainerOneB", "dest_vertex_label": "ListOneB", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ContainerLeafListB", "source_vertex_label": "ContainerOneB", "dest_vertex_label": "LeafListB", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ListListTwoB", "source_vertex_label": "ListOneB", "dest_vertex_label": "ListTwoB", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]