/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
/*****************************************************************************
 Description  : 图模型支持值校验
 Notes        :
 History      :
 Author       : yangfuwen ywx1060383
 Modification :
*****************************************************************************/

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "YangDMLOperation_test.h"

#define LABELNAME_MAX_LENGTH 128
#define SCHEMA_JSON_SIZE 1024

AsyncUserDataT data = {0};
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
int32_t ret;
GmcBatchT *batch = NULL;
char *test_schema1 = NULL;
char g_labelName[LABELNAME_MAX_LENGTH] = "T20_all_type";
char g_T20_labelName[LABELNAME_MAX_LENGTH] = "T20";
char lalable_name_PK1[] = "T20_PK";
char g_configJson[128] = "{\"max_record_count\" : 10000, \"isFastReadUncommitted\" : 0}";

class DML_Asyn_test : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh ");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    };

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    };
    virtual void SetUp();
    virtual void TearDown();
};

void DML_Asyn_test::SetUp()
{
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    g_mSTrxConfig.readOnly = false;
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "Namespace_004_3_A";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schema_file/all_constraints_type_dml_schema.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(test_schema1);
    AW_CHECK_LOG_BEGIN();
}

void DML_Asyn_test::TearDown()
{
    AW_CHECK_LOG_END();
    ret = GmcDropVertexLabelAsync(g_stmt_async, g_labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);

    const char *namespace1 = "Namespace_004_3_A";
    AsyncUserDataT userData = {0};
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

// 075.整型，浮点型数据定义range约束，String定义length和pattern，invert_pattern约束，异步insert符合定义范围的数据
TEST_F(DML_Asyn_test, Yang_004_075)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetVertexProperty_PK(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"GMDBB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);

    GmcBatchDestroy(batch);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 076.整型，浮点型数据定义range约束，String定义length和pattern，invert_pattern约束，异步replace_insert符合定义范围的数据
TEST_F(DML_Asyn_test, Yang_004_076)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetVertexProperty_PK(g_stmt_async, 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"123456789", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"ABCC", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 077.整型，浮点型数据定义range约束，String定义length和pattern，invert_pattern约束，异步merge_insert符合定义范围的数据
TEST_F(DML_Asyn_test, Yang_004_077)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f0_value = 9;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_OK, ret);
    char Pk_Name[] = "T20_PK";
    ret = GmcSetIndexKeyName(g_stmt_async, Pk_Name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetVertexProperty_PK(g_stmt_async, 4, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 4, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"123456789", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"DEF", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 078.整型，浮点型数据定义range约束，String定义length和pattern，invert_pattern约束，异步update符合定义范围的数据
TEST_F(DML_Asyn_test, Yang_004_078)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetVertexProperty_PK(g_stmt_async, 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"12345678", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"GMDBB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f0_value = 9;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_OK, ret);
    char Pk_Name[] = "T20_PK";
    ret = GmcSetIndexKeyName(g_stmt_async, Pk_Name);
    EXPECT_EQ(GMERR_OK, ret);

    // merge_update设置属性值
    ret = testYangSetVertexProperty_PK(g_stmt_async, 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 5, GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"01234567891",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"GMDBB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 079.整型，浮点型数据定义range约束，String定义length和pattern，invert_pattern约束，异步replace_update符合定义范围的数据
TEST_F(DML_Asyn_test, Yang_004_079)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetVertexProperty_PK(g_stmt_async, 9, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 9, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"12345678", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"GMDBB", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "replace_update");
    ret = testYangSetVertexProperty_PK(g_stmt_async, 9, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"0123456789012",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"GMDBB", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 080.整型，浮点型数据定义range约束，String定义length和pattern，invert_pattern约束，异步merge_update符合定义范围的数据
TEST_F(DML_Asyn_test, Yang_004_080)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetVertexProperty_PK(g_stmt_async, 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 9, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"12345678", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"GMDBB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);

    AW_FUN_Log(LOG_STEP, "merge_update");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f0_value = 9;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_OK, ret);
    char Pk_Name[] = "T20_PK";
    ret = GmcSetIndexKeyName(g_stmt_async, Pk_Name);
    EXPECT_EQ(GMERR_OK, ret);

    // merge_update设置属性值
    ret = testYangSetVertexProperty_PK(g_stmt_async, 9, GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 5, GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"1234", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"GMDBB", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 081.Range与default共同设置，异步创建Vertex，不设置该字段，异步插入数据
TEST_F(DML_Asyn_test, Yang_004_081)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schema_file/range_length_and_default_schema.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    AW_FUN_Log(LOG_STEP, "建表");
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_T20_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testYangSetVertexProperty_PK(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    ret = GmcDropVertexLabelAsync(g_stmt_async, g_T20_labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 082.Range与default共同设置，异步创建Vertex，设置该字段在range范围上，异步插入数据
TEST_F(DML_Asyn_test, Yang_004_082)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schema_file/range_length_and_default_schema.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    AW_FUN_Log(LOG_STEP, "建表");
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_T20_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testYangSetVertexProperty_PK(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f1_value = 8;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f1_value, sizeof(int32_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F2", (char *)"123", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    ret = GmcDropVertexLabelAsync(g_stmt_async, g_T20_labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 异常场景
// 083.整型，浮点型数据定义range约束，String定义length约束，异步insert不符合定义范围的数据
TEST_F(DML_Asyn_test, Yang_004_083)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    uint32_t PK_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int8_t f1_value = -1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT8, &f1_value, sizeof(int8_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint8_t f2_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT8, &f2_value, sizeof(uint8_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int16_t f3_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT16, &f3_value, sizeof(int16_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint16_t f4_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT16, &f4_value, sizeof(uint16_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int32_t f5_value = 100;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f5_value, sizeof(int32_t), "F5",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int64_t f6_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t), "F6",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint64_t f7_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t), "F7",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    float f8_value = 0.1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_FLOAT, &f8_value, sizeof(f8_value), "F8",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    double f9_value = 0.9;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f10_value = (char *)"0123456789";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f10_value, (strlen(f10_value)), "F10",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, (char *)"", (strlen(f10_value)), "F10",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    char *f11_value = (char *)"GMDBB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f11_value, (strlen(f11_value)), "F11",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f12_value = (char *)"GMDB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f12_value, (strlen(f12_value)), "F12",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 084.整型，浮点型数据定义range约束，String定义length约束，异步replace_insert不符合定义范围的数据
TEST_F(DML_Asyn_test, Yang_004_084)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    uint32_t PK_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int8_t f1_value = -1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT8, &f1_value, sizeof(int8_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint8_t f2_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT8, &f2_value, sizeof(uint8_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int16_t f3_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT16, &f3_value, sizeof(int16_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint16_t f4_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT16, &f4_value, sizeof(uint16_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int32_t f5_value = 100;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f5_value, sizeof(int32_t), "F5",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int64_t f6_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t), "F6",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint64_t f7_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t), "F7",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    float f8_value = 0.9999;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_FLOAT, &f8_value, sizeof(f8_value), "F8",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    f8_value = 9.0001;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_FLOAT, &f8_value, sizeof(f8_value), "F8",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    double f9_value = 0.9999;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    f9_value = 9.0001;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    f9_value = 20.00;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    f9_value = 20.0110;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    f9_value = 29.99;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f10_value = (char *)"01234567890123";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f10_value, (strlen(f10_value)), "F10",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f11_value = (char *)"GMDBB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f11_value, (strlen(f11_value)), "F11",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f12_value = (char *)"GMDB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f12_value, (strlen(f12_value)), "F12",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    f12_value = (char *)"ABC";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f12_value, (strlen(f12_value)), "F12",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    f12_value = (char *)"def";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f12_value, (strlen(f12_value)), "F12",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 085.整型，浮点型数据定义range约束，String定义length约束，异步merge_insert不符合定义范围的数据
TEST_F(DML_Asyn_test, Yang_004_085)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    uint32_t PK_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int8_t f1_value = -1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT8, &f1_value, sizeof(int8_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint8_t f2_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT8, &f2_value, sizeof(uint8_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int16_t f3_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT16, &f3_value, sizeof(int16_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint16_t f4_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT16, &f4_value, sizeof(uint16_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int32_t f5_value = 100;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f5_value, sizeof(int32_t), "F5",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int64_t f6_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t), "F6",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint64_t f7_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t), "F7",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    float f8_value = 0.9;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_FLOAT, &f8_value, sizeof(f8_value), "F8",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    double f9_value = 9.1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f10_value = (char *)"0123456789";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f10_value, (strlen(f10_value)), "F10",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f11_value = (char *)"GMDBB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f11_value, (strlen(f11_value)), "F11",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f12_value = (char *)"GMDB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f12_value, (strlen(f12_value)), "F12",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 086.整型，浮点型数据定义range约束，String定义length和pattern，invert_pattern约束，异步update不符合定义范围的数据
TEST_F(DML_Asyn_test, Yang_004_086)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetVertexProperty_PK(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"GMDBGMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f0_value = 1;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_OK, ret);
    char Pk_Name[] = "T20_PK";
    ret = GmcSetIndexKeyName(g_stmt_async, Pk_Name);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    int8_t f1_value = -1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT8, &f1_value, sizeof(int8_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint8_t f2_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT8, &f2_value, sizeof(uint8_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int16_t f3_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT16, &f3_value, sizeof(int16_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint16_t f4_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT16, &f4_value, sizeof(uint16_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int32_t f5_value = 100;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f5_value, sizeof(int32_t), "F5",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int64_t f6_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t), "F6",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint64_t f7_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t), "F7",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    float f8_value = 0.1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_FLOAT, &f8_value, sizeof(f8_value), "F8",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    double f9_value = 0.9;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f10_value = (char *)"0123456789";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f10_value, (strlen(f10_value)), "F10",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f11_value = (char *)"GMDBB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f11_value, (strlen(f11_value)), "F11",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f12_value = (char *)"GMDB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f12_value, (strlen(f12_value)), "F12",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 087.整型，浮点型数据定义range约束，String定义length和pattern，invert_pattern约束，异步replace_update不符合定义范围的数据
TEST_F(DML_Asyn_test, Yang_004_087)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetVertexProperty_PK(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"GMDBGMDB", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    uint32_t fpk_value = 1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT32, &fpk_value, sizeof(uint32_t), "F0",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f1_value = -1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT8, &f1_value, sizeof(int8_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint8_t f2_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT8, &f2_value, sizeof(uint8_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int16_t f3_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT16, &f3_value, sizeof(int16_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint16_t f4_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT16, &f4_value, sizeof(uint16_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int32_t f5_value = 100;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f5_value, sizeof(int32_t), "F5",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int64_t f6_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t), "F6",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint64_t f7_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t), "F7",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    float f8_value = 0.1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_FLOAT, &f8_value, sizeof(f8_value), "F8",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    double f9_value = 0.9;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f10_value = (char *)"0123456789";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f10_value, (strlen(f10_value)), "F10",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f11_value = (char *)"GMDBB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f11_value, (strlen(f11_value)), "F11",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f12_value = (char *)"GMDB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f12_value, (strlen(f12_value)), "F12",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 088.整型，浮点型数据定义range约束，String定义length和pattern，invert_pattern约束，异步merge_update不符合定义范围的数据
TEST_F(DML_Asyn_test, Yang_004_088)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetVertexProperty_PK(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"GMDBGMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f0_value = 1;
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_OK, ret);
    char Pk_Name[] = "T20_PK";
    ret = GmcSetIndexKeyName(g_stmt_async, Pk_Name);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    int8_t f1_value = -1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT8, &f1_value, sizeof(int8_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint8_t f2_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT8, &f2_value, sizeof(uint8_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int16_t f3_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT16, &f3_value, sizeof(int16_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint16_t f4_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT16, &f4_value, sizeof(uint16_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int32_t f5_value = 100;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f5_value, sizeof(int32_t), "F5",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    int64_t f6_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t), "F6",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    uint64_t f7_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t), "F7",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    float f8_value = 0.1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_FLOAT, &f8_value, sizeof(f8_value), "F8",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    double f9_value = 0.9;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f10_value = (char *)"0123456789";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f10_value, (strlen(f10_value)), "F10",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f11_value = (char *)"GMDBB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f11_value, (strlen(f11_value)), "F11",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    char *f12_value = (char *)"GMDB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f12_value, (strlen(f12_value)), "F12",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 089.String定义相同的pattern，invert_pattern(^GMDB$)约束在一个字段上，异步插入数据
TEST_F(DML_Asyn_test, Yang_004_089)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("schema_file/same_pattern_and_invert_pattern_schema.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_T20_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testYangSetVertexProperty_PK(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F1", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F1", (char *)"ABCDEF", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    ret = GmcDropVertexLabelAsync(g_stmt_async, g_T20_labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 090.Range与default共同设置，异步创建Vertex，设置该字段不在range范围上，异步插入数据
TEST_F(DML_Asyn_test, Yang_004_090)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("schema_file/range_length_and_default_schema.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_T20_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testYangSetVertexProperty_PK(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    // test_point
    int32_t f1_value = 0.9;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f1_value, sizeof(int32_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    f1_value = 11;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f1_value, sizeof(int32_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    f1_value = 19;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f1_value, sizeof(int32_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    f1_value = 31;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f1_value, sizeof(int32_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    f1_value = 49;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f1_value, sizeof(int32_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    f1_value = 51;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f1_value, sizeof(int32_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    ret = GmcDropVertexLabelAsync(g_stmt_async, g_T20_labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 109.string字段多个约束，设置不符合范围内的数据，然后设置符合范围内的数据，异步插入数据
TEST_F(DML_Asyn_test, Yang_004_109)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, g_labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("schema_file/all_type_schema_length_pattern_invert_pattern.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testYangSetVertexProperty_PK(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    int8_t f2_value = 1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F14", (char *)"0123456789",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F14", (char *)"456",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F14", (char *)"ABCD",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F14", (char *)"G",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F14", (char *)"GL",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F14", (char *)"gmdb",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F14", (char *)"GMDBBB",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    ret = GmcDropVertexLabelAsync(g_stmt_async, g_labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* container--list--choice--case Yang模型
    root(container)
        |
   ┌---------┬
   |         |
list    choice
           |
         case
*/
// 110.container-list-choice-case插入不符合范围数据
TEST_F(DML_Asyn_test, Yang_004_110)
{
    const char *g_vertexLabelT0 = "SubT0Con";
    const char *g_vertexLabelT1 = "SubT1List";
    const char *g_vertexLabelT1choice = "SubT1choice";
    const char *g_vertexLabelT2 = "SubT2choiceCase";
    const char *g_edgeLabeT0T1 = "SubT0ConSubT1List";
    const char *g_edgeLabeT0T1choice = "SubT0ConSubT1choice";
    const char *g_edgeLabelT1ChoiceT2case = "SubT1choiceSubT2choiceCase";
    AsyncUserDataT userData = {0};
    const char *namespace1 = "Namespace_004_3_A";
    GmcConnT *g_conn_async_list = NULL;
    GmcStmtT *g_stmt_async_list = NULL;
    GmcConnT *g_conn_async_choice = NULL;
    GmcStmtT *g_stmt_async_choice = NULL;
    GmcConnT *g_conn_async_case = NULL;
    GmcStmtT *g_stmt_async_case = NULL;
    ret = testGmcConnect(&g_conn_async_list, &g_stmt_async_list, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async_choice, &g_stmt_async_choice, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async_case, &g_stmt_async_case, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "建表");
    const char *g_labelconfig = "{\"max_record_count\" : 10000, \"isFastReadUncommitted\":0, \"auto_increment\":1}";
    readJanssonFile("schema_file/SubTreeVertexLabel.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_labelconfig, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(test_schema1);
    AW_FUN_Log(LOG_STEP, "建边");
    readJanssonFile("schema_file/SubTreeEdgelLabel.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, test_schema1, g_labelconfig, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(test_schema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    AW_FUN_Log(LOG_STEP, "container插入数据");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabelT0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    uint32_t f0_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT32, &f0_value, sizeof(uint32_t), "F0",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    int8_t f1_value = -1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT8, &f1_value, sizeof(int8_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    uint8_t f2_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT8, &f2_value, sizeof(uint8_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    int16_t f3_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT16, &f3_value, sizeof(int16_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    uint16_t f4_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT16, &f4_value, sizeof(uint16_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    int32_t f5_value = 100;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT32, &f5_value, sizeof(int32_t), "F5",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    int64_t f6_value = 0;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t), "F6",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    uint64_t f7_value = 10;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t), "F7",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    float f8_value = 0.1;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_FLOAT, &f8_value, sizeof(f8_value), "F8",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    double f9_value = 0.9;
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    char *f10_value = (char *)"0123456789";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f10_value, (strlen(f10_value)), "F10",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    char *f11_value = (char *)"GMDBB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f11_value, (strlen(f11_value)), "F11",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    char *f12_value = (char *)"GMDB";
    ret = testYangSetField(g_stmt_async, GMC_DATATYPE_STRING, f12_value, (strlen(f12_value)), "F12",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "list插入数据");
    ret = GmcUseNamespaceAsync(g_stmt_async_list, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_list, g_vertexLabelT1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_async_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    f0_value = 10;
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_UINT32, &f0_value, sizeof(uint32_t), "F0",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_INT8, &f1_value, sizeof(int8_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_UINT8, &f2_value, sizeof(uint8_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_INT16, &f3_value, sizeof(int16_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_UINT16, &f4_value, sizeof(uint16_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_INT32, &f5_value, sizeof(int32_t), "F5",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t), "F6",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t), "F7",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_FLOAT, &f8_value, sizeof(f8_value), "F8",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_STRING, f10_value, (strlen(f10_value)), "F10",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_STRING, f11_value, (strlen(f11_value)), "F11",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_list, GMC_DATATYPE_STRING, f12_value, (strlen(f12_value)), "F12",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "choice插入数据");
    ret = GmcUseNamespaceAsync(g_stmt_async_choice, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_choice, g_vertexLabelT1choice, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_async_choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "case插入数据");
    ret = GmcUseNamespaceAsync(g_stmt_async_case, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_case, g_vertexLabelT2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangBindChild(batch, g_stmt_async_choice, g_stmt_async_case);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0_value = 10;
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_UINT32, &f0_value, sizeof(uint32_t), "F0",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_INT8, &f1_value, sizeof(int8_t), "F1",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_UINT8, &f2_value, sizeof(uint8_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_INT16, &f3_value, sizeof(int16_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_UINT16, &f4_value, sizeof(uint16_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_INT32, &f5_value, sizeof(int32_t), "F5",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t), "F6",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t), "F7",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_FLOAT, &f8_value, sizeof(f8_value), "F8",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_DOUBLE, &f9_value, sizeof(f9_value), "F9",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_STRING, f10_value, (strlen(f10_value)), "F10",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_STRING, f11_value, (strlen(f11_value)), "F11",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testYangSetField(g_stmt_async_case, GMC_DATATYPE_STRING, f12_value, (strlen(f12_value)), "F12",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "删边");
    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    AW_FUN_Log(LOG_STEP, "断连");
    ret = testGmcDisconnect(g_conn_async_list, g_stmt_async_list);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async_choice, g_stmt_async_choice);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async_case, g_stmt_async_case);
    EXPECT_EQ(GMERR_OK, ret);
}

// 111.设置length的范围为min..max，插入数据时设置64K+1的字符串，然后再设置64K的字符串
TEST_F(DML_Asyn_test, Yang_004_111)
{
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, g_labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("schema_file/min_max_schema.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, g_configJson, create_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    free(test_schema1);

    AW_FUN_Log(LOG_STEP, "插入数据");
    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "设置64k+1长度失败");
    char Over64k[1024 * 64 + 2] = {0};
    memset(Over64k, 'a', sizeof(Over64k));
    Over64k[1024 * 64 + 1] = '\0';
    AW_FUN_Log(LOG_STEP, "Over64k length is %d", (strlen(Over64k)));
    ret = testYangSetVertexStringProperty(g_stmt_async, "F0", Over64k, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    AW_FUN_Log(LOG_STEP, "设置64k长度成功");
    char String64k[1024 * 64 + 1] = {0};
    memset(String64k, 'a', sizeof(String64k));
    String64k[1024 * 64] = '\0';
    AW_FUN_Log(LOG_STEP, "String64k length is %d", (strlen(String64k)));
    ret = testYangSetVertexStringProperty(g_stmt_async, "F0", String64k, GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    GmcBatchDestroy(batch);

    ret = GmcDropVertexLabelAsync(g_stmt_async, g_labelName, drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);

    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 200.乐观事务，开启事务后，反复插删数据后提交
TEST_F(DML_Asyn_test, Yang_004_200)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};

    for (uint32_t i = 0; i < 100; i++) {
        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexProperty_PK(g_stmt_async, 4, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexProperty(g_stmt_async, 4, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexStringProperty(
            g_stmt_async, "F10", (char *)"123456789", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"DEF", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
        GmcBatchDestroy(batch);
    }

    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 201.乐观事务，开启事务后，反复插删数据后回滚
TEST_F(DML_Asyn_test, Yang_004_201)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};

    for (uint32_t i = 0; i < 100; i++) {
        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetVertexProperty_PK(g_stmt_async, 4, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexProperty(g_stmt_async, 4, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexStringProperty(
            g_stmt_async, "F10", (char *)"123456789", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"DEF", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    ret = GmcTransRollBackAsync(g_conn_async, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 202.乐观事务，预制一条数据后，开启事务后，反复删插该数据后提交
TEST_F(DML_Asyn_test, Yang_004_202)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};

    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetVertexProperty_PK(g_stmt_async, 4, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexProperty(g_stmt_async, 4, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F10", (char *)"123456789", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"DEF", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (uint32_t i = 0; i < 100; i++) {
        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetVertexProperty_PK(g_stmt_async, 4, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexProperty(g_stmt_async, 4, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexStringProperty(
            g_stmt_async, "F10", (char *)"123456789", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexStringProperty(g_stmt_async, "F11", (char *)"GMDB", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetVertexStringProperty(g_stmt_async, "F12", (char *)"DEF", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview subtree -ns Namespace_004_3_A -rn T20_all_type -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "test end.");
}
