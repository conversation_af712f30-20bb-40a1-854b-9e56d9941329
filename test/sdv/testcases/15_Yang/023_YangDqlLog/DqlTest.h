/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :yang dql审计日志测试头文件
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2022/09/05
**************************************************************************** */
#ifndef DQLTEST_H
#define DQLTEST_H
#include <errno.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <stdarg.h>
#include <signal.h>
#include <sys/sem.h>
#include <sys/wait.h>
#include <sys/time.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"


#define TEST_STMT_NUM 2
#define ONE_THREAD_CREATE_DATA_NUM 50
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_connAsync = NULL;
GmcStmtT *g_stmtAsync = NULL;
GmcConnT *g_connAsync1 = NULL;
GmcStmtT *g_stmtAsync1 = NULL;
GmcStmtT *g_stmtAsy[TEST_STMT_NUM] = {NULL};
GmcTxConfigT g_trxConfig;
GmcTxConfigT g_trxConfig1;
const char *g_labelconfig =
    "{\"max_record_count\" : 2000000, \"isFastReadUncommitted\":0, \"auto_increment\":1, \"yang_model\":1}";
const char *g_nameSpaceT = (char *)"testns";
char const *g_cfglogName = "logLocalFoldThreshold";
int g_recoverSvalue = 1;
int g_changeSvalue = 10;
char const *g_cfglogName2 = "logGlobalFoldThreshold";
int g_recoverSvalue2 = 50;
int g_changeSvalue2 = 198;
#define TEST_FAILED 1
// 当前版本所有平台均不支持stlm日志
#define IS_SUPPORT_STLM_LOG 0

#define STLM_AUDIT_LOG_PATH  "$TEST_HOME/testcases/15_Yang/023_YangDqlLog/log/"

// sysconfig.ini -> 0:EULER, 1:docker+DAP(lite), 2:docker(yang)
typedef enum TagRunMode {
    RUN_MODE_EULER = 0,
    RUN_MODE_DAP = 1,
    RUN_MODE_YANG = 2
} TestRunModeE;


char g_timeStart[80];
char g_timeEnd[80];
// 获取当前系统时间
void GetTimeStart()
{
    time_t rawtime;
    struct tm *info;
    (void)time(&rawtime);
    info = localtime(&rawtime);
    (void)strftime(g_timeStart, 80, "%Y-%m-%d %H:%M:%S", info);
    return;
}

#ifdef ENV_RTOSV2X
void GetTimeEndAndLogCmd()
{
    #ifdef RUN_INDEPENDENT
    #else
    sleep(2);
    time_t rawtime;
    struct tm *info;
    (void)time(&rawtime);
    info = localtime(&rawtime);
    (void)strftime(g_timeEnd, 80, "%Y-%m-%d %H:%M:%S", info);
    system("rm -rf  /opt/vrpv8/home/<USER>/hpe_log.csv");
    system("hpecli log dump");
    char AuditLogCmd[256] = {0};
    system("rm -rf  "
        "$TEST_HOME/testcases/15_Yang/023_YangDqlLog/log/");
    system("mkdir $TEST_HOME/testcases/15_Yang/023_YangDqlLog/log/");
    // 过滤审计日志到指定目录
    (void)snprintf(AuditLogCmd, 256, "cp /opt/vrpv8/home/<USER>/hpe_log.csv $TEST_HOME/testcases"
                   "/15_Yang/023_YangDqlLog/log/audit.log");
    printf("%s \n", AuditLogCmd);
    system(AuditLogCmd);
    #endif
    return;
}
#else
void GetTimeEndAndLogCmd()
{
    return;
}
#endif

// [out] result: 执行系统调用的结果, 使用结束后必须调用free()释放内存
int TestExecSystemCmd(char **result, const char *format, ...)
{
    int ret;
    errno = 0;
    va_list args;
    va_start(args, format);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), format, args);
    if (ret < 0) {
        AW_FUN_Log(LOG_ERROR, "execute vsnprintf failed, ret = %d, %s.", ret, strerror(errno));
        va_end(args);
        return TEST_FAILED;
    }
    va_end(args);

    FILE *fd = popen(cmd, "r");
    if (fd == NULL) {
        AW_FUN_Log(LOG_ERROR, "popen failed, %s.", strerror(errno));
        return TEST_FAILED;
    }

    // XXX 优化为动态获取流长度
    int size = 1024 * 100;
    char *tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        AW_FUN_Log(LOG_ERROR, "malloc failed, %s.", strerror(errno));
        return TEST_FAILED;
    }
    memset(tmpResult, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat(tmpResult, buf);
    }

    ret = pclose(fd);
    if (ret == -1) {
        AW_FUN_Log(LOG_ERROR, "pclose failed, %s.", strerror(errno));
        free(tmpResult);
        return TEST_FAILED;
    }
    *result = tmpResult;
    return GMERR_OK;
}


// 清除运行时日志
int32_t TestClearAuditLog()
{
    int32_t ret = 0;
    char *result = NULL;
#ifdef ENV_RTOSV2X
    #ifdef RUN_INDEPENDENT
    ret = TestExecSystemCmd(&result, "cat /dev/null > ${TEST_HOME}/log/secure/sgmserver/sgmserver.log");
    if (result) {
        free(result);
    }
    #else
    ret = TestExecSystemCmd(&result, "rm -rf %s/*", STLM_AUDIT_LOG_PATH);
    if (result) {
        free(result);
    }
    #endif
#else
    ret = TestExecSystemCmd(&result, "cat /dev/null > ${TEST_HOME}/log/secure/sgmserver/sgmserver.log");
    if (result) {
        free(result);
    }
#endif
    return GMERR_OK;
}

// 检查本地日志中的慢操作场景日志
int TestCheckLocalAuditLog(const char *auditType, const char *resource, const char *status, const char *keyWords,
                           bool isNull = false)
{
    int ret = GMERR_OK;
    char *result = NULL;

    // 过滤审计日志关键字 DCL/DDL/DML, 提取关键信息 awk -F '[][]'  '{print $4}'
#if READ_LOG_BY_GREP
    ret = TestExecSystemCmd(&result, "grep -Er \"\\[%s\\].*\" ${TEST_HOME}/log/secure/*", auditType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "execute system command failed, ret = %d", ret);
        free(result);
        return TEST_FAILED;
    }
    if (strlen(result) > 1) {
        result[strlen(result) - 1] = '\0';
    }
#else
    char logFile[256];
#ifdef ENV_RTOSV2X
    #ifdef RUN_INDEPENDENT
    char *dir = getenv("TEST_HOME");
    if (dir) {
        snprintf(logFile, sizeof(logFile), "%s/log/secure/sgmserver/sgmserver.log", dir);
    } else {
        snprintf(logFile, sizeof(logFile), "./log/secure/sgmserver/sgmserver.log");
    }
    #else
    (void)snprintf(logFile, sizeof(logFile), "./log/sgmserver/sgmserver.log");
    #endif
#else
    char *dir = getenv("TEST_HOME");
    if (dir) {
        snprintf(logFile, sizeof(logFile), "%s/log/secure/sgmserver/sgmserver.log", dir);
    } else {
        snprintf(logFile, sizeof(logFile), "./log/secure/sgmserver/sgmserver.log");
    }
#endif
    readJanssonFile(logFile, &result);
    if (result == NULL) {
        AW_FUN_Log(LOG_ERROR, "read log file failed");
        return TEST_FAILED;
    }

    if (strstr(result, auditType) == NULL && !isNull) {
        AW_FUN_Log(LOG_ERROR, "check audit log failed, expect auditType is \"%s\"", auditType);
        free(result);
        return TEST_FAILED;
    } else if (strstr(result, auditType) == NULL && isNull) {
        free(result);
        return GMERR_OK;
    }
#endif
    if (!isNull) {
        if (strstr(result, resource) == NULL) {
            AW_FUN_Log(LOG_ERROR, "check audit log failed, expect resouce is \"%s\"", resource);
            ret = TEST_FAILED;
        }
        if (strstr(result, status) == NULL) {
            AW_FUN_Log(LOG_ERROR, "check audit log failed, expect status is \"%s\"", status);
            ret = TEST_FAILED;
        }
        if (strstr(result, keyWords) == NULL) {
            AW_FUN_Log(LOG_ERROR, "check audit log failed, expect keyWords is \"%s\"", keyWords);
            ret = TEST_FAILED;
        }

        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "audit log is:\n\"\n%s\n\"", result);
        }
    }
    free(result);
    return ret;
}

// 检查审计日志
int TestCheckAuditLog(const char *auditType, const char *resource, const char *status, const char *keyWords)
{
    return TestCheckLocalAuditLog(auditType, resource, status, keyWords);
}

// 检查审计日志
int TestCheckAuditLogNull(const char *auditType, bool isNull)
{
    return TestCheckLocalAuditLog(auditType, NULL, NULL, NULL, isNull);
}

const char *g_containRootName = "T0";
// container--list类型的Vertex Name
const char *g_listChildName01 = "T1";
const char *g_listChildName02 = "T2";
const char *g_listChildName03 = "T3";
const char *g_listChildName04 = "T4";
const char *g_listChildName05 = "T5";

static vector<string> expectDiffCreateList = {
    "T0:create[(priKey(ID:1)),(NULL)]\n"
    "T0.F0:create(100)\n"
    "T0.F1:create(100)\n"
    "T0.F2:create(string)\n"
    "T0.T1:create[(priKey(PID:1,F0:1)),(NULL)]\n"
    "T1.ID:create(1)\n"
    "T1.F1:create(1)\n"
    "T1.F2:create(string)\n"
    "T0.T1:create[(priKey(PID:1,F0:2), preKey(PID:1,F0:1)),(NULL)]\n"
    "T1.ID:create(2)\n"
    "T1.F1:create(2)\n"
    "T1.F2:create(string)\n"
    "T0.T1:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "T1.ID:create(3)\n"
    "T1.F1:create(3)\n"
    "T1.F2:create(string)\n"
    "T0.T1:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "T1.ID:create(4)\n"
    "T1.F1:create(4)\n"
    "T1.F2:create(string)\n"
    "T0.T1:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "T1.ID:create(5)\n"
    "T1.F1:create(5)\n"
    "T1.F2:create(string)\n"
};

void CreateYangContainerList(GmcStmtT *stmt, AsyncUserDataT data)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int ret = 0;

    readJanssonFile("./schemaFile/container_list.gmjson", &vertexSchema);
    AW_MACRO_ASSERT_NOTNULL(vertexSchema);
    readJanssonFile("./schemaFile/container_list_edge.gmjson", &edgeSchema);
    AW_MACRO_ASSERT_NOTNULL(edgeSchema);

    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T1", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T2", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T3", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T4", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T5", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(stmt, "T0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(stmt, "T1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(stmt, "T2", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(stmt, "T3", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(stmt, "T4", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(stmt, "T5", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_labelconfig, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(stmt, edgeSchema, g_labelconfig, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(edgeSchema);
    free(vertexSchema);
}

void DropYangContainerList(GmcStmtT *stmt, AsyncUserDataT data)
{
    int ret = 0;
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T1", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T2", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T3", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T4", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropEdgeLabelAsync(stmt, "T0_to_T5", drop_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcDropVertexLabelAsync(stmt, "T0", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T2", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T3", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T4", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropVertexLabelAsync(stmt, "T5", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

int testTransStartAsync(GmcConnT *conn, GmcTxConfigT config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return data.status;
    }
}

int testTransRollBackAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testBatchPrepareAndSetDiff(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

void testBatchExecuteAndWait(GmcBatchT *batch, AsyncUserDataT data, int totalNum, int succNum)
{
    int ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(totalNum, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(succNum, data.succNum);
}

int testYangSetField(GmcStmtT *stmt, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetVertexProperty(stmt, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError();
        return ret;
    }

    return ret;
}


void testYangSetVertexProperty_PK(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t valueF0 = i;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testYangSetVertexProperty(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF1 = value;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, &valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testSetkeyNameAndValue(GmcStmtT *stmt, uint32_t keyValue, bool isList = false)
{
    int ret;

    if (isList == false) {
    } else {
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcSetIndexKeyName(stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// id:1,F0:1,F1:2,更新F2为4，id:2,F0:1,F1:4
void testYangInsertList(GmcConnT *conn, GmcStmtT *stmt, GmcStmtT *stmt1)
{
    // 启动事务
    uint32_t keyValue = 100;
    int ret = 0;
    GmcBatchT *batch = NULL;
    ret = testTransStartAsync(conn, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt, g_containRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(stmt, keyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(stmt, keyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, g_listChildName01, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty_PK(stmt1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty(stmt1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    testBatchExecuteAndWait(batch, data, 6, 6);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void testYangInsertListNotCommit(GmcConnT *conn, GmcStmtT *stmt, GmcStmtT *stmt1, GmcBatchT *batch,
                                 AsyncUserDataT data, int start, int count)
{
    // 启动事务
    uint32_t keyValue = 100;
    int ret = 0;

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt, g_containRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(stmt, keyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(stmt, keyValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int i = start; i < start + count; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, g_listChildName01, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty_PK(stmt1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty(stmt1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    testBatchExecuteAndWait(batch, data, count + 1, count + 1);
}

void testYangUpdateVertexProperty(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF1 = value;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "tester";
    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, &valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void TestDiffCallbackNotCheck(void *userData, GmcFetchRetT *fetchRet, int32_t status,
    const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->status = status;
        AW_MACRO_EXPECT_EQ_INT(userData1->expStatus, status);
        userData1->historyRecvNum++;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        userData1->recvNum++;
    }
}

void TestDiffCallback2(void *userData, GmcFetchRetT *fetchRet, int32_t status,
    const char *errMsg)
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, status);
    if (userData) {
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->status = status;
        userData1->historyRecvNum++;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        userData1->recvNum++;
    }
}

void TestMemoryFullDiffCallback(void *userData, GmcFetchRetT *fetchRet, int32_t status,
    const char *errMsg)
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, status);
    if (userData) {
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->status = status;
        userData1->historyRecvNum++;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        userData1->recvNum++;
    }
}

void testFetchAndDeparseDiff(GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchAndDeparseDiff_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// subTree查询
bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB);
bool testYangJsonIsEqualReal(const json_t *valueA, const json_t *valueB)
{
    double doubleA = json_real_value(valueA);
    double doubleB = json_real_value(valueB);
    if (fabs(doubleA - doubleB) < 1e-6) {
        return true;
    }
    return false;
}
bool testYangJsonIsEqualField(const json_t *valueA, const json_t *valueB)
{
    if (json_typeof(valueA) == JSON_STRING) {
        return strcmp(json_string_value(valueA), json_string_value(valueB)) == 0;
    }
    if (json_typeof(valueA) == JSON_INTEGER) {
        return json_integer_value(valueA) == json_integer_value(valueB);
    }
    if (json_typeof(valueA) == JSON_REAL) {
        return testYangJsonIsEqualReal(valueA, valueB);
    }
    return true;
}
bool testYangJsonIsEqualArray(const json_t *valueA, const json_t *valueB)
{
    bool isEqual = true;
    uint32_t sizeA = (uint32_t)json_array_size(valueA);
    uint32_t sizeB = (uint32_t)json_array_size(valueB);
    if (sizeA != sizeB) {
        return false;
    }
    for (uint32_t i = 0; i < sizeA; ++i) {
        json_t *itemA = json_array_get(valueA, i);
        json_t *itemB = json_array_get(valueB, i);
        if (json_typeof(itemA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(itemA, itemB);
        } else {
            isEqual = testYangJsonIsEqualField(itemA, itemB);
        }
        if (!isEqual) {
            return false;
        }
    }
    return true;
}
bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB)
{
    bool isEqual = true;
    void *itA = json_object_iter((json_t *)jsonA);
    void *itB = json_object_iter((json_t *)jsonB);

    if (itA) {
        const char *keyA = json_object_iter_key(itA);

        while ((strcmp(keyA, "ID") == 0) || (strcmp(keyA, "PID") == 0)) {
            itA = json_object_iter_next((json_t *)jsonA, itA);
            if (itA == NULL) {
                return true;
            }
            keyA = json_object_iter_key(itA);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }
    if (itB) {
        const char *keyB = json_object_iter_key(itB);

        while ((strcmp(keyB, "ID") == 0) || (strcmp(keyB, "PID") == 0)) {
            itB = json_object_iter_next((json_t *)jsonA, itB);
            if (itB == NULL) {
                return true;
            }
            keyB = json_object_iter_key(itB);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }

    while (itA && itB) {
        const char *keyA = json_object_iter_key(itA);
        json_t *valueA = json_object_iter_value(itA);
        const char *keyB = json_object_iter_key(itB);
        json_t *valueB = json_object_iter_value(itB);

        if ((json_typeof(valueA) != json_typeof(valueB)) || (strcmp(keyA, keyB) != 0)) {
            return false;
        }
        if (json_typeof(valueA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(valueA, valueB);
        } else if (json_typeof(valueA) == JSON_ARRAY) {
            isEqual = testYangJsonIsEqualArray(valueA, valueB);
        } else {
            isEqual = testYangJsonIsEqualField(valueA, valueB);
        }
        if (!isEqual) {
            return false;
        }
        itA = json_object_iter_next((json_t *)jsonA, itA);
        itB = json_object_iter_next((json_t *)jsonB, itB);
    }

    return itA == itB;
}

bool testYangJsonIsEqual(const char *json1, const char *json2)
{
    json_error_t jsonError;
    json_t *jsonA = json_loads(json1, JSON_REJECT_DUPLICATES, &jsonError);
    json_t *jsonB = json_loads(json2, JSON_REJECT_DUPLICATES, &jsonError);
    bool isEqual = testYangJsonIsEqualInner(jsonA, jsonB);
    json_decref(jsonA);
    json_decref(jsonB);
    return isEqual;
}
// userData结构
struct subtreeFilterCbParam {
    int step;
    int32_t expectStatus;          // 预期的操作状态
    const char *expectReplyJson;  // 预期返回的subtree查询结果, json字符串
};
// userData ：用户数据 replyJson ：服务端返回的子树 json status ：服务器端操作处理结果  errMsg ：错误信息
void AsyncSubtreeFilterCallBack(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    subtreeFilterCbParam *param = (subtreeFilterCbParam *)(userData);
    param->expectStatus = status;
    // 将replyJson 与预期json比较
    bool isEnd = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count);
    if (param->expectReplyJson != NULL) {
        ASSERT_TRUE(testYangJsonIsEqual((const char*)jsonReply[0], param->expectReplyJson));
    }
    param->step++;
    if (GMERR_OK != status) {
        printf("[err] status is %d  errMsg  is %s   \n ", status, errMsg);
        return ;
    }
}

int TestWaitAsyncSubtreeRecvMultiEpoll(void *userData, int expRecvNum = 1, bool isAutoReset = true, int timeout = -1,
    int32_t epollFd = g_epollDataOneThread.userEpollFd)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    struct epoll_event events[MAX_EPOLL_EVENT_COUNT];
    subtreeFilterCbParam *userdata1 = (subtreeFilterCbParam *)userData;
    int num = userdata1->step;
    if (num != 0) {
        printf("%d\n", num);
    }
    while (userdata1->step != expRecvNum) {
        int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        while (fdCount > 0) {
            --fdCount;
            if (g_runMode == 1) {
                GmcHandleEvent(events[fdCount].data.fd);
            } else {
                GmcHandleRWEvent(events[fdCount].data.fd, events[fdCount].events);
            }
        }
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf s, all OpNum : %d,\actually recived num : %d\n", (double)duration / 1000000,
                expRecvNum, userdata1->step);
            return -1;  // 接收超时
        }
    }
    if (isAutoReset) {
        userdata1->step = 0;
    }
    return 0;
}


int TestWaitAsyncSubtreeRecv(void *userData, int expRecvNum = 1, int timeout = 5000000, bool isAutoReset = true)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    subtreeFilterCbParam *userdata1 = (subtreeFilterCbParam *)userData;
    while (userdata1->step != expRecvNum) {
        usleep(10);
        waitCnt++;
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf ", (double)duration / 1000000);
            return -1;  // 接收超时
        }
    }
    return 0;
}

int TestModifyDql(int32_t value, char *expectValue)
{
    char cmd[512] = {0};
    int ret = 0;
    (void)snprintf(cmd, 512, "%s/gmadmin -cfgName auditLogEnableDQL -cfgVal %d", g_toolPath, value);
    ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        system(cmd);
    }
    return ret;
}

void testYangListCreate(GmcConnT *conn, GmcStmtT *stmt, GmcStmtT *stmt1, GmcBatchT *batch,
                        AsyncUserDataT data, int start, int count)
{
    uint32_t keyValue = 100;
    int ret = 0;
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key
    testSetkeyNameAndValue(stmt, keyValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (int i = start; i < start + count; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1, g_listChildName01, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty_PK(stmt1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty(stmt1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    testBatchExecuteAndWait(batch, data, count + 1, count + 1);
}

#endif
