/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "aliasTool.h"

class indexBasicFunc : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void indexBasicFunc::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"yangAutoIndex=1\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void indexBasicFunc::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

const char *g_nspName01 = "indexBasicFunc";
const char *g_nspName02 = "indexBasicFunc02";

void indexBasicFunc::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "indexBasicFunc";
    const char *namespaceUserName = "abc";

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();
}

void indexBasicFunc::TearDown()
{
    const char *namespace1 = "indexBasicFunc";
    TryDropNameSpace(g_stmt_async, namespace1);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void TestCheckValidateModelAsync(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);*/
void AsyncValidateLeafRefCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;

        printf(">>> validateRes: %d\n", validateRes.validateRes);
        printf(">>> failCount: %u\n", validateRes.failCount);

        if (uData->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));

            // 结果检查
            printf("--- errcode: %d\n", msg.errorCode);
            printf("--- errorClauseIndex: %u\n", msg.errorClauseIndex);
            printf("--- errorMsg: %s\n", msg.errorMsg);
            printf("--- errorPath: %s\n", msg.errorPath);
            EXPECT_EQ(uData->expectedErrCode, msg.errorCode);
            EXPECT_EQ(uData->expectedErrClauseIndex, msg.errorClauseIndex);
            EXPECT_STREQ(uData->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(uData->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }

        uData->recvNum++;
    }
}


/*****************************************************************************
 * Description  : 001.list表，属性节点上有when，xpath里谓词语句里：主键值=current()
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
        F6: "formula": "../../ListOne[F0=current()]/F1 = 100"
    */

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff001.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_001");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 002.list表，属性节点上有must，xpath里谓词语句里：主键值=基于current()兄弟属性
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel2.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F5",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
        must F6: "/ContainerOne/ListOne[F0=current()/../F5]/F1 = 100"
    */

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff002.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_002");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 003.list表，子contain节点上有when，xpath里谓词语句里：主键值=基于current()子节点属性
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel3.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F5",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    /*
        when F6: "/ContainerOne/ListOne[F0=current()/../ContainerTwo/F10]/F1 = 101"
    */

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff003.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_003");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
  * Description  : 004.list表，孙子contain节点上有must，xpath里谓词语句里：主键值=基于current()祖先节点属性
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel4.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
        must: "formula": "/alias_ContainerOne/alias_ListOne[F0=current()/../../F10]/F1 = 100"
    */

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/ContainerThree/ContainerFour/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff004.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_004");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
  * Description  : 005.list表，子contain节点上有must，xpath里谓词语句里：主键值=基于current()向下跨表节点属性
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel5.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
        must : "formula": "/alias_ContainerOne/alias_ListOne[F0=current()/../alias_ListTwo/F6]/F1 = 100"
    */
    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i+1);
        ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/ContainerTwo/F10";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff005.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_005");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
  * Description  : 006.list表，子contain节点上有when，xpath里谓词语句里：主键值=基于current()向上跨表节点属性
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel6.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    /*
        must : "formula": "/alias_ContainerOne/alias_ListOne[F0=current()/../../../F10]/F1 = 100"
    */
    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i+1);
        ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/ContainerTwo/F10";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = failRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff006.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_006");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 007.list表，子choice节点上有when，xpath里谓词语句里：主键值=基于current()祖先节点属性
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel7.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff007.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_007");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 008.list表，子choice-case节点上有must，xpath里谓词语句里：主键值=基于current()祖先节点属性
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel8.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff008.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_008");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 009.list表，子choice-case节点的属性字段上有must，xpath里谓词语句里：主键值=current()
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel9.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/Listchoice/ListchoiceCase/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff009.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_009");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 010.list表，子choice-case节点的属性字段上有must，xpath里谓词语句里：主键值=current()兄弟case下的属性
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel10.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/Listchoice/ListchoiceCase/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff010.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_010");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 011.list表，多层级子contain子choice-case节点，节点上有when，xpath里谓词语句里：主键值=基于current()向上后向下属性值
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel11.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/Listchoice/ListchoiceCase/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff011.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_011");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 012.list表，多层级子contain子choice-case节点，节点上有must，xpath里谓词语句里：主键值=基于current()向下后向上属性值
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel12.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/Listchoice/ListchoiceCase/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff012.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_012");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 013.list表，多层级子contain子choice-case节点，节点上有when，xpath里谓词语句里：主键值=基于current()向上后向下跨表
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel13.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel13.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/Listchoice/ListchoiceCase/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff013.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_013");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 014.list表，多层级子contain子choice-case节点，节点上有must，xpath里谓词语句里：主键值=基于current()向下后向上跨表
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel14.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel14.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/Listchoice/ListchoiceCase/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff014.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_014");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 015.list表，属性节点上有when，xpath里非谓词语句：主键=current()
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel15.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + 1);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/Listchoice/ListchoiceCase/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff015.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_015");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 016.list表，属性节点上有when，xpath里非谓词语句：主键!=current()，预期不会建二级索引
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel16.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + 1);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/Listchoice/ListchoiceCase/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff016.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_016");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 017.list表，属性节点上有when，xpath里谓词语句里：/A/B[name=current()]/name = current()，预期建二级索引
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel17.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + 1);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/Listchoice/ListchoiceCase/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff017.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_017");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 018.list表，属性节点上有when，xpath里谓词语句里：/A/B[name=current()]/name = current()/../F2 ，预期不建二级索引
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel18.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + 1);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/Listchoice/ListchoiceCase/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff018.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_018");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 019.list表，属性节点上有leafref，xpath里路径为主键
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel19.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + 1);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00102\"]/F6";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff019.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_019");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 020.list表，when校验使用二级索引，must校验使用二级索引，并依赖when校验结果
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel20.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + 1);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/ContainerTwo";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff020.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_020");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 021.list表，when校验使用二级索引，leafref校验使用二级索引，并依赖when校验结果
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel21.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/ContainerTwo/F10";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff021.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_021");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 022.list表，when校验使用二级索引，must校验不使用二级索引，并依赖when校验结果
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel22.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/ContainerTwo/F10";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff022.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_022");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 023.list表，when校验使用二级索引，leafref校验不使用二级索引，并依赖when校验结果
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel23.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/ContainerTwo/F10";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff023.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_023");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 024.list表，when校验使用二级索引，mandatory校验依赖when校验结果
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel24.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MANDATORY_CHOICE;
    dataLef.expectedErrMsg = "mandatory verify no choice";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00101\"]/ContainerTwo/Listchoice";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff024.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_024");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 025.list表，when校验使用二级索引，max-min elements校验依赖when校验结果
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel25.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // 获取diff
    AsyncUserDataT data = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diffBasicFunc/diff025.json", &diffreply01);

    std::vector<std::string> diffreply(1);
    diffreply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_async, batch, diffreply, data);
    free(diffreply01);

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_025");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 026.list表，有二级索引，全量校验
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel26.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex
    ret = checkAutoIndexView(3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_026");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns indexBasicFunc -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 027.list表，有二级索引，导入导出
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(indexBasicFunc, Yang_093_indexBasicFunc_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaBasicFunc/SubTreeVertexLabel27.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaBasicFunc/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex  （4个formula，有2个在同一个字段上建）
    ret = checkAutoIndexView(3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    char tmpfieldStr[15] = "str00101";
    ret = testYangSetField(g_vertexLabelT0Node, GMC_DATATYPE_STRING, &tmpfieldStr, (strlen(tmpfieldStr)), "F10",
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE, "F1");

        memset(fieldStr, 0, sizeof(fieldStr));
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_containerT2Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        int32_t f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 choice case 子节点做replace操作
        ret = GmcYangEditChildNode(g_vertexLabelT1Node, g_listChoiceNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_listChoiceNode, g_listChoiceCaseNodeName, GMC_OPERATION_REPLACE_GRAPH, &g_listChoiceCaseNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作
        testYangSetVertexProperty_F0(g_listChoiceCaseNode1, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 对子节点字段 做merge操作
        f1fieldValue = i;
        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_INT32, &f1fieldValue, sizeof(int32_t), "F1",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_listChoiceCaseNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        for (int j = 0; j< 2; j++) {
            // 这里需要prepar list的labelname
            ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 对子节点字段 做replace操作  --ListTwo
            fieldValue = i + j;
            testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            memset(fieldStr, 0, sizeof(fieldStr));
            snprintf(fieldStr, sizeof(fieldStr), "str00%d", i + j + 1);
            ret = testYangSetField(g_vertexLabelT3Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            nDmlCnt++;
        }
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_indexBasicFunc_027");

    // 提交事务
    TransCommit(g_conn_async);

#ifndef FEATURE_PERSISTENCE  //光启不支持导入导出
    system("mkdir -p /root/data/");

    // 导出之前切换到需要导出的nsp
    ret = useNameSpaceAsync(g_stmt_async, g_nspName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};

    // 导出配置设置
    GmcPersistenceConfigT exportConfig =
        GetPersistenceConfig(GMC_PERSISTENCE_MODE_YANG, GMC_DIGEST_ALGORITHM_NONE, FILEPATH, NULL);
    // 普通算法，只含点表的导入导出
    ret = GmcYangExportDataAsync(g_stmt_async, &exportConfig, ConfigExportCallback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    testClearNsp(g_stmt_async, g_nspName01);
    dropNameSpaceAsync(g_stmt_async, g_nspName01);
    createNameSpaceAsync(g_stmt_async, g_nspName02, "abc");

    AW_FUN_Log(LOG_INFO, "导入另一个nsp");
    ret = useNameSpaceAsync(g_stmt_async, g_nspName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = checkAutoIndexView(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangImportDataAsync(g_stmt_async, &exportConfig, ConfigImportCallback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 检查表元数据 和 STORAGE_HASH_COLLISION_STAT中的autoIndex  （4个formula，有2个在同一个字段上建）
    ret = checkAutoIndexView(3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testClearNsp(g_stmt_async, g_nspName02);
    dropNameSpaceAsync(g_stmt_async, g_nspName02);

    system("rm -rf /root/data/");
#endif

    AW_FUN_Log(LOG_STEP, "END");
}
