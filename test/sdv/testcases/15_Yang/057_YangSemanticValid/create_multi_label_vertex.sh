#!/bin/bash
# for multi-label

CUR_DIR=`pwd`

if [ $# -lt 1 ];then
    echo "usage:$0 sh [create label nums]"
    exit  1
fi

## 数据清除及准备  $CUR_DIR/multi_vertexlabel文件夹
cd $CUR_DIR
mkdir multiVertexlabel > /dev/null 2>&1
cp schemafile/Container.gmjson ./multiVertexlabel/Container2.gmjson
sleep 1

# 构造多个 Vertex
cd $CUR_DIR/multiVertexlabel
echo create_multi_label $1

for i in $(seq 0 $1)
do	
	cp Container2.gmjson Container_$i.gmjson
	sed -i "s/\"name\":\"root\"/\"name\": \"root_"$i"\"/g" ./Container_$i.gmjson
done
