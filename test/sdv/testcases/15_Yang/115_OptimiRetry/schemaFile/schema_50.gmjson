[{"type": "container", "name": "main_label", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "int32", "nullable": true}, {"name": "F2", "type": "int64", "nullable": true}, {"name": "F3", "type": "string", "nullable": true}], "keys": [{"name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "lst_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": true}, {"name": "F2", "type": "int64", "nullable": true}, {"name": "F3", "type": "string", "nullable": true}], "keys": [{"fields": ["PID", "F0"], "node": "lst_1", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "lst_1", "name": "<PERSON><PERSON><PERSON>", "fields": ["F0"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}, {"type": "list", "name": "lst_2", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": true}, {"name": "F2", "type": "int64", "nullable": true}, {"name": "F3", "type": "string", "nullable": true}], "keys": [{"fields": ["PID", "F0"], "node": "lst_2", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "lst_2", "name": "<PERSON><PERSON><PERSON>", "fields": ["F0"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}, {"type": "list", "name": "lst_3", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": true}, {"name": "F2", "type": "int64", "nullable": true}, {"name": "F3", "type": "string", "nullable": true}], "keys": [{"fields": ["PID", "F0"], "node": "lst_3", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "lst_3", "name": "<PERSON><PERSON><PERSON>", "fields": ["F0"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}, {"type": "leaf-list", "name": "leaf_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "F0"], "node": "leaf_1", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "leaf_1", "name": "<PERSON><PERSON><PERSON>", "fields": ["F0"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}, {"type": "leaf-list", "name": "leaf_2", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "F0"], "node": "leaf_2", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "leaf_2", "name": "<PERSON><PERSON><PERSON>", "fields": ["F0"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}, {"type": "leaf-list", "name": "leaf_3", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "F0"], "node": "leaf_3", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "leaf_3", "name": "<PERSON><PERSON><PERSON>", "fields": ["F0"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}]