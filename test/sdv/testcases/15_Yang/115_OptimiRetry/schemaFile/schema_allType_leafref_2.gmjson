[{"type": "container", "name": "root", "presence": false, "rfc7951_invisible": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "/root/F0 < 10"}]}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "char", "nullable": true}, {"name": "F6", "type": "uchar", "nullable": true}, {"name": "F7", "type": "int8", "nullable": true}, {"name": "F8", "type": "uint8", "nullable": true}, {"name": "F9", "type": "int16", "nullable": true}, {"name": "F10", "type": "uint16", "nullable": true}, {"name": "F11", "type": "int32", "nullable": true}, {"name": "F12", "type": "uint32", "nullable": true}, {"name": "F13", "type": "int64", "nullable": true}, {"name": "F14", "type": "uint64", "nullable": true}, {"name": "F15", "type": "boolean", "nullable": true}, {"name": "F16", "type": "float", "nullable": true}, {"name": "F17", "type": "double", "nullable": true}, {"name": "F18", "type": "time", "nullable": true}, {"name": "F19", "type": "string", "nullable": true, "size": 20}, {"name": "F20", "type": "bytes", "nullable": true, "size": 20}, {"name": "F21", "type": "fixed", "nullable": true, "size": 20}, {"name": "F22", "type": "bitmap", "nullable": true, "size": 128}, {"name": "F23", "type": "enum", "nullable": true, "enumerate_identity": "F23", "enumerate": [{"name": "level1", "value": 1}, {"name": "level2", "value": 2}, {"name": "level3", "value": 3}, {"name": "level0", "value": 0}, {"name": "level-1", "value": -1}, {"name": "level-2", "value": -2}, {"name": "level-3", "value": -3}]}, {"name": "F24", "type": "identity", "nullable": true, "enumerate_identity": "F24", "enumerate": [{"name": "level1", "value": 1, "derived-paths": [{"derived-path": "level1"}]}, {"name": "level2", "value": 2, "derived-paths": [{"derived-path": "level1/level2"}]}, {"name": "level3", "value": 3, "derived-paths": [{"derived-path": "level1/level2/level3"}]}, {"name": "level0", "value": 0, "derived-paths": [{"derived-path": "level0"}]}, {"name": "level-1", "value": -1, "derived-paths": [{"derived-path": "level-1"}]}, {"name": "level-2", "value": -2, "derived-paths": [{"derived-path": "level-1/level-2"}]}, {"name": "level-3", "value": -3, "derived-paths": [{"derived-path": "level-1/level-2/level-3"}]}]}, {"name": "F25", "type": "empty", "nullable": true}, {"name": "F26", "type": "union", "nullable": true, "size": 128, "union_types": ["int32", "int64", "string"]}], "keys": [{"name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_1", "rfc7951_invisible": false, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true, "clause": [{"type": "leafref", "formula": "current()/../F1"}]}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "char", "nullable": true}, {"name": "F6", "type": "uchar", "nullable": true}, {"name": "F7", "type": "int8", "nullable": true}, {"name": "F8", "type": "uint8", "nullable": true}, {"name": "F9", "type": "int16", "nullable": true}, {"name": "F10", "type": "uint16", "nullable": true}, {"name": "F11", "type": "int32", "nullable": true}, {"name": "F12", "type": "uint32", "nullable": true}, {"name": "F13", "type": "int64", "nullable": true}, {"name": "F14", "type": "uint64", "nullable": true}, {"name": "F15", "type": "boolean", "nullable": true}, {"name": "F16", "type": "float", "nullable": true}, {"name": "F17", "type": "double", "nullable": true}, {"name": "F18", "type": "time", "nullable": true}, {"name": "F19", "type": "string", "nullable": true, "size": 20}, {"name": "F20", "type": "bytes", "nullable": true, "size": 20}, {"name": "F21", "type": "fixed", "nullable": true, "size": 20}, {"name": "F22", "type": "bitmap", "nullable": true, "size": 128}, {"name": "F23", "type": "enum", "nullable": true, "enumerate_identity": "F23", "enumerate": [{"name": "level1", "value": 1}, {"name": "level2", "value": 2}, {"name": "level3", "value": 3}, {"name": "level0", "value": 0}, {"name": "level-1", "value": -1}, {"name": "level-2", "value": -2}, {"name": "level-3", "value": -3}]}, {"name": "F24", "type": "identity", "nullable": true, "enumerate_identity": "F24", "enumerate": [{"name": "level1", "value": 1, "derived-paths": [{"derived-path": "level1"}]}, {"name": "level2", "value": 2, "derived-paths": [{"derived-path": "level1/level2"}]}, {"name": "level3", "value": 3, "derived-paths": [{"derived-path": "level1/level2/level3"}]}, {"name": "level0", "value": 0, "derived-paths": [{"derived-path": "level0"}]}, {"name": "level-1", "value": -1, "derived-paths": [{"derived-path": "level-1"}]}, {"name": "level-2", "value": -2, "derived-paths": [{"derived-path": "level-1/level-2"}]}, {"name": "level-3", "value": -3, "derived-paths": [{"derived-path": "level-1/level-2/level-3"}]}]}, {"name": "F25", "type": "empty", "nullable": true}, {"name": "F26", "type": "union", "nullable": true, "size": 128, "union_types": ["int32", "int64", "string"]}], "keys": [{"fields": ["PID", "PK", "F0"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "list_1", "name": "list_localhash", "fields": ["F1"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}]