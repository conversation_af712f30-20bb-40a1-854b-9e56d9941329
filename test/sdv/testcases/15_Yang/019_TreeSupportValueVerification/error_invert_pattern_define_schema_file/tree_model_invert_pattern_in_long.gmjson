[{"type": "container", "name": "Con_0", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "string", "nullable": true, "length": "1|2|3|4|5|6|7|8|9"}, {"name": "Con_1", "type": "container", "fields": [{"name": "A0", "type": "long", "nullable": true, "invert_pattern": ["^GMDB$", "^ABC$", "^DEF$"]}]}, {"name": "Choice_1", "type": "choice", "fields": [{"name": "Case_2", "type": "case", "fields": [{"name": "B0", "type": "string", "nullable": true, "length": "1|2|3|4|5|6|7|8|9"}]}]}], "keys": [{"node": "CON_0", "name": "CON_0_PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "L0", "type": "string", "nullable": true, "length": "1|2|3|4|5|6|7|8|9"}], "keys": [{"node": "list_1", "name": "list_1_PK", "fields": ["ID", "PID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]