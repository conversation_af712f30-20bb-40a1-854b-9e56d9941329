[{"type": "container", "name": "main_label_1", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "root_F1", "type": "uint32", "nullable": true, "default": 1}, {"name": "root_F2", "type": "uint32", "nullable": true}, {"name": "root_F3", "type": "union", "nullable": true, "union_types": ["uint32"], "invert_pattern": ["\\i+"]}], "keys": [{"name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]