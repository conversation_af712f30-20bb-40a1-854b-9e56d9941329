[{"type": "container", "name": "Con_8_N1", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string", "size": 8}, {"name": "F7", "type": "char", "nullable": true}, {"name": "F8", "type": "uchar", "nullable": true}, {"name": "F9", "type": "int8", "nullable": true}, {"name": "F10", "type": "uint8", "nullable": true}, {"name": "F11", "type": "int16", "nullable": true}, {"name": "F12", "type": "uint16", "nullable": true}, {"name": "F13", "type": "uint32", "nullable": true}, {"name": "F14", "type": "int64", "nullable": true}, {"name": "F15", "type": "uint64", "nullable": true}, {"name": "F16", "type": "time", "nullable": true}, {"name": "F17", "type": "uint8: 4", "nullable": true}, {"name": "F18", "type": "uint16: 15", "nullable": true}, {"name": "F19", "type": "uint32: 31", "nullable": true}, {"name": "F20", "type": "bytes", "size": 7, "nullable": true}, {"name": "F21", "type": "fixed", "size": 7, "nullable": true}, {"type": "container", "name": "Con_8_N2", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"type": "container", "name": "Con_8_N3", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"type": "container", "name": "Con_8_N4", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"type": "container", "name": "Con_8_N5", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"type": "container", "name": "Con_8_N6", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"type": "container", "name": "Con_8_N7", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"type": "container", "name": "Con_8_N8", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32", "nullable": false}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}]}]}]}]}]}]}]}], "keys": [{"node": "Con_8_N1", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]