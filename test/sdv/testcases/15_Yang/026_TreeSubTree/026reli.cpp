/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "TreeSubTree_common.h"
#include "t_datacom_lite.h"

class reliabilityTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void reliabilityTest::SetUpTestCase()
{
}

void reliabilityTest::TearDownTestCase()
{
}

void reliabilityTest::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void reliabilityTest::TearDown()
{
    AW_CHECK_LOG_END();
}
bool g_is_replace = false;
bool g_is_subtree = false;
void *thread_replace_delete(void *args)
{
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    const char *namespace1 = "NamespaceA026100";
    const char *namespaceUserName = "abc";
    int ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT userData1 = {0};
    ret = GmcUseNamespaceAsync(stmt_async, namespace1, use_namespace_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    ret = GmcTransStartAsync(conn_async, &config, trans_start_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    testYangReplaceDate(g_conn_async);
    // 提交事务
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    g_is_replace = true;
    while(g_is_subtree == false)
    {
        sleep(1);
    }
    ret = GmcTransStartAsync(conn_async, &config, trans_start_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    // delete
    testYangdeleteDate(g_conn_async);
    // 提交事务
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *thread_subtree(void *args)
{
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    const char *namespace1 = "NamespaceA026100";
    const char *namespaceUserName = "abc";
    int ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT userData1 = {0};
    ret = GmcUseNamespaceAsync(stmt_async, namespace1, use_namespace_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    while(g_is_replace == false)
    {
        sleep(1);
    }
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    ret = GmcTransStartAsync(conn_async, &config, trans_start_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    // 使用接口完成subtree 查询
    GmcNodeT * root = NULL;
    const char *SubT0ConNode = "SubT0Con";
    ret = testGmcPrepareStmtByLabelName(stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = testsubtreeSetvalue(root,GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
        char *suntreeReturnJson = NULL;
        int TimeZoneType = GetTimeZoneType();
        if (TimeZoneType == 0) {
            readJanssonFile("SubtreeReplyJson/replaceReply.json", &suntreeReturnJson);
        } else {
            readJanssonFile("ARM32SubtreeReplyJson/replaceReply.json", &suntreeReturnJson);
        }
        
        std::vector<std::string> reply(1);
        reply[0] = suntreeReturnJson;
        FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(stmt_async, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson);
    // 提交事务
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    g_is_subtree = true;
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
bool g_is_insert = false;
void *thread_insert(void *args)
{
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    const char *namespace1 = "NamespaceA026098";
    const char *namespaceUserName = "abc";
    int ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT userData1 = {0};
    ret = GmcUseNamespaceAsync(stmt_async, namespace1, use_namespace_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    ret = GmcTransStartAsync(conn_async, &config, trans_start_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    testYangPresetAllDate(g_conn_async);
    // 提交事务
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    g_is_insert = true ;
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *thr_arr_Subtree(void *args)
{
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    const char *namespace1 = "NamespaceA026098";
    const char *namespaceUserName = "abc";
    int ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT userData1 = {0};
    ret = GmcUseNamespaceAsync(stmt_async, namespace1, use_namespace_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    while( g_is_insert == false)
    {
        sleep(1);
    }
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    ret = GmcTransStartAsync(conn_async, &config, trans_start_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    // 使用接口完成subtree 查询
    GmcNodeT * root = NULL;
    const char *SubT0ConNode = "SubT0Con";
    ret = testGmcPrepareStmtByLabelName(stmt_async, SubT0ConNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = testsubtreeSetvalue(root,GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;
#if defined(CPU_BIT_32) && defined(ENV_RTOSV2)
    readJanssonFile("ARM32SubtreeReplyJson/containerReply.json", &suntreeReturnJson);
#else
    readJanssonFile("SubtreeReplyJson/containerReply.json", &suntreeReturnJson);
#endif
    
    std::vector<std::string> reply(1);
    reply[0] = suntreeReturnJson;
    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(stmt_async, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson);
    // 提交事务
    ret = GmcTransCommitAsync(conn_async, trans_commit_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
/*****************************************************************************
 * Description  : 1.subtree过滤查询循环1W次
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(reliabilityTest, Yang_026_095)
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "NamespaceA026095";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);
    
    // alloc all stmt
    TestYangAllocAllstmt();
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt_sync, namespace1);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test start.");
    //树模型表预制数据
    testYangPresetAllDate(g_conn_async);
    // 使用接口完成subtree 查询
    for(int i = 0; i < 10000; i++) {
        // 启动事务
        g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        g_trxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
        g_trxConfig.readOnly = false;
        g_trxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
        ret = testTransStartAsync(g_conn_async, g_trxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT * root = NULL;
        const char *SubT0ConNode = "SubT0Con";
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "SubT0Con", GMC_OPERATION_SUBTREE_FILTER);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT * T1choice = NULL;
        const char *SubT1choiceNode = "SubT1choice";
        ret = GmcYangEditChildNode(root, SubT1choiceNode, GMC_OPERATION_SUBTREE_FILTER, &T1choice);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT * T1choicecase = NULL;
        const char *SubT1choicecaseNode = "SubT1choiceCase";
        ret = GmcYangEditChildNode(T1choice, SubT1choicecaseNode, GMC_OPERATION_SUBTREE_FILTER, &T1choicecase);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t f0 = 1;
        ret = testsubtreeSetvalue(T1choicecase,GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };
        char *suntreeReturnJson = NULL;
#if defined(CPU_BIT_32) && defined(ENV_RTOSV2)
        readJanssonFile("ARM32SubtreeReplyJson/choicecaseReply.json", &suntreeReturnJson);
#else
        readJanssonFile("SubtreeReplyJson/choicecaseReply.json", &suntreeReturnJson);
#endif
        ASSERT_NE((void *)NULL, suntreeReturnJson);
        std::vector<std::string> reply(1);
        reply[0] = suntreeReturnJson;
        FetchRetCbParam param = {
            .step = 0,
            .stmt = g_stmt_async,
            .expectStatus = GMERR_OK,
            .filterMode = filters.filterMode,
            .expectReply = reply,
        };
        ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(suntreeReturnJson);
        // 提交事务
        ret = testTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //  删表
    testClearNsp(g_stmt_async, namespace1);
    // 释放all stmt
    TestYangFreeAllstmt();
    
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 2.连接满场景下，执行subtree查询
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(reliabilityTest, Yang_026_096)
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "NamespaceA026096";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);
    
    // alloc all stmt
    TestYangAllocAllstmt();
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt_sync, namespace1);
    EXPECT_EQ(GMERR_OK, ret);
    // alloc all stmt
    TestYangAllocAllstmt();
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn[MAX_CONN_SIZE] = {NULL};
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_ASSERT_EQ_INT(0, ret);

    for (int i = 0; i < (MAX_CONN_SIZE - 4 - existConnNum); i++) {
        AW_FUN_Log(LOG_DEBUG, "testGmcConnect:%d\n", i);
        ret = testGmcConnect(&conn[i]);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "i:%d ret:%d\n", i, ret);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    //树模型表预制数据
    testYangPresetAllDate(g_conn_async);
    // 启动事务
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    g_trxConfig.readOnly = false;
    g_trxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询
    GmcNodeT * root = NULL;
    const char *SubT0ConNode = "SubT0Con";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "SubT0Con", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = testsubtreeSetvalue(root,GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;
#if defined(CPU_BIT_32) && defined(ENV_RTOSV2)
    readJanssonFile("ARM32SubtreeReplyJson/containerReply.json", &suntreeReturnJson);
#else
    readJanssonFile("SubtreeReplyJson/containerReply.json", &suntreeReturnJson);
#endif
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = suntreeReturnJson;
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  删边
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //  删表
    testClearNsp(g_stmt_async, namespace1);
    // 释放all stmt
    TestYangFreeAllstmt();
    
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < (MAX_CONN_SIZE - 4 - existConnNum); i++) {
        ret = testGmcDisconnect(conn[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 3.内存满场景下，执行subtree查询
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(reliabilityTest, Yang_026_097)
{
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    if (g_envType == 2) {
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/stop.sh");                        // 修改配置，先停服务
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");  // 内存大小改小，减少单个用例执行时间
        system("sh $TEST_HOME/tools/start.sh -f");
    }
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "NamespaceA026097";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);
    
    // alloc all stmt
    TestYangAllocAllstmt();
    // 新建普通vertex表，向表中写数据到内存满
    char *Vertexschema = NULL;
    readJanssonFile("schema/vertexlabel.gmjson", &Vertexschema);
    ASSERT_NE((void *)NULL, Vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, Vertexschema, g_labelCcehConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Vertexschema);
    //树模型表预制数据
    testYangPresetAllDate(g_conn_async);
    TestGmcInsertVertex_OUTMem(g_stmt_sync);
    AW_FUN_Log(LOG_STEP, "test start.");
    // 启动事务
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    g_trxConfig.readOnly = false;
    g_trxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询
    GmcNodeT * root = NULL;
    const char *SubT0ConNode = "SubT0Con";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "SubT0Con", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = testsubtreeSetvalue(root,GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson = NULL;
#if defined(CPU_BIT_32) && defined(ENV_RTOSV2)
    readJanssonFile("ARM32SubtreeReplyJson/containerReply.json", &suntreeReturnJson);
#else
    readJanssonFile("SubtreeReplyJson/containerReply.json", &suntreeReturnJson);
#endif
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = suntreeReturnJson;
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_vertexLabel);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //  删表
    testClearNsp(g_stmt_async, namespace1);
    // 释放all stmt
    TestYangFreeAllstmt();
    
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
/*****************************************************************************
 * Description  : 4.多线程读写并发
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(reliabilityTest, Yang_026_098)
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "NamespaceA026098";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);
    // alloc all stmt
    TestYangAllocAllstmt();
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt_sync, namespace1);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test start.");
    // alloc all stmt
    TestYangAllocAllstmt();
    uint32_t thrSubtreeScan = 9;
#if defined ENV_RTOSV2X
    uint32_t currUseConn = 0;
    uint32_t currFreeConn = 0;
    ret = testGetConnNum(&currUseConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    currFreeConn = 64 - currUseConn;
    // 空4个链接保证稳定性,1线程写 占用1个链接， subtree读，每个线程占用1个链接
    thrSubtreeScan = (currFreeConn - 5) > thrSubtreeScan ? thrSubtreeScan : (currFreeConn - 5);
    AW_FUN_Log(LOG_INFO, "rtosv2x currUseConn = %u, currFreeConn = %u , thrNum = %u.", currUseConn, currFreeConn,
        thrSubtreeScan);
#endif
    //一个线程写,一个线程subtree查询
    pthread_t thr_arr_insert_delete;
    pthread_t thr_arr_subtree[9];
    int i = 0;
    pthread_create(&thr_arr_insert_delete, NULL, thread_insert, NULL);
    for(i = 0; i < thrSubtreeScan; i++)
    {
        pthread_create(&thr_arr_subtree[i], NULL, thr_arr_Subtree, NULL);
    }
    pthread_join(thr_arr_insert_delete, NULL);
    for(i = 0; i < thrSubtreeScan; i++)
    {
        pthread_join(thr_arr_subtree[i], NULL);
    }
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //  删表
    testClearNsp(g_stmt_async, namespace1);
    // 释放all stmt
    TestYangFreeAllstmt();
    
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 5.subtree过滤creat 查询 replace merge 查询循环1W次
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(reliabilityTest, Yang_026_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "NamespaceA026099";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);
    
    // alloc all stmt
    TestYangAllocAllstmt();
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt_sync, namespace1);
    EXPECT_EQ(GMERR_OK, ret);
    for(int i =0; i < 10; i++) {
        //树模型表预制数据
        testYangPresetAllDate(g_conn_async);
        // 启动事务
        g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        g_trxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
        g_trxConfig.readOnly = false;
        g_trxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
        ret = testTransStartAsync(g_conn_async, g_trxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 使用接口完成subtree 查询
        GmcNodeT * root = NULL;
        const char *SubT0ConNode = "SubT0Con";
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "SubT0Con", GMC_OPERATION_SUBTREE_FILTER);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0 = 1;
        ret = testsubtreeSetvalue(root,GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };
        char *suntreeReturnJson = NULL;
        int TimeZoneType = GetTimeZoneType();
        if (TimeZoneType == 0) {
            readJanssonFile("SubtreeReplyJson/containerReply.json", &suntreeReturnJson);
        } else {
            readJanssonFile("ARM32SubtreeReplyJson/containerReply.json", &suntreeReturnJson);
        }
        
        ASSERT_NE((void *)NULL, suntreeReturnJson);
        std::vector<std::string> reply(1);
        reply[0] = suntreeReturnJson;
        FetchRetCbParam param = {
            .step = 0,
            .stmt = g_stmt_async,
            .expectStatus = GMERR_OK,
            .filterMode = filters.filterMode,
            .expectReply = reply,
        };
        ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(suntreeReturnJson);
        // 提交事务
        ret = testTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //merge
        testYangMergeDate(g_conn_async);
        // 启动事务
        g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        g_trxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
        g_trxConfig.readOnly = false;
        g_trxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
        ret = testTransStartAsync(g_conn_async, g_trxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 使用接口完成subtree 查询
        GmcNodeT * root1 = NULL;
        const char *SubT0ConNode1 = "SubT0Con";
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode1, GMC_OPERATION_SUBTREE_FILTER);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &root1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f01 = 2;
        ret = testsubtreeSetvalue(root1, GMC_DATATYPE_UINT32, &f01, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcSubtreeFilterItemT filter1 = {
            .rootName = NULL,
            .subtree = {.obj = root1},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters1 = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter1,
        };
        char *suntreeReturnJson1 = NULL;
        TimeZoneType = GetTimeZoneType();
        if (TimeZoneType == 0) {
            readJanssonFile("SubtreeReplyJson/mergeReply.json", &suntreeReturnJson1);
        } else {
            readJanssonFile("ARM32SubtreeReplyJson/mergeReply.json", &suntreeReturnJson1);
        }
        
        ASSERT_NE((void *)NULL, suntreeReturnJson1);
        std::vector<std::string> reply1(1);
        reply1[0] = suntreeReturnJson1;
        FetchRetCbParam param1 = {
            .step = 0,
            .stmt = g_stmt_async,
            .expectStatus = GMERR_OK,
            .filterMode = filters1.filterMode,
            .expectReply = reply1,
        };
        ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters1, NULL, AsyncFetchRetCb, &param1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(suntreeReturnJson1);
        // 提交事务
        ret = testTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //replace
        testYangReplaceDate(g_conn_async);
        // 启动事务
        g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        g_trxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
        g_trxConfig.readOnly = false;
        g_trxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
        ret = testTransStartAsync(g_conn_async, g_trxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 使用接口完成subtree 查询
        GmcNodeT * root2 = NULL;
        const char *SubT0ConNode2 = "SubT0Con";
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode2, GMC_OPERATION_SUBTREE_FILTER);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &root2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f02 = 1;
        ret = testsubtreeSetvalue(root2,GMC_DATATYPE_UINT32, &f02, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcSubtreeFilterItemT filter2 = {
            .rootName = NULL,
            .subtree = {.obj = root2},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters2 = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter2,
        };
        char *suntreeReturnJson2 = NULL;
        TimeZoneType = GetTimeZoneType();
        if (TimeZoneType == 0) {
            readJanssonFile("SubtreeReplyJson/replaceReply.json", &suntreeReturnJson2);
        } else {
            readJanssonFile("ARM32SubtreeReplyJson/replaceReply.json", &suntreeReturnJson2);
        }
        
        ASSERT_NE((void *)NULL, suntreeReturnJson2);
        std::vector<std::string> reply2(1);
        reply2[0] = suntreeReturnJson2;
        FetchRetCbParam param2 = {
            .step = 0,
            .stmt = g_stmt_async,
            .expectStatus = GMERR_OK,
            .filterMode = filters2.filterMode,
            .expectReply = reply2,
        };
        ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters2, NULL, AsyncFetchRetCb, &param2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(suntreeReturnJson2);
        // 提交事务
        ret = testTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // delete
        testYangdeleteDate(g_conn_async);
        // 启动事务
        g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        g_trxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
        g_trxConfig.readOnly = false;
        g_trxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
        ret = testTransStartAsync(g_conn_async, g_trxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 使用接口完成subtree 查询
        GmcNodeT * root3 = NULL;
        const char *SubT0ConNode3 = "SubT0Con";
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, SubT0ConNode3, GMC_OPERATION_SUBTREE_FILTER);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &root3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f03 = 1;
        ret = testsubtreeSetvalue(root3,GMC_DATATYPE_UINT32, &f03, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcSubtreeFilterItemT filter3 = {
            .rootName = NULL,
            .subtree = {.obj = root3},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters3 = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter3,
        };
        std::vector<std::string> reply3(1);
        reply3[0] = "{}";
        FetchRetCbParam param3 = {
            .step = 0,
            .stmt = g_stmt_async,
            .expectStatus = GMERR_OK,
            .filterMode = filters3.filterMode,
            .expectReply = reply3,
        };
        ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters3, NULL, AsyncFetchRetCb, &param3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 提交事务
        ret = testTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //  删表
    testClearNsp(g_stmt_async, namespace1);
    // 释放all stmt
    TestYangFreeAllstmt();
    
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 6.预置数据 查询和repalce并发操作
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(reliabilityTest, Yang_026_100)
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "NamespaceA026100";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

    // alloc all stmt
    TestYangAllocAllstmt();
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt_sync, namespace1);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test start.");
    // alloc all stmt
    TestYangAllocAllstmt();
    //预置数据
    testYangPresetAllDate(g_conn_async);
    //一个线程replace操作和删除操作, 一个线程subtree查询
    pthread_t thr_01;
    pthread_t thr_02;
    ret = pthread_create(&thr_01, NULL, thread_replace_delete, NULL);
    ret = pthread_create(&thr_02, NULL, thread_subtree, NULL);
    pthread_join(thr_01, NULL);
    pthread_join(thr_02, NULL);
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //  删表
    testClearNsp(g_stmt_async, namespace1);
    // 释放all stmt
    TestYangFreeAllstmt();

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 101.对普通表进行subtree查询
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(reliabilityTest, Yang_026_101)
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "NamespaceA026101";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // alloc all stmt
    TestYangAllocAllstmt();
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt_sync, namespace1);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test start.");
    // alloc all stmt
    TestYangAllocAllstmt();
    // 新建普通vertex表
    char *Vertexschema = NULL;
    readJanssonFile("schema/vertexlabel.gmjson", &Vertexschema);
    ASSERT_NE((void *)NULL, Vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, Vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Vertexschema);
    TestGmcInsertVertex(g_stmt_sync);
    // 对普通表做subtree查询
    GmcNodeT * root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_vertexLabel, GMC_OPERATION_SUBTREE_FILTER);
    // 非yang表subtree查询报错提前 适配错误码
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret=testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root);
    // 非yang表subtree查询报错提前 适配错误码
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);
    ret=testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabelAsync(g_stmt_async, g_vertexLabel, drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 释放all stmt
    TestYangFreeAllstmt();
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
