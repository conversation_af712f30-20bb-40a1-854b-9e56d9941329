alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.F0:remove(100)
alias_ContainerOne.F1:remove([null])
alias_ContainerOne.F2:remove(100)
alias_ContainerOne.alias_ListOne:remove[(NULL),(priKey(PID:1,F0:100))]
alias_ListOne.ID:remove(1)
alias_ListOne.F1:remove([null])
alias_ListOne.F2:remove(100)
alias_ListOne.F3:remove(456)
alias_ListOne.F4:remove(456)
alias_ListOne.F6:remove(str123)
alias_ContainerOne.alias_ListOne:create[(priKey(PID:1,F0:104)),(NULL)]
alias_ListOne.ID:create(5)
alias_ListOne.F1:create([null])
alias_ListOne.F2:create(104)
alias_ListOne.F3:create(800)
alias_ListOne.F4:create(456)
alias_ListOne.F6:create(str456)
alias_ContainerOne.alias_ListOne:remove[(NULL),(priKey(PID:1,F0:103), preKey(PID:1,F0:102))]
alias_ListOne.ID:remove(4)
alias_ListOne.F1:remove([null])
alias_ListOne.F2:remove(103)
alias_ListOne.F3:remove(456)
alias_ListOne.F4:remove(456)
alias_ListOne.F6:remove(str123)
alias_ContainerOne.alias_ListOne:remove[(NULL),(priKey(PID:1,F0:102), preKey(PID:1,F0:101))]
alias_ListOne.ID:remove(3)
alias_ListOne.F1:remove([null])
alias_ListOne.F2:remove(102)
alias_ListOne.F3:remove(456)
alias_ListOne.F4:remove(456)
alias_ListOne.F6:remove(str123)
alias_ContainerOne.alias_ListOne:remove[(NULL),(priKey(PID:1,F0:101), preKey(PID:1,F0:100))]
alias_ListOne.ID:remove(2)
alias_ListOne.F1:remove([null])
alias_ListOne.F2:remove(101)
alias_ListOne.F3:remove(456)
alias_ListOne.F4:remove(456)
alias_ListOne.F6:remove(str123)
