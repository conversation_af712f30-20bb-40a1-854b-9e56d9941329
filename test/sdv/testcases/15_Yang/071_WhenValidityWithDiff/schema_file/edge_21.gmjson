[{"name": "yang_to_ds", "source_vertex_label": "yang", "dest_vertex_label": "ds", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ds_to_hua<PERSON>_AAA", "source_vertex_label": "ds", "dest_vertex_label": "huawei_AAA", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "huawei_AAA_to_leaflist_1", "source_vertex_label": "huawei_AAA", "dest_vertex_label": "leaflist_1", "source_node_path": "/choice_1/case_1", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "yang_to_list_1", "source_vertex_label": "yang", "dest_vertex_label": "list_1", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "yang_to_leaflist_2", "source_vertex_label": "yang", "dest_vertex_label": "leaflist_2", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "yang_to_leaflist_3", "source_vertex_label": "yang", "dest_vertex_label": "leaflist_3", "source_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]