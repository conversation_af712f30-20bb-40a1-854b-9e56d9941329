{"type": "container", "name": "container_1", "clause": [{"type": "must", "formula": "re-match('str1')"}, {"type": "must", "formula": "concat('str1') = 'str'"}, {"type": "must", "formula": "contains('str1')"}, {"type": "must", "formula": "substring-before('str1') = 'str'"}, {"type": "must", "formula": "substring-after('str1') != 'str'"}, {"type": "when", "formula": "substring('str1') = 'str'"}], "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "con_1_F1", "type": "int32", "clause": [{"type": "leafref", "formula": "deref()"}, {"type": "must", "formula": "boolean()"}, {"type": "must", "formula": "lang()"}, {"type": "must", "formula": "sum() > 1"}, {"type": "must", "formula": "floor() > 1"}, {"type": "when", "formula": "deref() = /container_1"}]}, {"type": "container", "name": "container_2", "clause": [{"type": "must", "formula": "derived-from(/container_1)"}, {"type": "must", "formula": "derived-from-or-self(/container_1)"}, {"type": "must", "formula": "enum-value() = 1"}, {"type": "must", "formula": "bit-is-set(/container_1)"}, {"type": "must", "formula": "re-match('str1')"}, {"type": "must", "formula": "ceiling() > 1"}, {"type": "must", "formula": "round() < 1"}, {"type": "when", "formula": "re-match('str1')"}], "fields": [{"name": "con_2_F1", "type": "int32", "clause": [{"type": "leafref", "formula": "deref()"}, {"type": "must", "formula": "concat('str1') = 'str'"}, {"type": "must", "formula": "contains('str1')"}, {"type": "must", "formula": "substring-before('str1') = 'str'"}, {"type": "must", "formula": "substring-after('str1') != 'str'"}, {"type": "must", "formula": "substring('str1') = 'str'"}, {"type": "when", "formula": "substring('str1') = 'str'"}]}, {"type": "choice", "name": "choice_1", "clause": [{"type": "when", "formula": "substring('str1') = 'str'"}], "fields": [{"type": "case", "name": "case_1_1", "clause": [{"type": "when", "formula": "boolean()"}], "fields": [{"name": "case_1_1_F1", "type": "int32", "clause": [{"type": "leafref", "formula": "deref()"}, {"type": "must", "formula": "lang()"}, {"type": "must", "formula": "sum() > 1"}, {"type": "must", "formula": "floor() > 1"}, {"type": "must", "formula": "deref() = /container_1"}, {"type": "must", "formula": "derived-from(/container_1)"}, {"type": "must", "formula": "derived-from-or-self(/container_1)"}, {"type": "when", "formula": "enum-value() = 1"}]}]}, {"type": "case", "name": "case_1_2", "clause": [{"type": "when", "formula": "bit-is-set(/container_1)"}], "fields": [{"name": "case_1_2_F1", "type": "int32", "clause": [{"type": "leafref", "formula": "deref()"}, {"type": "must", "formula": "ceiling() > 1"}, {"type": "must", "formula": "round() < 1"}, {"type": "must", "formula": "re-match('str1')"}, {"type": "must", "formula": "concat('str1') = 'str'"}, {"type": "when", "formula": "contains('str1')"}]}]}]}]}], "keys": [{"node": "container_1", "name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}