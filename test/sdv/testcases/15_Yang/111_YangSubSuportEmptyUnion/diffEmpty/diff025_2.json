alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.F0:update(101,100)
alias_ContainerOne.F1:remove([null])
alias_ContainerOne.F2:remove(100)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:100)),(priKey(PID:1,F0:100))]
alias_ListOne.F2:remove(100)
alias_ListOne.ContainerTwo:update
ContainerTwo.F0:update(101,100)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:101), preKey(PID:1,F0:100)),(priKey(PID:1,F0:101), preKey(PID:1,F0:100))]
alias_ListOne.F2:remove(101)
alias_ListOne.F3:remove([null])
alias_ListOne.ContainerTwo:remove
ContainerTwo.F0:remove(101)
ContainerTwo.F1:remove([null])
ContainerTwo.F5:remove(555)
ContainerTwo.F6:remove(666)
ContainerTwo.F7:remove(777)
ContainerTwo.F8:remove(888)
ContainerTwo.F9:remove(999)
ContainerTwo.F11:remove(default11)
ContainerTwo.F12:remove(default12)
ContainerTwo.F13:remove(default13)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:102), preKey(PID:1,F0:101)),(priKey(PID:1,F0:102), preKey(PID:1,F0:101))]
alias_ListOne.F2:remove(102)
alias_ContainerOne.alias_ListOne:create[(priKey(PID:1,F0:103), preKey(PID:1,F0:102)),(NULL)]
alias_ListOne.ID:create(7)
alias_ListOne.F1:create([null])
alias_ListOne.F4:create(456)
