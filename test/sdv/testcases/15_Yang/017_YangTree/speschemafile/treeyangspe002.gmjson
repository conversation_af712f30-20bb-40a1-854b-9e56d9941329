[{"type": "container", "name": "T0", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}], "keys": [{"name": "T0.PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "T0::T1", "is_config": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "T2", "type": "container", "is_config": false, "fields": [{"name": "A0", "type": "int32"}]}], "keys": [{"name": "T1.PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"name": "T2.F1", "fields": ["PID", "F1"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}]