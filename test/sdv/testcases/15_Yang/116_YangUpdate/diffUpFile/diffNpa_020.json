alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.F1:update(101,100)
alias_ContainerOne.F5:remove(555)
alias_ContainerOne.F6:create(666)
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:str000)),(priKey(PID:1,F0:str000))]
alias_ListOne.F3:remove(100)
alias_ListOne.ContainerTwo:remove
ContainerTwo.F0:remove(100)
alias_ListOne.ContainerThree:create
ContainerThree.F1:create(100)
alias_ListOne.Choice:remove
Choice.CaseOne:remove
CaseOne.F0:remove(100)
alias_ListOne.alias_LeafListThree:remove[(NULL),(priKey(PID:1,F0:2))]
alias_ListOne.alias_LeafListThree:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]
alias_ListOne.alias_ListTwo:update[(priKey(PID:1,F0:1)),(priKey(PID:1,F0:1))]
alias_ListTwo.F3:remove(100)
alias_ListTwo.F4:create(100)
alias_ListTwo.ContainerTwo:remove
ContainerTwo.F0:remove(100)
alias_ListTwo.ContainerThree:create
ContainerThree.F1:create(100)
alias_ListTwo.Choice:remove
Choice.CaseOne:remove
CaseOne.F0:remove(100)
alias_ListOne.alias_LeafListAdd:remove[(NULL),(priKey(PID:1,F0:2))]
alias_ListOne.alias_LeafListAdd:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]
alias_ContainerOne.alias_ListOne:update[(priKey(PID:1,F0:str001), preKey(PID:1,F0:str000)),(priKey(PID:1,F0:str001), preKey(PID:1,F0:str000))]
alias_ListOne.F3:remove(100)
alias_ListOne.ContainerTwo:remove
ContainerTwo.F0:remove(100)
alias_ListOne.ContainerThree:create
ContainerThree.F1:create(100)
alias_ListOne.Choice:remove
Choice.CaseOne:remove
CaseOne.F0:remove(100)
alias_ListOne.alias_LeafListThree:remove[(NULL),(priKey(PID:2,F0:2))]
alias_ListOne.alias_LeafListThree:remove[(NULL),(priKey(PID:2,F0:3), preKey(PID:2,F0:2))]
alias_ListOne.alias_ListTwo:update[(priKey(PID:2,F0:1)),(priKey(PID:2,F0:1))]
alias_ListTwo.F3:remove(100)
alias_ListTwo.F4:create(100)
alias_ListTwo.ContainerTwo:remove
ContainerTwo.F0:remove(100)
alias_ListTwo.ContainerThree:create
ContainerThree.F1:create(100)
alias_ListTwo.Choice:remove
Choice.CaseOne:remove
CaseOne.F0:remove(100)
alias_ListOne.alias_LeafListAdd:remove[(NULL),(priKey(PID:2,F0:2))]
alias_ListOne.alias_LeafListAdd:remove[(NULL),(priKey(PID:2,F0:3), preKey(PID:2,F0:2))]
