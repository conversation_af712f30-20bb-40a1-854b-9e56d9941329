[{"name": "ContainerList", "source_vertex_label": "ContainerOne", "dest_vertex_label": "ListOne", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ContainerLeafList", "source_vertex_label": "ListOne", "dest_vertex_label": "LeafList", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ContainerLeafListTwo", "source_vertex_label": "ListOne", "dest_vertex_label": "LeafListTwo", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ContainerLeafList<PERSON><PERSON>ee", "source_vertex_label": "ListOne", "dest_vertex_label": "LeafList<PERSON><PERSON><PERSON>", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}, {"name": "ListListTwo", "source_vertex_label": "ListOne", "dest_vertex_label": "ListTwo", "constraint": {"operator_type": "and", "conditions": [{"source_property": "ID", "dest_property": "PID"}]}}]