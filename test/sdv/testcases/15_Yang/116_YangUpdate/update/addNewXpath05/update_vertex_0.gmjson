[{"type": "container", "name": "main_label", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "root_F1", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/main_label/root_F2 < 100"}]}, {"name": "root_F2", "type": "uint32", "nullable": true}, {"name": "root_F3", "type": "uint32", "nullable": true, "clause": [{"type": "must", "formula": "/main_label/root_F4 < 100"}]}, {"name": "root_F4", "type": "uint32", "nullable": true}, {"name": "root_F5", "type": "uint32", "nullable": true}, {"name": "root_F6", "type": "uint32", "nullable": false}, {"name": "root_F7", "type": "uint32", "nullable": true, "clause": [{"type": "leafref", "formula": "/main_label/list_label/list_F1"}]}], "keys": [{"name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_label", "min-elements": 5, "max-elements": 10, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "list_F1", "type": "uint32", "nullable": false}, {"name": "list_F2", "type": "uint32", "nullable": true}, {"name": "list_F3", "type": "uint32", "nullable": true}], "keys": [{"name": "pk", "fields": ["PID", "list_F1"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]