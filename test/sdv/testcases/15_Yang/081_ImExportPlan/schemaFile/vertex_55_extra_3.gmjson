[{"type": "container", "name": "main_label", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "root_F1", "type": "int32", "nullable": true, "default": 1}, {"name": "root_F2", "type": "int32", "nullable": true, "default": 2}, {"type": "container", "name": "container_1", "clause": [{"type": "when", "formula": "/main_label/root_F2 < 100"}], "fields": [{"name": "con_1_F1", "type": "int32", "nullable": true, "default": 3}, {"name": "con_1_F2", "type": "int32", "nullable": true, "default": 4}]}, {"type": "container", "name": "container_2", "fields": [{"name": "con_2_F1", "type": "int32", "nullable": true, "default": 5}, {"name": "con_2_F2", "type": "int32", "nullable": true, "default": 6}, {"type": "container", "name": "container_2_1", "clause": [{"type": "when", "formula": "/main_label/root_F2 < 100"}], "fields": [{"name": "con_2_1_F1", "type": "int32", "nullable": true, "default": 7}, {"name": "con_2_1_F2", "type": "int32", "nullable": true, "default": 8}]}]}], "keys": [{"node": "main_label", "name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]