{"type": "container", "name": "container_1", "clause": [{"type": "when", "formula": "undefined() < 0"}], "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "con_1_F1", "type": "int32", "clause": [{"type": "when", "formula": "undefined() = '1'"}]}, {"name": "con_1_F2", "type": "int32", "clause": [{"type": "when", "formula": "undefined() = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefg'"}]}, {"name": "con_1_F3", "type": "int32", "clause": [{"type": "when", "formula": "undefined('ABC', 'AC')"}]}, {"type": "container", "name": "container_2", "clause": [{"type": "when", "formula": "undefined() != 'ABC'"}], "fields": [{"name": "con_2_F1", "type": "int32", "clause": [{"type": "when", "formula": "undefined() < 10000"}]}, {"name": "con_2_F2", "type": "int32", "clause": [{"type": "when", "formula": "undefined()"}]}, {"name": "con_2_F3", "type": "int32", "clause": [{"type": "when", "formula": "undefined()"}]}, {"type": "choice", "name": "choice_1", "clause": [{"type": "when", "formula": "undefined()"}], "fields": [{"type": "case", "name": "case_1_1", "clause": [{"type": "when", "formula": "undefined() < 0"}], "fields": [{"name": "case_1_1_F1", "type": "int32", "clause": [{"type": "when", "formula": "undefined() = '1'"}]}, {"name": "case_1_1_F2", "type": "int32", "clause": [{"type": "when", "formula": "undefined() = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefg'"}]}, {"name": "case_1_1_F3", "type": "int32", "clause": [{"type": "when", "formula": "undefined()"}]}]}, {"type": "case", "name": "case_1_2", "clause": [{"type": "when", "formula": "undefined() != 'ABC'"}], "fields": [{"name": "case_1_2_F1", "type": "int32", "clause": [{"type": "when", "formula": "undefined() < 10000"}]}, {"name": "case_1_2_F2", "type": "int32", "clause": [{"type": "when", "formula": "undefined()"}]}, {"name": "case_1_2_F3", "type": "int32", "clause": [{"type": "when", "formula": "undefined()"}]}]}]}]}], "keys": [{"node": "container_1", "name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}