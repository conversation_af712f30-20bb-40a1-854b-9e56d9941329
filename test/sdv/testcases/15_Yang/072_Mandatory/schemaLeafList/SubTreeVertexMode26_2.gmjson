{
    "type":"leaf-list",
    "name":"LeafList#(0)#",
    "min-elements":0,
    "max-elements":10000,
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false, "default":[#(1)#]}
    ],
    "keys":[
        {
            "fields":["PID", "F0"],
            "node":"LeafList#(0)#",
            "name":"PK",
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
}
