[{"type": "container", "name": "root_A", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true, "comment": "绝对路径+上层字段", "clause": [{"type": "when", "formula": "/root_A/con_2_A or /root_A/con_2_A/F2 > 100"}]}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_1_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}, {"type": "container", "name": "con_2_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true, "comment": "绝对路径+上层字段", "clause": [{"type": "when", "formula": "/root_A/con_2_A or /root_A/con_4_A/list_4_1_A/F2 > 100"}]}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_2_1_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}, {"type": "container", "name": "con_3_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "choice", "name": "choice_3_1_A", "fields": [{"type": "case", "name": "case_3_1_1_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true, "comment": "绝对路径+上层字段", "clause": [{"type": "when", "formula": "/root_A/con_2_A or ../F4 > 100"}]}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true, "comment": "绝对路径+上层字段", "clause": [{"type": "when", "formula": "/root_A/con_2_A or /root_A/list_14_A/list_14_1_A/F2 > 100"}]}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case_3_1_2_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}]}, {"type": "container", "name": "con_4_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}, {"type": "container", "name": "con_5_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}, {"type": "choice", "name": "choice_6_A", "fields": [{"type": "case", "name": "case_6_1_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}, {"type": "choice", "name": "choice_7_A", "fields": [{"type": "case", "name": "case_7_1_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_7_1_1_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}]}, {"type": "choice", "name": "choice_8_A", "fields": [{"type": "case", "name": "case_8_1_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "choice", "name": "choice_8_1_1_A", "fields": [{"type": "case", "name": "case_8_1_1_1_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}]}]}, {"type": "choice", "name": "choice_9_A", "fields": [{"type": "case", "name": "case_9_1_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case_9_2_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true, "comment": "绝对路径+上层字段", "clause": [{"type": "when", "formula": "/root_A/con_2_A or /root_A/con_3_A/choice_3_1_A/case_3_1_1_A/F2 > 100"}]}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}, {"type": "choice", "name": "choice_10_A", "fields": [{"type": "case", "name": "case_10_1_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}], "keys": [{"node": "root_A", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_4_1_A", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true, "comment": "绝对路径+上层字段", "clause": [{"type": "when", "formula": "/root_A/con_2_A or /root_A/leaflist_16_A/PK > 100"}]}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_4_1_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_9_1_1_A", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_9_1_1_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_9_2_1_A", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_9_2_1_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_11_A", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_11_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_12_A", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_12_1_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}], "keys": [{"fields": ["PID", "PK"], "node": "list_12_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_13_A", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}, {"type": "choice", "name": "choice_13_1_A", "fields": [{"type": "case", "name": "case_13_1_1_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case_13_1_2_A", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}]}]}], "keys": [{"fields": ["PID", "PK"], "node": "list_13_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_14_A", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_14_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_14_1_A", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true, "comment": "绝对路径+上层字段", "clause": [{"type": "when", "formula": "/root_A/con_2_A or ../../../leaflist_19_A/PK > 100"}]}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_14_1_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_15_A", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true}, {"name": "F6", "type": "uint32", "nullable": true}, {"name": "F7", "type": "uint32", "nullable": true}, {"name": "F8", "type": "uint32", "nullable": true}, {"name": "F9", "type": "uint32", "nullable": true}, {"name": "F10", "type": "uint32", "nullable": true}], "keys": [{"fields": ["PID", "PK"], "node": "list_15_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_5_1_A", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_5_1_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_10_1_1_A", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_10_1_1_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_15_1_A", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_15_1_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_16_A", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false, "comment": "绝对路径+上层字段", "clause": [{"type": "when", "formula": "/root_A/con_2_A or /root_A/choice_9_A/case_9_2_A/F2 > 100"}]}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_16_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_17_A", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_17_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_18_A", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_18_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_19_A", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false, "comment": "绝对路径+上层字段", "clause": [{"type": "when", "formula": "/root_A/con_2_A or ../../F2 > 100"}]}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_19_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_20_A", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "PK"], "node": "leaflist_20_A", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}]