/*
表空间隔离  迭代三
=================================== 表空间导入 ===================================
120 带-t参数，表指定tsp和nsp绑定tsp，查询tablespace视图
121 没有-ns参数，查询tablespace视图
122 122 带-ns参数，表指定tsp，不带-t，预期失败
123 带-ns参数，两个表指定不同的tsp，tsp绑定相同的nsp
124 带-ns参数，表没有指定tsp，预期使用public

author : 唐广明
*/

#include "tablespace_isolation.h"

class TspImport_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TspImport_test::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TspImport_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("ipcrm -a");
    GmcDetachAllShmSeg();
    testEnvClean();
}

void TspImport_test::SetUp()
{
    int ret;
    // 同步连接、异步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, "simpleLabel");
    GmcDropVertexLabel(g_stmt, "simpleLabel2");
    GmcDropVertexLabel(g_stmt, "simpleLabel3");
    
    GmcDropTablespace(g_stmt, "tsp1");
    GmcDropTablespace(g_stmt, "tsp2");
    GmcDropTablespace(g_stmt, "tsp3");
}

void TspImport_test::TearDown()
{
    int ret;
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 120 带-t参数，表指定tsp和nsp绑定tsp，查询tablespace视图
TEST_F(TspImport_test, Resilience_010_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    const char *tablespaceName = "tsp1";
    const char *namespaceName = (const char *)"userA";

    // 创建表空间
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 4;
    tspCfg.stepSize = 4;
    tspCfg.maxSize = 4;

    ret = GmcCreateTablespaceAsync(g_stmt_async, &tspCfg, create_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建namespace1绑定tsp1
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespaceName;
    nspCfg.userName = (const char *)"abc";
    nspCfg.tablespaceName = tablespaceName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 悲观+读已提交

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_INFO, "============ 导入表空间 ============\n");
    snprintf(g_cmd, MAX_CMD_SIZE,
        "%s/gmimport -c vschema -t simpleLabel -f ./schemaFile/001.gmjson -ns userA", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_cmd);
    ret = executeCommand(g_cmd, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ 查看表空间信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看Heap信息 ============\n");
    HeapInfo();

    AW_FUN_Log(LOG_INFO, "============ 查看表信息 ============\n");
    VertexLabelInfo();

    AW_FUN_Log(LOG_INFO, "============ 删表、nsp和tsp ============\n");
    ret = GmcUseNamespaceAsync(g_stmt_async, namespaceName, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestDeleteVertexAsync(g_vertexLabelName2);
    
    // 删除nsp
    TestDropNspAsync(namespaceName);
    // 删除tsp
    TestDropTablespaceAsync(tablespaceName);

    AW_FUN_Log(LOG_INFO, "============ 查看表空间信息 ============\n");
    TspInfo();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 121 没有-ns参数，查询tablespace视图
TEST_F(TspImport_test, Resilience_010_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    const char *tablespaceName = "tsp1";
    const char *namespaceName = (const char *)"userA";

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName);

    // 创建namespace1绑定tsp1
    TestCreateNspAsync(namespaceName, tablespaceName);

    AW_FUN_Log(LOG_INFO, "============ 导入表空间 ============\n");
    snprintf(g_cmd, MAX_CMD_SIZE,
        "%s/gmimport -c vschema -t simpleLabel -f ./schemaFile/001.gmjson", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_cmd);
    ret = executeCommand(g_cmd, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ 查看表空间信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看Heap信息 ============\n");
    HeapInfo();

    AW_FUN_Log(LOG_INFO, "============ 查看表信息 ============\n");
    VertexLabelInfo();

    AW_FUN_Log(LOG_INFO, "============ 删表、nsp和tsp ============\n");
    // 未使用-ns，删表时不需要使用nsp
    TestDeleteVertexAsync(g_vertexLabelName2);
    
    // 删除nsp
    TestDropNspAsync(namespaceName);
    // 删除tsp
    TestDropTablespaceAsync(tablespaceName);

    AW_FUN_Log(LOG_INFO, "============ 查看表空间信息 ============\n");
    TspInfo();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 122 带-ns参数，表指定tsp，不带-t，预期失败
TEST_F(TspImport_test, Resilience_010_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    const char *tablespaceName = "tsp1";
    const char *namespaceName = (const char *)"userA";

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName);

    // 创建namespace1绑定tsp1
    TestCreateNspAsync(namespaceName, tablespaceName);

    AW_FUN_Log(LOG_INFO, "============ 导入表空间 ============\n");
    // 导入失败
    snprintf(g_cmd, MAX_CMD_SIZE,
        "%s/gmimport -c vschema -f ./schemaFile/001.gmjson -ns userA", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_cmd);
    ret = executeCommand(g_cmd, "ret = 1009001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入成功
    snprintf(g_cmd, MAX_CMD_SIZE,
        "%s/gmimport -c vschema -t simpleLabel -f ./schemaFile/001.gmjson -ns userA", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_cmd);
    ret = executeCommand(g_cmd, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ 查看表空间信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看Heap信息 ============\n");
    HeapInfo();

    AW_FUN_Log(LOG_INFO, "============ 查看表信息 ============\n");
    VertexLabelInfo();

    AW_FUN_Log(LOG_INFO, "============ 删表、nsp和tsp ============\n");
    ret = GmcUseNamespaceAsync(g_stmt_async, namespaceName, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestDeleteVertexAsync(g_vertexLabelName2);
    
    // 删除nsp
    TestDropNspAsync(namespaceName);
    // 删除tsp
    TestDropTablespaceAsync(tablespaceName);

    AW_FUN_Log(LOG_INFO, "============ 查看表空间信息 ============\n");
    TspInfo();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 123 带-ns参数，两个表指定不同的tsp，tsp绑定相同的nsp
TEST_F(TspImport_test, Resilience_010_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    const char *tablespaceName1 = "tsp1";
    const char *tablespaceName2 = "tsp2";
    const char *tablespaceName3 = "tsp3";
    const char *namespaceName = (const char *)"userA";

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName1);
    testCreateTablespaceAsync(tablespaceName2);
    testCreateTablespaceAsync(tablespaceName3);

    // 创建namespace1绑定tsp3
    TestCreateNspAsync(namespaceName, tablespaceName3);

    // use namespace
    ret = GmcUseNamespaceAsync(g_stmt_async, namespaceName, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    
    AW_FUN_Log(LOG_INFO, "============ 导入表空间 ============\n");
    // simpleLabel绑定tsp1
    snprintf(g_cmd, MAX_CMD_SIZE,
        "%s/gmimport -c vschema -t simpleLabel -f ./schemaFile/001.gmjson -ns userA", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_cmd);
    ret = executeCommand(g_cmd, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // simpleLabel3绑定tsp2
    snprintf(g_cmd, MAX_CMD_SIZE,
        "%s/gmimport -c vschema -t simpleLabel3 -f ./schemaFile/003.gmjson -ns userA", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_cmd);
    ret = executeCommand(g_cmd, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ 查看表空间信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看Heap信息 ============\n");
    HeapInfo();

    AW_FUN_Log(LOG_INFO, "============ 查看表信息 ============\n");
    VertexLabelInfo();

    AW_FUN_Log(LOG_INFO, "============ 删表、nsp和tsp ============\n");
    ret = GmcUseNamespaceAsync(g_stmt_async, namespaceName, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestDeleteVertexAsync(g_vertexLabelName2);
    TestDeleteVertexAsync(g_vertexLabelName3);
    
    // 删除nsp
    TestDropNspAsync(namespaceName);
    // 删除tsp
    TestDropTablespaceAsync(tablespaceName1);
    TestDropTablespaceAsync(tablespaceName2);
    TestDropTablespaceAsync(tablespaceName3);

    AW_FUN_Log(LOG_INFO, "============ 查看表空间信息 ============\n");
    TspInfo();

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 124 带-ns参数，表没有指定tsp，预期使用public
TEST_F(TspImport_test, Resilience_010_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    AsyncUserDataT userData = {0};
    const char *tablespaceName = "tsp1";

    // 创建表空间
    testCreateTablespaceAsync(tablespaceName);

    AW_FUN_Log(LOG_INFO, "============ 导入表空间 ============\n");
    snprintf(g_cmd, MAX_CMD_SIZE,
        "%s/gmimport -c vschema -t simpleLabel -f ./schemaFile/001_notsp.gmjson -ns public", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_cmd);
    ret = executeCommand(g_cmd, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, "============ 查看表空间信息 ============\n");
    TspInfo();
    AW_FUN_Log(LOG_INFO, "============ 查看Heap信息 ============\n");
    HeapInfo();

    AW_FUN_Log(LOG_INFO, "============ 查看表信息 ============\n");
    VertexLabelInfo();

    AW_FUN_Log(LOG_INFO, "============ 删表、nsp和tsp ============\n");
    // 使用默认nsp，删表时不需要使用nsp
    TestDeleteVertexAsync(g_vertexLabelName2);

    // 删除tsp
    TestDropTablespaceAsync(tablespaceName);

    AW_FUN_Log(LOG_INFO, "============ 查看表空间信息 ============\n");
    TspInfo();

    AW_FUN_Log(LOG_STEP, "test end.");
}
