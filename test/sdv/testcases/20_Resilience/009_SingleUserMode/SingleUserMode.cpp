/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include "gtest/gtest.h"

#include "t_datacom_lite.h"

GmcConnT *g_conn_sync = NULL;
GmcStmtT *g_stmt_sync = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_sub = NULL;
GmcStmtT *g_stmt_sub = NULL;
AsyncUserDataT data = {0};
#define MAX_CMD_SIZE 1024
#define MAX_LABELNAME_LEN 128

char g_command[MAX_CMD_SIZE];
char messCompare[MAX_CMD_SIZE];
char messCompare1[MAX_CMD_SIZE];
char view_name[32] = "V\\$DRT_CONN_STAT";
const char *g_subConnName = "subConnName";
const char *keyName = "T40_PK";
bool gIsSnCallbackWait = true;
const char *normal_config_json = R"(
    {
        "max_record_count":100000
    }
)";

class SingleUserMode : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    #ifdef ENV_EULER
        (void)snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/modifyCfg.sh \"clientServerFlowControl=0;0;0;0\"");
        system(g_command);
    #endif
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/stop.sh -f");
    };
    virtual void SetUp()
    {
        // 导入白名单
        const char *allow_list_file = "./schema_file/allow_list.gmuser";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
            g_connServer);

        int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //导入必备系统权限 create get drop
        const char *sys_policy_file = "./schema_file/sysVertex.gmpolicy";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath,
            sys_policy_file, g_connServer);
        ret = executeCommand(g_command, "import policy. object privilege success: 0, warning: 0.",
            "import policy. system privilege success: 1, warning: 0.");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_CHECK_LOG_BEGIN();
        const int errCodeLen = 1024;
        char errorMsg1[errCodeLen] = {0};
        (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
        AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        // 删除白名单
        const char *allow_list_file = "./schema_file/allow_list.gmuser";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
            g_connServer);
        int ret = executeCommand(g_command, "remove allowlist, remove db user. success: 5, warning: 0.",
            "remove allowlist, remove db group. success: 1, warning: 0.");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};

// 001 gmips -mode -1
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode -1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    snprintf(messCompare, MAX_CMD_SIZE, "[gmips] DB init option param unsucc, ret = %d", GMERR_SYNTAX_ERROR);
    snprintf(messCompare1, MAX_CMD_SIZE, "[gmips] Init args unsucc. ret = %d", GMERR_SYNTAX_ERROR);
    ret = executeCommand(g_command,
        "The parameter of option(\"-mode\") is overflow the range((0, 1)), the \"-1\" is unsound.",
        messCompare, messCompare1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 002 gmips -mode 2
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 2", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    snprintf(messCompare, MAX_CMD_SIZE, "[gmips] DB init option param unsucc, ret = %d", GMERR_SYNTAX_ERROR);
    snprintf(messCompare1, MAX_CMD_SIZE, "[gmips] Init args unsucc. ret = %d", GMERR_SYNTAX_ERROR);
    ret = executeCommand(g_command,
        "The parameter of option(\"-mode\") is overflow the range((0, 1)), the \"2\" is unsound.",
        messCompare, messCompare1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 003 gmips -modes 0
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -modes 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    snprintf(messCompare, MAX_CMD_SIZE, "[gmips] DB init option param unsucc, ret = %d", GMERR_INVALID_OPTION);
    snprintf(messCompare1, MAX_CMD_SIZE, "[gmips] Init args unsucc. ret = %d", GMERR_INVALID_OPTION);
    ret = executeCommand(g_command,
        "The first option \"-modes\" is undefined.", messCompare, messCompare1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 004 gmips -mode abc
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode abc", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    snprintf(messCompare, MAX_CMD_SIZE, "[gmips] DB init option param unsucc, ret = %d", GMERR_SYNTAX_ERROR);
    snprintf(messCompare1, MAX_CMD_SIZE, "[gmips] Init args unsucc. ret = %d", GMERR_SYNTAX_ERROR);
    ret = executeCommand(g_command,
        "The parameter of option(\"-mode\") is integer and only 32-bit signed integers are supported,"
        " the \"abc\" is unsound.", messCompare, messCompare1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005 正常模式切换单用户模式
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 006 单用户模式切换正常模式
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预置模式为单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 007 单用户模式调用正常模式下的连接
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 创建同步/异步/订阅连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, "subConnName");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    char *label_schema = NULL;
    readJanssonFile("schema_file/NormalVertexLabel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcGetLastError("Have no privilege. Check server mode unsucc.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sub, g_stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 008 单用户模式释放正常模式下的连接
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 创建同步/异步/订阅连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, "subConnName");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sub, g_stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009 单用户模式下进行建连
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
     // 创建同步/异步/订阅连接  报错
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, "subConnName");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
class SingleUserMode_01 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    };

    virtual void SetUp()
    {
        AW_CHECK_LOG_BEGIN();
        const int errCodeLen = 1024;
        char errorMsg1[errCodeLen] = {0};
        (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
        AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
    }
};
// 010 不鉴权进行切换单用户模式
TEST_F(SingleUserMode_01, Resilience_009_SingleUserMode_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    // 切换单用户模式 报错
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    snprintf(messCompare, MAX_CMD_SIZE, "[gmips] set server mode 1 unsucc, ret = %d",
        GMERR_FEATURE_NOT_SUPPORTED);
    ret = executeCommand(g_command, messCompare);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 011 连续不同模式的切换后使用正常模式的连接
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 创建同步/异步/订阅连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, "subConnName");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    char *label_schema = NULL;
    readJanssonFile("schema_file/NormalVertexLabel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);//报错
    free(label_schema);
    // 释放连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sub, g_stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 012 连续不同模式的切换后使用单用户模式的连接
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(2, "GMERR-1018004", "GMERR-1004005");
    int ret = 0;
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建同步/异步/订阅连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, "subConnName");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    char *label_schema = NULL;
    readJanssonFile("schema_file/NormalVertexLabel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // 切换正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
     // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建同步/异步/订阅连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, "subConnName");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    free(label_schema);
    // 建表
    readJanssonFile("schema_file/NormalVertexLabel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);//报错
    free(label_schema);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 013 切换单用户模式后新建连接进行DML操作
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(2, "GMERR-1018004", "GMERR-1004005");
    int ret = 0;
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建同步/异步/订阅连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, "subConnName");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    char *label_schema = NULL;
    readJanssonFile("schema_file/NormalVertexLabel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    free(label_schema);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 014 单用户模式切换正常模式后新建连接进行DML操作
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建同步/异步/订阅连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建同步/异步/订阅连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建表
    char *label_schema = NULL;
    readJanssonFile("schema_file/NormalVertexLabel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "T40", GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1000; i++) {
        ret = GmcSetVertexProperty(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F2", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F3", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt_sync, "T40");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 切换单用户模式后重启server验证模式重置为正常模式
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(2, "GMERR-1018004", "GMERR-1009012");
    int ret = 0;
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建同步/异步/订阅连接 报错
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *allow_list_file = "./schema_file/allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //导入必备系统权限 create get drop
    const char *sys_policy_file = "./schema_file/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);

    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建同步/异步/订阅连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建表
    char *label_schema = NULL;
    readJanssonFile("schema_file/NormalVertexLabel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "T40", GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1000; i++) {
        ret = GmcSetVertexProperty(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F2", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F3", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt_sync, "T40");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017 连续切换单用户模式后使用第一次切换的连接
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建同步/异步/订阅连接 报错
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, "subConnName");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建同步/异步/订阅连接 报错
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, "subConnName");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 018 连续切换正常模式后使用第一次切换的连接
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 创建同步/异步/订阅连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    char *label_schema = NULL;
    readJanssonFile("schema_file/NormalVertexLabel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "T40", GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1000; i++) {
        ret = GmcSetVertexProperty(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F2", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F3", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(g_stmt_sync, "T40");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 019 循环10000次切换单用户模式
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 0; i < 10000; i++) {
        ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 020 循环10000次切换正常模式
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 切换正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 0; i < 10000; i++) {
        ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 021 切换单用户模式后切换正常模式，循环10000次
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    #if defined CPU_BIT_32
    int num = 500;
    #else
    int num = 5000;
    #endif
    AW_ADD_ERR_WHITE_LIST(4, "GMERR-1018004", "GMERR-1010000", "GMERR-10015002", "GMERR-10015004");
    int ret = 0;
    char command1[1024];
    char command2[1024];
    snprintf(command1, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", command1);
    snprintf(command2, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", command2);
    for(int i = 0; i < num; i++) {
        // 切换单用户模式
        ret = executeCommand(command1, "[gmips] set server mode 1 successfully");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 恢复正常模式
        ret = executeCommand(command2, "[gmips] set server mode 0 successfully");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *NormalMode_Thread(void *args)
{
    char command1[MAX_CMD_SIZE];
    // 切换正常模式
    snprintf(command1, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    system(command1);
    return NULL;
}
void *SingleUserMode_Thread(void *args)
{
    char command2[MAX_CMD_SIZE];
    // 切换正常模式
    snprintf(command2, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    system(command2);
    return NULL;
}
// 022 多线程并发切换单用户模式和正常模式
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1010003");
    int ret = 0;
    pthread_t tid[100];
    for (int i = 0; i < 50; i++) {
        ret = pthread_create(&tid[i], NULL, NormalMode_Thread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&tid[i + 50], NULL, SingleUserMode_Thread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < 100; i++) {
       pthread_join(tid[i], NULL);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 023 正常模式满连接后切换单用户模式
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNEXPECTED_NULL_VALUE);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
    int ret = 0;
    uint32_t existConnNum = 0;
    GmcConnT *conn[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt[MAX_CONN_SIZE] = {0};
    for (int i = 0; i < MAX_CONN_SIZE; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 切换单用户模式 baocuo
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    snprintf(messCompare, MAX_CMD_SIZE, "[gmips] Connect server unsucc. ret = %d", GMERR_TOO_MANY_CONNECTIONS);
    ret = executeCommand(g_command, messCompare);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < MAX_CONN_SIZE; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 单用户模式满连接后切换正常模式
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNEXPECTED_NULL_VALUE);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    AddWhiteList(GMERR_TOO_MANY_CONNECTIONS);
    int ret = 0;
    GmcConnT *conn[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt[MAX_CONN_SIZE] = {0};
    for (int i = 0; i < MAX_CONN_SIZE - 1; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建连1024个
    ret = testGmcConnect(&conn[MAX_CONN_SIZE-1], &stmt[MAX_CONN_SIZE-1]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    snprintf(messCompare, MAX_CMD_SIZE, "[gmips] Connect server unsucc. ret = %d", GMERR_TOO_MANY_CONNECTIONS);
    ret = executeCommand(g_command, messCompare);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < MAX_CONN_SIZE; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}
class SingleUserMode_02 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"DBA=root:gmrule;gmips;gmadmin\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"maxSeMem=300\"");
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    };

    virtual void SetUp()
    {
        // 导入白名单
        const char *allow_list_file = "./schema_file/allow_list.gmuser";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s ", g_toolPath, allow_list_file,
            g_connServer);

        int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //导入必备系统权限 create get drop
        const char *sys_policy_file = "./schema_file/sysVertex.gmpolicy";
        snprintf(
            g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s ", g_toolPath, sys_policy_file);

        ret = executeCommand(g_command, "Import single policy file", "successfully.");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    virtual void TearDown()
    {
        // 删除白名单
        const char *allow_list_file = "./schema_file/allow_list.gmuser";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s ", g_toolPath, allow_list_file,
            g_connServer);

        int ret = executeCommand(g_command, "remove allowlist, remove db user. success: 5, warning: 0.",
            "remove allowlist, remove db group. success: 1, warning: 0.");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
};
// 025 内存写满后切换单用户模式进行操作
TEST_F(SingleUserMode_02, Resilience_009_SingleUserMode_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建表
    char *label_schema = NULL;
    readJanssonFile("schema_file/Vertex_01_Memory.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, label_schema, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    int i = 0;
    uint32_t insertvalue = 0;
    while (ret == 0) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "vertex_01_Memory", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        insertvalue = i;
        ret = GmcSetVertexProperty(g_stmt_sync, "PK", GMC_DATATYPE_UINT32, &insertvalue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &insertvalue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F1", GMC_DATATYPE_UINT32, &insertvalue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写string数据
        uint32_t SuperSize = 1024;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'B', (SuperSize - 1));
        SuperValue[SuperSize - 1] = '\0';
        ret = GmcSetVertexProperty(g_stmt_sync, "P0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "P1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "P2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "P3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "P4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "P5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "P6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(SuperValue);
        ret = GmcExecute(g_stmt_sync);
        i++;
        if ((i % 50000) == 0) {
            AW_FUN_Log(LOG_DEBUG, "till now:insert records %d\n", i);
        }
    }
    AW_FUN_Log(LOG_DEBUG, "actul insert records is:%d\n", i);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret= GmcDropVertexLabel(g_stmt_sync, "vertex_01_Memory");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *replace_Thread(void *args)
{
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn1 = NULL;
    int ret = testGmcConnect(&conn1, &stmt1);
    ret = (ret == GMERR_INSUFFICIENT_PRIVILEGE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1, "T40", GMC_OPERATION_REPLACE);
    ret = (ret == GMERR_NULL_VALUE_NOT_ALLOWED ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < 100; i++) {
        ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ret = (ret == GMERR_NULL_VALUE_NOT_ALLOWED ? GMERR_OK : ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ret = (ret == GMERR_NULL_VALUE_NOT_ALLOWED ? GMERR_OK : ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ret = (ret == GMERR_NULL_VALUE_NOT_ALLOWED ? GMERR_OK : ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ret = (ret == GMERR_NULL_VALUE_NOT_ALLOWED ? GMERR_OK : ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        if (ret != GMERR_OK) {
            break;
        }
    }
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
void *query_Thread(void *args)
{
    GmcStmtT *stmt2 = NULL;
    GmcConnT *conn2 = NULL;
    int ret = testGmcConnect(&conn2, &stmt2);
    ret = (ret == GMERR_INSUFFICIENT_PRIVILEGE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt2, "T40", GMC_OPERATION_SCAN);
        if (ret != GMERR_OK) {
            break;
        }
        ret = GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        if (ret != GMERR_OK) {
            break;
        }
        ret = GmcSetIndexKeyName(stmt2, keyName);
        if (ret != GMERR_OK) {
                break;
        }
        ret = GmcExecute(stmt2);
        if (ret != GMERR_OK) {
                break;
        }
        bool isFinish;
        ret = GmcFetch(stmt2, &isFinish);
        if (ret != GMERR_OK || isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(stmt2, "F0", GMC_DATATYPE_UINT32, &i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt2, "F1", GMC_DATATYPE_UINT32, &i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt2, "F2", GMC_DATATYPE_UINT32, &i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt2, "F3", GMC_DATATYPE_UINT32, &i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

// 026 多线程并发切换单用户模式和正常模式，部分线程进行业务操作
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建表
    char *label_schema = NULL;
    readJanssonFile("schema_file/NormalVertexLabel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, label_schema, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "T40", GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1000; i++) {
        ret = GmcSetVertexProperty(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F2", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F3", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < 1000; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "T40", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt_sync, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = queryPropertyAndCompare(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &i);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = queryPropertyAndCompare(g_stmt_sync, "F1", GMC_DATATYPE_UINT32, &i);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = queryPropertyAndCompare(g_stmt_sync, "F2", GMC_DATATYPE_UINT32, &i);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = queryPropertyAndCompare(g_stmt_sync, "F3", GMC_DATATYPE_UINT32, &i);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    pthread_t tid[40];
    for (int i = 0; i < 10; i++) {
        ret = pthread_create(&tid[i], NULL, NormalMode_Thread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&tid[i + 10], NULL, SingleUserMode_Thread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&tid[i + 20], NULL, replace_Thread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&tid[i + 30], NULL, query_Thread, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < 40; i++) {
       pthread_join(tid[i], NULL);
    }
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, "T40");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027 切换单用户模式后查询视图，预期失败
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, view_name);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    snprintf(messCompare, MAX_CMD_SIZE, "sysview connect unsucc, ret = %d",
        GMERR_INSUFFICIENT_PRIVILEGE);
    ret = executeCommand(g_command, messCompare);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, view_name);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "CONN_STATUS: CONN_STATUS_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028 切换单用户模式后使用其他gm工具，预期除了gmrule成功，其他失败
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 建表
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *label_schema = NULL;
    readJanssonFile("schema_file/NormalVertexLabel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, label_schema, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret =executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // gmimport导入
    const char* file_path = "./schema_file/NormalVertexLabel.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s", g_toolPath, file_path);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    snprintf(messCompare, MAX_CMD_SIZE, "gmimport connect server unsucc. ret = %d",
        GMERR_INSUFFICIENT_PRIVILEGE);
    ret = executeCommand(g_command, messCompare);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // gmlog查看日志
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmlog -g gmserver", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    snprintf(messCompare, MAX_CMD_SIZE, "Execute this command unsuccessful. ret = %d",
        GMERR_INSUFFICIENT_PRIVILEGE);
    ret =executeCommand(g_command, "[gmlog] Insufficient permission.", messCompare);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // gmrule导入白名单
    const char *allow_list_file = "./schema_file/allow_list1.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s ", g_toolPath, allow_list_file,
        g_connServer);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s ", g_toolPath, allow_list_file,
        g_connServer);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 2, warning: 0.",
        "remove allowlist, remove db group. success: 1, warning: 0.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret =executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, "T40");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030 正常模式建连后切换单用户模式后建连的数量上限达不到1024
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(4, "GMERR-1010000", "GMERR-1015004", "GMERR-1015002", "GMERR-1010002");
    int ret = 0;
    GmcConnT *conn[MAX_CONN_SIZE] = {0};
    GmcStmtT *stmt[MAX_CONN_SIZE] = {0};
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < MAX_CONN_SIZE; i++) {
        if (i == MAX_CONN_SIZE - 1) {
            ret = testGmcConnect(&conn[i], &stmt[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_TOO_MANY_CONNECTIONS, ret);
        } else {
            ret = testGmcConnect(&conn[i], &stmt[i]);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

    }
    for (int i = 0; i < MAX_CONN_SIZE - 1; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031 建连后切换单用户模式再切回正常模式查询连接视图观察连接状态
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 查询连接视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s > conn_status.txt", g_toolPath, view_name);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "grep -rn CONN_STATUS_NORMAL conn_status.txt|wc -l");
    ret = executeCommand(g_command, "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (g_envType == 2) {
        // 客户端连接视图
        char view_name1[MAX_CMD_SIZE] = "V\\$CLT_PROCESS_CONN";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, view_name1);
        AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
        system(g_command);
    }
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, view_name);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "sysview connect unsucc, ret = 18004");
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询连接视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s >> conn_status.txt", g_toolPath, view_name);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    system(g_command);
    snprintf(g_command, MAX_CMD_SIZE, "grep -rn CONN_STATUS_NORMAL conn_status.txt|wc -l");
    ret = executeCommand(g_command, "4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    #if defined ENV_RTOSV2X
    // 客户端连接视图
    char view_name1[MAX_CMD_SIZE] = "V\\$CLT_PROCESS_CONN";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, view_name1);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    system(g_command);
    #else
    #endif
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf conn_status.txt");
    AW_FUN_Log(LOG_STEP, "test end.");
}

void sn_callback_not_cmp(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    while (gIsSnCallbackWait) {
        sleep(1);
    }
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_LABELNAME_LEN] = {0};
    unsigned int labelNameLen = MAX_LABELNAME_LEN;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
void TestGmcSetVertexProperty(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    uint32_t PK_value = i;
    ret = GmcSetVertexProperty(stmt, "PK", GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value_8 = i % 128;
    uint8_t value_u8 = i % 256;
    char f8_value[8] = "string";

    int32_t f0_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f1_value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f2_value = value_8;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f3_value = value_u8;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f4_value = true;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_BOOL, &f4_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f5_value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_FLOAT, &f5_value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f6_value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_DOUBLE, &f6_value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_TIME, &f7_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, f8_value, (strlen(f8_value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FIXED, f8_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 032 生产未消费后切换单用户模式，预期推送正常
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "Connection send buffer overflows");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    char errorMsg5[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    (void)snprintf(errorMsg5, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(5, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5);
    int ret;
    const char *labelName = "FlowControlLabel";
    // 创建连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataT *user_data = NULL;
    ret = testSnMallocUserData(&user_data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // create label
    char *g_schema = NULL;
    readJanssonFile("./schema_file/FlowControlLabel.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    char *g_sub_info = NULL;
    const char *g_subName = "subVertexLabel";
    readJanssonFile("schema_file/FlowControlLabel_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_not_cmp, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_sub_info);
    // 不消费订阅消息
    gIsSnCallbackWait = true;
    int i = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (!ret) {
        TestGmcSetVertexProperty(g_stmt_sync, i);
        i++;
        if(i > 16000){
            break;
        }
        ret = GmcExecute(g_stmt_sync);
    }
    // 校验收到的消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新消费订阅消息
    gIsSnCallbackWait = false;
    // 校验收到的消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, i - 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(g_conn_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(user_data);
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(g_conn_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033 切换单用户模式后查看审计日志
TEST_F(SingleUserMode, Resilience_009_SingleUserMode_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 切换单用户模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 1", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 1 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "grep -rn ips ../../../log/secure/audit.log");
    //校验审计日志
    ret = executeCommand(g_command,
        "[set user mode: 1.] [DCL] [success] sessionId: \"0\" stmtId: \"0\" type:"
        " \"IPS SET USERMODE\" action: \"prepare\"",
        "[set user mode: 1.] [DCL] [success] sessionId: \"0\" stmtId: \"0\" type:"
        " \"IPS SET USERMODE\" action: \"execute\"");
    // 恢复正常模式
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmips -mode 0", g_toolPath);
    AW_FUN_Log(LOG_DEBUG, "%s\n", g_command);
    ret = executeCommand(g_command, "[gmips] set server mode 0 successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //校验审计日志
    snprintf(g_command, MAX_CMD_SIZE, "grep -rn ips ../../../log/secure/audit.log");
    ret = executeCommand(g_command,
        "[set user mode: 0.] [DCL] [success] sessionId: \"0\" stmtId: \"0\" type:"
        " \"IPS SET USERMODE\" action: \"prepare\"",
        "[set user mode: 0.] [DCL] [success] sessionId: \"0\" stmtId: \"0\" type:"
        " \"IPS SET USERMODE\" action: \"execute\"");
    AW_FUN_Log(LOG_STEP, "test end.");
}
