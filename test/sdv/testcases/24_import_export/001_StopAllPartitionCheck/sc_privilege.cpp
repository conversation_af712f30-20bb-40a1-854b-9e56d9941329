/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 接口权限测试
 * Author: lushiguang
 * Create: 2024-2-21
 */


#include "stop_all_check.h"


int g_beginIndex = 0;
int g_endIndex = 20000;
bool g_isAbnormal = false;

class Privilege : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    }
};

void Privilege::SetUp()
{
    AW_FUN_Log(LOG_STEP, "[INFO] check interface Start.");
    // 导入白名单
    char command[512] = {0};
    const char *allow_list_file = "privi_config/gmrule_allow_list.gmuser";
    snprintf(command, sizeof(command), "gmrule -c import_allowlist -f %s -s %s ", allow_list_file, g_connServer);
    int ret = executeCommand(command, "Import single allow list file", "successfully.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 导入必备系统权限
    const char *sys_policy_file = "privi_config/sysVertex.gmpolicy";
    snprintf(command, sizeof(command), "gmrule -c import_policy  -f %s -s %s ", sys_policy_file, g_connServer);
    ret = executeCommand(command, "Import single policy file", "successfully.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void Privilege::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 取消权限
    char command[512] = {0};
    const char *allow_list_file = "privi_config/gmrule_allow_list.gmuser";
    snprintf(command, sizeof(command), "gmrule -c remove_allowlist -f %s -s %s ", allow_list_file, g_connServer);
    ret = executeCommand(command, "remove allowlist, remove db user. success: 1, warning: 0.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_STEP, "[INFO] check interface End.");
}

// 001. 接口GmcEndAllPartitionCheck权限测试  预期：正常校验
TEST_F(Privilege, imexport_001_04_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    bool isPartition = true;
    g_isAbnormal = false;
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_checkPtSchema, g_checkConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启对账
    int partition1 = 1;
    int partition2 = 2;
    // 开启对账

    ret = GmcBeginCheck(g_stmt, g_tableName, partition1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBeginCheck(g_stmt, g_tableName, partition2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex / 4, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char command[512] = {0};
    const char *sys_policy_file = "privi_config/sysVertex_modify.gmpolicy";
    snprintf(command, sizeof(command), "gmrule -c revoke_policy  -f %s -s %s ", sys_policy_file, g_connServer);
    ret = executeCommand(command, "revoke policy", "success");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // // 结束对账
    ret = GmcEndAllPartitionCheck(g_stmt, g_tableName, g_isAbnormal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    const char *sys_policy_file2 = "privi_config/sysVertex.gmpolicy";
    snprintf(command, sizeof(command), "gmrule -c import_policy  -f %s -s %s ", sys_policy_file2, g_connServer);
    ret = executeCommand(command, "Import single policy file", "successfully.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 结束对账
    ret = GmcEndAllPartitionCheck(g_stmt, g_tableName, g_isAbnormal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    // 总共10个分区，1分区2000数据，刷新了四分一数据，老化掉1500*2
    int agedCount = (g_endIndex / 10 - g_endIndex / 10 / 4) * 2;
    // 查询数据
    int expectCount = g_endIndex - g_beginIndex - agedCount;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_INSUFFICIENT_PRIVILEGE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

