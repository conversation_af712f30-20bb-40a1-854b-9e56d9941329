/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 结束分区对账并发场景
 * Author: lushiguang
 * Create: 2024-2-22
 */


#include "stop_all_check.h"

GmcConnT *g_conn2 = NULL;
GmcStmtT *g_stmt2 = NULL;
int g_beginIndex = 0;
int g_endIndex = 10000;
bool g_isAbnormal = false;

class Concurrent : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxWriteCacheSize=200\"");
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    }
};

void Concurrent::SetUp()
{
    AW_FUN_Log(LOG_INFO, "[INFO] check interface Start.");
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void Concurrent::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    AW_FUN_Log(LOG_INFO, "[INFO] check interface End.");
}


void *StopCheckAsync(void *args)
{
    for (int i = 0; i < 1000; i++) {
        (void)GmcEndAllPartitionCheck(g_stmt, g_tableName, g_isAbnormal);
    }
    return nullptr;
}

// 001. 多表全表对账，并发调用接口正常停止对账  预期 数据老化
TEST_F(Concurrent, imexport_001_03_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_isAbnormal = false;
    char tableName[] = "lable2";
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    (void)GmcDropVertexLabel(g_stmt2, tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_checkSchema, g_checkConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabelWithName(g_stmt, g_checkSchema, g_checkConfig, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt2, tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启对账
    ret = GmcBeginCheck(g_stmt, g_tableName, 0xff);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBeginCheck(g_stmt, tableName, 0xff);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex / 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt2, tableName, g_beginIndex, g_endIndex / 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结束对账
    pthread_t th;
    ret = pthread_create(&th, NULL, StopCheckAsync, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 1000; i++) {
        (void)GmcEndAllPartitionCheck(g_stmt2, tableName, g_isAbnormal);
    }
    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    // 查询数据
    int expectCount = g_endIndex / 4 - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexCount(g_stmt2, tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt2, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002. 多表全表对账，并发调用接口异常停止对账  预期 数据回滚
TEST_F(Concurrent, imexport_001_03_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    g_isAbnormal = true;
    char tableName[] = "lable2";
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    (void)GmcDropVertexLabel(g_stmt2, tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_checkSchema, g_checkConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabelWithName(g_stmt, g_checkSchema, g_checkConfig, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt2, tableName, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启对账
    ret = GmcBeginCheck(g_stmt, g_tableName, 0xff);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBeginCheck(g_stmt, tableName, 0xff);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex / 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt2, tableName, g_beginIndex, g_endIndex / 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结束对账
    pthread_t th;
    ret = pthread_create(&th, NULL, StopCheckAsync, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 1000; i++) {
        (void)GmcEndAllPartitionCheck(g_stmt2, tableName, g_isAbnormal);
    }
    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    // 查询数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexCount(g_stmt2, tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt2, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003. 多表分区对账，并发调用接口正常停止对账 预期：结束对账，数据老化删除
TEST_F(Concurrent, imexport_001_03_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    bool isPartition = true;
    g_isAbnormal = false;
    char tableName[] = "lable2";
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    (void)GmcDropVertexLabel(g_stmt2, tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_checkPtSchema, g_checkConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabelWithName(g_stmt2, g_checkPtSchema, g_checkConfig, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt2, tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启对账
    int partition1 = 1;
    int partition2 = 2;
    // 开启对账
    ret = GmcBeginCheck(g_stmt, g_tableName, partition1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBeginCheck(g_stmt, g_tableName, partition2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex / 4, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcBeginCheck(g_stmt2, tableName, partition1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBeginCheck(g_stmt2, tableName, partition2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt2, tableName, g_beginIndex, g_endIndex / 4, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结束对账
    pthread_t th;
    ret = pthread_create(&th, NULL, StopCheckAsync, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 1000; i++) {
        (void)GmcEndAllPartitionCheck(g_stmt2, tableName, g_isAbnormal);
    }
    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    // 总共10个分区，1分区1000数据，刷新了四分一数据，老化掉1500*2
    int agedCount = (g_endIndex / 10 - g_endIndex / 10 / 4) * 2;
    // 查询数据
    int expectCount = g_endIndex - g_beginIndex - agedCount;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexCount(g_stmt2, tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt2, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004. 多表分区对账，并发调用接口异常停止对账 预期：结束对账，数据回滚
TEST_F(Concurrent, imexport_001_03_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    bool isPartition = true;
    g_isAbnormal = true;
    char tableName[] = "lable2";
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    (void)GmcDropVertexLabel(g_stmt2, tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_checkPtSchema, g_checkConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabelWithName(g_stmt2, g_checkPtSchema, g_checkConfig, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt2, tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启对账
    int partition1 = 1;
    int partition2 = 2;
    // 开启对账
    ret = GmcBeginCheck(g_stmt, g_tableName, partition1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBeginCheck(g_stmt, g_tableName, partition2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex / 4, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcBeginCheck(g_stmt2, tableName, partition1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBeginCheck(g_stmt2, tableName, partition2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt2, tableName, g_beginIndex, g_endIndex / 4, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结束对账
    pthread_t th;
    ret = pthread_create(&th, NULL, StopCheckAsync, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 1000; i++) {
        (void)GmcEndAllPartitionCheck(g_stmt2, tableName, g_isAbnormal);
    }
    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    int agedCount = 0;
    // 查询数据
    int expectCount = g_endIndex - g_beginIndex - agedCount;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSelVertexCount(g_stmt2, tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt2, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005. 单表多个分区对账，并发调用接口正常停止对账 预期：结束对账，数据老化删除
TEST_F(Concurrent, imexport_001_03_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    bool isPartition = true;
    g_isAbnormal = false;
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_checkPtSchema, g_checkConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启对账
    int partitionCount = 6;
    // 开启对账
    for (int i = 1; i <= partitionCount; i++) {
        ret = GmcBeginCheck(g_stmt, g_tableName, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex / 4, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结束对账
    pthread_t th;
    ret = pthread_create(&th, NULL, StopCheckAsync, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 1000; i++) {
        (void)GmcEndAllPartitionCheck(g_stmt2, g_tableName, g_isAbnormal);
    }
    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    // 总共10个分区，1分区1000数据，刷新了四分一数据，老化掉1500*partitionCount
    int agedCount = (g_endIndex / 10 - g_endIndex / 10 / 4) * partitionCount;
    // 查询数据
    int expectCount = g_endIndex - g_beginIndex - agedCount;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006. 单表多个分区对账，并发调用接口异常停止对账 预期：结束对账，数据回滚
TEST_F(Concurrent, imexport_001_03_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    bool isPartition = true;
    g_isAbnormal = true;
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_checkPtSchema, g_checkConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启对账
    int partitionCount = 6;
    // 开启对账
    for (int i = 1; i <= partitionCount; i++) {
        ret = GmcBeginCheck(g_stmt, g_tableName, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex / 4, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结束对账
    pthread_t th;
    ret = pthread_create(&th, NULL, StopCheckAsync, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 1000; i++) {
        (void)GmcEndAllPartitionCheck(g_stmt2, g_tableName, g_isAbnormal);
    }
    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    int agedCount = 0;
    // 查询数据
    int expectCount = g_endIndex - g_beginIndex - agedCount;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007. 开启对账和正常停止对账接口并发调用  数据老化
TEST_F(Concurrent, imexport_001_03_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    bool isPartition = true;
    g_isAbnormal = false;
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_checkPtSchema, g_checkConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启对账
    int partitionCount = 6;
    // 开启对账
    for (int i = 1; i <= partitionCount; i++) {
        ret = GmcBeginCheck(g_stmt, g_tableName, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex / 4, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结束对账
    pthread_t th;
    ret = pthread_create(&th, NULL, StopCheckAsync, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int tempRet = T_FAILED;
    for (int n = 0; n < 100; n++) {
        for (int i = 1; i <= partitionCount; i++) {
            ret = GmcBeginCheck(g_stmt2, g_tableName, i);
            if (ret == GMERR_OK || ret == GMERR_TABLE_IN_CHECKING) {
                tempRet = GMERR_OK;
            }
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, tempRet);
        }
    }
    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndAllPartitionCheck(g_stmt, g_tableName, g_isAbnormal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    // 总共10个分区，1分区1000数据，无刷新了数据，老化掉1000*partitionCount
    int agedCount = g_endIndex / 10 * partitionCount;
    // 查询数据
    int expectCount = g_endIndex - g_beginIndex - agedCount;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_IN_CHECKING);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008. 开启对账和异常停止对账接口并发调用  数据不老化
TEST_F(Concurrent, imexport_001_03_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    bool isPartition = true;
    g_isAbnormal = true;
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_checkPtSchema, g_checkConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启对账
    int partitionCount = 6;
    // 开启对账
    for (int i = 1; i <= partitionCount; i++) {
        ret = GmcBeginCheck(g_stmt, g_tableName, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex / 4, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 结束对账
    pthread_t th;
    ret = pthread_create(&th, NULL, StopCheckAsync, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int tempRet = T_FAILED;
    for (int n = 0; n < 100; n++) {
        for (int i = 1; i <= partitionCount; i++) {
            ret = GmcBeginCheck(g_stmt2, g_tableName, i);
            if (ret == GMERR_OK || ret == GMERR_TABLE_IN_CHECKING) {
                tempRet = GMERR_OK;
            }
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, tempRet);
        }
    }
    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndAllPartitionCheck(g_stmt, g_tableName, g_isAbnormal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    int agedCount = 0;
    // 查询数据
    int expectCount = g_endIndex - g_beginIndex - agedCount;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_IN_CHECKING);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009. 停止对账和dml并发  无异常
TEST_F(Concurrent, imexport_001_03_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    bool isPartition = true;
    g_isAbnormal = false;
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_checkPtSchema, g_checkConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启对账
    int partitionCount = 6;
    // 开启对账
    for (int i = 1; i <= partitionCount; i++) {
        ret = GmcBeginCheck(g_stmt, g_tableName, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 结束对账
    pthread_t th;
    ret = pthread_create(&th, NULL, StopCheckAsync, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt2, g_tableName, g_beginIndex, g_endIndex / 4, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndAllPartitionCheck(g_stmt, g_tableName, g_isAbnormal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt2, g_tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    // 查询数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt, g_tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010. 停止对账和ddl并发(alter,drop)  无异常
TEST_F(Concurrent, imexport_001_03_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    bool isPartition = true;
    g_isAbnormal = false;
    (void)GmcDropVertexLabel(g_stmt, g_tableName);
    int ret = GmcCreateVertexLabel(g_stmt, g_checkPtSchema, g_checkConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt, g_tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启对账
    int partitionCount = 6;
    // 开启对账
    for (int i = 1; i <= partitionCount; i++) {
        ret = GmcBeginCheck(g_stmt, g_tableName, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 结束对账
    pthread_t th;
    ret = pthread_create(&th, NULL, StopCheckAsync, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt2, g_tableName);
    AW_MACRO_ASSERT_EQ_BOOL(true, (ret == GMERR_TABLE_IN_CHECKING || ret == GMERR_OK));
    if (ret == GMERR_OK) {
        ret = GmcCreateVertexLabel(g_stmt2, g_checkPtSchema, g_checkConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = pthread_join(th, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcEndAllPartitionCheck(g_stmt, g_tableName, g_isAbnormal);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = WriteIp4f(g_stmt2, g_tableName, g_beginIndex, g_endIndex, isPartition);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    // 查询数据
    int expectCount = g_endIndex - g_beginIndex;
    ret = TestSelVertexCount(g_stmt2, g_tableName, g_pIndexName, g_pCond, expectCount, g_beginIndex, g_endIndex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt2, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_TABLE_IN_CHECKING);
    AW_FUN_Log(LOG_STEP, "test end.");
}

