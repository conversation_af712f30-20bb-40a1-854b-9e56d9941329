[{"comment": "一般复杂表", "type": "record", "name": "vl_general_complex", "schema_version": 1, "fields": [{"name": "A0", "type": "int32", "nullable": false}, {"name": "A1", "type": "int64", "nullable": false}, {"name": "A2", "type": "uint32", "nullable": false}, {"name": "A3", "type": "uint64", "nullable": false}, {"name": "A4", "type": "string", "nullable": false}, {"name": "M0", "type": "record", "array": true, "size": 1024, "fields": [{"name": "B0", "type": "int32", "nullable": true}, {"name": "B1", "type": "string", "nullable": true}]}, {"name": "M1", "type": "record", "array": false, "size": 1024, "fields": [{"name": "F1", "type": "int32", "nullable": true}, {"name": "F2", "type": "string", "nullable": true}, {"name": "F3", "type": "int32", "nullable": true, "default": 4}]}, {"name": "A5", "type": "double", "nullable": true}], "keys": [{"node": "vl_general_complex", "name": "<PERSON><PERSON><PERSON>", "index": {"type": "primary"}, "fields": ["A0"], "constraints": {"unique": true}, "comment": "主键索引"}, {"node": "M0", "name": "M0MemberKey", "fields": ["B0"], "constraints": {"unique": true}, "comment": "M0节点成员索引"}, {"node": "vl_general_complex", "name": "local_key", "fields": ["A1", "A3"], "index": {"type": "local"}, "constraints": {"unique": false}, "comment": "local索引"}]}]