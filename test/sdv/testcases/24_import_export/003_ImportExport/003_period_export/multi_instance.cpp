/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 多实例定期导出导入
 * Author: lushiguang
 * Create: 2023-12-25
 */

#include "period_export.h"

int g_beginIndex = 0;
int g_endIndex = 2000;
char g_exportDir[512] = {0};
char g_periodExportDir[512] = {0};
int g_periodTime = 10;

class MultiInstance : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};

void MultiInstance::SetUp()
{
    printf("[INFO] check interface Start.\n");
    system("sh $TEST_HOME/tools/stop.sh -f");
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(g_exportDir, "%s/export_dir", pwdDir);
    (void)sprintf(g_periodExportDir, "%s/period_export_dir", pwdDir);
    (void)Rmdir(g_exportDir);
    (void)Rmdir(g_periodExportDir);
    int ret = mkdir(g_exportDir, S_IRUSR | S_IWUSR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = mkdir(g_periodExportDir, S_IRUSR | S_IWUSR);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GtExecSystemCmd("sed -i '/periodicPersistence/d' %s", g_sysGMDBCfg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("echo 'periodicPersistence=0' >> %s", g_sysGMDBCfg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("sed -i '/periodicPersistenceDirPath/d' %s", g_sysGMDBCfg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("echo 'periodicPersistenceDirPath=PcPersistenceDir' >> %s", g_sysGMDBCfg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_CHECK_LOG_BEGIN();
}

void MultiInstance::TearDown()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    printf("[INFO] check interface End.\n");
}

int StartEnvWithConfig(int num, int count, ...)
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxWriteCacheSize=200\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=200\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=200\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=100\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=100\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=30\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"periodicPersistence=5\"");
    (void)GtExecSystemCmd("sh $TEST_HOME/tools/modifyCfg.sh \"periodicPersistenceDirPath=%s\"", g_periodExportDir);

    if (g_enableClusterHash) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=1\"");
    }
    va_list varList;
    char tempCmd[200] = {0};
    const char *strKey;
    va_start(varList, count);
    char socket[200] = {0};
    char tempStr[200] = {0};
    for (int i = 0; i < count; i++) {
        strKey = va_arg(varList, const char *);
        (void)strcpy(tempStr, strKey);
        if (strstr(tempStr, "localLocatorListened")) {
            char *temp = strstr(tempStr, "=");
            (void)strcpy(socket, temp + 1);
        }
        (void)sprintf(tempCmd, "sh $TEST_HOME/tools/modifyCfg.sh \"%s\"", strKey);
        printf("tempcmd: %s\n", tempCmd);
        system(tempCmd);
    }
    va_end(varList);
    int ret = GtExecSystemCmd("sh startServer.sh -n %d -s '%s'", num, socket);
    return ret;
}

int DoAccess(GmcStmtT *stmt, char *tableName, int bIndex, int eIndex)
{
    int maxLen = 100;
    char strValue[maxLen];
    (void)memset(strValue, 'a', maxLen - 1);
    strValue[maxLen - 1] = '\0';
    int ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_REPLACE);
    RETURN_IFERR(ret);
    for (int i = bIndex; i < eIndex; i++) {
        uint32_t vr_id = i;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint32_t vrf_index = i + 2;
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint32_t dest_ip_addr = i + 3;
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
        RETURN_IFERR(ret);
        uint8_t mask_len = i % 127;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
        RETURN_IFERR(ret);
        ret = GmcSetVertexProperty(stmt, "other", GMC_DATATYPE_STRING, strValue, maxLen - 1);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
    }
    return T_OK;
}

// 001. 起两个实例，定期导出
TEST_F(MultiInstance, imexport_003_003_04_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcConnT *conn[64];
    GmcStmtT *stmt[64];
    char connServer[64][100];
    int instanceCount = 2;
    for (int i = 0; i < instanceCount; i++) {
        (void)snprintf(connServer[i], sizeof(connServer[i]), "usocket:/run/verona/unix_emserver_%d", i);
    }
    char config1[100] = {0};
    char config2[100] = {0};
    for (int i = 0; i < instanceCount; i++) {
        (void)snprintf(config1, sizeof(config1), "instanceId=%d", i + 1);
        (void)snprintf(config2, sizeof(config2), "localLocatorListened=usocket:/run/verona/unix_emserver_%d", i);
        ret = StartEnvWithConfig(i + 1, 2, config1, config2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < instanceCount; i++) {
        ret = TestGmcConnectLocator(&conn[i], &stmt[i], connServer[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        (void)GmcDropVertexLabel(stmt[i], g_tableName);
        ret = GmcCreateVertexLabel(stmt[i], g_periodEptSchema, g_periodEptConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = DoAccess(stmt[i], g_tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待两周期，定期导出
    sleep(g_periodTime * 2);
    // 重启
    for (int i = 0; i < instanceCount; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    for (int i = 0; i < instanceCount; i++) {
        (void)snprintf(config1, sizeof(config1), "instanceId=%d", i + 1);
        (void)snprintf(config2, sizeof(config2), "localLocatorListened=usocket:/run/verona/unix_emserver_%d", i);
        ret = StartEnvWithConfig(i + 1, 2, config1, config2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < instanceCount; i++) {
        ret = TestGmcConnectLocator(&conn[i], &stmt[i], connServer[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)GmcDropVertexLabel(stmt[i], g_tableName);
        ret = GmcCreateVertexLabel(stmt[i], g_periodEptSchema, g_periodEptConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    system("tree period_export_dir");
    // 导入
    bool isScheduled = true;
    for (int i = 0; i < instanceCount; i++) {
        ret = ImportData(g_periodExportDir, 1, isScheduled, connServer[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 查询数据
    int exportCount = g_endIndex - g_beginIndex;
    for (int i = 0; i < instanceCount; i++) {
        ret = TestSelVertexCount(stmt[i], g_tableName, g_pIndexName, g_pCond, exportCount, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < instanceCount; i++) {
        ret = GmcDropVertexLabel(stmt[i], g_tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002. 起10个实例，定期导出
TEST_F(MultiInstance, imexport_003_003_04_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int instanceCount = 10;
    GmcConnT *conn[10];
    GmcStmtT *stmt[10];
    char connServer[10][100];
    for (int i = 0; i < instanceCount; i++) {
        (void)snprintf(connServer[i], sizeof(connServer[i]), "usocket:/run/verona/unix_emserver_%d", i);
    }
    char config1[100] = {0};
    char config2[100] = {0};
    for (int i = 0; i < instanceCount; i++) {
        (void)snprintf(config1, sizeof(config1), "instanceId=%d", i + 1);
        (void)snprintf(config2, sizeof(config2), "localLocatorListened=usocket:/run/verona/unix_emserver_%d", i);
        ret = StartEnvWithConfig(i + 1, 2, config1, config2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < instanceCount; i++) {
        ret = TestGmcConnectLocator(&conn[i], &stmt[i], connServer[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        (void)GmcDropVertexLabel(stmt[i], g_tableName);
        ret = GmcCreateVertexLabel(stmt[i], g_periodEptSchema, g_periodEptConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = DoAccess(stmt[i], g_tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待两周期，定期导出
    sleep(g_periodTime * 2);
    // 重启
    for (int i = 0; i < instanceCount; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    for (int i = 0; i < instanceCount; i++) {
        (void)snprintf(config1, sizeof(config1), "instanceId=%d", i + 1);
        (void)snprintf(config2, sizeof(config2), "localLocatorListened=usocket:/run/verona/unix_emserver_%d", i);
        ret = StartEnvWithConfig(i + 1, 2, config1, config2, "periodicPersistence=0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < instanceCount; i++) {
        ret = TestGmcConnectLocator(&conn[i], &stmt[i], connServer[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)GmcDropVertexLabel(stmt[i], g_tableName);
        ret = GmcCreateVertexLabel(stmt[i], g_periodEptSchema, g_periodEptConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    system("tree period_export_dir");
    // 导入
    bool isScheduled = true;
    for (int i = 0; i < instanceCount; i++) {
        ret = ImportData(g_periodExportDir, 1, isScheduled, connServer[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 查询数据
    int exportCount = g_endIndex - g_beginIndex;
    for (int i = 0; i < instanceCount; i++) {
        ret = TestSelVertexCount(stmt[i], g_tableName, g_pIndexName, g_pCond, exportCount, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < instanceCount; i++) {
        ret = GmcDropVertexLabel(stmt[i], g_tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002. 起64个实例，定期导出, 执行时间过长，手工执行
TEST_F(MultiInstance, imexport_003_003_04_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int instanceCount = 64;
    GmcConnT *conn[64];
    GmcStmtT *stmt[64];
    char connServer[64][100];
    for (int i = 0; i < instanceCount; i++) {
        (void)snprintf(connServer[i], sizeof(connServer[i]), "usocket:/run/verona/unix_emserver_%d", i);
    }
    char config1[100] = {0};
    char config2[100] = {0};
    for (int i = 0; i < instanceCount; i++) {
        (void)snprintf(config1, sizeof(config1), "instanceId=%d", i + 1);
        (void)snprintf(config2, sizeof(config2), "localLocatorListened=usocket:/run/verona/unix_emserver_%d", i);
        ret = StartEnvWithConfig(i + 1, 2, config1, config2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < instanceCount; i++) {
        ret = TestGmcConnectLocator(&conn[i], &stmt[i], connServer[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        (void)GmcDropVertexLabel(stmt[i], g_tableName);
        ret = GmcCreateVertexLabel(stmt[i], g_periodEptSchema, g_periodEptConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = DoAccess(stmt[i], g_tableName, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 等待两周期，定期导出
    sleep(g_periodTime * 2);
    // 重启
    for (int i = 0; i < instanceCount; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    for (int i = 0; i < instanceCount; i++) {
        (void)snprintf(config1, sizeof(config1), "instanceId=%d", i + 1);
        (void)snprintf(config2, sizeof(config2), "localLocatorListened=usocket:/run/verona/unix_emserver_%d", i);
        ret = StartEnvWithConfig(i + 1, 2, config1, config2, "periodicPersistence=0");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < instanceCount; i++) {
        ret = TestGmcConnectLocator(&conn[i], &stmt[i], connServer[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)GmcDropVertexLabel(stmt[i], g_tableName);
        ret = GmcCreateVertexLabel(stmt[i], g_periodEptSchema, g_periodEptConfig);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    system("tree period_export_dir");
    // 导入
    bool isScheduled = true;
    for (int i = 0; i < instanceCount; i++) {
        ret = ImportData(g_periodExportDir, 1, isScheduled, connServer[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 查询数据
    int exportCount = g_endIndex - g_beginIndex;
    for (int i = 0; i < instanceCount; i++) {
        ret = TestSelVertexCount(stmt[i], g_tableName, g_pIndexName, g_pCond, exportCount, g_beginIndex, g_endIndex);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < instanceCount; i++) {
        ret = GmcDropVertexLabel(stmt[i], g_tableName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

