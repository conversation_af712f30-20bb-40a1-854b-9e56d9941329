[{"comment": "定长表", "type": "record", "name": "lable1", "special_complex": false, "fields": [{"name": "A0", "type": "int32", "nullable": false}, {"name": "A1", "type": "int64", "nullable": false}, {"name": "A2", "type": "uint32", "nullable": false}, {"name": "A3", "type": "uint64", "nullable": false}, {"name": "A4", "type": "int8", "default": 21}, {"name": "A5", "type": "uint8", "default": 12}, {"name": "A6", "type": "int16", "default": 111}, {"name": "A7", "type": "uint16", "default": 10}, {"name": "A8", "type": "boolean", "default": 0}, {"name": "A9", "type": "partition", "nullable": false}, {"name": "B1", "type": "uint64", "defalut": 6666}, {"name": "B2", "type": "uint64", "defalut": 6666}, {"name": "B3", "type": "uint64", "defalut": 6666}, {"name": "B4", "type": "uint64", "defalut": 6666}, {"name": "B5", "type": "uint64", "defalut": 6666}, {"name": "B6", "type": "uint64", "defalut": 6666}, {"name": "B7", "type": "uint64", "defalut": 6666}, {"name": "B8", "type": "uint64", "defalut": 6666}, {"name": "B9", "type": "uint64", "defalut": 6666}, {"name": "B10", "type": "uint64", "defalut": 6666}, {"name": "B11", "type": "uint64", "defalut": 6666}, {"name": "B12", "type": "uint64", "defalut": 6666}, {"name": "B13", "type": "uint64", "defalut": 6666}, {"name": "B14", "type": "uint64", "defalut": 6666}, {"name": "B15", "type": "uint64", "defalut": 6666}, {"name": "B16", "type": "uint64", "defalut": 6666}, {"name": "B17", "type": "uint64", "defalut": 6666}, {"name": "B18", "type": "uint64", "defalut": 6666}], "keys": [{"node": "lable1", "name": "<PERSON><PERSON><PERSON>", "index": {"type": "primary"}, "fields": ["A0"], "constraints": {"unique": true}, "comment": "主键索引"}, {"node": "lable1", "name": "LocalKey", "index": {"type": "local"}, "fields": ["A2"], "constraints": {"unique": false}, "comment": "local索引"}, {"node": "lable1", "name": "LocalHashKey", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["A3"], "constraints": {"unique": false}, "comment": "localhash索引"}]}]