[{"type": "record", "name": "N000", "fields": [{"name": "a", "type": "int8", "nullable": false}, {"name": "b", "type": "int16", "nullable": false}, {"name": "c", "type": "int32", "nullable": true}, {"name": "d", "type": "int64", "nullable": true}, {"name": "e", "type": "fixed", "size": 1, "default": "f"}, {"name": "f", "type": "fixed", "size": 1, "default": "f"}, {"name": "g", "type": "fixed", "size": 1, "default": "f"}, {"name": "a1", "type": "int8", "nullable": true}, {"name": "b1", "type": "int16", "nullable": true}, {"name": "c1", "type": "int32", "nullable": true}, {"name": "d1", "type": "int64", "nullable": true}, {"name": "e1", "type": "fixed", "size": 1, "default": "f"}, {"name": "f1", "type": "fixed", "size": 1, "default": "f"}, {"name": "g1", "type": "fixed", "size": 1, "default": "f"}, {"name": "a2", "type": "int8", "nullable": true}, {"name": "b2", "type": "int16", "nullable": true}, {"name": "c2", "type": "int32", "nullable": true}, {"name": "d2", "type": "int64", "nullable": true}, {"name": "e2", "type": "fixed", "size": 1, "default": "f"}, {"name": "f2", "type": "fixed", "size": 1, "default": "f"}, {"name": "g2", "type": "fixed", "size": 1, "default": "f"}, {"name": "a3", "type": "int8", "nullable": true}, {"name": "b3", "type": "int16", "nullable": true}, {"name": "c3", "type": "int32", "nullable": true}, {"name": "d3", "type": "int64", "nullable": true}, {"name": "e3", "type": "fixed", "size": 1, "default": "f"}, {"name": "f3", "type": "fixed", "size": 1, "default": "f"}, {"name": "g3", "type": "fixed", "size": 1, "default": "f"}, {"name": "a4", "type": "int8", "nullable": true}, {"name": "b4", "type": "int16", "nullable": true}, {"name": "c4", "type": "int32", "nullable": true}, {"name": "d4", "type": "int64", "nullable": true}, {"name": "e4", "type": "fixed", "size": 1, "default": "f"}, {"name": "f4", "type": "fixed", "size": 1, "default": "f"}, {"name": "g4", "type": "fixed", "size": 1, "default": "f"}, {"name": "a5", "type": "int8", "nullable": true}, {"name": "b5", "type": "int16", "nullable": true}, {"name": "c5", "type": "int32", "nullable": true}, {"name": "d5", "type": "int64", "nullable": true}, {"name": "e5", "type": "fixed", "size": 1, "default": "f"}, {"name": "f5", "type": "fixed", "size": 1, "default": "f"}, {"name": "g5", "type": "fixed", "size": 1, "default": "f"}, {"name": "a6", "type": "int8", "nullable": true}, {"name": "b6", "type": "int16", "nullable": true}, {"name": "c6", "type": "int32", "nullable": true}, {"name": "d6", "type": "int64", "nullable": true}, {"name": "e6", "type": "fixed", "size": 1, "default": "f"}, {"name": "f6", "type": "fixed", "size": 1, "default": "f"}, {"name": "g6", "type": "fixed", "size": 1, "default": "f"}, {"name": "a7", "type": "int8", "nullable": true}, {"name": "b7", "type": "int16", "nullable": true}, {"name": "c7", "type": "int32", "nullable": true}, {"name": "d7", "type": "int64", "nullable": true}, {"name": "e7", "type": "fixed", "size": 1, "default": "f"}, {"name": "f7", "type": "fixed", "size": 1, "default": "f"}, {"name": "g7", "type": "fixed", "size": 1, "default": "f"}, {"name": "a8", "type": "int8", "nullable": true}, {"name": "b8", "type": "int16", "nullable": true}, {"name": "c8", "type": "int32", "nullable": true}, {"name": "d8", "type": "int64", "nullable": true}, {"name": "e8", "type": "fixed", "size": 1, "default": "f"}, {"name": "f8", "type": "fixed", "size": 1, "default": "f"}, {"name": "a9", "type": "string", "size": 100}], "keys": [{"node": "N000", "name": "pk", "fields": ["a", "b"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]