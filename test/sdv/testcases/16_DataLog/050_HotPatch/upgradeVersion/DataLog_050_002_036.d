%version v1.0.0
%table normalA000(a:int1, b:int2, c:int4, d:int8)
{ index(0(b, d)), index(1(b)), index(2(c)), index(3(d))}

%table normalB000(a:int1, b:int2, c:int4, d:int8)
{
index(0(b,c)),index(1(b)),index(2(c)),index(3(d))
}

%table normalC000(a:int1, b:int2, c:int4, d:int8)
{
index(0(b, d)),index(1(b)),index(2(c)),index(3(d))
}

// 投影规则
normalB000(a,b,c,d) :- normalC000(a,b,c,d).
normalA000(a,b,c,d) :- normalB000(a,b,c,d).
