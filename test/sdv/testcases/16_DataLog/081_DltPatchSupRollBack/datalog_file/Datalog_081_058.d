%version v1.0.0
namespace ns1{
%table A(a:int4, b:str, c:byte256, d:int8) {index(0(a,d))
}
%aggregate agg(a: int8 -> b: int8) {
    ordered
}
%function func1(a: int8 -> b: int8){}
%table D(a:int4, b:str, c:byte256, d:int8){index(0(a,d))}
%table D1(a:int4, b:str, c:byte256, d:int8){index(0(a,d))}
%table B(a:int4, b:str, c:byte256, d:int8){external, index(0(a))}
%readonly A, agg, func1
%readwrite B,D,D1
}
using namespace ns1

D(a, b, c, d1) :- A(a, b, c, d),func1(d,d1).
D1(a, b, c, sum) :-D(a, b, c, d) GROUP-BY(b,c,a)  agg(d, sum).
B(a, b, c, d) :-D1(a, b, c, d).
