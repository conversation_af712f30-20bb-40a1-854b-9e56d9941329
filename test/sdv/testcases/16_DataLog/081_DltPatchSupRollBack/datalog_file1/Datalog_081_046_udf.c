/*  版权所有 (c) 华为技术有限公司 2022-2023 */
#include <string.h>
#include "stdio.h"
#include "unistd.h"
#include "gm_udf.h"

#pragma pack(1)

typedef struct A {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    uint8_t c[256];
    int64_t d;
    uint32_t bLen;
    char *b;
} A;

#pragma pack(0)
const char *g_logOrgBName = "/root/_datalog_/RunLogOrgB.txt";
const char *g_logDeltaBName = "/root/_datalog_/RunLogDeltaB.txt";
const char *g_errorLogName = "/root/_datalog_/readkv.txt";

// 需要使用function来延长重做时间达到和过期线程并发的场景
#pragma pack(1)
typedef struct {
    int32_t count;
    int64_t a;
    int64_t b;
} Func;
#pragma pack(0)

int32_t dtl_ext_func_ns1_func(void *tuple, GmUdfCtxT *ctx)
{
    Func *a = (Func *)tuple;
    a->b = a->a;
    return GMERR_OK;
}
