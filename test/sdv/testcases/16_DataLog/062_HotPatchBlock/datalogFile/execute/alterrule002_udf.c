/*  版权所有 (c) 华为技术有限公司 2022-2023 */
#include "gm_udf.h"
#include "stdio.h"

#pragma pack(1)

typedef struct Out1 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} Out1;

#pragma pack(0)

const char *g_logName = "/root/_datalog_/TbmRunLog.txt";

int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_ext_func_init.\n");
    (void)fclose(fp);

    return GMERR_OK;
}

int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_ext_func_uninit.\n");
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_out1(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_tbm_tbl_out1, op = %d, a = %d, b = %d, c = %d, dtlReservedCount = %d.\n", op,
        ((Out1 *)tuple)->a, ((Out1 *)tuple)->b, ((Out1 *)tuple)->c, ((Out1 *)tuple)->dtlReservedCount);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_out2(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_tbm_tbl_out2, op = %d, a = %d, b = %d, c = %d, dtlReservedCount = %d.\n", op,
        ((Out1 *)tuple)->a, ((Out1 *)tuple)->b, ((Out1 *)tuple)->c, ((Out1 *)tuple)->dtlReservedCount);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_out3(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_tbm_tbl_out3, op = %d, a = %d, b = %d, c = %d, dtlReservedCount = %d.\n", op,
        ((Out1 *)tuple)->a, ((Out1 *)tuple)->b, ((Out1 *)tuple)->c, ((Out1 *)tuple)->dtlReservedCount);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_out4(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_tbm_tbl_out4, op = %d, a = %d, b = %d, c = %d, dtlReservedCount = %d.\n", op,
        ((Out1 *)tuple)->a, ((Out1 *)tuple)->b, ((Out1 *)tuple)->c, ((Out1 *)tuple)->dtlReservedCount);
    (void)fclose(fp);
    return GMERR_OK;
}
