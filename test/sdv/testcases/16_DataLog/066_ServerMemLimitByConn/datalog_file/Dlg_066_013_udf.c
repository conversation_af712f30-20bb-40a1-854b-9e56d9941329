/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include <stdio.h>
#include <string.h>
#include "gm_udf.h"
#include "assert.h"

#pragma pack(1)
typedef struct {
    int32_t count;
    int32_t a;
    int32_t b;
} Tup;
#pragma pack(0)

int32_t dtl_ext_func_func(void *tuple, GmUdfCtxT *ctx)
{
    Tup *a = (Tup *)tuple;
    a->b = a->a + 1;
    // 增加内存申请操作
    Tup *outStruct = GmUdfMemAlloc(ctx, 2*1024);
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}
