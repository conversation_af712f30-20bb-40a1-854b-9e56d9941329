/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: hotpatcblock.h
 * Description:
 * Author: luyang 00618033
 * Create: 2024-4-13
 */

#ifndef DTLSAMEPROCESS_H
#define DTLSAMEPROCESS_H

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <pthread.h>
#include <vector>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "share_function.h"
#include "DatalogRun.h"
#include "gms.h"

#define PRINT_INFO 1
#define FILE_PATH 512
#define LABEL_NAME 512

const char *g_tbmlogName = "TbmRunLog.txt";

const char *g_msglogName = "msgNotifyRunLog.txt";

const char *g_funclogName = "funcLog.txt";

char g_hFile[FILE_PATH] = "../../../../../pub/include/";

char const *g_viewName = "V\\$PTL_DATALOG_PATCH_INFO";

char const *g_viewName2 = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";

char g_cfgName[MAX_CMD_SIZE] = {0};

// 存放错误信息
char g_errorMsg[MAX_CMD_SIZE] = {0};

// 存放日志白名单错误码
char g_errorCode01[MAX_CMD_SIZE] = {0};
char g_errorCode02[MAX_CMD_SIZE] = {0};
char g_errorCode03[MAX_CMD_SIZE] = {0};

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

char g_tableName[128] = "cap058";
char g_configJson[128] = "{\"max_record_count\":100000, \"isFastReadUncommitted\": false}";

int g_threadWait = 0;
pthread_mutex_t g_threadLock;

const char *g_schemaJson1 = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int64"},
            {"name" : "b", "type" : "int64"},
            {"name" : "c", "type" : "int64"},
            {"name" : "d", "type" : "int64"},
            {"name" : "e", "type" : "int64"}
        ]
    } ])";

/*--------------------------------------编译、加载------------------------------------*/
void CompileTest(char *inputFilePath, char *outputFilePath, char *soName, bool haveUdf)
{
    // 初始化
    char command[MAX_CMD_SIZE] = {0};
    char datalogFile[FILE_PATH] = {0};
    char outputFile[FILE_PATH] = {0};
    char libName[FILE_PATH] = {0};
    char udfFile[FILE_PATH] = {0};
    // 赋值
    (void)sprintf(datalogFile, "%s/%s.d", inputFilePath, soName);
    (void)sprintf(outputFile, "%s/%s.c", outputFilePath, soName);
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    if (haveUdf) {
        (void)sprintf(udfFile, "%s/%s_udf.c", inputFilePath, soName);
    }
    // .d->.c
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, datalogFile, outputFile);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    if (haveUdf) {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s -o %s \n", g_hFile,
            outputFile, udfFile, libName);
    } else {
        (void)snprintf(
            command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n", g_hFile, outputFile, libName);
    }
    system(command);
}

/*--------------------------------编译生成patch.c+full.d、加载升级patchso---------------------------------*/
void CompileUpgradeAndRollBackTest(char *inputFilePath, char *outputFilePath, char *soName, bool havePatchUdf,
    int upgradeCnt)
{
    // 初始化
    char command[MAX_CMD_SIZE] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char libName[FILE_PATH] = {0};
    char rollLibName[FILE_PATH] = {0};
    char udfPatchFile[FILE_PATH] = {0};

    if (upgradeCnt == 1) {
        (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
        (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
        (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, upgradeCnt + 1);
        (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);
        (void)snprintf(rollbackPatchCName, FILE_PATH, "%s/%s_patch_rollback.c", outputFilePath, soName);
        if (havePatchUdf) {
            (void)snprintf(udfPatchFile, FILE_PATH, "%s/%s_patch_udf.c", outputFilePath, soName);
        }
    } else {
        (void)snprintf(ruleFile, FILE_PATH, "%s/%s_ruleV%d.d", inputFilePath, soName, upgradeCnt);
        (void)snprintf(patchFile, FILE_PATH, "%s/%s_ruleV%d_patch.d", inputFilePath, soName, upgradeCnt);
        (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, upgradeCnt + 1);
        (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_ruleV%d_patch.c", outputFilePath, soName, upgradeCnt);
        (void)snprintf(rollbackPatchCName, FILE_PATH, "%s/%s_ruleV%d_patch_rollback.c", outputFilePath, soName,
            upgradeCnt);
        if (havePatchUdf) {
            (void)snprintf(udfPatchFile, FILE_PATH, "%s/%s_ruleV%d_patch_udf.c", outputFilePath, soName, upgradeCnt);
        }
    }
    (void)snprintf(libName, FILE_PATH, "%s/%s_patchV%d.so", outputFilePath, soName, upgradeCnt + 1);
    (void)snprintf(rollLibName, FILE_PATH, "%s/%s_rollbackV%d.so", outputFilePath, soName, upgradeCnt + 1);
    // rule.d + patch.d -> patch.c + ruleV2.d + patch_rollback.c
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    int ret = executeCommand(command, "Serialize done");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // .c->.so
    if (havePatchUdf) {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s %s -o %s \n", g_hFile,
            patchCOutputFile, udfPatchFile, libName);
    } else {
        (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n", g_hFile,
            patchCOutputFile, libName);
    }
    system(command);
    // 生成回滚so
    (void)snprintf(command, MAX_CMD_SIZE, "gcc -Wl,-Bsymbolic -fPIC -I %s --shared %s -o %s \n", g_hFile,
        rollbackPatchCName, rollLibName);
    system(command);
}

// 加载升级的so
int TestLoadUpgradeDatalog(const char *soName)
{
    const int commandSize = 1024;
    char loadCommand[commandSize] = {0};
    if (soName == NULL) {
        RETURN_IFERR(FAILED);
    }
    (void)snprintf(
        loadCommand, commandSize, "%s/gmimport -s %s -c datalog -upgrade %s", g_toolPath, g_connServer, soName);

    return system(loadCommand);
}

// 加载回滚的so
int TestLoadRollbackDatalog(const char *soName)
{
    const int commandSize = 1024;
    char loadCommand[commandSize] = {0};
    if (soName == NULL) {
        RETURN_IFERR(FAILED);
    }
    (void)snprintf(
        loadCommand, commandSize, "%s/gmimport -s %s -c datalog -rollback %s", g_toolPath, g_connServer, soName);

    return system(loadCommand);
}

/*----------------------------------------表结构--------------------------------------------*/
#pragma pack(1)
// (a, b, c)
typedef struct TagC3Int8 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} C3Int8T;

typedef struct TagC3Int8C1Int4 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int32_t d;
} C3Int8C1Int4T;

typedef struct TagC4Int8 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
} C4Int8T;

typedef struct TagC5Int8 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
    int64_t e;
} C5Int8T;

typedef struct TagC1Int1C2Int4 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t ifIndex;
    int8_t attr;
    int32_t value;
} C1Int1C2Int4T;

typedef struct TagThreadData {
    char labelName[128];
    C3Int8T *obj;
    int objLen;
} ThreadDataT;

#pragma pack(0)

// struct模式设置
int C3Int8Set(GmcStmtT *stmt, void *t)
{
    C3Int8T *obj = (C3Int8T *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Set] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Set] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &obj->c, sizeof(obj->c));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Set] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Set] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int C5Int8Set(GmcStmtT *stmt, void *t)
{
    C5Int8T *obj = (C5Int8T *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Set] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Set] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &obj->c, sizeof(obj->c));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Set] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(obj->d));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Set] d: %d, ret = %d.", obj->d, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_INT64, &obj->e, sizeof(obj->e));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Set] e: %d, ret = %d.", obj->e, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Set] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int C1Int1C2Int4Set(GmcStmtT *stmt, void *t)
{
    C1Int1C2Int4T *obj = (C1Int1C2Int4T *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "ifIndex", GMC_DATATYPE_INT32, &obj->ifIndex, sizeof(obj->ifIndex));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] ifIndex: %d, ret = %d.", obj->ifIndex, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "attr", GMC_DATATYPE_INT8, &obj->attr, sizeof(obj->attr));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] attr: %d, ret = %d.", obj->attr, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "value", GMC_DATATYPE_INT32, &obj->value, sizeof(obj->value));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] value: %d, ret = %d.", obj->value, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Int1C2Int4Set] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
        return ret;
    }
    return ret;
}

int C3Int8TimeoutSet(GmcStmtT *stmt, void *t)
{
    C3Int8T *obj = (C3Int8T *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutSet] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutSet] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    // 改成按分钟为颗粒度
    int64_t cValue = int64_t(obj->c * 1000 * 60);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &cValue, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutSet] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutSet] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// 输出表数据校验
int C3Int8Cmp(const C3Int8T *st1, const C3Int8T *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] a, st1: %ld, st2: %ld.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] b, st1: %ld, st2: %ld.", st1->b, st2->b)) : ({});
            break;
        }
        if (st1->c != st2->c) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] c, st1: %ld, st2: %ld.", st1->c, st2->c)) : ({});
            break;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] dtlReservedCount, st1: %d, st2: %d.",
                    st1->dtlReservedCount, st2->dtlReservedCount)) : ({});
                break;
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8Cmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                    st2->upgradeVersion)) : ({});
                break;
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int C5Int8Cmp(const C5Int8T *st1, const C5Int8T *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] a, st1: %ld, st2: %ld.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] b, st1: %ld, st2: %ld.", st1->b, st2->b)) : ({});
            break;
        }
        if (st1->c != st2->c) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] c, st1: %ld, st2: %ld.", st1->c, st2->c)) : ({});
            break;
        }
        if (st1->d != st2->d) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] d, st1: %ld, st2: %ld.", st1->d, st2->d)) : ({});
            break;
        }
        if (st1->e != st2->e) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] e, st1: %ld, st2: %ld.", st1->e, st2->e)) : ({});
            break;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] dtlReservedCount, st1: %d, st2: %d.",
                    st1->dtlReservedCount, st2->dtlReservedCount)) : ({});
                break;
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C5Int8Cmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                    st2->upgradeVersion)) : ({});
                break;
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int C3Int8C1Int4Cmp(const C3Int8C1Int4T *st1, const C3Int8C1Int4T *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] a, st1: %ld, st2: %ld.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] b, st1: %ld, st2: %ld.", st1->b, st2->b)) : ({});
            break;
        }
        if (st1->c != st2->c) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] c, st1: %ld, st2: %ld.", st1->c, st2->c)) : ({});
            break;
        }
        if (st1->d != st2->d) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] d, st1: %ld, st2: %ld.", st1->d, st2->d)) : ({});
            break;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] dtlReservedCount, st1: %d, st2: %d.",
                    st1->dtlReservedCount, st2->dtlReservedCount)) : ({});
                break;
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8C1Int4Cmp] upgradeVersion, st1: %d, st2: %d.",
                    st1->upgradeVersion, st2->upgradeVersion)) : ({});
                break;
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

// 不校验过期字段值
int C3Int8TimeoutCmp(const C3Int8T *st1, const C3Int8T *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8TimeoutCmp] a, st1: %ld, st2: %ld.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8TimeoutCmp] b, st1: %ld, st2: %ld.", st1->b, st2->b)) : ({});
            break;
        }
        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8TimeoutCmp] dtlReservedCount, st1: %d, st2: %d.",
                    st1->dtlReservedCount, st2->dtlReservedCount)) : ({});
                break;
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                isPrint ? (AW_FUN_Log(LOG_INFO, "[C3Int8TimeoutCmp] upgradeVersion, st1: %d, st2: %d.",
                    st1->upgradeVersion, st2->upgradeVersion)) : ({});
                break;
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

// 读表的数据
// 读（a, b, c）
int C3Int8Get(GmcStmtT *stmt, void *t, int len, bool isExternal)
{
    C3Int8T *checkObj = (C3Int8T *)t;
    C3Int8T getObj = {};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8Get] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    if (PRINT_INFO & !isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d %d", getObj.a, getObj.b, getObj.c,
            getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    if (PRINT_INFO & isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d", getObj.a, getObj.b, getObj.c);
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (C3Int8Cmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8Cmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}
// 读（a, b, c, d, e）
int C5Int8Get(GmcStmtT *stmt, void *t, int len, bool isExternal)
{
    C5Int8T *checkObj = (C5Int8T *)t;
    C5Int8T getObj = {};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'd' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "e", &getObj.e, sizeof(getObj.e), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'e' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C5Int8Get] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    if (PRINT_INFO & !isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %ld %ld %ld %ld %ld", getObj.a, getObj.b, getObj.c, getObj.d,
            getObj.e, getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    if (PRINT_INFO & isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %ld %ld %dl", getObj.a, getObj.b, getObj.c, getObj.d, getObj.e);
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (C5Int8Cmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C5Int8Cmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[C3Int8Get] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}
// 读(a, b, c, d)
int C3Int8C1Int4Get(GmcStmtT *stmt, void *t, int len, bool isExternal)
{
    C3Int8C1Int4T *checkObj = (C3Int8C1Int4T *)t;
    C3Int8C1Int4T getObj = {};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'd' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Gett] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    if (PRINT_INFO & !isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d %d %d", getObj.a, getObj.b, getObj.c, getObj.d,
            getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    if (PRINT_INFO & isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %d %d %d %d", getObj.a, getObj.b, getObj.c, getObj.d);
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (C3Int8C1Int4Cmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8C1Int4Cmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4Get] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// 读过期表的表
int C3Int8TimeoutGet(GmcStmtT *stmt, void *t, int len, bool isExternal)
{
    C3Int8T *checkObj = (C3Int8T *)t;
    C3Int8T getObj = {};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    if (PRINT_INFO & !isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %ld %ld %ld %ld %ld", getObj.a, getObj.b, getObj.c,
            getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    if (PRINT_INFO & isExternal) {
        AW_FUN_Log(LOG_DEBUG, "[INFO]OUT_LABEL_RESULT: %ld %ld %ld", getObj.a, getObj.b, getObj.c);
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (C3Int8TimeoutCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8TimeoutCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[C3Int8TimeoutGet] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

// 读(a, b, c), notify表
int C3Int8RescGet(GmcStmtT *stmt, void *t, int len, bool isResourcePubSub)
{
    C3Int8T *checkObj = (C3Int8T *)t;
    C3Int8T getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] get 'upgradeVersion' fail, ret = %d.", ret);
        return ret;
    }

    if (PRINT_INFO) {
        AW_FUN_Log(LOG_DEBUG, "[INFO][C3Int8RescGet]OUT_LABEL_RESULT: %ld %ld %ld %d %d ", getObj.a, getObj.b, getObj.c,
            getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    ret = -1;
    // 校验
    for (int i = 0; i < len; i++) {
        if (C3Int8Cmp(&getObj, &checkObj[i], NULL, NULL) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8Cmp(&getObj, &checkObj[0], NULL, NULL);
        AW_FUN_Log(LOG_ERROR, "[C3Int8RescGet] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

int C3Int8C1Int4RescGet(GmcStmtT *stmt, void *t, int len, bool isResourcePubSub)
{
    C3Int8C1Int4T *checkObj = (C3Int8C1Int4T *)t;
    C3Int8C1Int4T getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }
    // pubsub 资源表
    if (isResourcePubSub) {
        getObj.d = 1;
    } else {
        ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'e' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] get 'upgradeVersion' fail, ret = %d.", ret);
        return ret;
    }

    if (PRINT_INFO) {
        AW_FUN_Log(LOG_DEBUG, "[INFO][C3Int8C1Int4RescGet]OUT_LABEL_RESULT: %ld %ld %ld %d %d %d ", getObj.a, getObj.b,
            getObj.c, getObj.d, getObj.dtlReservedCount, getObj.upgradeVersion);
    }
    ret = -1;
    // 校验
    for (int i = 0; i < len; i++) {
        if (C3Int8C1Int4Cmp(&getObj, &checkObj[i], NULL, NULL) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)C3Int8C1Int4Cmp(&getObj, &checkObj[0], NULL, NULL);
        AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] count: %d.", getObj.dtlReservedCount);
    }
    // 设置字段发回服务端
    if (isResourcePubSub) {
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &getObj.a, sizeof(getObj.a));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] a: %d, ret = %d.", getObj.a, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &getObj.b, sizeof(getObj.b));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] b: %d, ret = %d.", getObj.b, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &getObj.c, sizeof(getObj.c));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] c: %d, ret = %d.", getObj.c, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT32, &getObj.d, sizeof(getObj.d));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] d: %d, ret = %d.", getObj.d, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(
                LOG_ERROR, "[C3Int8C1Int4RescGet] dtlReservedCount: %d, ret = %d.", getObj.dtlReservedCount, ret);
            return ret;
        }
        ret = GmcSetVertexProperty(
            stmt, "upgradeVersion", GMC_DATATYPE_INT32, &getObj.upgradeVersion, sizeof(getObj.dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[C3Int8C1Int4RescGet] upgradeVersion: %d, ret = %d.", getObj.dtlReservedCount, ret);
            return ret;
        }
    }
    return ret;
}

string GetStr(const char *command, const char *keyWord1, const char *keyWord2)
{
    string str;
    char cmdOutPut[1024] = {0};
    FILE *pf = popen(command, "r");
    while (fgets(cmdOutPut, sizeof(cmdOutPut), pf) != NULL) {
        str.assign(cmdOutPut);
        string::size_type idx = str.find(keyWord1);
        if (idx != string::npos) {
            int pos1 = str.find(keyWord1) + strlen(keyWord1);
            int pos2 = str.find(keyWord2) - str.find(keyWord1) - strlen(keyWord1);
            str = str.substr(pos1, pos2);
            pclose(pf);
            return str;
        }
    }
    pclose(pf);
    return "No Str";
}

const char *g_messageFilePath = "/root/_datalog_/message.txt";
void WriteInFile(const char *filePath, const char *info)
{
    (void)snprintf(g_command, MAX_CMD_SIZE, "echo \"%s\" >> %s", info, filePath);
    system(g_command);
}

int CheckExpect(bool expect, const char *fileName, int lineNum, bool isServer)
{
    if (!expect) {
        cout << endl << "isServer:" << isServer << ", line:" << lineNum << endl;

        char command[MAX_CMD_SIZE];
        char info[MAX_CMD_SIZE];
        (void)snprintf(command, MAX_CMD_SIZE, "cat %s|tail -n +%d|head -n 1", fileName, lineNum);
        string unExpectInfoStr = GetStr(command, "CHECK_EXPECT(", ");");
        const char *unExpectInfo = unExpectInfoStr.c_str();
        (void)snprintf(info, MAX_CMD_SIZE, "UnExpect:\"%s\" in file:%s:%d", unExpectInfo, fileName, lineNum);
        WriteInFile(g_messageFilePath, info);
        return -1;
    }
    return 0;
}

int GetValue(const char *command, const char *keyWord)
{
    string valueStr;
    int value = 0;
    char cmdOutPut[1024] = {0};
    FILE *pf = popen(command, "r");
    while (fgets(cmdOutPut, sizeof(cmdOutPut), pf) != NULL) {
        valueStr.assign(cmdOutPut);
        string::size_type idx = valueStr.find(keyWord);
        if (idx != string::npos) {
            valueStr = valueStr.substr(valueStr.find(keyWord) + strlen(keyWord));
            value = stoi(valueStr);
            pclose(pf);
            return value;
        }
    }
    pclose(pf);
    return -1;
}

int ReadTablePKScan(
    GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int64_t aValue, int64_t bValue, int64_t expectCValue)
{
    int ret = 0;
    int32_t upVerVal = -1;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取upgradeVersion值并设置
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upVerVal, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &aValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &bValue, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish;
    bool isNull = true;
    int cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        int64_t readC;
        ret = GmcGetVertexPropertyByName(stmt, "c", &readC, sizeof(int64_t), &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectCValue, readC);
    }
    return ret;
}

// 获取CATA_VERTEX_LABEL_INFO视图中，datalog表的记录
int GetCataDatalogCount(int *value, const char *labelname)
{
    int ret = 0;
    char command[1024] = {0};
    ret = snprintf(
        command, 1024, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s | grep index", labelname);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, value);
    if (ret) {
        return FAILED;
    }
    return 0;
}

#endif
