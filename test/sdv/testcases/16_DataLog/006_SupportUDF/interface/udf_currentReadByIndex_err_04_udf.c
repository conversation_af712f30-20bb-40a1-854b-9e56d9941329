/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include "gm_udf.h"

#pragma pack(1)

typedef struct Para {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
    int32_t c;
} Para;

typedef struct Index {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} Index;

#pragma pack(0)

int32_t dtl_ext_func_func(void *tuple, GmUdfCtxT *ctx)
{
    // 增加一列
    Para *para = (Para *)tuple;
    para->c = para->a + para->b;

    // 索引读C_org表
    GmUdfReaderT *reader = NULL;
    Index key = {.a = 2};
    int ret = GmUdfCreateCurrentReaderByIndex(ctx, sizeof(Index), &key, 0, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}
