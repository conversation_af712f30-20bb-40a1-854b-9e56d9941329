%table A2(a:int4, b:int4)
%table B2(a:int4, min:int4, max:int4)


%aggregate A2agg001(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg002(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg003(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg004(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg005(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg006(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg007(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg008(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg009(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg010(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg011(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg012(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg013(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg014(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg015(a: int4->min: int4, max: int4) {ordered}
%aggregate A2agg016(a: int4->min: int4, max: int4) {ordered}



B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg001(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg002(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg003(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg004(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg005(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg006(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg007(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg008(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg009(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg010(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg011(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg012(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg013(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg014(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg015(b, min, max).
null(0) :- B2(a, min, max) .
B2(a, min, max) :- A2(a, b) GROUP-BY(a) A2agg016(b, min, max).
null(0) :- B2(a, min, max) .

