/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalog.h
 * Description: datalog compilation
 * Author: jiangshan/j00811785
 * Create: 2022-09-13
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t dtlReservedCount;
    int32_t a;
} A;

typedef struct B {
    int32_t a;
    int32_t b;
} B;

#pragma pack(0)

int32_t dtl_agg_compare_A25agg001(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg001(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg002(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg002(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg003(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg003(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg004(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg004(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg005(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg005(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg006(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg006(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg007(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg007(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg008(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg008(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg009(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg009(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg010(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg010(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg011(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg011(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg012(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg012(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg013(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg013(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg014(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg014(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg015(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg015(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
int32_t dtl_agg_compare_A25agg016(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    A *inp1 = (A *)tuple1;
    A *inp2 = (A *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        return 0;
    }
}
int32_t dtl_agg_func_A25agg016(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    A *inpStruct;
    B *outStruct;
    int32_t ret = GmUdfGetNext(inputBak, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t min = inpStruct->a;
    int32_t max = inpStruct->a;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        max = inpStruct->a;
    }
    outStruct->a = min;
    outStruct->b = max;

    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;
    return GMERR_OK;
}
