%table A(a:int4, b:int4)
{
    index(0(a,b)),
    index(1(a))
}
%table B(a:int4, min:int4, max:int4)
%table out(a:int4, b:int4, c:int4, d:int4)
namespace ns1{
    %table C1(a:int4, b:int4)
    %readonly C1
}
%table D1(a:int4, b:int4)

namespace ns2{
    %table C1(a:int4, b:int4)
    %readonly C1
}

%function func(a: int4, b: int4->c: int4, d: int4) {}

%aggregate agg(a: int4->min: int4, max: int4) {
    access_current(func)
    
}


B(a, min, max) :- A(a, b) GROUP-BY(a) agg(b, min, max).
null(0) :- B(a, min, max) .
using namespace ns1
D1(a, b) :- ns1.C1(a, b).
D1(a, b) :- ns2.C1(a, b).
out(a, b, c, d) :- A(a, b), func(a, b, c, d).




