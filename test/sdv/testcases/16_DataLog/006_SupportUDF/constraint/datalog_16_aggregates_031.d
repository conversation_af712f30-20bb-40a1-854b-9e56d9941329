%table A31(a:int4, b:int4)
%table B31(a:int4, min:int4, max:int4)


%aggregate A31agg001(a: int4->min: int4, max: int4) {}
%aggregate A31agg002(a: int4->min: int4, max: int4) {}
%aggregate A31agg003(a: int4->min: int4, max: int4) {}
%aggregate A31agg004(a: int4->min: int4, max: int4) {}
%aggregate A31agg005(a: int4->min: int4, max: int4) {}
%aggregate A31agg006(a: int4->min: int4, max: int4) {}
%aggregate A31agg007(a: int4->min: int4, max: int4) {}
%aggregate A31agg008(a: int4->min: int4, max: int4) {}
%aggregate A31agg009(a: int4->min: int4, max: int4) {}
%aggregate A31agg010(a: int4->min: int4, max: int4) {}
%aggregate A31agg011(a: int4->min: int4, max: int4) {}
%aggregate A31agg012(a: int4->min: int4, max: int4) {}
%aggregate A31agg013(a: int4->min: int4, max: int4) {}
%aggregate A31agg014(a: int4->min: int4, max: int4) {}
%aggregate A31agg015(a: int4->min: int4, max: int4) {}
%aggregate A31agg016(a: int4->min: int4, max: int4) {}




B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg001(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg002(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg003(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg004(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg005(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg006(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg007(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg008(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg009(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg010(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg011(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg012(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg013(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg014(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg015(b, min, max).
null(0) :- B31(a, min, max) .
B31(a, min, max) :- A31(a, b) GROUP-BY(a) A31agg016(b, min, max).
null(0) :- B31(a, min, max) .

