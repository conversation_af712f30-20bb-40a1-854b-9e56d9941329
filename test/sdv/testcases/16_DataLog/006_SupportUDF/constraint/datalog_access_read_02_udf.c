/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t a;
    int32_t b;
} A;

typedef struct B {
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t dtlReservedCount;
} B;

#pragma pack(0)

int32_t dtl_ext_func_func(void *tuple, GmUdfCtxT *ctx)
{
    // 增加一列
    B *b = (B *)tuple;
    b->c = b->a + b->b;

    int32_t count[][4] = {{1, 2, 3, 1}, {2, 1, 3, 1}, {3, 3, 6, 1}};
    // 读B_org表,表中没有数据，下面校验没起作用
    GmUdfReaderT *readerOrg = NULL;
    int ret = GmUdfCreateCurrentReader(ctx, 0, &readerOrg);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b1;
    int i = 0;
    while (ret = GmUdfGetNext(readerOrg, (void **)&b1), ret == GMERR_OK) {
        // 校验值
        if (b1->a != count[i][0] || b1->b != count[i][1] || b1->c != count[i][2]) {
            return ret;
        }
        i++;
    }
    GmUdfDestroyReader(ctx, readerOrg);

    // 读B_delta表
    GmUdfReaderT *readerDelta = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &readerDelta);
    if (ret != GMERR_OK) {
        return ret;
    }

    B *b2;
    i = 0;
    while (ret = GmUdfGetNext(readerDelta, (void **)&b2), ret == GMERR_OK) {
    }
    GmUdfDestroyReader(ctx, readerDelta);
    return GMERR_OK;
}
