%table A(a:int4, b:int4)
{
    index(0(a,b)),
    index(1(a))
}
%table B(a:int4, min:int4, max:int4)

%table C1(a:int4, b:int4)
%table D1(a:int4, b:int4)

%table C2(a:int4, b:int4) {transient(field(b))}
%table C3(a:int4, b:int4) {transient(tuple)}
%table D2(a:int4, b:int4)
%table D3(a:int4, b:int4, c:int4)
%resource res1(a:int4, b:int4->c:int4){sequential(max_size(5))}
%resource res2(a:int4, b:int4->c:int4){pending_id(-1)}
%table E1(a:int4, b:int4)
%table E2(a:int4, b:int4, c:int4)



%aggregate agg(a: int4->min: int4, max: int4) {
    access_current(C1, C2, C3, res1, res2)
    
}


B(a, min, max) :- A(a, b) GROUP-BY(a) agg(b, min, max).
null(0) :- B(a, min, max) .

D1(a, b) :- C1(a, b).
C2(a, b) :- D1(a, b).
C3(a, b) :- C2(a, b).
D2(a, b) :- C3(a, b).

res1(a, b, -) :- D2(a, b).
D3(a, b, c) :- res1(a, b, c).
res2(a, b, -) :- E1(a, b).
E2(a, b, c) :- res2(a, b, c).




