/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalog.h
 * Description: datalog compilation
 * Author: jiangshan/j00811785
 * Create: 2022-09-13
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t a;
    int32_t b;
} A;

typedef struct B {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t d;
} B;

#pragma pack(0)

int32_t dtl_ext_func_A41func001(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func002(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func003(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func004(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func005(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func006(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func007(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func008(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func009(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func010(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func011(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func012(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func013(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func014(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func015(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}
int32_t dtl_ext_func_A41func016(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    b->d = b->a * b->b;
    return GMERR_OK;
}

