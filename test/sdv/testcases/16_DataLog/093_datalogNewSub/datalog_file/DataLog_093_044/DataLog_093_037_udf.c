/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */


#include <stdio.h>
#include <string.h>
#include "gm_udf.h"
#include "assert.h"
#include "unistd.h"

#pragma pack(1)
typedef struct Tuple {
    int32_t count;
    int32_t p;
    int32_t s;
} TupleT;

typedef struct A {
    int32_t count;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
} A;
#pragma pack(0)

int32_t dtl_ext_func_tran(void *tuple, GmUdfCtxT *ctx)
{
    TupleT *result = (TupleT *)tuple;
    int *p = &result->p;
    int *s = &result->s;
    
    int *count = &result->count;
    GmUdfReaderT *reader = NULL;
    int32_t ret = GmUdfCreateCurrentReader(ctx, 0, &reader);  // 读触发表
    if (ret != GMERR_OK) {
        return ret;
    }
    usleep(2000 * 1000);
    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    // 对应的状态变化
    // if p == 0 && count == 1 && s == 1, s = 0;
    // if p == 1 && count == 1 && s == 0, s = 1;
    // if p == 2 && count == 0, init, s = 1, count = 1;
    // if p == 3 && count == 1, delete, count = -1;
    if (*count == 0) {
        if (*p == 2) {
            *s = 1;
            *count = 1;
            return GMERR_OK;
        }
    } else if (*count == 1) {
        if (*s == 1 && *p == 0) {
            *s = 0;
            return GMERR_OK;
        }
        if (*s == 0 && *p == 1) {
            *s = 1;
            return GMERR_OK;
        }
        if (*p == 3) {
            *count = -1;
            return GMERR_OK;
        }
    } else {
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_NO_DATA;
}
