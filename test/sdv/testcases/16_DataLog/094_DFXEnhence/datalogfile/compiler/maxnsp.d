%table inp0(a:int8, b:int8, c:int8)
%table mid0(a:int8, b:int8, c:int8)
%table out255(a:int8, b:int8, c:int8)

namespace dnsp1 {
    %table inp1(a:int8, b:int8, c:int8)
    %table mid1(a:int8, b:int8, c:int8)
    %table out0(a:int8, b:int8, c:int8)
    %readwrite inp1
    %readwrite mid1
    %readwrite out0
}
namespace dnsp2 {
    %table inp2(a:int8, b:int8, c:int8)
    %table mid2(a:int8, b:int8, c:int8)
    %table out1(a:int8, b:int8, c:int8)
    %readwrite inp2
    %readwrite mid2
    %readwrite out1
}
namespace dnsp3 {
    %table inp3(a:int8, b:int8, c:int8)
    %table mid3(a:int8, b:int8, c:int8)
    %table out2(a:int8, b:int8, c:int8)
    %readwrite inp3
    %readwrite mid3
    %readwrite out2
}
namespace dnsp4 {
    %table inp4(a:int8, b:int8, c:int8)
    %table mid4(a:int8, b:int8, c:int8)
    %table out3(a:int8, b:int8, c:int8)
    %readwrite inp4
    %readwrite mid4
    %readwrite out3
}
namespace dnsp5 {
    %table inp5(a:int8, b:int8, c:int8)
    %table mid5(a:int8, b:int8, c:int8)
    %table out4(a:int8, b:int8, c:int8)
    %readwrite inp5
    %readwrite mid5
    %readwrite out4
}
namespace dnsp6 {
    %table inp6(a:int8, b:int8, c:int8)
    %table mid6(a:int8, b:int8, c:int8)
    %table out5(a:int8, b:int8, c:int8)
    %readwrite inp6
    %readwrite mid6
    %readwrite out5
}
namespace dnsp7 {
    %table inp7(a:int8, b:int8, c:int8)
    %table mid7(a:int8, b:int8, c:int8)
    %table out6(a:int8, b:int8, c:int8)
    %readwrite inp7
    %readwrite mid7
    %readwrite out6
}
namespace dnsp8 {
    %table inp8(a:int8, b:int8, c:int8)
    %table mid8(a:int8, b:int8, c:int8)
    %table out7(a:int8, b:int8, c:int8)
    %readwrite inp8
    %readwrite mid8
    %readwrite out7
}
namespace dnsp9 {
    %table inp9(a:int8, b:int8, c:int8)
    %table mid9(a:int8, b:int8, c:int8)
    %table out8(a:int8, b:int8, c:int8)
    %readwrite inp9
    %readwrite mid9
    %readwrite out8
}
namespace dnsp10 {
    %table inp10(a:int8, b:int8, c:int8)
    %table mid10(a:int8, b:int8, c:int8)
    %table out9(a:int8, b:int8, c:int8)
    %readwrite inp10
    %readwrite mid10
    %readwrite out9
}
namespace dnsp11 {
    %table inp11(a:int8, b:int8, c:int8)
    %table mid11(a:int8, b:int8, c:int8)
    %table out10(a:int8, b:int8, c:int8)
    %readwrite inp11
    %readwrite mid11
    %readwrite out10
}
namespace dnsp12 {
    %table inp12(a:int8, b:int8, c:int8)
    %table mid12(a:int8, b:int8, c:int8)
    %table out11(a:int8, b:int8, c:int8)
    %readwrite inp12
    %readwrite mid12
    %readwrite out11
}
namespace dnsp13 {
    %table inp13(a:int8, b:int8, c:int8)
    %table mid13(a:int8, b:int8, c:int8)
    %table out12(a:int8, b:int8, c:int8)
    %readwrite inp13
    %readwrite mid13
    %readwrite out12
}
namespace dnsp14 {
    %table inp14(a:int8, b:int8, c:int8)
    %table mid14(a:int8, b:int8, c:int8)
    %table out13(a:int8, b:int8, c:int8)
    %readwrite inp14
    %readwrite mid14
    %readwrite out13
}
namespace dnsp15 {
    %table inp15(a:int8, b:int8, c:int8)
    %table mid15(a:int8, b:int8, c:int8)
    %table out14(a:int8, b:int8, c:int8)
    %readwrite inp15
    %readwrite mid15
    %readwrite out14
}
namespace dnsp16 {
    %table inp16(a:int8, b:int8, c:int8)
    %table mid16(a:int8, b:int8, c:int8)
    %table out15(a:int8, b:int8, c:int8)
    %readwrite inp16
    %readwrite mid16
    %readwrite out15
}
namespace dnsp17 {
    %table inp17(a:int8, b:int8, c:int8)
    %table mid17(a:int8, b:int8, c:int8)
    %table out16(a:int8, b:int8, c:int8)
    %readwrite inp17
    %readwrite mid17
    %readwrite out16
}
namespace dnsp18 {
    %table inp18(a:int8, b:int8, c:int8)
    %table mid18(a:int8, b:int8, c:int8)
    %table out17(a:int8, b:int8, c:int8)
    %readwrite inp18
    %readwrite mid18
    %readwrite out17
}
namespace dnsp19 {
    %table inp19(a:int8, b:int8, c:int8)
    %table mid19(a:int8, b:int8, c:int8)
    %table out18(a:int8, b:int8, c:int8)
    %readwrite inp19
    %readwrite mid19
    %readwrite out18
}
namespace dnsp20 {
    %table inp20(a:int8, b:int8, c:int8)
    %table mid20(a:int8, b:int8, c:int8)
    %table out19(a:int8, b:int8, c:int8)
    %readwrite inp20
    %readwrite mid20
    %readwrite out19
}
namespace dnsp21 {
    %table inp21(a:int8, b:int8, c:int8)
    %table mid21(a:int8, b:int8, c:int8)
    %table out20(a:int8, b:int8, c:int8)
    %readwrite inp21
    %readwrite mid21
    %readwrite out20
}
namespace dnsp22 {
    %table inp22(a:int8, b:int8, c:int8)
    %table mid22(a:int8, b:int8, c:int8)
    %table out21(a:int8, b:int8, c:int8)
    %readwrite inp22
    %readwrite mid22
    %readwrite out21
}
namespace dnsp23 {
    %table inp23(a:int8, b:int8, c:int8)
    %table mid23(a:int8, b:int8, c:int8)
    %table out22(a:int8, b:int8, c:int8)
    %readwrite inp23
    %readwrite mid23
    %readwrite out22
}
namespace dnsp24 {
    %table inp24(a:int8, b:int8, c:int8)
    %table mid24(a:int8, b:int8, c:int8)
    %table out23(a:int8, b:int8, c:int8)
    %readwrite inp24
    %readwrite mid24
    %readwrite out23
}
namespace dnsp25 {
    %table inp25(a:int8, b:int8, c:int8)
    %table mid25(a:int8, b:int8, c:int8)
    %table out24(a:int8, b:int8, c:int8)
    %readwrite inp25
    %readwrite mid25
    %readwrite out24
}
namespace dnsp26 {
    %table inp26(a:int8, b:int8, c:int8)
    %table mid26(a:int8, b:int8, c:int8)
    %table out25(a:int8, b:int8, c:int8)
    %readwrite inp26
    %readwrite mid26
    %readwrite out25
}
namespace dnsp27 {
    %table inp27(a:int8, b:int8, c:int8)
    %table mid27(a:int8, b:int8, c:int8)
    %table out26(a:int8, b:int8, c:int8)
    %readwrite inp27
    %readwrite mid27
    %readwrite out26
}
namespace dnsp28 {
    %table inp28(a:int8, b:int8, c:int8)
    %table mid28(a:int8, b:int8, c:int8)
    %table out27(a:int8, b:int8, c:int8)
    %readwrite inp28
    %readwrite mid28
    %readwrite out27
}
namespace dnsp29 {
    %table inp29(a:int8, b:int8, c:int8)
    %table mid29(a:int8, b:int8, c:int8)
    %table out28(a:int8, b:int8, c:int8)
    %readwrite inp29
    %readwrite mid29
    %readwrite out28
}
namespace dnsp30 {
    %table inp30(a:int8, b:int8, c:int8)
    %table mid30(a:int8, b:int8, c:int8)
    %table out29(a:int8, b:int8, c:int8)
    %readwrite inp30
    %readwrite mid30
    %readwrite out29
}
namespace dnsp31 {
    %table inp31(a:int8, b:int8, c:int8)
    %table mid31(a:int8, b:int8, c:int8)
    %table out30(a:int8, b:int8, c:int8)
    %readwrite inp31
    %readwrite mid31
    %readwrite out30
}
namespace dnsp32 {
    %table inp32(a:int8, b:int8, c:int8)
    %table mid32(a:int8, b:int8, c:int8)
    %table out31(a:int8, b:int8, c:int8)
    %readwrite inp32
    %readwrite mid32
    %readwrite out31
}
namespace dnsp33 {
    %table inp33(a:int8, b:int8, c:int8)
    %table mid33(a:int8, b:int8, c:int8)
    %table out32(a:int8, b:int8, c:int8)
    %readwrite inp33
    %readwrite mid33
    %readwrite out32
}
namespace dnsp34 {
    %table inp34(a:int8, b:int8, c:int8)
    %table mid34(a:int8, b:int8, c:int8)
    %table out33(a:int8, b:int8, c:int8)
    %readwrite inp34
    %readwrite mid34
    %readwrite out33
}
namespace dnsp35 {
    %table inp35(a:int8, b:int8, c:int8)
    %table mid35(a:int8, b:int8, c:int8)
    %table out34(a:int8, b:int8, c:int8)
    %readwrite inp35
    %readwrite mid35
    %readwrite out34
}
namespace dnsp36 {
    %table inp36(a:int8, b:int8, c:int8)
    %table mid36(a:int8, b:int8, c:int8)
    %table out35(a:int8, b:int8, c:int8)
    %readwrite inp36
    %readwrite mid36
    %readwrite out35
}
namespace dnsp37 {
    %table inp37(a:int8, b:int8, c:int8)
    %table mid37(a:int8, b:int8, c:int8)
    %table out36(a:int8, b:int8, c:int8)
    %readwrite inp37
    %readwrite mid37
    %readwrite out36
}
namespace dnsp38 {
    %table inp38(a:int8, b:int8, c:int8)
    %table mid38(a:int8, b:int8, c:int8)
    %table out37(a:int8, b:int8, c:int8)
    %readwrite inp38
    %readwrite mid38
    %readwrite out37
}
namespace dnsp39 {
    %table inp39(a:int8, b:int8, c:int8)
    %table mid39(a:int8, b:int8, c:int8)
    %table out38(a:int8, b:int8, c:int8)
    %readwrite inp39
    %readwrite mid39
    %readwrite out38
}
namespace dnsp40 {
    %table inp40(a:int8, b:int8, c:int8)
    %table mid40(a:int8, b:int8, c:int8)
    %table out39(a:int8, b:int8, c:int8)
    %readwrite inp40
    %readwrite mid40
    %readwrite out39
}
namespace dnsp41 {
    %table inp41(a:int8, b:int8, c:int8)
    %table mid41(a:int8, b:int8, c:int8)
    %table out40(a:int8, b:int8, c:int8)
    %readwrite inp41
    %readwrite mid41
    %readwrite out40
}
namespace dnsp42 {
    %table inp42(a:int8, b:int8, c:int8)
    %table mid42(a:int8, b:int8, c:int8)
    %table out41(a:int8, b:int8, c:int8)
    %readwrite inp42
    %readwrite mid42
    %readwrite out41
}
namespace dnsp43 {
    %table inp43(a:int8, b:int8, c:int8)
    %table mid43(a:int8, b:int8, c:int8)
    %table out42(a:int8, b:int8, c:int8)
    %readwrite inp43
    %readwrite mid43
    %readwrite out42
}
namespace dnsp44 {
    %table inp44(a:int8, b:int8, c:int8)
    %table mid44(a:int8, b:int8, c:int8)
    %table out43(a:int8, b:int8, c:int8)
    %readwrite inp44
    %readwrite mid44
    %readwrite out43
}
namespace dnsp45 {
    %table inp45(a:int8, b:int8, c:int8)
    %table mid45(a:int8, b:int8, c:int8)
    %table out44(a:int8, b:int8, c:int8)
    %readwrite inp45
    %readwrite mid45
    %readwrite out44
}
namespace dnsp46 {
    %table inp46(a:int8, b:int8, c:int8)
    %table mid46(a:int8, b:int8, c:int8)
    %table out45(a:int8, b:int8, c:int8)
    %readwrite inp46
    %readwrite mid46
    %readwrite out45
}
namespace dnsp47 {
    %table inp47(a:int8, b:int8, c:int8)
    %table mid47(a:int8, b:int8, c:int8)
    %table out46(a:int8, b:int8, c:int8)
    %readwrite inp47
    %readwrite mid47
    %readwrite out46
}
namespace dnsp48 {
    %table inp48(a:int8, b:int8, c:int8)
    %table mid48(a:int8, b:int8, c:int8)
    %table out47(a:int8, b:int8, c:int8)
    %readwrite inp48
    %readwrite mid48
    %readwrite out47
}
namespace dnsp49 {
    %table inp49(a:int8, b:int8, c:int8)
    %table mid49(a:int8, b:int8, c:int8)
    %table out48(a:int8, b:int8, c:int8)
    %readwrite inp49
    %readwrite mid49
    %readwrite out48
}
namespace dnsp50 {
    %table inp50(a:int8, b:int8, c:int8)
    %table mid50(a:int8, b:int8, c:int8)
    %table out49(a:int8, b:int8, c:int8)
    %readwrite inp50
    %readwrite mid50
    %readwrite out49
}
namespace dnsp51 {
    %table inp51(a:int8, b:int8, c:int8)
    %table mid51(a:int8, b:int8, c:int8)
    %table out50(a:int8, b:int8, c:int8)
    %readwrite inp51
    %readwrite mid51
    %readwrite out50
}
namespace dnsp52 {
    %table inp52(a:int8, b:int8, c:int8)
    %table mid52(a:int8, b:int8, c:int8)
    %table out51(a:int8, b:int8, c:int8)
    %readwrite inp52
    %readwrite mid52
    %readwrite out51
}
namespace dnsp53 {
    %table inp53(a:int8, b:int8, c:int8)
    %table mid53(a:int8, b:int8, c:int8)
    %table out52(a:int8, b:int8, c:int8)
    %readwrite inp53
    %readwrite mid53
    %readwrite out52
}
namespace dnsp54 {
    %table inp54(a:int8, b:int8, c:int8)
    %table mid54(a:int8, b:int8, c:int8)
    %table out53(a:int8, b:int8, c:int8)
    %readwrite inp54
    %readwrite mid54
    %readwrite out53
}
namespace dnsp55 {
    %table inp55(a:int8, b:int8, c:int8)
    %table mid55(a:int8, b:int8, c:int8)
    %table out54(a:int8, b:int8, c:int8)
    %readwrite inp55
    %readwrite mid55
    %readwrite out54
}
namespace dnsp56 {
    %table inp56(a:int8, b:int8, c:int8)
    %table mid56(a:int8, b:int8, c:int8)
    %table out55(a:int8, b:int8, c:int8)
    %readwrite inp56
    %readwrite mid56
    %readwrite out55
}
namespace dnsp57 {
    %table inp57(a:int8, b:int8, c:int8)
    %table mid57(a:int8, b:int8, c:int8)
    %table out56(a:int8, b:int8, c:int8)
    %readwrite inp57
    %readwrite mid57
    %readwrite out56
}
namespace dnsp58 {
    %table inp58(a:int8, b:int8, c:int8)
    %table mid58(a:int8, b:int8, c:int8)
    %table out57(a:int8, b:int8, c:int8)
    %readwrite inp58
    %readwrite mid58
    %readwrite out57
}
namespace dnsp59 {
    %table inp59(a:int8, b:int8, c:int8)
    %table mid59(a:int8, b:int8, c:int8)
    %table out58(a:int8, b:int8, c:int8)
    %readwrite inp59
    %readwrite mid59
    %readwrite out58
}
namespace dnsp60 {
    %table inp60(a:int8, b:int8, c:int8)
    %table mid60(a:int8, b:int8, c:int8)
    %table out59(a:int8, b:int8, c:int8)
    %readwrite inp60
    %readwrite mid60
    %readwrite out59
}
namespace dnsp61 {
    %table inp61(a:int8, b:int8, c:int8)
    %table mid61(a:int8, b:int8, c:int8)
    %table out60(a:int8, b:int8, c:int8)
    %readwrite inp61
    %readwrite mid61
    %readwrite out60
}
namespace dnsp62 {
    %table inp62(a:int8, b:int8, c:int8)
    %table mid62(a:int8, b:int8, c:int8)
    %table out61(a:int8, b:int8, c:int8)
    %readwrite inp62
    %readwrite mid62
    %readwrite out61
}
namespace dnsp63 {
    %table inp63(a:int8, b:int8, c:int8)
    %table mid63(a:int8, b:int8, c:int8)
    %table out62(a:int8, b:int8, c:int8)
    %readwrite inp63
    %readwrite mid63
    %readwrite out62
}
namespace dnsp64 {
    %table inp64(a:int8, b:int8, c:int8)
    %table mid64(a:int8, b:int8, c:int8)
    %table out63(a:int8, b:int8, c:int8)
    %readwrite inp64
    %readwrite mid64
    %readwrite out63
}
namespace dnsp65 {
    %table inp65(a:int8, b:int8, c:int8)
    %table mid65(a:int8, b:int8, c:int8)
    %table out64(a:int8, b:int8, c:int8)
    %readwrite inp65
    %readwrite mid65
    %readwrite out64
}
namespace dnsp66 {
    %table inp66(a:int8, b:int8, c:int8)
    %table mid66(a:int8, b:int8, c:int8)
    %table out65(a:int8, b:int8, c:int8)
    %readwrite inp66
    %readwrite mid66
    %readwrite out65
}
namespace dnsp67 {
    %table inp67(a:int8, b:int8, c:int8)
    %table mid67(a:int8, b:int8, c:int8)
    %table out66(a:int8, b:int8, c:int8)
    %readwrite inp67
    %readwrite mid67
    %readwrite out66
}
namespace dnsp68 {
    %table inp68(a:int8, b:int8, c:int8)
    %table mid68(a:int8, b:int8, c:int8)
    %table out67(a:int8, b:int8, c:int8)
    %readwrite inp68
    %readwrite mid68
    %readwrite out67
}
namespace dnsp69 {
    %table inp69(a:int8, b:int8, c:int8)
    %table mid69(a:int8, b:int8, c:int8)
    %table out68(a:int8, b:int8, c:int8)
    %readwrite inp69
    %readwrite mid69
    %readwrite out68
}
namespace dnsp70 {
    %table inp70(a:int8, b:int8, c:int8)
    %table mid70(a:int8, b:int8, c:int8)
    %table out69(a:int8, b:int8, c:int8)
    %readwrite inp70
    %readwrite mid70
    %readwrite out69
}
namespace dnsp71 {
    %table inp71(a:int8, b:int8, c:int8)
    %table mid71(a:int8, b:int8, c:int8)
    %table out70(a:int8, b:int8, c:int8)
    %readwrite inp71
    %readwrite mid71
    %readwrite out70
}
namespace dnsp72 {
    %table inp72(a:int8, b:int8, c:int8)
    %table mid72(a:int8, b:int8, c:int8)
    %table out71(a:int8, b:int8, c:int8)
    %readwrite inp72
    %readwrite mid72
    %readwrite out71
}
namespace dnsp73 {
    %table inp73(a:int8, b:int8, c:int8)
    %table mid73(a:int8, b:int8, c:int8)
    %table out72(a:int8, b:int8, c:int8)
    %readwrite inp73
    %readwrite mid73
    %readwrite out72
}
namespace dnsp74 {
    %table inp74(a:int8, b:int8, c:int8)
    %table mid74(a:int8, b:int8, c:int8)
    %table out73(a:int8, b:int8, c:int8)
    %readwrite inp74
    %readwrite mid74
    %readwrite out73
}
namespace dnsp75 {
    %table inp75(a:int8, b:int8, c:int8)
    %table mid75(a:int8, b:int8, c:int8)
    %table out74(a:int8, b:int8, c:int8)
    %readwrite inp75
    %readwrite mid75
    %readwrite out74
}
namespace dnsp76 {
    %table inp76(a:int8, b:int8, c:int8)
    %table mid76(a:int8, b:int8, c:int8)
    %table out75(a:int8, b:int8, c:int8)
    %readwrite inp76
    %readwrite mid76
    %readwrite out75
}
namespace dnsp77 {
    %table inp77(a:int8, b:int8, c:int8)
    %table mid77(a:int8, b:int8, c:int8)
    %table out76(a:int8, b:int8, c:int8)
    %readwrite inp77
    %readwrite mid77
    %readwrite out76
}
namespace dnsp78 {
    %table inp78(a:int8, b:int8, c:int8)
    %table mid78(a:int8, b:int8, c:int8)
    %table out77(a:int8, b:int8, c:int8)
    %readwrite inp78
    %readwrite mid78
    %readwrite out77
}
namespace dnsp79 {
    %table inp79(a:int8, b:int8, c:int8)
    %table mid79(a:int8, b:int8, c:int8)
    %table out78(a:int8, b:int8, c:int8)
    %readwrite inp79
    %readwrite mid79
    %readwrite out78
}
namespace dnsp80 {
    %table inp80(a:int8, b:int8, c:int8)
    %table mid80(a:int8, b:int8, c:int8)
    %table out79(a:int8, b:int8, c:int8)
    %readwrite inp80
    %readwrite mid80
    %readwrite out79
}
namespace dnsp81 {
    %table inp81(a:int8, b:int8, c:int8)
    %table mid81(a:int8, b:int8, c:int8)
    %table out80(a:int8, b:int8, c:int8)
    %readwrite inp81
    %readwrite mid81
    %readwrite out80
}
namespace dnsp82 {
    %table inp82(a:int8, b:int8, c:int8)
    %table mid82(a:int8, b:int8, c:int8)
    %table out81(a:int8, b:int8, c:int8)
    %readwrite inp82
    %readwrite mid82
    %readwrite out81
}
namespace dnsp83 {
    %table inp83(a:int8, b:int8, c:int8)
    %table mid83(a:int8, b:int8, c:int8)
    %table out82(a:int8, b:int8, c:int8)
    %readwrite inp83
    %readwrite mid83
    %readwrite out82
}
namespace dnsp84 {
    %table inp84(a:int8, b:int8, c:int8)
    %table mid84(a:int8, b:int8, c:int8)
    %table out83(a:int8, b:int8, c:int8)
    %readwrite inp84
    %readwrite mid84
    %readwrite out83
}
namespace dnsp85 {
    %table inp85(a:int8, b:int8, c:int8)
    %table mid85(a:int8, b:int8, c:int8)
    %table out84(a:int8, b:int8, c:int8)
    %readwrite inp85
    %readwrite mid85
    %readwrite out84
}
namespace dnsp86 {
    %table inp86(a:int8, b:int8, c:int8)
    %table mid86(a:int8, b:int8, c:int8)
    %table out85(a:int8, b:int8, c:int8)
    %readwrite inp86
    %readwrite mid86
    %readwrite out85
}
namespace dnsp87 {
    %table inp87(a:int8, b:int8, c:int8)
    %table mid87(a:int8, b:int8, c:int8)
    %table out86(a:int8, b:int8, c:int8)
    %readwrite inp87
    %readwrite mid87
    %readwrite out86
}
namespace dnsp88 {
    %table inp88(a:int8, b:int8, c:int8)
    %table mid88(a:int8, b:int8, c:int8)
    %table out87(a:int8, b:int8, c:int8)
    %readwrite inp88
    %readwrite mid88
    %readwrite out87
}
namespace dnsp89 {
    %table inp89(a:int8, b:int8, c:int8)
    %table mid89(a:int8, b:int8, c:int8)
    %table out88(a:int8, b:int8, c:int8)
    %readwrite inp89
    %readwrite mid89
    %readwrite out88
}
namespace dnsp90 {
    %table inp90(a:int8, b:int8, c:int8)
    %table mid90(a:int8, b:int8, c:int8)
    %table out89(a:int8, b:int8, c:int8)
    %readwrite inp90
    %readwrite mid90
    %readwrite out89
}
namespace dnsp91 {
    %table inp91(a:int8, b:int8, c:int8)
    %table mid91(a:int8, b:int8, c:int8)
    %table out90(a:int8, b:int8, c:int8)
    %readwrite inp91
    %readwrite mid91
    %readwrite out90
}
namespace dnsp92 {
    %table inp92(a:int8, b:int8, c:int8)
    %table mid92(a:int8, b:int8, c:int8)
    %table out91(a:int8, b:int8, c:int8)
    %readwrite inp92
    %readwrite mid92
    %readwrite out91
}
namespace dnsp93 {
    %table inp93(a:int8, b:int8, c:int8)
    %table mid93(a:int8, b:int8, c:int8)
    %table out92(a:int8, b:int8, c:int8)
    %readwrite inp93
    %readwrite mid93
    %readwrite out92
}
namespace dnsp94 {
    %table inp94(a:int8, b:int8, c:int8)
    %table mid94(a:int8, b:int8, c:int8)
    %table out93(a:int8, b:int8, c:int8)
    %readwrite inp94
    %readwrite mid94
    %readwrite out93
}
namespace dnsp95 {
    %table inp95(a:int8, b:int8, c:int8)
    %table mid95(a:int8, b:int8, c:int8)
    %table out94(a:int8, b:int8, c:int8)
    %readwrite inp95
    %readwrite mid95
    %readwrite out94
}
namespace dnsp96 {
    %table inp96(a:int8, b:int8, c:int8)
    %table mid96(a:int8, b:int8, c:int8)
    %table out95(a:int8, b:int8, c:int8)
    %readwrite inp96
    %readwrite mid96
    %readwrite out95
}
namespace dnsp97 {
    %table inp97(a:int8, b:int8, c:int8)
    %table mid97(a:int8, b:int8, c:int8)
    %table out96(a:int8, b:int8, c:int8)
    %readwrite inp97
    %readwrite mid97
    %readwrite out96
}
namespace dnsp98 {
    %table inp98(a:int8, b:int8, c:int8)
    %table mid98(a:int8, b:int8, c:int8)
    %table out97(a:int8, b:int8, c:int8)
    %readwrite inp98
    %readwrite mid98
    %readwrite out97
}
namespace dnsp99 {
    %table inp99(a:int8, b:int8, c:int8)
    %table mid99(a:int8, b:int8, c:int8)
    %table out98(a:int8, b:int8, c:int8)
    %readwrite inp99
    %readwrite mid99
    %readwrite out98
}
namespace dnsp100 {
    %table inp100(a:int8, b:int8, c:int8)
    %table mid100(a:int8, b:int8, c:int8)
    %table out99(a:int8, b:int8, c:int8)
    %readwrite inp100
    %readwrite mid100
    %readwrite out99
}
namespace dnsp101 {
    %table inp101(a:int8, b:int8, c:int8)
    %table mid101(a:int8, b:int8, c:int8)
    %table out100(a:int8, b:int8, c:int8)
    %readwrite inp101
    %readwrite mid101
    %readwrite out100
}
namespace dnsp102 {
    %table inp102(a:int8, b:int8, c:int8)
    %table mid102(a:int8, b:int8, c:int8)
    %table out101(a:int8, b:int8, c:int8)
    %readwrite inp102
    %readwrite mid102
    %readwrite out101
}
namespace dnsp103 {
    %table inp103(a:int8, b:int8, c:int8)
    %table mid103(a:int8, b:int8, c:int8)
    %table out102(a:int8, b:int8, c:int8)
    %readwrite inp103
    %readwrite mid103
    %readwrite out102
}
namespace dnsp104 {
    %table inp104(a:int8, b:int8, c:int8)
    %table mid104(a:int8, b:int8, c:int8)
    %table out103(a:int8, b:int8, c:int8)
    %readwrite inp104
    %readwrite mid104
    %readwrite out103
}
namespace dnsp105 {
    %table inp105(a:int8, b:int8, c:int8)
    %table mid105(a:int8, b:int8, c:int8)
    %table out104(a:int8, b:int8, c:int8)
    %readwrite inp105
    %readwrite mid105
    %readwrite out104
}
namespace dnsp106 {
    %table inp106(a:int8, b:int8, c:int8)
    %table mid106(a:int8, b:int8, c:int8)
    %table out105(a:int8, b:int8, c:int8)
    %readwrite inp106
    %readwrite mid106
    %readwrite out105
}
namespace dnsp107 {
    %table inp107(a:int8, b:int8, c:int8)
    %table mid107(a:int8, b:int8, c:int8)
    %table out106(a:int8, b:int8, c:int8)
    %readwrite inp107
    %readwrite mid107
    %readwrite out106
}
namespace dnsp108 {
    %table inp108(a:int8, b:int8, c:int8)
    %table mid108(a:int8, b:int8, c:int8)
    %table out107(a:int8, b:int8, c:int8)
    %readwrite inp108
    %readwrite mid108
    %readwrite out107
}
namespace dnsp109 {
    %table inp109(a:int8, b:int8, c:int8)
    %table mid109(a:int8, b:int8, c:int8)
    %table out108(a:int8, b:int8, c:int8)
    %readwrite inp109
    %readwrite mid109
    %readwrite out108
}
namespace dnsp110 {
    %table inp110(a:int8, b:int8, c:int8)
    %table mid110(a:int8, b:int8, c:int8)
    %table out109(a:int8, b:int8, c:int8)
    %readwrite inp110
    %readwrite mid110
    %readwrite out109
}
namespace dnsp111 {
    %table inp111(a:int8, b:int8, c:int8)
    %table mid111(a:int8, b:int8, c:int8)
    %table out110(a:int8, b:int8, c:int8)
    %readwrite inp111
    %readwrite mid111
    %readwrite out110
}
namespace dnsp112 {
    %table inp112(a:int8, b:int8, c:int8)
    %table mid112(a:int8, b:int8, c:int8)
    %table out111(a:int8, b:int8, c:int8)
    %readwrite inp112
    %readwrite mid112
    %readwrite out111
}
namespace dnsp113 {
    %table inp113(a:int8, b:int8, c:int8)
    %table mid113(a:int8, b:int8, c:int8)
    %table out112(a:int8, b:int8, c:int8)
    %readwrite inp113
    %readwrite mid113
    %readwrite out112
}
namespace dnsp114 {
    %table inp114(a:int8, b:int8, c:int8)
    %table mid114(a:int8, b:int8, c:int8)
    %table out113(a:int8, b:int8, c:int8)
    %readwrite inp114
    %readwrite mid114
    %readwrite out113
}
namespace dnsp115 {
    %table inp115(a:int8, b:int8, c:int8)
    %table mid115(a:int8, b:int8, c:int8)
    %table out114(a:int8, b:int8, c:int8)
    %readwrite inp115
    %readwrite mid115
    %readwrite out114
}
namespace dnsp116 {
    %table inp116(a:int8, b:int8, c:int8)
    %table mid116(a:int8, b:int8, c:int8)
    %table out115(a:int8, b:int8, c:int8)
    %readwrite inp116
    %readwrite mid116
    %readwrite out115
}
namespace dnsp117 {
    %table inp117(a:int8, b:int8, c:int8)
    %table mid117(a:int8, b:int8, c:int8)
    %table out116(a:int8, b:int8, c:int8)
    %readwrite inp117
    %readwrite mid117
    %readwrite out116
}
namespace dnsp118 {
    %table inp118(a:int8, b:int8, c:int8)
    %table mid118(a:int8, b:int8, c:int8)
    %table out117(a:int8, b:int8, c:int8)
    %readwrite inp118
    %readwrite mid118
    %readwrite out117
}
namespace dnsp119 {
    %table inp119(a:int8, b:int8, c:int8)
    %table mid119(a:int8, b:int8, c:int8)
    %table out118(a:int8, b:int8, c:int8)
    %readwrite inp119
    %readwrite mid119
    %readwrite out118
}
namespace dnsp120 {
    %table inp120(a:int8, b:int8, c:int8)
    %table mid120(a:int8, b:int8, c:int8)
    %table out119(a:int8, b:int8, c:int8)
    %readwrite inp120
    %readwrite mid120
    %readwrite out119
}
namespace dnsp121 {
    %table inp121(a:int8, b:int8, c:int8)
    %table mid121(a:int8, b:int8, c:int8)
    %table out120(a:int8, b:int8, c:int8)
    %readwrite inp121
    %readwrite mid121
    %readwrite out120
}
namespace dnsp122 {
    %table inp122(a:int8, b:int8, c:int8)
    %table mid122(a:int8, b:int8, c:int8)
    %table out121(a:int8, b:int8, c:int8)
    %readwrite inp122
    %readwrite mid122
    %readwrite out121
}
namespace dnsp123 {
    %table inp123(a:int8, b:int8, c:int8)
    %table mid123(a:int8, b:int8, c:int8)
    %table out122(a:int8, b:int8, c:int8)
    %readwrite inp123
    %readwrite mid123
    %readwrite out122
}
namespace dnsp124 {
    %table inp124(a:int8, b:int8, c:int8)
    %table mid124(a:int8, b:int8, c:int8)
    %table out123(a:int8, b:int8, c:int8)
    %readwrite inp124
    %readwrite mid124
    %readwrite out123
}
namespace dnsp125 {
    %table inp125(a:int8, b:int8, c:int8)
    %table mid125(a:int8, b:int8, c:int8)
    %table out124(a:int8, b:int8, c:int8)
    %readwrite inp125
    %readwrite mid125
    %readwrite out124
}
namespace dnsp126 {
    %table inp126(a:int8, b:int8, c:int8)
    %table mid126(a:int8, b:int8, c:int8)
    %table out125(a:int8, b:int8, c:int8)
    %readwrite inp126
    %readwrite mid126
    %readwrite out125
}
namespace dnsp127 {
    %table inp127(a:int8, b:int8, c:int8)
    %table mid127(a:int8, b:int8, c:int8)
    %table out126(a:int8, b:int8, c:int8)
    %readwrite inp127
    %readwrite mid127
    %readwrite out126
}
namespace dnsp128 {
    %table inp128(a:int8, b:int8, c:int8)
    %table mid128(a:int8, b:int8, c:int8)
    %table out127(a:int8, b:int8, c:int8)
    %readwrite inp128
    %readwrite mid128
    %readwrite out127
}
namespace dnsp129 {
    %table inp129(a:int8, b:int8, c:int8)
    %table mid129(a:int8, b:int8, c:int8)
    %table out128(a:int8, b:int8, c:int8)
    %readwrite inp129
    %readwrite mid129
    %readwrite out128
}
namespace dnsp130 {
    %table inp130(a:int8, b:int8, c:int8)
    %table mid130(a:int8, b:int8, c:int8)
    %table out129(a:int8, b:int8, c:int8)
    %readwrite inp130
    %readwrite mid130
    %readwrite out129
}
namespace dnsp131 {
    %table inp131(a:int8, b:int8, c:int8)
    %table mid131(a:int8, b:int8, c:int8)
    %table out130(a:int8, b:int8, c:int8)
    %readwrite inp131
    %readwrite mid131
    %readwrite out130
}
namespace dnsp132 {
    %table inp132(a:int8, b:int8, c:int8)
    %table mid132(a:int8, b:int8, c:int8)
    %table out131(a:int8, b:int8, c:int8)
    %readwrite inp132
    %readwrite mid132
    %readwrite out131
}
namespace dnsp133 {
    %table inp133(a:int8, b:int8, c:int8)
    %table mid133(a:int8, b:int8, c:int8)
    %table out132(a:int8, b:int8, c:int8)
    %readwrite inp133
    %readwrite mid133
    %readwrite out132
}
namespace dnsp134 {
    %table inp134(a:int8, b:int8, c:int8)
    %table mid134(a:int8, b:int8, c:int8)
    %table out133(a:int8, b:int8, c:int8)
    %readwrite inp134
    %readwrite mid134
    %readwrite out133
}
namespace dnsp135 {
    %table inp135(a:int8, b:int8, c:int8)
    %table mid135(a:int8, b:int8, c:int8)
    %table out134(a:int8, b:int8, c:int8)
    %readwrite inp135
    %readwrite mid135
    %readwrite out134
}
namespace dnsp136 {
    %table inp136(a:int8, b:int8, c:int8)
    %table mid136(a:int8, b:int8, c:int8)
    %table out135(a:int8, b:int8, c:int8)
    %readwrite inp136
    %readwrite mid136
    %readwrite out135
}
namespace dnsp137 {
    %table inp137(a:int8, b:int8, c:int8)
    %table mid137(a:int8, b:int8, c:int8)
    %table out136(a:int8, b:int8, c:int8)
    %readwrite inp137
    %readwrite mid137
    %readwrite out136
}
namespace dnsp138 {
    %table inp138(a:int8, b:int8, c:int8)
    %table mid138(a:int8, b:int8, c:int8)
    %table out137(a:int8, b:int8, c:int8)
    %readwrite inp138
    %readwrite mid138
    %readwrite out137
}
namespace dnsp139 {
    %table inp139(a:int8, b:int8, c:int8)
    %table mid139(a:int8, b:int8, c:int8)
    %table out138(a:int8, b:int8, c:int8)
    %readwrite inp139
    %readwrite mid139
    %readwrite out138
}
namespace dnsp140 {
    %table inp140(a:int8, b:int8, c:int8)
    %table mid140(a:int8, b:int8, c:int8)
    %table out139(a:int8, b:int8, c:int8)
    %readwrite inp140
    %readwrite mid140
    %readwrite out139
}
namespace dnsp141 {
    %table inp141(a:int8, b:int8, c:int8)
    %table mid141(a:int8, b:int8, c:int8)
    %table out140(a:int8, b:int8, c:int8)
    %readwrite inp141
    %readwrite mid141
    %readwrite out140
}
namespace dnsp142 {
    %table inp142(a:int8, b:int8, c:int8)
    %table mid142(a:int8, b:int8, c:int8)
    %table out141(a:int8, b:int8, c:int8)
    %readwrite inp142
    %readwrite mid142
    %readwrite out141
}
namespace dnsp143 {
    %table inp143(a:int8, b:int8, c:int8)
    %table mid143(a:int8, b:int8, c:int8)
    %table out142(a:int8, b:int8, c:int8)
    %readwrite inp143
    %readwrite mid143
    %readwrite out142
}
namespace dnsp144 {
    %table inp144(a:int8, b:int8, c:int8)
    %table mid144(a:int8, b:int8, c:int8)
    %table out143(a:int8, b:int8, c:int8)
    %readwrite inp144
    %readwrite mid144
    %readwrite out143
}
namespace dnsp145 {
    %table inp145(a:int8, b:int8, c:int8)
    %table mid145(a:int8, b:int8, c:int8)
    %table out144(a:int8, b:int8, c:int8)
    %readwrite inp145
    %readwrite mid145
    %readwrite out144
}
namespace dnsp146 {
    %table inp146(a:int8, b:int8, c:int8)
    %table mid146(a:int8, b:int8, c:int8)
    %table out145(a:int8, b:int8, c:int8)
    %readwrite inp146
    %readwrite mid146
    %readwrite out145
}
namespace dnsp147 {
    %table inp147(a:int8, b:int8, c:int8)
    %table mid147(a:int8, b:int8, c:int8)
    %table out146(a:int8, b:int8, c:int8)
    %readwrite inp147
    %readwrite mid147
    %readwrite out146
}
namespace dnsp148 {
    %table inp148(a:int8, b:int8, c:int8)
    %table mid148(a:int8, b:int8, c:int8)
    %table out147(a:int8, b:int8, c:int8)
    %readwrite inp148
    %readwrite mid148
    %readwrite out147
}
namespace dnsp149 {
    %table inp149(a:int8, b:int8, c:int8)
    %table mid149(a:int8, b:int8, c:int8)
    %table out148(a:int8, b:int8, c:int8)
    %readwrite inp149
    %readwrite mid149
    %readwrite out148
}
namespace dnsp150 {
    %table inp150(a:int8, b:int8, c:int8)
    %table mid150(a:int8, b:int8, c:int8)
    %table out149(a:int8, b:int8, c:int8)
    %readwrite inp150
    %readwrite mid150
    %readwrite out149
}
namespace dnsp151 {
    %table inp151(a:int8, b:int8, c:int8)
    %table mid151(a:int8, b:int8, c:int8)
    %table out150(a:int8, b:int8, c:int8)
    %readwrite inp151
    %readwrite mid151
    %readwrite out150
}
namespace dnsp152 {
    %table inp152(a:int8, b:int8, c:int8)
    %table mid152(a:int8, b:int8, c:int8)
    %table out151(a:int8, b:int8, c:int8)
    %readwrite inp152
    %readwrite mid152
    %readwrite out151
}
namespace dnsp153 {
    %table inp153(a:int8, b:int8, c:int8)
    %table mid153(a:int8, b:int8, c:int8)
    %table out152(a:int8, b:int8, c:int8)
    %readwrite inp153
    %readwrite mid153
    %readwrite out152
}
namespace dnsp154 {
    %table inp154(a:int8, b:int8, c:int8)
    %table mid154(a:int8, b:int8, c:int8)
    %table out153(a:int8, b:int8, c:int8)
    %readwrite inp154
    %readwrite mid154
    %readwrite out153
}
namespace dnsp155 {
    %table inp155(a:int8, b:int8, c:int8)
    %table mid155(a:int8, b:int8, c:int8)
    %table out154(a:int8, b:int8, c:int8)
    %readwrite inp155
    %readwrite mid155
    %readwrite out154
}
namespace dnsp156 {
    %table inp156(a:int8, b:int8, c:int8)
    %table mid156(a:int8, b:int8, c:int8)
    %table out155(a:int8, b:int8, c:int8)
    %readwrite inp156
    %readwrite mid156
    %readwrite out155
}
namespace dnsp157 {
    %table inp157(a:int8, b:int8, c:int8)
    %table mid157(a:int8, b:int8, c:int8)
    %table out156(a:int8, b:int8, c:int8)
    %readwrite inp157
    %readwrite mid157
    %readwrite out156
}
namespace dnsp158 {
    %table inp158(a:int8, b:int8, c:int8)
    %table mid158(a:int8, b:int8, c:int8)
    %table out157(a:int8, b:int8, c:int8)
    %readwrite inp158
    %readwrite mid158
    %readwrite out157
}
namespace dnsp159 {
    %table inp159(a:int8, b:int8, c:int8)
    %table mid159(a:int8, b:int8, c:int8)
    %table out158(a:int8, b:int8, c:int8)
    %readwrite inp159
    %readwrite mid159
    %readwrite out158
}
namespace dnsp160 {
    %table inp160(a:int8, b:int8, c:int8)
    %table mid160(a:int8, b:int8, c:int8)
    %table out159(a:int8, b:int8, c:int8)
    %readwrite inp160
    %readwrite mid160
    %readwrite out159
}
namespace dnsp161 {
    %table inp161(a:int8, b:int8, c:int8)
    %table mid161(a:int8, b:int8, c:int8)
    %table out160(a:int8, b:int8, c:int8)
    %readwrite inp161
    %readwrite mid161
    %readwrite out160
}
namespace dnsp162 {
    %table inp162(a:int8, b:int8, c:int8)
    %table mid162(a:int8, b:int8, c:int8)
    %table out161(a:int8, b:int8, c:int8)
    %readwrite inp162
    %readwrite mid162
    %readwrite out161
}
namespace dnsp163 {
    %table inp163(a:int8, b:int8, c:int8)
    %table mid163(a:int8, b:int8, c:int8)
    %table out162(a:int8, b:int8, c:int8)
    %readwrite inp163
    %readwrite mid163
    %readwrite out162
}
namespace dnsp164 {
    %table inp164(a:int8, b:int8, c:int8)
    %table mid164(a:int8, b:int8, c:int8)
    %table out163(a:int8, b:int8, c:int8)
    %readwrite inp164
    %readwrite mid164
    %readwrite out163
}
namespace dnsp165 {
    %table inp165(a:int8, b:int8, c:int8)
    %table mid165(a:int8, b:int8, c:int8)
    %table out164(a:int8, b:int8, c:int8)
    %readwrite inp165
    %readwrite mid165
    %readwrite out164
}
namespace dnsp166 {
    %table inp166(a:int8, b:int8, c:int8)
    %table mid166(a:int8, b:int8, c:int8)
    %table out165(a:int8, b:int8, c:int8)
    %readwrite inp166
    %readwrite mid166
    %readwrite out165
}
namespace dnsp167 {
    %table inp167(a:int8, b:int8, c:int8)
    %table mid167(a:int8, b:int8, c:int8)
    %table out166(a:int8, b:int8, c:int8)
    %readwrite inp167
    %readwrite mid167
    %readwrite out166
}
namespace dnsp168 {
    %table inp168(a:int8, b:int8, c:int8)
    %table mid168(a:int8, b:int8, c:int8)
    %table out167(a:int8, b:int8, c:int8)
    %readwrite inp168
    %readwrite mid168
    %readwrite out167
}
namespace dnsp169 {
    %table inp169(a:int8, b:int8, c:int8)
    %table mid169(a:int8, b:int8, c:int8)
    %table out168(a:int8, b:int8, c:int8)
    %readwrite inp169
    %readwrite mid169
    %readwrite out168
}
namespace dnsp170 {
    %table inp170(a:int8, b:int8, c:int8)
    %table mid170(a:int8, b:int8, c:int8)
    %table out169(a:int8, b:int8, c:int8)
    %readwrite inp170
    %readwrite mid170
    %readwrite out169
}
namespace dnsp171 {
    %table inp171(a:int8, b:int8, c:int8)
    %table mid171(a:int8, b:int8, c:int8)
    %table out170(a:int8, b:int8, c:int8)
    %readwrite inp171
    %readwrite mid171
    %readwrite out170
}
namespace dnsp172 {
    %table inp172(a:int8, b:int8, c:int8)
    %table mid172(a:int8, b:int8, c:int8)
    %table out171(a:int8, b:int8, c:int8)
    %readwrite inp172
    %readwrite mid172
    %readwrite out171
}
namespace dnsp173 {
    %table inp173(a:int8, b:int8, c:int8)
    %table mid173(a:int8, b:int8, c:int8)
    %table out172(a:int8, b:int8, c:int8)
    %readwrite inp173
    %readwrite mid173
    %readwrite out172
}
namespace dnsp174 {
    %table inp174(a:int8, b:int8, c:int8)
    %table mid174(a:int8, b:int8, c:int8)
    %table out173(a:int8, b:int8, c:int8)
    %readwrite inp174
    %readwrite mid174
    %readwrite out173
}
namespace dnsp175 {
    %table inp175(a:int8, b:int8, c:int8)
    %table mid175(a:int8, b:int8, c:int8)
    %table out174(a:int8, b:int8, c:int8)
    %readwrite inp175
    %readwrite mid175
    %readwrite out174
}
namespace dnsp176 {
    %table inp176(a:int8, b:int8, c:int8)
    %table mid176(a:int8, b:int8, c:int8)
    %table out175(a:int8, b:int8, c:int8)
    %readwrite inp176
    %readwrite mid176
    %readwrite out175
}
namespace dnsp177 {
    %table inp177(a:int8, b:int8, c:int8)
    %table mid177(a:int8, b:int8, c:int8)
    %table out176(a:int8, b:int8, c:int8)
    %readwrite inp177
    %readwrite mid177
    %readwrite out176
}
namespace dnsp178 {
    %table inp178(a:int8, b:int8, c:int8)
    %table mid178(a:int8, b:int8, c:int8)
    %table out177(a:int8, b:int8, c:int8)
    %readwrite inp178
    %readwrite mid178
    %readwrite out177
}
namespace dnsp179 {
    %table inp179(a:int8, b:int8, c:int8)
    %table mid179(a:int8, b:int8, c:int8)
    %table out178(a:int8, b:int8, c:int8)
    %readwrite inp179
    %readwrite mid179
    %readwrite out178
}
namespace dnsp180 {
    %table inp180(a:int8, b:int8, c:int8)
    %table mid180(a:int8, b:int8, c:int8)
    %table out179(a:int8, b:int8, c:int8)
    %readwrite inp180
    %readwrite mid180
    %readwrite out179
}
namespace dnsp181 {
    %table inp181(a:int8, b:int8, c:int8)
    %table mid181(a:int8, b:int8, c:int8)
    %table out180(a:int8, b:int8, c:int8)
    %readwrite inp181
    %readwrite mid181
    %readwrite out180
}
namespace dnsp182 {
    %table inp182(a:int8, b:int8, c:int8)
    %table mid182(a:int8, b:int8, c:int8)
    %table out181(a:int8, b:int8, c:int8)
    %readwrite inp182
    %readwrite mid182
    %readwrite out181
}
namespace dnsp183 {
    %table inp183(a:int8, b:int8, c:int8)
    %table mid183(a:int8, b:int8, c:int8)
    %table out182(a:int8, b:int8, c:int8)
    %readwrite inp183
    %readwrite mid183
    %readwrite out182
}
namespace dnsp184 {
    %table inp184(a:int8, b:int8, c:int8)
    %table mid184(a:int8, b:int8, c:int8)
    %table out183(a:int8, b:int8, c:int8)
    %readwrite inp184
    %readwrite mid184
    %readwrite out183
}
namespace dnsp185 {
    %table inp185(a:int8, b:int8, c:int8)
    %table mid185(a:int8, b:int8, c:int8)
    %table out184(a:int8, b:int8, c:int8)
    %readwrite inp185
    %readwrite mid185
    %readwrite out184
}
namespace dnsp186 {
    %table inp186(a:int8, b:int8, c:int8)
    %table mid186(a:int8, b:int8, c:int8)
    %table out185(a:int8, b:int8, c:int8)
    %readwrite inp186
    %readwrite mid186
    %readwrite out185
}
namespace dnsp187 {
    %table inp187(a:int8, b:int8, c:int8)
    %table mid187(a:int8, b:int8, c:int8)
    %table out186(a:int8, b:int8, c:int8)
    %readwrite inp187
    %readwrite mid187
    %readwrite out186
}
namespace dnsp188 {
    %table inp188(a:int8, b:int8, c:int8)
    %table mid188(a:int8, b:int8, c:int8)
    %table out187(a:int8, b:int8, c:int8)
    %readwrite inp188
    %readwrite mid188
    %readwrite out187
}
namespace dnsp189 {
    %table inp189(a:int8, b:int8, c:int8)
    %table mid189(a:int8, b:int8, c:int8)
    %table out188(a:int8, b:int8, c:int8)
    %readwrite inp189
    %readwrite mid189
    %readwrite out188
}
namespace dnsp190 {
    %table inp190(a:int8, b:int8, c:int8)
    %table mid190(a:int8, b:int8, c:int8)
    %table out189(a:int8, b:int8, c:int8)
    %readwrite inp190
    %readwrite mid190
    %readwrite out189
}
namespace dnsp191 {
    %table inp191(a:int8, b:int8, c:int8)
    %table mid191(a:int8, b:int8, c:int8)
    %table out190(a:int8, b:int8, c:int8)
    %readwrite inp191
    %readwrite mid191
    %readwrite out190
}
namespace dnsp192 {
    %table inp192(a:int8, b:int8, c:int8)
    %table mid192(a:int8, b:int8, c:int8)
    %table out191(a:int8, b:int8, c:int8)
    %readwrite inp192
    %readwrite mid192
    %readwrite out191
}
namespace dnsp193 {
    %table inp193(a:int8, b:int8, c:int8)
    %table mid193(a:int8, b:int8, c:int8)
    %table out192(a:int8, b:int8, c:int8)
    %readwrite inp193
    %readwrite mid193
    %readwrite out192
}
namespace dnsp194 {
    %table inp194(a:int8, b:int8, c:int8)
    %table mid194(a:int8, b:int8, c:int8)
    %table out193(a:int8, b:int8, c:int8)
    %readwrite inp194
    %readwrite mid194
    %readwrite out193
}
namespace dnsp195 {
    %table inp195(a:int8, b:int8, c:int8)
    %table mid195(a:int8, b:int8, c:int8)
    %table out194(a:int8, b:int8, c:int8)
    %readwrite inp195
    %readwrite mid195
    %readwrite out194
}
namespace dnsp196 {
    %table inp196(a:int8, b:int8, c:int8)
    %table mid196(a:int8, b:int8, c:int8)
    %table out195(a:int8, b:int8, c:int8)
    %readwrite inp196
    %readwrite mid196
    %readwrite out195
}
namespace dnsp197 {
    %table inp197(a:int8, b:int8, c:int8)
    %table mid197(a:int8, b:int8, c:int8)
    %table out196(a:int8, b:int8, c:int8)
    %readwrite inp197
    %readwrite mid197
    %readwrite out196
}
namespace dnsp198 {
    %table inp198(a:int8, b:int8, c:int8)
    %table mid198(a:int8, b:int8, c:int8)
    %table out197(a:int8, b:int8, c:int8)
    %readwrite inp198
    %readwrite mid198
    %readwrite out197
}
namespace dnsp199 {
    %table inp199(a:int8, b:int8, c:int8)
    %table mid199(a:int8, b:int8, c:int8)
    %table out198(a:int8, b:int8, c:int8)
    %readwrite inp199
    %readwrite mid199
    %readwrite out198
}
namespace dnsp200 {
    %table inp200(a:int8, b:int8, c:int8)
    %table mid200(a:int8, b:int8, c:int8)
    %table out199(a:int8, b:int8, c:int8)
    %readwrite inp200
    %readwrite mid200
    %readwrite out199
}
namespace dnsp201 {
    %table inp201(a:int8, b:int8, c:int8)
    %table mid201(a:int8, b:int8, c:int8)
    %table out200(a:int8, b:int8, c:int8)
    %readwrite inp201
    %readwrite mid201
    %readwrite out200
}
namespace dnsp202 {
    %table inp202(a:int8, b:int8, c:int8)
    %table mid202(a:int8, b:int8, c:int8)
    %table out201(a:int8, b:int8, c:int8)
    %readwrite inp202
    %readwrite mid202
    %readwrite out201
}
namespace dnsp203 {
    %table inp203(a:int8, b:int8, c:int8)
    %table mid203(a:int8, b:int8, c:int8)
    %table out202(a:int8, b:int8, c:int8)
    %readwrite inp203
    %readwrite mid203
    %readwrite out202
}
namespace dnsp204 {
    %table inp204(a:int8, b:int8, c:int8)
    %table mid204(a:int8, b:int8, c:int8)
    %table out203(a:int8, b:int8, c:int8)
    %readwrite inp204
    %readwrite mid204
    %readwrite out203
}
namespace dnsp205 {
    %table inp205(a:int8, b:int8, c:int8)
    %table mid205(a:int8, b:int8, c:int8)
    %table out204(a:int8, b:int8, c:int8)
    %readwrite inp205
    %readwrite mid205
    %readwrite out204
}
namespace dnsp206 {
    %table inp206(a:int8, b:int8, c:int8)
    %table mid206(a:int8, b:int8, c:int8)
    %table out205(a:int8, b:int8, c:int8)
    %readwrite inp206
    %readwrite mid206
    %readwrite out205
}
namespace dnsp207 {
    %table inp207(a:int8, b:int8, c:int8)
    %table mid207(a:int8, b:int8, c:int8)
    %table out206(a:int8, b:int8, c:int8)
    %readwrite inp207
    %readwrite mid207
    %readwrite out206
}
namespace dnsp208 {
    %table inp208(a:int8, b:int8, c:int8)
    %table mid208(a:int8, b:int8, c:int8)
    %table out207(a:int8, b:int8, c:int8)
    %readwrite inp208
    %readwrite mid208
    %readwrite out207
}
namespace dnsp209 {
    %table inp209(a:int8, b:int8, c:int8)
    %table mid209(a:int8, b:int8, c:int8)
    %table out208(a:int8, b:int8, c:int8)
    %readwrite inp209
    %readwrite mid209
    %readwrite out208
}
namespace dnsp210 {
    %table inp210(a:int8, b:int8, c:int8)
    %table mid210(a:int8, b:int8, c:int8)
    %table out209(a:int8, b:int8, c:int8)
    %readwrite inp210
    %readwrite mid210
    %readwrite out209
}
namespace dnsp211 {
    %table inp211(a:int8, b:int8, c:int8)
    %table mid211(a:int8, b:int8, c:int8)
    %table out210(a:int8, b:int8, c:int8)
    %readwrite inp211
    %readwrite mid211
    %readwrite out210
}
namespace dnsp212 {
    %table inp212(a:int8, b:int8, c:int8)
    %table mid212(a:int8, b:int8, c:int8)
    %table out211(a:int8, b:int8, c:int8)
    %readwrite inp212
    %readwrite mid212
    %readwrite out211
}
namespace dnsp213 {
    %table inp213(a:int8, b:int8, c:int8)
    %table mid213(a:int8, b:int8, c:int8)
    %table out212(a:int8, b:int8, c:int8)
    %readwrite inp213
    %readwrite mid213
    %readwrite out212
}
namespace dnsp214 {
    %table inp214(a:int8, b:int8, c:int8)
    %table mid214(a:int8, b:int8, c:int8)
    %table out213(a:int8, b:int8, c:int8)
    %readwrite inp214
    %readwrite mid214
    %readwrite out213
}
namespace dnsp215 {
    %table inp215(a:int8, b:int8, c:int8)
    %table mid215(a:int8, b:int8, c:int8)
    %table out214(a:int8, b:int8, c:int8)
    %readwrite inp215
    %readwrite mid215
    %readwrite out214
}
namespace dnsp216 {
    %table inp216(a:int8, b:int8, c:int8)
    %table mid216(a:int8, b:int8, c:int8)
    %table out215(a:int8, b:int8, c:int8)
    %readwrite inp216
    %readwrite mid216
    %readwrite out215
}
namespace dnsp217 {
    %table inp217(a:int8, b:int8, c:int8)
    %table mid217(a:int8, b:int8, c:int8)
    %table out216(a:int8, b:int8, c:int8)
    %readwrite inp217
    %readwrite mid217
    %readwrite out216
}
namespace dnsp218 {
    %table inp218(a:int8, b:int8, c:int8)
    %table mid218(a:int8, b:int8, c:int8)
    %table out217(a:int8, b:int8, c:int8)
    %readwrite inp218
    %readwrite mid218
    %readwrite out217
}
namespace dnsp219 {
    %table inp219(a:int8, b:int8, c:int8)
    %table mid219(a:int8, b:int8, c:int8)
    %table out218(a:int8, b:int8, c:int8)
    %readwrite inp219
    %readwrite mid219
    %readwrite out218
}
namespace dnsp220 {
    %table inp220(a:int8, b:int8, c:int8)
    %table mid220(a:int8, b:int8, c:int8)
    %table out219(a:int8, b:int8, c:int8)
    %readwrite inp220
    %readwrite mid220
    %readwrite out219
}
namespace dnsp221 {
    %table inp221(a:int8, b:int8, c:int8)
    %table mid221(a:int8, b:int8, c:int8)
    %table out220(a:int8, b:int8, c:int8)
    %readwrite inp221
    %readwrite mid221
    %readwrite out220
}
namespace dnsp222 {
    %table inp222(a:int8, b:int8, c:int8)
    %table mid222(a:int8, b:int8, c:int8)
    %table out221(a:int8, b:int8, c:int8)
    %readwrite inp222
    %readwrite mid222
    %readwrite out221
}
namespace dnsp223 {
    %table inp223(a:int8, b:int8, c:int8)
    %table mid223(a:int8, b:int8, c:int8)
    %table out222(a:int8, b:int8, c:int8)
    %readwrite inp223
    %readwrite mid223
    %readwrite out222
}
namespace dnsp224 {
    %table inp224(a:int8, b:int8, c:int8)
    %table mid224(a:int8, b:int8, c:int8)
    %table out223(a:int8, b:int8, c:int8)
    %readwrite inp224
    %readwrite mid224
    %readwrite out223
}
namespace dnsp225 {
    %table inp225(a:int8, b:int8, c:int8)
    %table mid225(a:int8, b:int8, c:int8)
    %table out224(a:int8, b:int8, c:int8)
    %readwrite inp225
    %readwrite mid225
    %readwrite out224
}
namespace dnsp226 {
    %table inp226(a:int8, b:int8, c:int8)
    %table mid226(a:int8, b:int8, c:int8)
    %table out225(a:int8, b:int8, c:int8)
    %readwrite inp226
    %readwrite mid226
    %readwrite out225
}
namespace dnsp227 {
    %table inp227(a:int8, b:int8, c:int8)
    %table mid227(a:int8, b:int8, c:int8)
    %table out226(a:int8, b:int8, c:int8)
    %readwrite inp227
    %readwrite mid227
    %readwrite out226
}
namespace dnsp228 {
    %table inp228(a:int8, b:int8, c:int8)
    %table mid228(a:int8, b:int8, c:int8)
    %table out227(a:int8, b:int8, c:int8)
    %readwrite inp228
    %readwrite mid228
    %readwrite out227
}
namespace dnsp229 {
    %table inp229(a:int8, b:int8, c:int8)
    %table mid229(a:int8, b:int8, c:int8)
    %table out228(a:int8, b:int8, c:int8)
    %readwrite inp229
    %readwrite mid229
    %readwrite out228
}
namespace dnsp230 {
    %table inp230(a:int8, b:int8, c:int8)
    %table mid230(a:int8, b:int8, c:int8)
    %table out229(a:int8, b:int8, c:int8)
    %readwrite inp230
    %readwrite mid230
    %readwrite out229
}
namespace dnsp231 {
    %table inp231(a:int8, b:int8, c:int8)
    %table mid231(a:int8, b:int8, c:int8)
    %table out230(a:int8, b:int8, c:int8)
    %readwrite inp231
    %readwrite mid231
    %readwrite out230
}
namespace dnsp232 {
    %table inp232(a:int8, b:int8, c:int8)
    %table mid232(a:int8, b:int8, c:int8)
    %table out231(a:int8, b:int8, c:int8)
    %readwrite inp232
    %readwrite mid232
    %readwrite out231
}
namespace dnsp233 {
    %table inp233(a:int8, b:int8, c:int8)
    %table mid233(a:int8, b:int8, c:int8)
    %table out232(a:int8, b:int8, c:int8)
    %readwrite inp233
    %readwrite mid233
    %readwrite out232
}
namespace dnsp234 {
    %table inp234(a:int8, b:int8, c:int8)
    %table mid234(a:int8, b:int8, c:int8)
    %table out233(a:int8, b:int8, c:int8)
    %readwrite inp234
    %readwrite mid234
    %readwrite out233
}
namespace dnsp235 {
    %table inp235(a:int8, b:int8, c:int8)
    %table mid235(a:int8, b:int8, c:int8)
    %table out234(a:int8, b:int8, c:int8)
    %readwrite inp235
    %readwrite mid235
    %readwrite out234
}
namespace dnsp236 {
    %table inp236(a:int8, b:int8, c:int8)
    %table mid236(a:int8, b:int8, c:int8)
    %table out235(a:int8, b:int8, c:int8)
    %readwrite inp236
    %readwrite mid236
    %readwrite out235
}
namespace dnsp237 {
    %table inp237(a:int8, b:int8, c:int8)
    %table mid237(a:int8, b:int8, c:int8)
    %table out236(a:int8, b:int8, c:int8)
    %readwrite inp237
    %readwrite mid237
    %readwrite out236
}
namespace dnsp238 {
    %table inp238(a:int8, b:int8, c:int8)
    %table mid238(a:int8, b:int8, c:int8)
    %table out237(a:int8, b:int8, c:int8)
    %readwrite inp238
    %readwrite mid238
    %readwrite out237
}
namespace dnsp239 {
    %table inp239(a:int8, b:int8, c:int8)
    %table mid239(a:int8, b:int8, c:int8)
    %table out238(a:int8, b:int8, c:int8)
    %readwrite inp239
    %readwrite mid239
    %readwrite out238
}
namespace dnsp240 {
    %table inp240(a:int8, b:int8, c:int8)
    %table mid240(a:int8, b:int8, c:int8)
    %table out239(a:int8, b:int8, c:int8)
    %readwrite inp240
    %readwrite mid240
    %readwrite out239
}
namespace dnsp241 {
    %table inp241(a:int8, b:int8, c:int8)
    %table mid241(a:int8, b:int8, c:int8)
    %table out240(a:int8, b:int8, c:int8)
    %readwrite inp241
    %readwrite mid241
    %readwrite out240
}
namespace dnsp242 {
    %table inp242(a:int8, b:int8, c:int8)
    %table mid242(a:int8, b:int8, c:int8)
    %table out241(a:int8, b:int8, c:int8)
    %readwrite inp242
    %readwrite mid242
    %readwrite out241
}
namespace dnsp243 {
    %table inp243(a:int8, b:int8, c:int8)
    %table mid243(a:int8, b:int8, c:int8)
    %table out242(a:int8, b:int8, c:int8)
    %readwrite inp243
    %readwrite mid243
    %readwrite out242
}
namespace dnsp244 {
    %table inp244(a:int8, b:int8, c:int8)
    %table mid244(a:int8, b:int8, c:int8)
    %table out243(a:int8, b:int8, c:int8)
    %readwrite inp244
    %readwrite mid244
    %readwrite out243
}
namespace dnsp245 {
    %table inp245(a:int8, b:int8, c:int8)
    %table mid245(a:int8, b:int8, c:int8)
    %table out244(a:int8, b:int8, c:int8)
    %readwrite inp245
    %readwrite mid245
    %readwrite out244
}
namespace dnsp246 {
    %table inp246(a:int8, b:int8, c:int8)
    %table mid246(a:int8, b:int8, c:int8)
    %table out245(a:int8, b:int8, c:int8)
    %readwrite inp246
    %readwrite mid246
    %readwrite out245
}
namespace dnsp247 {
    %table inp247(a:int8, b:int8, c:int8)
    %table mid247(a:int8, b:int8, c:int8)
    %table out246(a:int8, b:int8, c:int8)
    %readwrite inp247
    %readwrite mid247
    %readwrite out246
}
namespace dnsp248 {
    %table inp248(a:int8, b:int8, c:int8)
    %table mid248(a:int8, b:int8, c:int8)
    %table out247(a:int8, b:int8, c:int8)
    %readwrite inp248
    %readwrite mid248
    %readwrite out247
}
namespace dnsp249 {
    %table inp249(a:int8, b:int8, c:int8)
    %table mid249(a:int8, b:int8, c:int8)
    %table out248(a:int8, b:int8, c:int8)
    %readwrite inp249
    %readwrite mid249
    %readwrite out248
}
namespace dnsp250 {
    %table inp250(a:int8, b:int8, c:int8)
    %table mid250(a:int8, b:int8, c:int8)
    %table out249(a:int8, b:int8, c:int8)
    %readwrite inp250
    %readwrite mid250
    %readwrite out249
}
namespace dnsp251 {
    %table inp251(a:int8, b:int8, c:int8)
    %table mid251(a:int8, b:int8, c:int8)
    %table out250(a:int8, b:int8, c:int8)
    %readwrite inp251
    %readwrite mid251
    %readwrite out250
}
namespace dnsp252 {
    %table inp252(a:int8, b:int8, c:int8)
    %table mid252(a:int8, b:int8, c:int8)
    %table out251(a:int8, b:int8, c:int8)
    %readwrite inp252
    %readwrite mid252
    %readwrite out251
}
namespace dnsp253 {
    %table inp253(a:int8, b:int8, c:int8)
    %table mid253(a:int8, b:int8, c:int8)
    %table out252(a:int8, b:int8, c:int8)
    %readwrite inp253
    %readwrite mid253
    %readwrite out252
}
namespace dnsp254 {
    %table inp254(a:int8, b:int8, c:int8)
    %table mid254(a:int8, b:int8, c:int8)
    %table out253(a:int8, b:int8, c:int8)
    %readwrite inp254
    %readwrite mid254
    %readwrite out253
}
namespace dnsp255 {
    %table inp255(a:int8, b:int8, c:int8)
    %table mid255(a:int8, b:int8, c:int8)
    %table out254(a:int8, b:int8, c:int8)
    %readwrite inp255
    %readwrite mid255
    %readwrite out254
}
mid0(a, b, c) :- inp0(a, b, c).
dnsp1.out0(a, b, c) :- mid0(a, b, c).
dnsp1.mid1(a, b, c) :- dnsp1.inp1(a, b, c).
dnsp2.out1(a, b, c) :- dnsp1.mid1(a, b, c).
dnsp2.mid2(a, b, c) :- dnsp2.inp2(a, b, c).
dnsp3.out2(a, b, c) :- dnsp2.mid2(a, b, c).
dnsp3.mid3(a, b, c) :- dnsp3.inp3(a, b, c).
dnsp4.out3(a, b, c) :- dnsp3.mid3(a, b, c).
dnsp4.mid4(a, b, c) :- dnsp4.inp4(a, b, c).
dnsp5.out4(a, b, c) :- dnsp4.mid4(a, b, c).
dnsp5.mid5(a, b, c) :- dnsp5.inp5(a, b, c).
dnsp6.out5(a, b, c) :- dnsp5.mid5(a, b, c).
dnsp6.mid6(a, b, c) :- dnsp6.inp6(a, b, c).
dnsp7.out6(a, b, c) :- dnsp6.mid6(a, b, c).
dnsp7.mid7(a, b, c) :- dnsp7.inp7(a, b, c).
dnsp8.out7(a, b, c) :- dnsp7.mid7(a, b, c).
dnsp8.mid8(a, b, c) :- dnsp8.inp8(a, b, c).
dnsp9.out8(a, b, c) :- dnsp8.mid8(a, b, c).
dnsp9.mid9(a, b, c) :- dnsp9.inp9(a, b, c).
dnsp10.out9(a, b, c) :- dnsp9.mid9(a, b, c).
dnsp10.mid10(a, b, c) :- dnsp10.inp10(a, b, c).
dnsp11.out10(a, b, c) :- dnsp10.mid10(a, b, c).
dnsp11.mid11(a, b, c) :- dnsp11.inp11(a, b, c).
dnsp12.out11(a, b, c) :- dnsp11.mid11(a, b, c).
dnsp12.mid12(a, b, c) :- dnsp12.inp12(a, b, c).
dnsp13.out12(a, b, c) :- dnsp12.mid12(a, b, c).
dnsp13.mid13(a, b, c) :- dnsp13.inp13(a, b, c).
dnsp14.out13(a, b, c) :- dnsp13.mid13(a, b, c).
dnsp14.mid14(a, b, c) :- dnsp14.inp14(a, b, c).
dnsp15.out14(a, b, c) :- dnsp14.mid14(a, b, c).
dnsp15.mid15(a, b, c) :- dnsp15.inp15(a, b, c).
dnsp16.out15(a, b, c) :- dnsp15.mid15(a, b, c).
dnsp16.mid16(a, b, c) :- dnsp16.inp16(a, b, c).
dnsp17.out16(a, b, c) :- dnsp16.mid16(a, b, c).
dnsp17.mid17(a, b, c) :- dnsp17.inp17(a, b, c).
dnsp18.out17(a, b, c) :- dnsp17.mid17(a, b, c).
dnsp18.mid18(a, b, c) :- dnsp18.inp18(a, b, c).
dnsp19.out18(a, b, c) :- dnsp18.mid18(a, b, c).
dnsp19.mid19(a, b, c) :- dnsp19.inp19(a, b, c).
dnsp20.out19(a, b, c) :- dnsp19.mid19(a, b, c).
dnsp20.mid20(a, b, c) :- dnsp20.inp20(a, b, c).
dnsp21.out20(a, b, c) :- dnsp20.mid20(a, b, c).
dnsp21.mid21(a, b, c) :- dnsp21.inp21(a, b, c).
dnsp22.out21(a, b, c) :- dnsp21.mid21(a, b, c).
dnsp22.mid22(a, b, c) :- dnsp22.inp22(a, b, c).
dnsp23.out22(a, b, c) :- dnsp22.mid22(a, b, c).
dnsp23.mid23(a, b, c) :- dnsp23.inp23(a, b, c).
dnsp24.out23(a, b, c) :- dnsp23.mid23(a, b, c).
dnsp24.mid24(a, b, c) :- dnsp24.inp24(a, b, c).
dnsp25.out24(a, b, c) :- dnsp24.mid24(a, b, c).
dnsp25.mid25(a, b, c) :- dnsp25.inp25(a, b, c).
dnsp26.out25(a, b, c) :- dnsp25.mid25(a, b, c).
dnsp26.mid26(a, b, c) :- dnsp26.inp26(a, b, c).
dnsp27.out26(a, b, c) :- dnsp26.mid26(a, b, c).
dnsp27.mid27(a, b, c) :- dnsp27.inp27(a, b, c).
dnsp28.out27(a, b, c) :- dnsp27.mid27(a, b, c).
dnsp28.mid28(a, b, c) :- dnsp28.inp28(a, b, c).
dnsp29.out28(a, b, c) :- dnsp28.mid28(a, b, c).
dnsp29.mid29(a, b, c) :- dnsp29.inp29(a, b, c).
dnsp30.out29(a, b, c) :- dnsp29.mid29(a, b, c).
dnsp30.mid30(a, b, c) :- dnsp30.inp30(a, b, c).
dnsp31.out30(a, b, c) :- dnsp30.mid30(a, b, c).
dnsp31.mid31(a, b, c) :- dnsp31.inp31(a, b, c).
dnsp32.out31(a, b, c) :- dnsp31.mid31(a, b, c).
dnsp32.mid32(a, b, c) :- dnsp32.inp32(a, b, c).
dnsp33.out32(a, b, c) :- dnsp32.mid32(a, b, c).
dnsp33.mid33(a, b, c) :- dnsp33.inp33(a, b, c).
dnsp34.out33(a, b, c) :- dnsp33.mid33(a, b, c).
dnsp34.mid34(a, b, c) :- dnsp34.inp34(a, b, c).
dnsp35.out34(a, b, c) :- dnsp34.mid34(a, b, c).
dnsp35.mid35(a, b, c) :- dnsp35.inp35(a, b, c).
dnsp36.out35(a, b, c) :- dnsp35.mid35(a, b, c).
dnsp36.mid36(a, b, c) :- dnsp36.inp36(a, b, c).
dnsp37.out36(a, b, c) :- dnsp36.mid36(a, b, c).
dnsp37.mid37(a, b, c) :- dnsp37.inp37(a, b, c).
dnsp38.out37(a, b, c) :- dnsp37.mid37(a, b, c).
dnsp38.mid38(a, b, c) :- dnsp38.inp38(a, b, c).
dnsp39.out38(a, b, c) :- dnsp38.mid38(a, b, c).
dnsp39.mid39(a, b, c) :- dnsp39.inp39(a, b, c).
dnsp40.out39(a, b, c) :- dnsp39.mid39(a, b, c).
dnsp40.mid40(a, b, c) :- dnsp40.inp40(a, b, c).
dnsp41.out40(a, b, c) :- dnsp40.mid40(a, b, c).
dnsp41.mid41(a, b, c) :- dnsp41.inp41(a, b, c).
dnsp42.out41(a, b, c) :- dnsp41.mid41(a, b, c).
dnsp42.mid42(a, b, c) :- dnsp42.inp42(a, b, c).
dnsp43.out42(a, b, c) :- dnsp42.mid42(a, b, c).
dnsp43.mid43(a, b, c) :- dnsp43.inp43(a, b, c).
dnsp44.out43(a, b, c) :- dnsp43.mid43(a, b, c).
dnsp44.mid44(a, b, c) :- dnsp44.inp44(a, b, c).
dnsp45.out44(a, b, c) :- dnsp44.mid44(a, b, c).
dnsp45.mid45(a, b, c) :- dnsp45.inp45(a, b, c).
dnsp46.out45(a, b, c) :- dnsp45.mid45(a, b, c).
dnsp46.mid46(a, b, c) :- dnsp46.inp46(a, b, c).
dnsp47.out46(a, b, c) :- dnsp46.mid46(a, b, c).
dnsp47.mid47(a, b, c) :- dnsp47.inp47(a, b, c).
dnsp48.out47(a, b, c) :- dnsp47.mid47(a, b, c).
dnsp48.mid48(a, b, c) :- dnsp48.inp48(a, b, c).
dnsp49.out48(a, b, c) :- dnsp48.mid48(a, b, c).
dnsp49.mid49(a, b, c) :- dnsp49.inp49(a, b, c).
dnsp50.out49(a, b, c) :- dnsp49.mid49(a, b, c).
dnsp50.mid50(a, b, c) :- dnsp50.inp50(a, b, c).
dnsp51.out50(a, b, c) :- dnsp50.mid50(a, b, c).
dnsp51.mid51(a, b, c) :- dnsp51.inp51(a, b, c).
dnsp52.out51(a, b, c) :- dnsp51.mid51(a, b, c).
dnsp52.mid52(a, b, c) :- dnsp52.inp52(a, b, c).
dnsp53.out52(a, b, c) :- dnsp52.mid52(a, b, c).
dnsp53.mid53(a, b, c) :- dnsp53.inp53(a, b, c).
dnsp54.out53(a, b, c) :- dnsp53.mid53(a, b, c).
dnsp54.mid54(a, b, c) :- dnsp54.inp54(a, b, c).
dnsp55.out54(a, b, c) :- dnsp54.mid54(a, b, c).
dnsp55.mid55(a, b, c) :- dnsp55.inp55(a, b, c).
dnsp56.out55(a, b, c) :- dnsp55.mid55(a, b, c).
dnsp56.mid56(a, b, c) :- dnsp56.inp56(a, b, c).
dnsp57.out56(a, b, c) :- dnsp56.mid56(a, b, c).
dnsp57.mid57(a, b, c) :- dnsp57.inp57(a, b, c).
dnsp58.out57(a, b, c) :- dnsp57.mid57(a, b, c).
dnsp58.mid58(a, b, c) :- dnsp58.inp58(a, b, c).
dnsp59.out58(a, b, c) :- dnsp58.mid58(a, b, c).
dnsp59.mid59(a, b, c) :- dnsp59.inp59(a, b, c).
dnsp60.out59(a, b, c) :- dnsp59.mid59(a, b, c).
dnsp60.mid60(a, b, c) :- dnsp60.inp60(a, b, c).
dnsp61.out60(a, b, c) :- dnsp60.mid60(a, b, c).
dnsp61.mid61(a, b, c) :- dnsp61.inp61(a, b, c).
dnsp62.out61(a, b, c) :- dnsp61.mid61(a, b, c).
dnsp62.mid62(a, b, c) :- dnsp62.inp62(a, b, c).
dnsp63.out62(a, b, c) :- dnsp62.mid62(a, b, c).
dnsp63.mid63(a, b, c) :- dnsp63.inp63(a, b, c).
dnsp64.out63(a, b, c) :- dnsp63.mid63(a, b, c).
dnsp64.mid64(a, b, c) :- dnsp64.inp64(a, b, c).
dnsp65.out64(a, b, c) :- dnsp64.mid64(a, b, c).
dnsp65.mid65(a, b, c) :- dnsp65.inp65(a, b, c).
dnsp66.out65(a, b, c) :- dnsp65.mid65(a, b, c).
dnsp66.mid66(a, b, c) :- dnsp66.inp66(a, b, c).
dnsp67.out66(a, b, c) :- dnsp66.mid66(a, b, c).
dnsp67.mid67(a, b, c) :- dnsp67.inp67(a, b, c).
dnsp68.out67(a, b, c) :- dnsp67.mid67(a, b, c).
dnsp68.mid68(a, b, c) :- dnsp68.inp68(a, b, c).
dnsp69.out68(a, b, c) :- dnsp68.mid68(a, b, c).
dnsp69.mid69(a, b, c) :- dnsp69.inp69(a, b, c).
dnsp70.out69(a, b, c) :- dnsp69.mid69(a, b, c).
dnsp70.mid70(a, b, c) :- dnsp70.inp70(a, b, c).
dnsp71.out70(a, b, c) :- dnsp70.mid70(a, b, c).
dnsp71.mid71(a, b, c) :- dnsp71.inp71(a, b, c).
dnsp72.out71(a, b, c) :- dnsp71.mid71(a, b, c).
dnsp72.mid72(a, b, c) :- dnsp72.inp72(a, b, c).
dnsp73.out72(a, b, c) :- dnsp72.mid72(a, b, c).
dnsp73.mid73(a, b, c) :- dnsp73.inp73(a, b, c).
dnsp74.out73(a, b, c) :- dnsp73.mid73(a, b, c).
dnsp74.mid74(a, b, c) :- dnsp74.inp74(a, b, c).
dnsp75.out74(a, b, c) :- dnsp74.mid74(a, b, c).
dnsp75.mid75(a, b, c) :- dnsp75.inp75(a, b, c).
dnsp76.out75(a, b, c) :- dnsp75.mid75(a, b, c).
dnsp76.mid76(a, b, c) :- dnsp76.inp76(a, b, c).
dnsp77.out76(a, b, c) :- dnsp76.mid76(a, b, c).
dnsp77.mid77(a, b, c) :- dnsp77.inp77(a, b, c).
dnsp78.out77(a, b, c) :- dnsp77.mid77(a, b, c).
dnsp78.mid78(a, b, c) :- dnsp78.inp78(a, b, c).
dnsp79.out78(a, b, c) :- dnsp78.mid78(a, b, c).
dnsp79.mid79(a, b, c) :- dnsp79.inp79(a, b, c).
dnsp80.out79(a, b, c) :- dnsp79.mid79(a, b, c).
dnsp80.mid80(a, b, c) :- dnsp80.inp80(a, b, c).
dnsp81.out80(a, b, c) :- dnsp80.mid80(a, b, c).
dnsp81.mid81(a, b, c) :- dnsp81.inp81(a, b, c).
dnsp82.out81(a, b, c) :- dnsp81.mid81(a, b, c).
dnsp82.mid82(a, b, c) :- dnsp82.inp82(a, b, c).
dnsp83.out82(a, b, c) :- dnsp82.mid82(a, b, c).
dnsp83.mid83(a, b, c) :- dnsp83.inp83(a, b, c).
dnsp84.out83(a, b, c) :- dnsp83.mid83(a, b, c).
dnsp84.mid84(a, b, c) :- dnsp84.inp84(a, b, c).
dnsp85.out84(a, b, c) :- dnsp84.mid84(a, b, c).
dnsp85.mid85(a, b, c) :- dnsp85.inp85(a, b, c).
dnsp86.out85(a, b, c) :- dnsp85.mid85(a, b, c).
dnsp86.mid86(a, b, c) :- dnsp86.inp86(a, b, c).
dnsp87.out86(a, b, c) :- dnsp86.mid86(a, b, c).
dnsp87.mid87(a, b, c) :- dnsp87.inp87(a, b, c).
dnsp88.out87(a, b, c) :- dnsp87.mid87(a, b, c).
dnsp88.mid88(a, b, c) :- dnsp88.inp88(a, b, c).
dnsp89.out88(a, b, c) :- dnsp88.mid88(a, b, c).
dnsp89.mid89(a, b, c) :- dnsp89.inp89(a, b, c).
dnsp90.out89(a, b, c) :- dnsp89.mid89(a, b, c).
dnsp90.mid90(a, b, c) :- dnsp90.inp90(a, b, c).
dnsp91.out90(a, b, c) :- dnsp90.mid90(a, b, c).
dnsp91.mid91(a, b, c) :- dnsp91.inp91(a, b, c).
dnsp92.out91(a, b, c) :- dnsp91.mid91(a, b, c).
dnsp92.mid92(a, b, c) :- dnsp92.inp92(a, b, c).
dnsp93.out92(a, b, c) :- dnsp92.mid92(a, b, c).
dnsp93.mid93(a, b, c) :- dnsp93.inp93(a, b, c).
dnsp94.out93(a, b, c) :- dnsp93.mid93(a, b, c).
dnsp94.mid94(a, b, c) :- dnsp94.inp94(a, b, c).
dnsp95.out94(a, b, c) :- dnsp94.mid94(a, b, c).
dnsp95.mid95(a, b, c) :- dnsp95.inp95(a, b, c).
dnsp96.out95(a, b, c) :- dnsp95.mid95(a, b, c).
dnsp96.mid96(a, b, c) :- dnsp96.inp96(a, b, c).
dnsp97.out96(a, b, c) :- dnsp96.mid96(a, b, c).
dnsp97.mid97(a, b, c) :- dnsp97.inp97(a, b, c).
dnsp98.out97(a, b, c) :- dnsp97.mid97(a, b, c).
dnsp98.mid98(a, b, c) :- dnsp98.inp98(a, b, c).
dnsp99.out98(a, b, c) :- dnsp98.mid98(a, b, c).
dnsp99.mid99(a, b, c) :- dnsp99.inp99(a, b, c).
dnsp100.out99(a, b, c) :- dnsp99.mid99(a, b, c).
dnsp100.mid100(a, b, c) :- dnsp100.inp100(a, b, c).
dnsp101.out100(a, b, c) :- dnsp100.mid100(a, b, c).
dnsp101.mid101(a, b, c) :- dnsp101.inp101(a, b, c).
dnsp102.out101(a, b, c) :- dnsp101.mid101(a, b, c).
dnsp102.mid102(a, b, c) :- dnsp102.inp102(a, b, c).
dnsp103.out102(a, b, c) :- dnsp102.mid102(a, b, c).
dnsp103.mid103(a, b, c) :- dnsp103.inp103(a, b, c).
dnsp104.out103(a, b, c) :- dnsp103.mid103(a, b, c).
dnsp104.mid104(a, b, c) :- dnsp104.inp104(a, b, c).
dnsp105.out104(a, b, c) :- dnsp104.mid104(a, b, c).
dnsp105.mid105(a, b, c) :- dnsp105.inp105(a, b, c).
dnsp106.out105(a, b, c) :- dnsp105.mid105(a, b, c).
dnsp106.mid106(a, b, c) :- dnsp106.inp106(a, b, c).
dnsp107.out106(a, b, c) :- dnsp106.mid106(a, b, c).
dnsp107.mid107(a, b, c) :- dnsp107.inp107(a, b, c).
dnsp108.out107(a, b, c) :- dnsp107.mid107(a, b, c).
dnsp108.mid108(a, b, c) :- dnsp108.inp108(a, b, c).
dnsp109.out108(a, b, c) :- dnsp108.mid108(a, b, c).
dnsp109.mid109(a, b, c) :- dnsp109.inp109(a, b, c).
dnsp110.out109(a, b, c) :- dnsp109.mid109(a, b, c).
dnsp110.mid110(a, b, c) :- dnsp110.inp110(a, b, c).
dnsp111.out110(a, b, c) :- dnsp110.mid110(a, b, c).
dnsp111.mid111(a, b, c) :- dnsp111.inp111(a, b, c).
dnsp112.out111(a, b, c) :- dnsp111.mid111(a, b, c).
dnsp112.mid112(a, b, c) :- dnsp112.inp112(a, b, c).
dnsp113.out112(a, b, c) :- dnsp112.mid112(a, b, c).
dnsp113.mid113(a, b, c) :- dnsp113.inp113(a, b, c).
dnsp114.out113(a, b, c) :- dnsp113.mid113(a, b, c).
dnsp114.mid114(a, b, c) :- dnsp114.inp114(a, b, c).
dnsp115.out114(a, b, c) :- dnsp114.mid114(a, b, c).
dnsp115.mid115(a, b, c) :- dnsp115.inp115(a, b, c).
dnsp116.out115(a, b, c) :- dnsp115.mid115(a, b, c).
dnsp116.mid116(a, b, c) :- dnsp116.inp116(a, b, c).
dnsp117.out116(a, b, c) :- dnsp116.mid116(a, b, c).
dnsp117.mid117(a, b, c) :- dnsp117.inp117(a, b, c).
dnsp118.out117(a, b, c) :- dnsp117.mid117(a, b, c).
dnsp118.mid118(a, b, c) :- dnsp118.inp118(a, b, c).
dnsp119.out118(a, b, c) :- dnsp118.mid118(a, b, c).
dnsp119.mid119(a, b, c) :- dnsp119.inp119(a, b, c).
dnsp120.out119(a, b, c) :- dnsp119.mid119(a, b, c).
dnsp120.mid120(a, b, c) :- dnsp120.inp120(a, b, c).
dnsp121.out120(a, b, c) :- dnsp120.mid120(a, b, c).
dnsp121.mid121(a, b, c) :- dnsp121.inp121(a, b, c).
dnsp122.out121(a, b, c) :- dnsp121.mid121(a, b, c).
dnsp122.mid122(a, b, c) :- dnsp122.inp122(a, b, c).
dnsp123.out122(a, b, c) :- dnsp122.mid122(a, b, c).
dnsp123.mid123(a, b, c) :- dnsp123.inp123(a, b, c).
dnsp124.out123(a, b, c) :- dnsp123.mid123(a, b, c).
dnsp124.mid124(a, b, c) :- dnsp124.inp124(a, b, c).
dnsp125.out124(a, b, c) :- dnsp124.mid124(a, b, c).
dnsp125.mid125(a, b, c) :- dnsp125.inp125(a, b, c).
dnsp126.out125(a, b, c) :- dnsp125.mid125(a, b, c).
dnsp126.mid126(a, b, c) :- dnsp126.inp126(a, b, c).
dnsp127.out126(a, b, c) :- dnsp126.mid126(a, b, c).
dnsp127.mid127(a, b, c) :- dnsp127.inp127(a, b, c).
dnsp128.out127(a, b, c) :- dnsp127.mid127(a, b, c).
dnsp128.mid128(a, b, c) :- dnsp128.inp128(a, b, c).
dnsp129.out128(a, b, c) :- dnsp128.mid128(a, b, c).
dnsp129.mid129(a, b, c) :- dnsp129.inp129(a, b, c).
dnsp130.out129(a, b, c) :- dnsp129.mid129(a, b, c).
dnsp130.mid130(a, b, c) :- dnsp130.inp130(a, b, c).
dnsp131.out130(a, b, c) :- dnsp130.mid130(a, b, c).
dnsp131.mid131(a, b, c) :- dnsp131.inp131(a, b, c).
dnsp132.out131(a, b, c) :- dnsp131.mid131(a, b, c).
dnsp132.mid132(a, b, c) :- dnsp132.inp132(a, b, c).
dnsp133.out132(a, b, c) :- dnsp132.mid132(a, b, c).
dnsp133.mid133(a, b, c) :- dnsp133.inp133(a, b, c).
dnsp134.out133(a, b, c) :- dnsp133.mid133(a, b, c).
dnsp134.mid134(a, b, c) :- dnsp134.inp134(a, b, c).
dnsp135.out134(a, b, c) :- dnsp134.mid134(a, b, c).
dnsp135.mid135(a, b, c) :- dnsp135.inp135(a, b, c).
dnsp136.out135(a, b, c) :- dnsp135.mid135(a, b, c).
dnsp136.mid136(a, b, c) :- dnsp136.inp136(a, b, c).
dnsp137.out136(a, b, c) :- dnsp136.mid136(a, b, c).
dnsp137.mid137(a, b, c) :- dnsp137.inp137(a, b, c).
dnsp138.out137(a, b, c) :- dnsp137.mid137(a, b, c).
dnsp138.mid138(a, b, c) :- dnsp138.inp138(a, b, c).
dnsp139.out138(a, b, c) :- dnsp138.mid138(a, b, c).
dnsp139.mid139(a, b, c) :- dnsp139.inp139(a, b, c).
dnsp140.out139(a, b, c) :- dnsp139.mid139(a, b, c).
dnsp140.mid140(a, b, c) :- dnsp140.inp140(a, b, c).
dnsp141.out140(a, b, c) :- dnsp140.mid140(a, b, c).
dnsp141.mid141(a, b, c) :- dnsp141.inp141(a, b, c).
dnsp142.out141(a, b, c) :- dnsp141.mid141(a, b, c).
dnsp142.mid142(a, b, c) :- dnsp142.inp142(a, b, c).
dnsp143.out142(a, b, c) :- dnsp142.mid142(a, b, c).
dnsp143.mid143(a, b, c) :- dnsp143.inp143(a, b, c).
dnsp144.out143(a, b, c) :- dnsp143.mid143(a, b, c).
dnsp144.mid144(a, b, c) :- dnsp144.inp144(a, b, c).
dnsp145.out144(a, b, c) :- dnsp144.mid144(a, b, c).
dnsp145.mid145(a, b, c) :- dnsp145.inp145(a, b, c).
dnsp146.out145(a, b, c) :- dnsp145.mid145(a, b, c).
dnsp146.mid146(a, b, c) :- dnsp146.inp146(a, b, c).
dnsp147.out146(a, b, c) :- dnsp146.mid146(a, b, c).
dnsp147.mid147(a, b, c) :- dnsp147.inp147(a, b, c).
dnsp148.out147(a, b, c) :- dnsp147.mid147(a, b, c).
dnsp148.mid148(a, b, c) :- dnsp148.inp148(a, b, c).
dnsp149.out148(a, b, c) :- dnsp148.mid148(a, b, c).
dnsp149.mid149(a, b, c) :- dnsp149.inp149(a, b, c).
dnsp150.out149(a, b, c) :- dnsp149.mid149(a, b, c).
dnsp150.mid150(a, b, c) :- dnsp150.inp150(a, b, c).
dnsp151.out150(a, b, c) :- dnsp150.mid150(a, b, c).
dnsp151.mid151(a, b, c) :- dnsp151.inp151(a, b, c).
dnsp152.out151(a, b, c) :- dnsp151.mid151(a, b, c).
dnsp152.mid152(a, b, c) :- dnsp152.inp152(a, b, c).
dnsp153.out152(a, b, c) :- dnsp152.mid152(a, b, c).
dnsp153.mid153(a, b, c) :- dnsp153.inp153(a, b, c).
dnsp154.out153(a, b, c) :- dnsp153.mid153(a, b, c).
dnsp154.mid154(a, b, c) :- dnsp154.inp154(a, b, c).
dnsp155.out154(a, b, c) :- dnsp154.mid154(a, b, c).
dnsp155.mid155(a, b, c) :- dnsp155.inp155(a, b, c).
dnsp156.out155(a, b, c) :- dnsp155.mid155(a, b, c).
dnsp156.mid156(a, b, c) :- dnsp156.inp156(a, b, c).
dnsp157.out156(a, b, c) :- dnsp156.mid156(a, b, c).
dnsp157.mid157(a, b, c) :- dnsp157.inp157(a, b, c).
dnsp158.out157(a, b, c) :- dnsp157.mid157(a, b, c).
dnsp158.mid158(a, b, c) :- dnsp158.inp158(a, b, c).
dnsp159.out158(a, b, c) :- dnsp158.mid158(a, b, c).
dnsp159.mid159(a, b, c) :- dnsp159.inp159(a, b, c).
dnsp160.out159(a, b, c) :- dnsp159.mid159(a, b, c).
dnsp160.mid160(a, b, c) :- dnsp160.inp160(a, b, c).
dnsp161.out160(a, b, c) :- dnsp160.mid160(a, b, c).
dnsp161.mid161(a, b, c) :- dnsp161.inp161(a, b, c).
dnsp162.out161(a, b, c) :- dnsp161.mid161(a, b, c).
dnsp162.mid162(a, b, c) :- dnsp162.inp162(a, b, c).
dnsp163.out162(a, b, c) :- dnsp162.mid162(a, b, c).
dnsp163.mid163(a, b, c) :- dnsp163.inp163(a, b, c).
dnsp164.out163(a, b, c) :- dnsp163.mid163(a, b, c).
dnsp164.mid164(a, b, c) :- dnsp164.inp164(a, b, c).
dnsp165.out164(a, b, c) :- dnsp164.mid164(a, b, c).
dnsp165.mid165(a, b, c) :- dnsp165.inp165(a, b, c).
dnsp166.out165(a, b, c) :- dnsp165.mid165(a, b, c).
dnsp166.mid166(a, b, c) :- dnsp166.inp166(a, b, c).
dnsp167.out166(a, b, c) :- dnsp166.mid166(a, b, c).
dnsp167.mid167(a, b, c) :- dnsp167.inp167(a, b, c).
dnsp168.out167(a, b, c) :- dnsp167.mid167(a, b, c).
dnsp168.mid168(a, b, c) :- dnsp168.inp168(a, b, c).
dnsp169.out168(a, b, c) :- dnsp168.mid168(a, b, c).
dnsp169.mid169(a, b, c) :- dnsp169.inp169(a, b, c).
dnsp170.out169(a, b, c) :- dnsp169.mid169(a, b, c).
dnsp170.mid170(a, b, c) :- dnsp170.inp170(a, b, c).
dnsp171.out170(a, b, c) :- dnsp170.mid170(a, b, c).
dnsp171.mid171(a, b, c) :- dnsp171.inp171(a, b, c).
dnsp172.out171(a, b, c) :- dnsp171.mid171(a, b, c).
dnsp172.mid172(a, b, c) :- dnsp172.inp172(a, b, c).
dnsp173.out172(a, b, c) :- dnsp172.mid172(a, b, c).
dnsp173.mid173(a, b, c) :- dnsp173.inp173(a, b, c).
dnsp174.out173(a, b, c) :- dnsp173.mid173(a, b, c).
dnsp174.mid174(a, b, c) :- dnsp174.inp174(a, b, c).
dnsp175.out174(a, b, c) :- dnsp174.mid174(a, b, c).
dnsp175.mid175(a, b, c) :- dnsp175.inp175(a, b, c).
dnsp176.out175(a, b, c) :- dnsp175.mid175(a, b, c).
dnsp176.mid176(a, b, c) :- dnsp176.inp176(a, b, c).
dnsp177.out176(a, b, c) :- dnsp176.mid176(a, b, c).
dnsp177.mid177(a, b, c) :- dnsp177.inp177(a, b, c).
dnsp178.out177(a, b, c) :- dnsp177.mid177(a, b, c).
dnsp178.mid178(a, b, c) :- dnsp178.inp178(a, b, c).
dnsp179.out178(a, b, c) :- dnsp178.mid178(a, b, c).
dnsp179.mid179(a, b, c) :- dnsp179.inp179(a, b, c).
dnsp180.out179(a, b, c) :- dnsp179.mid179(a, b, c).
dnsp180.mid180(a, b, c) :- dnsp180.inp180(a, b, c).
dnsp181.out180(a, b, c) :- dnsp180.mid180(a, b, c).
dnsp181.mid181(a, b, c) :- dnsp181.inp181(a, b, c).
dnsp182.out181(a, b, c) :- dnsp181.mid181(a, b, c).
dnsp182.mid182(a, b, c) :- dnsp182.inp182(a, b, c).
dnsp183.out182(a, b, c) :- dnsp182.mid182(a, b, c).
dnsp183.mid183(a, b, c) :- dnsp183.inp183(a, b, c).
dnsp184.out183(a, b, c) :- dnsp183.mid183(a, b, c).
dnsp184.mid184(a, b, c) :- dnsp184.inp184(a, b, c).
dnsp185.out184(a, b, c) :- dnsp184.mid184(a, b, c).
dnsp185.mid185(a, b, c) :- dnsp185.inp185(a, b, c).
dnsp186.out185(a, b, c) :- dnsp185.mid185(a, b, c).
dnsp186.mid186(a, b, c) :- dnsp186.inp186(a, b, c).
dnsp187.out186(a, b, c) :- dnsp186.mid186(a, b, c).
dnsp187.mid187(a, b, c) :- dnsp187.inp187(a, b, c).
dnsp188.out187(a, b, c) :- dnsp187.mid187(a, b, c).
dnsp188.mid188(a, b, c) :- dnsp188.inp188(a, b, c).
dnsp189.out188(a, b, c) :- dnsp188.mid188(a, b, c).
dnsp189.mid189(a, b, c) :- dnsp189.inp189(a, b, c).
dnsp190.out189(a, b, c) :- dnsp189.mid189(a, b, c).
dnsp190.mid190(a, b, c) :- dnsp190.inp190(a, b, c).
dnsp191.out190(a, b, c) :- dnsp190.mid190(a, b, c).
dnsp191.mid191(a, b, c) :- dnsp191.inp191(a, b, c).
dnsp192.out191(a, b, c) :- dnsp191.mid191(a, b, c).
dnsp192.mid192(a, b, c) :- dnsp192.inp192(a, b, c).
dnsp193.out192(a, b, c) :- dnsp192.mid192(a, b, c).
dnsp193.mid193(a, b, c) :- dnsp193.inp193(a, b, c).
dnsp194.out193(a, b, c) :- dnsp193.mid193(a, b, c).
dnsp194.mid194(a, b, c) :- dnsp194.inp194(a, b, c).
dnsp195.out194(a, b, c) :- dnsp194.mid194(a, b, c).
dnsp195.mid195(a, b, c) :- dnsp195.inp195(a, b, c).
dnsp196.out195(a, b, c) :- dnsp195.mid195(a, b, c).
dnsp196.mid196(a, b, c) :- dnsp196.inp196(a, b, c).
dnsp197.out196(a, b, c) :- dnsp196.mid196(a, b, c).
dnsp197.mid197(a, b, c) :- dnsp197.inp197(a, b, c).
dnsp198.out197(a, b, c) :- dnsp197.mid197(a, b, c).
dnsp198.mid198(a, b, c) :- dnsp198.inp198(a, b, c).
dnsp199.out198(a, b, c) :- dnsp198.mid198(a, b, c).
dnsp199.mid199(a, b, c) :- dnsp199.inp199(a, b, c).
dnsp200.out199(a, b, c) :- dnsp199.mid199(a, b, c).
dnsp200.mid200(a, b, c) :- dnsp200.inp200(a, b, c).
dnsp201.out200(a, b, c) :- dnsp200.mid200(a, b, c).
dnsp201.mid201(a, b, c) :- dnsp201.inp201(a, b, c).
dnsp202.out201(a, b, c) :- dnsp201.mid201(a, b, c).
dnsp202.mid202(a, b, c) :- dnsp202.inp202(a, b, c).
dnsp203.out202(a, b, c) :- dnsp202.mid202(a, b, c).
dnsp203.mid203(a, b, c) :- dnsp203.inp203(a, b, c).
dnsp204.out203(a, b, c) :- dnsp203.mid203(a, b, c).
dnsp204.mid204(a, b, c) :- dnsp204.inp204(a, b, c).
dnsp205.out204(a, b, c) :- dnsp204.mid204(a, b, c).
dnsp205.mid205(a, b, c) :- dnsp205.inp205(a, b, c).
dnsp206.out205(a, b, c) :- dnsp205.mid205(a, b, c).
dnsp206.mid206(a, b, c) :- dnsp206.inp206(a, b, c).
dnsp207.out206(a, b, c) :- dnsp206.mid206(a, b, c).
dnsp207.mid207(a, b, c) :- dnsp207.inp207(a, b, c).
dnsp208.out207(a, b, c) :- dnsp207.mid207(a, b, c).
dnsp208.mid208(a, b, c) :- dnsp208.inp208(a, b, c).
dnsp209.out208(a, b, c) :- dnsp208.mid208(a, b, c).
dnsp209.mid209(a, b, c) :- dnsp209.inp209(a, b, c).
dnsp210.out209(a, b, c) :- dnsp209.mid209(a, b, c).
dnsp210.mid210(a, b, c) :- dnsp210.inp210(a, b, c).
dnsp211.out210(a, b, c) :- dnsp210.mid210(a, b, c).
dnsp211.mid211(a, b, c) :- dnsp211.inp211(a, b, c).
dnsp212.out211(a, b, c) :- dnsp211.mid211(a, b, c).
dnsp212.mid212(a, b, c) :- dnsp212.inp212(a, b, c).
dnsp213.out212(a, b, c) :- dnsp212.mid212(a, b, c).
dnsp213.mid213(a, b, c) :- dnsp213.inp213(a, b, c).
dnsp214.out213(a, b, c) :- dnsp213.mid213(a, b, c).
dnsp214.mid214(a, b, c) :- dnsp214.inp214(a, b, c).
dnsp215.out214(a, b, c) :- dnsp214.mid214(a, b, c).
dnsp215.mid215(a, b, c) :- dnsp215.inp215(a, b, c).
dnsp216.out215(a, b, c) :- dnsp215.mid215(a, b, c).
dnsp216.mid216(a, b, c) :- dnsp216.inp216(a, b, c).
dnsp217.out216(a, b, c) :- dnsp216.mid216(a, b, c).
dnsp217.mid217(a, b, c) :- dnsp217.inp217(a, b, c).
dnsp218.out217(a, b, c) :- dnsp217.mid217(a, b, c).
dnsp218.mid218(a, b, c) :- dnsp218.inp218(a, b, c).
dnsp219.out218(a, b, c) :- dnsp218.mid218(a, b, c).
dnsp219.mid219(a, b, c) :- dnsp219.inp219(a, b, c).
dnsp220.out219(a, b, c) :- dnsp219.mid219(a, b, c).
dnsp220.mid220(a, b, c) :- dnsp220.inp220(a, b, c).
dnsp221.out220(a, b, c) :- dnsp220.mid220(a, b, c).
dnsp221.mid221(a, b, c) :- dnsp221.inp221(a, b, c).
dnsp222.out221(a, b, c) :- dnsp221.mid221(a, b, c).
dnsp222.mid222(a, b, c) :- dnsp222.inp222(a, b, c).
dnsp223.out222(a, b, c) :- dnsp222.mid222(a, b, c).
dnsp223.mid223(a, b, c) :- dnsp223.inp223(a, b, c).
dnsp224.out223(a, b, c) :- dnsp223.mid223(a, b, c).
dnsp224.mid224(a, b, c) :- dnsp224.inp224(a, b, c).
dnsp225.out224(a, b, c) :- dnsp224.mid224(a, b, c).
dnsp225.mid225(a, b, c) :- dnsp225.inp225(a, b, c).
dnsp226.out225(a, b, c) :- dnsp225.mid225(a, b, c).
dnsp226.mid226(a, b, c) :- dnsp226.inp226(a, b, c).
dnsp227.out226(a, b, c) :- dnsp226.mid226(a, b, c).
dnsp227.mid227(a, b, c) :- dnsp227.inp227(a, b, c).
dnsp228.out227(a, b, c) :- dnsp227.mid227(a, b, c).
dnsp228.mid228(a, b, c) :- dnsp228.inp228(a, b, c).
dnsp229.out228(a, b, c) :- dnsp228.mid228(a, b, c).
dnsp229.mid229(a, b, c) :- dnsp229.inp229(a, b, c).
dnsp230.out229(a, b, c) :- dnsp229.mid229(a, b, c).
dnsp230.mid230(a, b, c) :- dnsp230.inp230(a, b, c).
dnsp231.out230(a, b, c) :- dnsp230.mid230(a, b, c).
dnsp231.mid231(a, b, c) :- dnsp231.inp231(a, b, c).
dnsp232.out231(a, b, c) :- dnsp231.mid231(a, b, c).
dnsp232.mid232(a, b, c) :- dnsp232.inp232(a, b, c).
dnsp233.out232(a, b, c) :- dnsp232.mid232(a, b, c).
dnsp233.mid233(a, b, c) :- dnsp233.inp233(a, b, c).
dnsp234.out233(a, b, c) :- dnsp233.mid233(a, b, c).
dnsp234.mid234(a, b, c) :- dnsp234.inp234(a, b, c).
dnsp235.out234(a, b, c) :- dnsp234.mid234(a, b, c).
dnsp235.mid235(a, b, c) :- dnsp235.inp235(a, b, c).
dnsp236.out235(a, b, c) :- dnsp235.mid235(a, b, c).
dnsp236.mid236(a, b, c) :- dnsp236.inp236(a, b, c).
dnsp237.out236(a, b, c) :- dnsp236.mid236(a, b, c).
dnsp237.mid237(a, b, c) :- dnsp237.inp237(a, b, c).
dnsp238.out237(a, b, c) :- dnsp237.mid237(a, b, c).
dnsp238.mid238(a, b, c) :- dnsp238.inp238(a, b, c).
dnsp239.out238(a, b, c) :- dnsp238.mid238(a, b, c).
dnsp239.mid239(a, b, c) :- dnsp239.inp239(a, b, c).
dnsp240.out239(a, b, c) :- dnsp239.mid239(a, b, c).
dnsp240.mid240(a, b, c) :- dnsp240.inp240(a, b, c).
dnsp241.out240(a, b, c) :- dnsp240.mid240(a, b, c).
dnsp241.mid241(a, b, c) :- dnsp241.inp241(a, b, c).
dnsp242.out241(a, b, c) :- dnsp241.mid241(a, b, c).
dnsp242.mid242(a, b, c) :- dnsp242.inp242(a, b, c).
dnsp243.out242(a, b, c) :- dnsp242.mid242(a, b, c).
dnsp243.mid243(a, b, c) :- dnsp243.inp243(a, b, c).
dnsp244.out243(a, b, c) :- dnsp243.mid243(a, b, c).
dnsp244.mid244(a, b, c) :- dnsp244.inp244(a, b, c).
dnsp245.out244(a, b, c) :- dnsp244.mid244(a, b, c).
dnsp245.mid245(a, b, c) :- dnsp245.inp245(a, b, c).
dnsp246.out245(a, b, c) :- dnsp245.mid245(a, b, c).
dnsp246.mid246(a, b, c) :- dnsp246.inp246(a, b, c).
dnsp247.out246(a, b, c) :- dnsp246.mid246(a, b, c).
dnsp247.mid247(a, b, c) :- dnsp247.inp247(a, b, c).
dnsp248.out247(a, b, c) :- dnsp247.mid247(a, b, c).
dnsp248.mid248(a, b, c) :- dnsp248.inp248(a, b, c).
dnsp249.out248(a, b, c) :- dnsp248.mid248(a, b, c).
dnsp249.mid249(a, b, c) :- dnsp249.inp249(a, b, c).
dnsp250.out249(a, b, c) :- dnsp249.mid249(a, b, c).
dnsp250.mid250(a, b, c) :- dnsp250.inp250(a, b, c).
dnsp251.out250(a, b, c) :- dnsp250.mid250(a, b, c).
dnsp251.mid251(a, b, c) :- dnsp251.inp251(a, b, c).
dnsp252.out251(a, b, c) :- dnsp251.mid251(a, b, c).
dnsp252.mid252(a, b, c) :- dnsp252.inp252(a, b, c).
dnsp253.out252(a, b, c) :- dnsp252.mid252(a, b, c).
dnsp253.mid253(a, b, c) :- dnsp253.inp253(a, b, c).
dnsp254.out253(a, b, c) :- dnsp253.mid253(a, b, c).
dnsp254.mid254(a, b, c) :- dnsp254.inp254(a, b, c).
dnsp255.out254(a, b, c) :- dnsp254.mid254(a, b, c).
dnsp255.mid255(a, b, c) :- dnsp255.inp255(a, b, c).
out255(a, b, c) :- dnsp255.mid255(a, b, c).
