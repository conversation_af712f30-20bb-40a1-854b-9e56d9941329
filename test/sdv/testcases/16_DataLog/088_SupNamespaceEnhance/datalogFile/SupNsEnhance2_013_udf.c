/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2024-02-23
 */
#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)

typedef struct B {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
    int32_t c;
} B;

typedef struct Inp1 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} Inp1;

typedef struct Inp2 {
    uint8_t propeNum; // 部分可更新表字段
    uint8_t *nullInfo; // 部分可更新表字段
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} Inp2;

// agg比较字段
typedef struct AggCmp {
    int32_t dtlReservedCount;
    int32_t a;
    int32_t b;
} AggCmp;

// 输入字段--聚合字段
typedef struct AggInp {
    int32_t dtlReservedCount;
    int32_t a;
} AggInp;
// 输出字段---count
typedef struct AggOut {
    int32_t a;
} AggOut;

#pragma pack(0)

// agg比较函数
int32_t dtl_agg_compare_agg(void *tuple1, void *tuple2, GmUdfCtxT *ctx)
{
    AggCmp *inp1 = (AggCmp *)tuple1;
    AggCmp *inp2 = (AggCmp *)tuple2;
    if (inp1->a < inp2->a) {
        return -1;
    } else if (inp1->a > inp2->a) {
        return 1;
    } else {
        if (inp1->b < inp2->b) {
            return -1;
        } else if (inp1->b > inp2->b) {
            return 1;
        }
        return 0;
    }
}

// agg 分组后求行数
int32_t dtl_agg_func_agg(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    GmUdfReaderT *inputBak = input;
    AggInp *inpStruct;
    AggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(AggOut));
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    int64_t cnt = 0;
    int32_t ret = 0;
    while (ret = GmUdfGetNext(inputBak, (void **)&inpStruct), ret == GMERR_OK) {
        cnt += inpStruct->a;
    }
    outStruct->a = cnt;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    return GMERR_OK;
}
