%version v0.0.0

namespace ns1 {
%readwrite inp1, mid1, out1
%table inp1(a:int4, b:int4, c:int4) {update, index(0(a))}
%table mid1(a:int4, b:int4, c:int4, d:int4) {index(0(a))}
%table out1(a:int4, b:int4, c:int4, d:int4) {index(0(a))}

%aggregate agg (a:int4 -> b:int4) {
    ordered,
    many_to_one,
    access_kv(kv1), 
    access_current(ns2.inp2, ns2.inp3, ns2.inp4, ns3.mid5, ns3.inp6, ns3.inp7, ns4.rs8, ns5.inp10),
    access_delta(ns2.inp2, ns2.inp3, ns2.inp4, ns3.mid5, ns3.inp6, ns3.inp7, ns4.rs8, ns5.inp10, ns5.inp11)
}

mid1(a, b, sum, 1) :- inp1(a, b, c) GROUP-BY(a, b) agg(c, sum).
out1(a, b, c, d) :- mid1(a, b, c, d).
}

namespace ns2 {
%readwrite inp2, out2, inp3, out3, inp4, out4
%table inp2(a: int4, b: int4) {
    index(0(a))
}

%table out2(a: int4, b: int4) {
    index(0(a))
}

out2(a, b) :- inp2(a, b).

%table inp3(a: int4, b: int4) {
    index(0(a)),
    update
}

%table out3(a: int4, b: int4) {
    index(0(a))
}

out3(a, b) :- inp3(a, b).

%table inp4(a: int4, b: int4) {
    index(0(a)),
    update_partial
}

%table out4(a: int4, b: int4) {
    index(0(a))
}

out4(a, b) :- inp4(a, b).
}

namespace ns3 {
%readwrite inp5, mid5, out5, inp6, out6, inp7, out7
%table inp5(a: int4, b: int4) {
    index(0(a))
}

%table mid5(a: int4, b: int4) {
    index(0(a)),
    transient(field(a))
}

%table out5(a: int4, b: int4) {
    index(0(a))
}

mid5(a, b) :- inp5(a, b).
out5(a, b) :- mid5(a, b).

%table inp6(a: int4, b: int4) {
    index(0(a)),
    transient(tuple),
    variant
}

%table out6(a: int4, b: int4) {
    index(0(a))
}

out6(a, b) :- inp6(a, b).

%table inp7(a: int4, b: int4) {
    index(0(a)),
    transient(finish),
    variant
}

%table out7(a: int4, b: int4) {
    index(0(a))
}

out7(a, b) :- inp7(a, b).
}

namespace ns4 {
%readwrite inp8, rs8, out8, inp9, rs9
%table inp8(a: int4, b: int4, c: int4) {
    index(0(a))
}

%resource rs8(a:int4, b:int4, c:int4 -> d:int4){
    sequential(max_size(1000)), index(0(a, b, c))
}

%table out8(a: int4, b: int4, c: int4, d: int4) {
    index(0(a))
}

rs8(a, b, c, -) :- inp8(a, b, c).
out8(a, b, c, d) :- rs8(a, b, c, d).

%table inp9(a: int4, b: int4, c: int4) {
    index(0(a))
}

%resource rs9(a:int4, b:int4, c:int4 -> d:int4){
    pending_id(-1)
}

rs9(a, b, c, -) :- inp9(a, b, c).
null(0) :- rs9(a, b, c, d).
}

namespace ns5 {
%readwrite inp10, out10, inp11, out11, A, B
%table inp10(a:int8, b:int8, c:int8) {
    index(0(a)),
    timeout(field(c), state_function)
}

%table out10(a: int8, b: int8, c: int8) {
    index(0(a))
}

out10(a, b, c) :- inp10(a, b, c).

%table inp11(a:int8, b:int8, c:int8) {
    index(0(a))
}

%table out11(a: int8, b: int8, c: int8) {
    index(0(a)),
    notify
}

out11(a, b, c) :- inp11(a, b, c).

%table A(a: int4, b: int4) {
    index(0(a))
}

%function tran(p: int4 -> s: int4) {
    state_transfer,
    access_current(A)
}

%table B(k: int4, s: int4) {
    index(0(k)),
    state
}

B(k, s) :- A(k, p), tran(p, s).
null(0) :- B(k, s).
}
