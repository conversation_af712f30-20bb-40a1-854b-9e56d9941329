%version v0.0.0

%table inp1(a:int4, b:int4, c:int4) {update, index(0(a))}
%table mid1(a:int4, b:int4, c:int4, d:int4) {index(0(a))}
%table out1(a:int4, b:int4, c:int4, d:int4) {index(0(a))}

%aggregate agg (a:int4 -> b:int4) {
    ordered, access_current(inp2, mid2), access_delta(inp2, mid2), many_to_one
}

mid1(a, b, sum, 1) :- inp1(a, b, c) GROUP-BY(a, b) agg(c, sum).
out1(a, b, c, d) :- mid1(a, b, c, d).

%table inp2(a:int4, b:int4, c:int4) {update, index(0(a))}
%table mid2(a:int4, b:int4, c:int4) {index(0(a))}
%table out2(a:int4, b:int4, c:int4) {index(0(a))}

mid2(a, b, c) :- inp2(a, b, c).
out2(a, b, c) :- mid2(a, b, c).
