%version v0.0.0

namespace ns1 {
%readwrite inp1, mid1, out1
%table inp1(a:int4, b:int4, c:int4) {update, index(0(a))}
%table mid1(a:int4, b:int4, c:int4, d:int4) {index(0(a))}
%table out1(a:int4, b:int4, c:int4, d:int4) {index(0(a))}

%aggregate agg (a:int4 -> b:int4) {
    ordered, access_current(ns2.inp2, ns3.inp3, ns3.mid3, ns3.out3), many_to_many
}

mid1(a, b, sum, 1) :- inp1(a, b, c) GROUP-BY(a, b) agg(c, sum).
out1(a, b, c, d) :- mid1(a, b, c, d).
}

namespace ns2 {
%readwrite inp2, mid2, out2
%table inp2(a:int4, b:int4, c:int4) {update, index(0(a))}
%table mid2(a:int4, b:int4, c:int4) {index(0(a))}
%table out2(a:int4, b:int4, c:int4) {index(0(a))}

mid2(a, b, c) :- inp2(a, b, c).
out2(a, b, c) :- mid2(a, b, c).
}

namespace ns3 {
%readwrite inp3, mid3, out3
%table inp3(a:int4, b:int4, c:int4) {update, index(0(a))}
%table mid3(a:int4, b:int4, c:int4) {index(0(a))}
%table out3(a:int4, b:int4, c:int4) {index(0(a))}

mid3(a, b, c) :- inp3(a, b, c).
out3(a, b, c) :- mid3(a, b, c).
}
