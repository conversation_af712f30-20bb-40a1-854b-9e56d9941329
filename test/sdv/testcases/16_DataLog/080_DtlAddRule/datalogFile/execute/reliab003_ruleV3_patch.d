%version v2.0.1 -> v2.0.3
%block 0
%redo REDO_OFF

%table inp4(a:int8, b:int8, c:int8, d:int8, e:int8) {index(0(a, b)), update}
%table inp5(a:int8, b:int8, c:int8, d:int8, e:int8) {index(0(a, b)), update}
%rule rN null(0) :- inp4(a, b, c, d, e).
%rule rN3 null(0) :- inp5(a, b, c, d, e).
%function func1(a:int8, b:int8, c:int8, d:int8, e:int8) {}
%function func2(a:int8, b:int8, c:int8, d:int8, e:int8) {}

%rule rN1 out1(a, b, c, d, e) :- inp1(a, b, c), inp4(a, b, c, d, e), func1(a, b, c, d, e).
%rule rN2 out2(a, b, c, d, e) :- inp2(a, b, c, d, e), mid3(a, b, c, d, e), inp5(a, b, c, d, e), func2(a, b, c, d, e).
%alter rule r1 out1(a, b, c, 1, -1) :- mid1(a, b, c), inp4(a, b, c, -, -).
%alter rule r6 out2(a, b, c, -10, 10) :- inp2(a, b, c, -, -), func(a, b, c), inp5(a, b, c, -, -).
%alter table out1(a:int8, b:int8, c:int8, d:int8, e:int8) {index(0(a, b, c, d, e)), tbm}
%alter table out2(a:int8, b:int8, c:int8, d:int8, e:int8) {index(0(a, b, c, d, e)), tbm}
