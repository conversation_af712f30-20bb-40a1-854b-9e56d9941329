/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: msg_notify_udf
 * Author: yang<PERSON>wen ywx1060383
 * Create: 2023-07-14
 */
#include <stdio.h>
#include "gm_udf.h"
#include "assert.h"
#pragma pack(1)

#define INSERT 0
#define DELETE 1
#define UPDATE 2

typedef struct MsgNotify2 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int32_t a;
    int32_t b;
    int32_t c;
} MsgNotify2;

typedef struct {
    uint32_t op;
    void *oldTup;
    void *newTup;
} Batch;
#pragma pack(0)

const char *g_logName = "/root/msgNotifyRunLog.txt";

int32_t dtl_msg_notify_msgNotify2(GmMsgNotifyTupleChangeT *tups, uint32_t arrLen)
{
    return 2000;
}
