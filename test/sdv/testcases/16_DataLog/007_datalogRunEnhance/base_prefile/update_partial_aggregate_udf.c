/*  版权所有 (c) 华为技术有限公司 2021-2022 */
#include "gm_udf.h"

#define PROPENUM_A 6

#pragma pack(1)

typedef struct A {
    uint8_t propeNum;
    uint8_t *nullInfo;
    int32_t count;
    int32_t upgradeVersion;
    int64_t a;
    int32_t b;
    int16_t c;
    int8_t d;
} A;

typedef struct B {
    int32_t count;
    int32_t upgradeVersion;
    int64_t a;
    int32_t b;
    int16_t c;
    int8_t d;
} B;

typedef struct FuncAggIn {
    int32_t count;
    int16_t a0;
    int8_t b0;
} FuncAggIn;

typedef struct FuncAggOut {
    int16_t a;
    int8_t b;
} FuncAggOut;

#pragma pack(0)

int32_t dtl_agg_func_ns1_funcA(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int16_t max_a = inpStruct->a0;
    int8_t min_b = inpStruct->b0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max_a = (max_a > inpStruct->a0) ? max_a : inpStruct->a0;
        min_b = (min_b < inpStruct->b0) ? min_b : inpStruct->b0;
    }
    outStruct->a = max_a;
    outStruct->b = min_b;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateDeltaReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_ns2_i_funcA(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int16_t max_a = inpStruct->a0;
    int8_t min_b = inpStruct->b0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max_a = (max_a > inpStruct->a0) ? max_a : inpStruct->a0;
        min_b = (min_b < inpStruct->b0) ? min_b : inpStruct->b0;
    }
    outStruct->a = max_a;
    outStruct->b = min_b;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    A a0 = {};
    a0.propeNum = PROPENUM_A;
    a0.nullInfo = GmUdfMemAlloc(ctx, sizeof(uint8_t) * PROPENUM_A);
    for (int i = 0; i < PROPENUM_A; i++) {
        a0.nullInfo[i] = 1;
    }
    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
        a0.a = a->a + 100;
        a0.b = a->b + 100;
        a0.c = a->c + 100;
        a0.d = a->d + 100;
        a0.upgradeVersion = 0;
        a0.count = (a->count > 0) ? 1 : 0;
        ret = GmUdfAppend(writer, sizeof(A), &a0);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    GmUdfDestroyReader(ctx, reader);
    return GMERR_OK;
}

int32_t dtl_agg_func_ns2_u_funcA(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int16_t max_a = inpStruct->a0;
    int8_t min_b = inpStruct->b0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max_a = (max_a > inpStruct->a0) ? max_a : inpStruct->a0;
        min_b = (min_b < inpStruct->b0) ? min_b : inpStruct->b0;
    }
    outStruct->a = max_a;
    outStruct->b = min_b;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    A a0 = {};
    a0.propeNum = PROPENUM_A;
    a0.nullInfo = GmUdfMemAlloc(ctx, sizeof(uint8_t) * PROPENUM_A);
    for (int i = 0; i < PROPENUM_A; i++) {
        a0.nullInfo[i] = 1;
    }
    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
        a0.a = a->a;
        a0.b = a->b + 100;
        a0.c = a->c + 100;
        a0.d = a->d + 100;
        a0.upgradeVersion = 0;
        a0.count = (a->count > 0) ? 1 : 0;
        ret = GmUdfAppend(writer, sizeof(A), &a0);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_ns2_up_funcA(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int16_t max_a = inpStruct->a0;
    int8_t min_b = inpStruct->b0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max_a = (max_a > inpStruct->a0) ? max_a : inpStruct->a0;
        min_b = (min_b < inpStruct->b0) ? min_b : inpStruct->b0;
    }
    outStruct->a = max_a;
    outStruct->b = min_b;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    A a0 = {};
    a0.propeNum = PROPENUM_A;
    a0.nullInfo = GmUdfMemAlloc(ctx, sizeof(uint8_t) * PROPENUM_A);
    for (int i = 0; i < PROPENUM_A; i++) {
        a0.nullInfo[i] = 1;
    }
    a0.nullInfo[4] = a0.nullInfo[5] = 0;
    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
        a0.a = a->a;
        a0.b = a->b + 1000;
        a0.c = 0;
        a0.d = 0;
        a0.upgradeVersion = 0;
        a0.count = (a->count > 0) ? 1 : 0;
        ret = GmUdfAppend(writer, sizeof(A), &a0);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    GmUdfDestroyReader(ctx, reader);
    return GMERR_OK;
}

int32_t dtl_agg_func_ns2_d_funcA(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int16_t max_a = inpStruct->a0;
    int8_t min_b = inpStruct->b0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max_a = (max_a > inpStruct->a0) ? max_a : inpStruct->a0;
        min_b = (min_b < inpStruct->b0) ? min_b : inpStruct->b0;
    }
    outStruct->a = max_a;
    outStruct->b = min_b;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    GmUdfWriterT *writer = NULL;
    ret = GmUdfGetDeltaWriter(ctx, 0, &writer);
    if (ret != GMERR_OK) {
        return ret;
    }

    A a0 = {};
    a0.propeNum = PROPENUM_A;
    a0.nullInfo = GmUdfMemAlloc(ctx, sizeof(uint8_t) * PROPENUM_A);
    for (int i = 0; i < PROPENUM_A; i++) {
        a0.nullInfo[i] = 1;
    }
    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
        a0.a = a->a;
        a0.b = a->b + 100;
        a0.c = a->c + 100;
        a0.d = a->d + 100;
        a0.upgradeVersion = 0;
        a0.count = (a->count > 0) ? -1 : 0;
        ret = GmUdfAppend(writer, sizeof(A), &a0);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}

int32_t dtl_agg_func_ns3_funcA(GmUdfReaderT *input, uint32_t *outputLen, void **output, GmUdfCtxT *ctx)
{
    FuncAggIn *inpStruct;
    FuncAggOut *outStruct = GmUdfMemAlloc(ctx, sizeof(FuncAggOut));
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    int32_t ret = GmUdfGetNext(input, (void **)&inpStruct);
    if (ret != GMERR_OK) {
        return ret;
    }
    int16_t max_a = inpStruct->a0;
    int8_t min_b = inpStruct->b0;
    while (ret = GmUdfGetNext(input, (void **)&inpStruct), ret == GMERR_OK) {
        max_a = (max_a > inpStruct->a0) ? max_a : inpStruct->a0;
        min_b = (min_b < inpStruct->b0) ? min_b : inpStruct->b0;
    }
    outStruct->a = max_a;
    outStruct->b = min_b;
    *outputLen = sizeof(*outStruct);
    *output = (void *)outStruct;

    GmUdfReaderT *reader = NULL;
    ret = GmUdfCreateCurrentReader(ctx, 0, &reader);
    if (ret != GMERR_OK) {
        return ret;
    }

    A *a = NULL;
    while (ret = GmUdfGetNext(reader, (void **)&a), ret == GMERR_OK) {
    }

    GmUdfDestroyReader(ctx, reader);

    return GMERR_OK;
}
