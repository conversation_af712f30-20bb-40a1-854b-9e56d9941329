// right table is resource
namespace ns4 {
%table A1(a: int8, b: int4, c: int2, d: int1) {
    update, index(0(a))}
%table A2(a: int8, b: int4, c: int2, d: int1) {
    update, index(0(a, b))}
%resource B01(a: int8, b: int4 -> rc: int4) {sequential(max_size(1000000))}
%resource B02(a: int8, b: int4 -> rc: int4) {sequential(max_size(1000000))}
%resource C01(a: int8, b: int4, c: int2, d: int1 -> rc: int4) {sequential(max_size(1000000))}
%resource C02(a: int8, b: int4, c: int2, d: int1 -> rc: int4) {sequential(max_size(1000000))}
%table B1(a: int8, b: int4) {}
%table B2(a: int8, b: int4) {}
%table C1(a: int8, b: int4, c: int2, d: int1) {}
%table C2(a: int8, b: int4, c: int2, d: int1) {}

B01(a, b, -) :- B1(a, b).
B02(a, b, -) :- B2(a, b).
C01(a, b, c, d, -) :- C1(a, b, c, d).
C02(a, b, c, d, -) :- C2(a, b, c, d).
A1(a, b, 1, 1) :- B01(a, b, -), B02(a, b, -).
A2(a, b, c, d) :- C01(a, b, c, d, -), C02(a, b, c, d, -).
}
