%table A(k: str, p: int4, v: int4)
%table B(max: int4, min: int4, k: str)
%aggregate min_max(v: int4 -> min: int4, max: int4){
      ordered
}
B(max, min, k) :- A(k, 1, v) GROUP-BY (k) min_max(v, min, max).
null(0) :- B(max, min, k).

%table C(a: str, b: str, c: int4)
%table D(a: str, c: int4)
%aggregate getSum(c: int4 -> sum: int4)
D(a, sum) :- C(a, "b00000001", c) GROUP-BY (a) getSum(c, sum).
null(0) :- D(a, sum).

%table I(a: str, b: byte4, c: int4, d: int4, e: int4)
%table J(max: int4, min: int4, c: int4)
J(max, min, c) :- I("a00000001", "0x01010101", c, d, 1) GROUP-BY (c) min_max(d, min, max).
null(0) :- J(max, min, c).
