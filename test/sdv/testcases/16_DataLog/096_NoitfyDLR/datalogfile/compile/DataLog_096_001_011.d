%table inp1(a1:int1, a2:int1, a3:int1, a4:int1, b1:int1, b2:int1, b3:int1, b4:int1, c1:int1, c2:int1, c3:int1, c4:int1, c5:int1, c6:int1, c7:int1, c8:int1, c9:int1, c10:int1,
a11:int1, a21:int1, a31:int1, a41:int1, b11:int1, b21:int1, b31:int1, b41:int1, c11:int1, c21:int1, c31:int1, c41:int1,c51:int1, c61:int1, c71:int1, c81:int1, c91:int1, c101:int1,
a12:int1, a22:int2, a32:int4, a42:int8, b12:uint1, b22:uint2, b32:uint4, b42:uint8, c12:byte1, c22:byte2, c32:byte4, c42:byte8,c52:byte16, c62:byte32, c72:byte64, c82:byte128, c92:byte256, c102:byte512,
d9:str, d10:byte)
{index(0(a1, a2, a3, a4, b1, b2, b3, b4, c1, c2, c3, c4, c5, c6, c7, c8, c9, c10, a11, a21, a31, a41, b11, b21, b31, b41, c11, c21, c31, c41, c51)), index(1(a1, a2, a3, a4, b1, b2, b3, b4, c1, c2, c3, c4, c5, c6, c7, c8, c9, c10))}

%table notifytable(a1:int1, a2:int1, a3:int1, a4:int1, b1:int1, b2:int1, b3:int1, b4:int1, c1:int1, c2:int1, c3:int1, c4:int1, c5:int1, c6:int1, c7:int1, c8:int1, c9:int1, c10:int1,
a11:int1, a21:int1, a31:int1, a41:int1, b11:int1, b21:int1, b31:int1, b41:int1, c11:int1, c21:int1, c31:int1, c41:int1,c51:int1, c61:int1, c71:int1, c81:int1, c91:int1, c101:int1,
a12:int1, a22:int2, a32:int4, a42:int8, b12:uint1, b22:uint2, b32:uint4, b42:uint8, c12:byte1, c22:byte2, c32:byte4, c42:byte8,c52:byte16, c62:byte32, c72:byte64, c82:byte128, c92:byte256, c102:byte512,
d9:str, d10:byte)
{index(0(a1, a2, a3, a4, b1, b2, b3, b4, c1, c2, c3, c4, c5, c6, c7, c8, c9, c10, a11, a21, a31, a41, b11, b21, b31, b41, c11, c21, c31, c41, c51)), index(1(a1, a2, a3, a4, b1, b2, b3, b4, c1, c2, c3, c4, c5, c6, c7, c8, c9, c10)), notify, update, data_sync_label(true), status_merge_sub(false)}


notifytable(a1, a2, a3, a4, b1, b2, b3, b4, c1, c2, c3, c4, c5, c6, c7, c8, c9, c10,a11, a21, a31, a41, b11, b21, b31, b41, c11, c21, c31, c41, c51, c61, c71, c81, c91, c101,
a12, a22, a32, a42, b12, b22, b32, b42, c12, c22, c32, c42, c52, c62, c72, c82, c92, c102, d9, d10) :- inp1(a1, a2, a3, a4, b1, b2, b3, b4, c1, c2, c3, c4, c5, c6, c7, c8, c9, c10,
a11, a21, a31, a41, b11, b21, b31, b41, c11, c21, c31, c41, c51, c61, c71, c81, c91, c101, a12, a22, a32, a42, b12, b22, b32, b42, c12, c22, c32, c42, c52, c62, c72, c82, c92, c102, d9, d10).
