/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include <stdio.h>
#include "gm_udf.h"

#pragma pack(1)
typedef struct Tuple {
    int p;
    int s;
    int count;
} TupleT;

typedef struct A {
    int32_t a;
    int32_t b;
    int32_t count;
} A;
#pragma pack(0)

int32_t dtl_ext_func_tran(void *tuple, GmUdfCtxT *ctx)
{
    TupleT *result = (TupleT *)tuple;
    int *p = &result->p;
    int *s = &result->s;
    int *count = &result->count;

    // 对应的状态变化
    // if p == 0 && count == 1 && s == 1, s = 0;
    // if p == 1 && count == 1 && s == 0, s = 1;
    // if p == 2 && count == 0, init, s = 1, count = 1;
    // if p == 3 && count == 1, delete, count = -1;
    if (*count == 0) {
        if (*p == 2) {
            *s = 1;
            *count = 1;
            
            return GMERR_OK;
        }
    } else if (*count == 1) {
        if (*s == 1 && *p == 0) {
            *s = 0;
            return GMERR_OK;
        }
        if (*s == 0 && *p == 1) {
            *s = 1;
            return GMERR_OK;
        }
        if (*p == 3) {
            *count = -1;
            return GMERR_OK;
        }
    } else {
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_NO_DATA;
}

const char *g_logName = "/root/_datalog_/TbmRunLog.txt";

int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    FILE* fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "[%s] dtl_ext_func_init.\n", __FILE__);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    FILE* fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "[%s] dtl_ext_func_uninit.\n", __FILE__);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbl_C(uint32_t op, void *tuple)
{
    FILE* fp = fopen(g_logName, "a");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "[%s] dtl_tbm_tbl_outC, op = %d, a = %d.\n",
        __FILE__, op, ((A *)tuple)->a);
    (void)fclose(fp);
    return GMERR_OK;
}
