# 用于测试datalog加载so文件：
# $1 输入 测试类型：并发加载同个文件/并发加载不同文件/加载中动态内存/端到端时间
# $2 输入 文件名（*.so）
# $3 输入 并发进程数

ARCH=$1
if [ "x" = "x${ARCH}" ] || [ "xh" = "x${ARCH}" ] || [ "X${ARCH}" = "X-h" ] || [ $# -lt 2 ]; then
    echo ">> support arm64 "
    echo ">>useage:arg1 [input testType : same/diff/mem/time]"
    echo ">>       arg2 [input libName(*.so)]"
    echo ">>       arg3 [input multiCount], if testType is same or diff"
    exit 1
fi

if [ x"$1" = x"same" ]; then
    testType=1
elif [ x"$1" = x"diff" ]; then
    testType=2
elif [ x"$1" = x"mem" ]; then
    testType=3
elif [ x"$1" = x"time" ]; then
    testType=4
elif [ x"$1" = x"unistall" ]; then
    testType=5
elif [ x"$1" = x"unst" ]; then
    testType=6
elif [ x"$1" = x"unistalldiff" ]; then
    testType=7
elif [ x"$1" = x"unstdiff" ]; then
    testType=8
else
    echo ">> check parameters, arg1 [input testType : same/diff/mem/time]"
    exit 1
fi

log_dir=`cat sysconfig.txt |grep logDir |awk -F '[:]' '{print $2}' |sed 's/ //g'`
mkdir -p $log_dir
lib_dir=`cat sysconfig.txt |grep libDir |awk -F '[:]' '{print $2}' |sed 's/ //g'`
mkdir -p $lib_dir

libName=${lib_dir}/$2
libUnistallName=$2
# if [ ! -f ${libName} ]; then
#     echo ">> file ${libName} is no exist."
#     exit 1
# fi

if [ $testType -eq 1 ] || [ $testType -eq 2 ]; then
    if [ x"$3" = "x" ]; then
        echo ">> check parameters, need arg3 [input multiCount]"
        exit 1
    fi
fi
multiCount=$3

if [ $testType -eq 1 ]; then
    for i in `seq 1 $multiCount`; do
        gmimport -c datalog -f $libName &
    done
    wait
elif [ $testType -eq 2 ]; then
    libNamePart=`echo $libName |awk -F '[.]' '{print $1}'`
    libNameType=`echo $libName |awk -F '[.]' '{print $2}'`
    for i in `seq 1 $multiCount`; do
        t_libName="${libNamePart}_$i.${libNameType}"
        cp $libName $t_libName
        gmimport -c datalog -f $t_libName &
    done
    wait
    copyfile="${libNamePart}_*.${libNameType}"
    rm -rf $copyfile
elif [ $testType -eq 3 ]; then
    # CTX_NAME:
    # Datalog load
    # Datalog service
    gmimport -c datalog -f $libName &
    cycleTimes=10
    interTime=0
    > ${log_dir}/memCat.txt
    for i in `seq 1 $cycleTimes`; do
        gmsysview -q 'V$COM_DYN_CTX' > tmp.txt
        echo "[test][info] num $i" >> ${log_dir}/memCat.txt
        cat tmp.txt |grep "Datalog" -B 4 -A 16 >> ${log_dir}/memCat.txt
        sleep $interTime
    done
    wait
    gmsysview -q 'V$COM_DYN_CTX' > tmp.txt
    echo "[test][info] last " >> ${log_dir}/memCat.txt
    cat tmp.txt |grep "Datalog" -B 4 -A 16 >> ${log_dir}/memCat.txt

    echo ">> save sysview message, file: ${log_dir}/memCat.txt"
elif [ $testType -eq 4 ]; then
    time1=`date +"%s.%N"`
    gmimport -c datalog -f $libName
    time2=`date +"%s.%N"`

    timePartS=`echo $time2 |awk -F '[.]' '{print $1}'`
    tmp=`echo $time1 |awk -F '[.]' '{print $1}'`
    timePartS=`expr $timePartS - $tmp`

    timePartN=`echo $time2 |awk -F '[.]' '{print $2}'`
    tmp=`echo $time1 |awk -F '[.]' '{print $2}'`
    timePartN=`expr $timePartN - $tmp`

    costMs=`awk -v x=$timePartS -v y=$timePartN 'BEGIN{printf ("%.2f",x*1000+y/1000000)}'`

    echo ">> cost: load datalog using $costMs ms."
elif [ $testType -eq 5 ]; then
    for i in `seq 1 $multiCount`; do
        gmimport -c datalog -d $libUnistallName &
    done
    wait
elif [ $testType -eq 6 ]; then
    for i in `seq 1 $multiCount`; do
        gmimport -c datalog -f ./base_prefile/normal.so &
        gmimport -c datalog -d $libUnistallName &
    done
    wait
elif [ $testType -eq 7 ]; then
    for i in `seq 1 $multiCount`; do
        gmimport -c datalog -d $libUnistallName &
        gmimport -c datalog -d allFieldType &
    done
    wait
elif [ $testType -eq 8 ]; then
    for i in `seq 1 $multiCount`; do
        gmimport -c datalog -f ./base_prefile/allFieldType.so &
        gmimport -c datalog -d $libUnistallName &
    done
    wait
fi
echo ">> test finish. "
