/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2023-10-16
 */
#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)

typedef struct B {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
    int64_t c;
} B;

#pragma pack(0)

int32_t dtl_ext_func_func(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a * b->b;
    return GMERR_OK;
}
