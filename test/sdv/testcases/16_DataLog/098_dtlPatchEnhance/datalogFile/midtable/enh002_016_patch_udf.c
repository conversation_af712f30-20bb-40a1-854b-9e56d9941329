/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: yang<PERSON><PERSON> ywx1060383
 * Create: 2024-04-07
 */

#include <stdio.h>
#include <string.h>
#include "gm_udf.h"

#pragma pack(1)
typedef struct {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
    int64_t c;
} TupleT;
#pragma pack(0)

const char *g_logInsertName = "/root/dtlpatchenh.txt";

int32_t dtl_ext_func_ns1_func1(void *tuple, GmUdfCtxT *ctx)
{
    FILE *fp1 = fopen(g_logInsertName, "a+");
    if (fp1 == NULL) {
        return -10003;
    }
    (void)fprintf(fp1, "dtl_ext_func_func1, a=%d, b=%d, c=%d, dtlReservedCount=%d.\n", ((<PERSON>pleT *)tuple)->a,
        ((TupleT *)tuple)->b, ((TupleT *)tuple)->c, ((TupleT *)tuple)->dtlReservedCount);
    (void)fclose(fp1);
    return GMERR_OK;
}

int32_t dtl_ext_func_ns1_func2(void *tuple, GmUdfCtxT *ctx)
{
    FILE *fp1 = fopen(g_logInsertName, "a+");
    if (fp1 == NULL) {
        return -10003;
    }
    (void)fprintf(fp1, "dtl_ext_func_func2, a=%d, b=%d, c=%d, dtlReservedCount=%d.\n", ((TupleT *)tuple)->a,
        ((TupleT *)tuple)->b, ((TupleT *)tuple)->c, ((TupleT *)tuple)->dtlReservedCount);
    (void)fclose(fp1);
    return GMERR_OK;
}

int32_t dtl_ext_func_ns1_func3(void *tuple, GmUdfCtxT *ctx)
{
    FILE *fp1 = fopen(g_logInsertName, "a+");
    if (fp1 == NULL) {
        return -10003;
    }
    (void)fprintf(fp1, "dtl_ext_func_func3, a=%d, b=%d, c=%d, dtlReservedCount=%d.\n", ((TupleT *)tuple)->a,
        ((TupleT *)tuple)->b, ((TupleT *)tuple)->c, ((TupleT *)tuple)->dtlReservedCount);
    (void)fclose(fp1);
    return GMERR_OK;
}

int32_t dtl_ext_func_ns1_func4(void *tuple, GmUdfCtxT *ctx)
{
    FILE *fp1 = fopen(g_logInsertName, "a+");
    if (fp1 == NULL) {
        return -10003;
    }
    (void)fprintf(fp1, "dtl_ext_func_func4, a=%d, b=%d, c=%d, dtlReservedCount=%d.\n", ((TupleT *)tuple)->a,
        ((TupleT *)tuple)->b, ((TupleT *)tuple)->c, ((TupleT *)tuple)->dtlReservedCount);
    (void)fclose(fp1);
    return GMERR_OK;
}

int32_t dtl_ext_func_ns1_func5(void *tuple, GmUdfCtxT *ctx)
{
    FILE *fp1 = fopen(g_logInsertName, "a+");
    if (fp1 == NULL) {
        return -10003;
    }
    (void)fprintf(fp1, "dtl_ext_func_func5, a=%d, b=%d, c=%d, dtlReservedCount=%d.\n", ((TupleT *)tuple)->a,
        ((TupleT *)tuple)->b, ((TupleT *)tuple)->c, ((TupleT *)tuple)->dtlReservedCount);
    (void)fclose(fp1);
    return GMERR_OK;
}
