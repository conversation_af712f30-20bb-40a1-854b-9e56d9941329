/*  版权所有 (c) 华为技术有限公司 2022-2023 */
#include "gm_udf.h"
#include "stdio.h"

#pragma pack(1)

typedef struct Out1 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} Out1;

#pragma pack(0)

const char *g_logName = "/root/_datalog_/TbmRunLog.txt";

int32_t dtl_tbm_tbl_ns2_newout2(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a+");
    Out1 *tmp = (Out1 *)tuple;
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_tbm_tbl_ns2_newout2, op = %d, a = %ld, b = %ld, c = %ld, dtlReservedCount = %d,"
        "upgradeVersion = %d.\n", op, tmp->a, tmp->b, tmp->c, tmp->dtlReservedCount, tmp->upgradeVersion);
    (void)fclose(fp);
    return GMERR_OK;
}
