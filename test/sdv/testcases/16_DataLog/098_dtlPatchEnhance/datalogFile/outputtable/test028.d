%version v1.0.0

namespace ns1 {
    %table inp1(a:int8, b:int8, c:int8) {index(0(a, b, c)), timeout(field(c), state_function)}
    %table inp2(a:int8, b:int8, c:int8) {index(0(a, b, c)), transient(tuple)}
    %table inp3(a:int8, b:int8, c:int8) {index(0(a, b, c)), transient(finish)}
    %table inp4(a:int8, b:int8, c:int8) {index(0(a, b, c))}
    %function func(a:int8, b:int8, c:int8) {access_current(ns2.out2), access_delta(inp4)}
    ns2.out1(a, b, c) :- inp1(a, b, c), func(a, b, c).

    ns2.out2(a, b, c) :- inp4(a, b, c).

    null(0) :- inp2(a, b, c).
    null(0) :- inp3(a, b, c).
}

namespace ns2 {
    %readwrite out1, out2
    %table out1(a:int8, b:int8, c:int8) {index(0(a, b, c))}
    %table out2(a:int8, b:int8, c:int8) {index(0(a, b, c))}
}
