%version v1.0.0 -> v1.0.1
%block 0
namespace ns1{
    %table out2(a:int8, b:int8, c:int8)
    %rule rN ns2.mid1(a, b, c) :- ns2.inp1(a, b, c), func2(a, b, c).
    %rule rN1 out2(a, b, c) :- ns2.mid1(a, b, c).
    %alter rule r1 out1(a, b, c) :- mid1(a, b, c), ns2.mid1(a, b, c).
}

namespace ns2 {
    %table inp1(a:int8, b:int8, c:int8) {update, index(0(a, b))}
    %table mid1(a:int8, b:int8, c:int8)
    %readwrite mid1
    %readonly inp1
}
