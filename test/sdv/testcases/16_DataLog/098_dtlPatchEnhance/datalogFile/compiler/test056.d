%version v1.0.0
namespace ns1 {
    %table inp1(a:int8, b:int8, c:int8) {index(0(a, b))}
    %table inp2(a:int8, b:int8, c:int8) {index(0(a, b))}
    %table inp3(a:int8, b:int8, c:int8, d:int8) {index(0(a, b)), update}
    %table mid1(a:int8, b:int8, c:int8)
    %table mid2(a:int8, b:int8, c:int8)
    %table mid3(a:int8, b:int8, c:int8, d:int8) {index(0(a, b))}
    %table out1(a:int8, b:int8, c:int8) {
        index(0(a, b)), tbm
    }

    %table out2(a:int8, b:int8, c:int8, d:int8) {
        index(0(a, b)), notify
    }
    
    %function func1(a:int8, b:int8, c:int8) {access_delta(inp2)}
    %function func2(a:int8, b:int8, c:int8)

    %function init()
    %function uninit()

    mid1(a, b, c) :- inp1(a, b, c), func1(a, b, c), inp3(a, b, c, -), func2(a, b, c).
    out1(a, b, c) :- mid1(a, b, c).

    mid2(a, b, c) :- inp2(a, b, c).
    out1(a, b, c) :- mid2(a, b, c), func1(a, b, c).

    mid3(a, b, sum, 100) :- inp3(a, b, c, -) GROUP-BY(a, b) ns2.agg(c, sum).
    out2(a, b, c, d) :- mid3(a, b, c, d).

    mid3(a, b, d, e) :- inp3(a, b, c, -) GROUP-BY(a, b) ns2.agg2(c, d, e).
    out2(a, b, c, d) :- mid3(a, b, c, d).

    null(0) :- inp3(a, b, c, d).

    %readonly inp1, mid2
}

namespace ns2 {

    %aggregate agg(a:int8 -> b:int8) {
        ordered,
        access_current(ns1.mid2)
    }

    %aggregate agg2(a:int8 -> b:int8, c:int8) {
        ordered,
        access_delta(ns1.inp1),
        many_to_many
    }

    %readwrite agg, agg2
}
