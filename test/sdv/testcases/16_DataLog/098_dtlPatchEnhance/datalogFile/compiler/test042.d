%version v1.0.0
namespace ns1 {
    %table inp1(a:int8, b:int8, c:int8) {index(0(a, b))}
    %table inp2(a:int8, b:int8, c:int8) {index(0(a, b))}
    %table inp3(a:int8, b:int8, c:int8) {index(0(a, b))}
    %table mid1(a:int8, b:int8, c:int8)
    %table mid2(a:int8, b:int8, c:int8)
    %table out1(a:int8, b:int8, c:int8) {
        index(0(a, b)), tbm
    }
    
    %function init()
    %function uninit()

    %function func1(a:int8, b:int8, c:int8) {access_delta(inp2)}
    %function func2(a:int8, b:int8, c:int8)

    mid1(a, b, c) :- inp1(a, b, c), func1(a, b, c), inp3(a, b, c), func2(a, b, c).
    out1(a, b, c) :- mid1(a, b, c).

    mid2(a, b, c) :- inp2(a, b, c).
    out1(a, b, c) :- mid2(a, b, c), func1(a, b, c).

    null(0) :- inp3(a, b, c).
}

namespace ns2 {

}
