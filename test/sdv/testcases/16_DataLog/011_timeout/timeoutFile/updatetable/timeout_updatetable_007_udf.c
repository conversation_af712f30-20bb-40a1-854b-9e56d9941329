/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalog.h
 * Description: datalog compilation
 * Author: jiangshan/j00811785
 * Create: 2022-10-18
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
} A;
#pragma pack(0)

int32_t dtl_timeout_callback_A(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple,
    GmUdfCtxT *ctx)
{
    int ret;
    // 返回记录：过期记录相关，修改过期字段值
    A *input = (A*)timeoutTuple;
    A *output = GmUdfMemAlloc(ctx, sizeof(A));
    if (output == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    output->a = input->a;
    output->b = input->b;
    output->c = input->c;
    // 缩减时间记录一直在过期，记录校验不稳定
    output->d = input->d;
    output->dtlReservedCount = 1;
    *extraTuple = (void *)output;
    *extraTupleLen = sizeof(A);

    return GMERR_OK;
}
