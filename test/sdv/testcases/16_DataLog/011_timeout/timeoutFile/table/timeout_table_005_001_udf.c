/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: datalog.h
 * Description: datalog compilation
 * Author: jiangshan/j00811785
 * Create: 2022-10-18
 */

#include "gm_udf.h"

#pragma pack(1)
typedef struct A {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
} A;
#pragma pack(0)

int32_t dtl_timeout_callback_A(const void *timeoutTuple, uint32_t *extraTupleLen, void **extraTuple,
    GmUdfCtxT *ctx)
{
    int ret;

    A *input = (A*)timeoutTuple;
    A *output = GmUdfMemAlloc(ctx, sizeof(A));
    if (output == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    output->a = input->a +  1;
    output->b = input->b + 1;
    output->c = input->c + 1;
    output->d = input->d + (int64_t)(2 * 60 * 60 * 1000);
    if (input->a == 10) {
        output->dtlReservedCount = -input->dtlReservedCount;
    } else {
        output->dtlReservedCount = input->dtlReservedCount;
    }
    
    *extraTuple = (void *)output;
    *extraTupleLen = sizeof(A);

    return GMERR_OK;
}
