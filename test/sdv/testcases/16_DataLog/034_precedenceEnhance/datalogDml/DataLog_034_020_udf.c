/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include <stdio.h>
#include "gm_udf.h"
#include "assert.h"
#pragma pack(1)

#define INSERT 0
#define DELETE 1
#define UPDATE 2
typedef struct {
    int32_t a;
    int32_t b;
    int32_t dtlReservedCount;
} TupleA;

typedef struct {
    uint32_t op;
    void *oldTup;
    void *newTup;
} Batch;

#pragma pack(0)

const char *g_logName = "/root/_datalog_/msgNotifyRunLog.txt";

int32_t dtl_msg_notify_A(GmMsgNotifyTupleChangeT *tups, uint32_t arrLen)
{
    FILE* fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return 0;
    }
    for (uint32_t i = 0; i < arrLen; ++i) {
        uint32_t op = tups[i].op;
        TupleA *oldTup = (TupleA *)(tups[i].oldTup);
        TupleA *newTup = (TupleA *)(tups[i].newTup);
        if (op == INSERT) {
            (void)fprintf(fp, "[%s] dtl_msg_notify_A insert a: %d, b: %d, count: %d, op = %d, \n",
            __FILE__, newTup->a, newTup->b, newTup->dtlReservedCount, tups[i].op);
        } else if (op == DELETE) {
            (void)fprintf(fp, "[%s] dtl_msg_notify_A deltete a: %d, b: %d, count: %d, op = %d, \n",
            __FILE__, oldTup->a, oldTup->b, oldTup->dtlReservedCount, tups[i].op);
        } else if (op == UPDATE) {
            (void)fprintf(fp, "[%s] dtl_msg_notify_A UPDATE a: %d, b: %d, count: %d, op = %d, \n",
            __FILE__, oldTup->a, oldTup->b, oldTup->dtlReservedCount, tups[i].op);
            (void)fprintf(fp, "[%s] dtl_msg_notify_A UPDATE a: %d, b: %d, count: %d, op = %d, \n",
            __FILE__, newTup->a, newTup->b, newTup->dtlReservedCount, tups[i].op);
        }
    }
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_msg_notify_B(GmMsgNotifyTupleChangeT *tups, uint32_t arrLen)
{
    FILE* fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return 0;
    }
    for (uint32_t i = 0; i < arrLen; ++i) {
        uint32_t op = tups[i].op;
        TupleA *oldTup = (TupleA *)(tups[i].oldTup);
        TupleA *newTup = (TupleA *)(tups[i].newTup);
        if (op == INSERT) {
            (void)fprintf(fp, "[%s] dtl_msg_notify_B insert a: %d, b: %d, count: %d, op = %d, \n",
            __FILE__, newTup->a, newTup->b, newTup->dtlReservedCount, tups[i].op);
        } else if (op == DELETE) {
            (void)fprintf(fp, "[%s] dtl_msg_notify_B deltete a: %d, b: %d, count: %d, op = %d, \n",
            __FILE__, oldTup->a, oldTup->b, oldTup->dtlReservedCount, tups[i].op);
        } else if (op == UPDATE) {
            (void)fprintf(fp, "[%s] dtl_msg_notify_B UPDATE a: %d, b: %d, count: %d, op = %d, \n",
            __FILE__, oldTup->a, oldTup->b, oldTup->dtlReservedCount, tups[i].op);
            (void)fprintf(fp, "[%s] dtl_msg_notify_B UPDATE a: %d, b: %d, count: %d, op = %d, \n",
            __FILE__, newTup->a, newTup->b, newTup->dtlReservedCount, tups[i].op);
        }
    }
    (void)fclose(fp);
    return GMERR_OK;
}
