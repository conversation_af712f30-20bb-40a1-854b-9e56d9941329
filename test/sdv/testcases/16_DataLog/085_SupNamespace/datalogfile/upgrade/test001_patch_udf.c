/*  版权所有 (c) 华为技术有限公司 2024-2024 */
#include "gm_udf.h"
#include "stdio.h"

#pragma pack(1)

typedef struct Out1 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
} Out1;

typedef struct B {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
    int64_t c;
} B;

#pragma pack(0)

const char *g_logName = "/root/_datalog_/TbmRunLog.txt";
const char *g_logfuncName = "/root/_datalog_/funcLog.txt";

int32_t dtl_ext_func_func2(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    return GMERR_OK;
}

int32_t dtl_ext_func_func(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    b->c = b->a + b->b;
    return GMERR_OK;
}
