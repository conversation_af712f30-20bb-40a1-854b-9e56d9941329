%function init()
%function uninit()

// msg_notify
%table A2(a:int8, b:int8, c:int8, d:int8) {
    index(0(a))
}
%table B2(a:int8, b:int8, c:int8, d:int8) {
    index(0(a)),
    msg_notify,
    batch_msg_size(10)
}
B2(a, b, c, d) :- A2(a, b, c, d).

// function
%table A3(a:int4, b:int4)
%table B3(a:int4, b:int4)
%table C3(a:int4, b:int4)
%function func(a:int4 ->c:int4){
    access_delta(C3)
}
B3(a, b) :- A3(a, b), func(a, b).
B3(a, b) :- C3(a, b).

// aggregate
%table A4(a:int4, b:int4)
%table B4(a:int4, b:int4, c:int4)
%aggregate agg(a:int4 ->b:int4, c:int4){
    ordered
}
B4(max, min, a) :- A4(a, b) GROUP-BY(a) agg(b, min, max).
null(0) :- B4(max, min, a).

// pubsub型资源表
%table A5(a: int4)
%table B5(a: int4, b: int4)
%resource pubsubrsc(a: int4 -> b: int4) { pending_id(-2) }
pubsubrsc(a, -) :- A5(a).
B5(a, b) :- pubsubrsc(a, b).

// 固定资源池资源表
%table A6(a: int4)
%table B6(a: int4, b: int4)
%resource seqrsc(a: int4 -> b: int4) { sequential(max_size(2)) }
seqrsc(a, -) :- A6(a).
B6(a, b) :- seqrsc(a, b).
