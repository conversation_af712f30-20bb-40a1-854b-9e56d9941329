/*****************************************************************************
 Description  : 对账对象权限check   对账不校验对象权限  2021/08/03
 Notes        :
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2021/07/08
*****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "ObjPrivsTest.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

char *config_json = NULL;
char *normal_vertexlabel_schema = NULL;

const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";

#define MAX_CMD_SIZE 2048
char g_command[MAX_CMD_SIZE] = {0};

class SEC_004_ObjPrivsAccount : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        printf("[INFO] ObjPrivsAccount Start.\n");
        system("sh $TEST_HOME/tools/stop.sh -f");
        //权限校验改为强制模式
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
        //拉起server
        system("sh $TEST_HOME/tools/start.sh -f");
        //环境初始化
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        readJanssonFile("schemaFile/config/configMS.gmconfig", &config_json);
        ASSERT_NE((void *)NULL, config_json);
        readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
        ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
        
    }
    static void TearDownTestCase()
    {
        //恢复校验模式
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        free(config_json);
        free(normal_vertexlabel_schema);
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SEC_004_ObjPrivsAccount::SetUp()
{
    //导入白名单
    const char *allow_list_file = "schemaFile/allow_list/account_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    int ret = executeCommand(g_command, "Import single allow list file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    //导入必备系统权限 create get drop
    const char *sys_policy_file = "schemaFile/account_policy/sysVertex.gmpolicy";
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, sys_policy_file, g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}

void SEC_004_ObjPrivsAccount::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    const char *allow_list_file = "schemaFile/allow_list/account_allow_list.gmuser";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath, allow_list_file,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "remove allowlist, remove db user. success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    AW_CHECK_LOG_END();
    printf("[INFO] ObjPrivsAccount End.\n");
}

// 001.Vertex对象权限Insert校验，同步插入，权限导入前返回错误码，导入后操作成功
TEST_F(SEC_004_ObjPrivsAccount, SEC_004_ObjPrivsAccount_001)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schemaFile/account_policy/objNoDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant
    AccountCheck(g_normal_vertexlabel_name, g_normal_pk_name, GMC_FULL_TABLE, GMERR_OK);

    // grant delete
    const char *obj_policy_file2 = "schemaFile/account_policy/objDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // check account
    AccountCheck(g_normal_vertexlabel_name, g_normal_pk_name, GMC_FULL_TABLE);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.导入delete对象权限，操作完整的分区对账流程
TEST_F(SEC_004_ObjPrivsAccount, SEC_004_ObjPrivsAccount_002)
{
    int ret = 0;
    int count = 15;
    int expectAffectRows = 1;
    void *vertexLabel = NULL;
    char *labelJson = NULL;
    readJanssonFile("schemaFile/PartitionVertexLabel.gmjson", &labelJson);
    ASSERT_NE((void *)NULL, labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelJson);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schemaFile/account_policy/objNoDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        uint8_t partition_id = 0;
        ret = GmcSetVertexProperty(g_stmt, "F9", GMC_DATATYPE_PARTITION, &partition_id, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant
    AccountCheck(g_normal_vertexlabel_name, g_normal_pk_name, 0, GMERR_OK);

    // grant delete
    const char *obj_policy_file2 = "schemaFile/account_policy/objDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    // check account
    AccountCheck(g_normal_vertexlabel_name, g_normal_pk_name, 0);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file2,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.无delete对象权限，调用GmcBeginCheck接口
TEST_F(SEC_004_ObjPrivsAccount, SEC_004_ObjPrivsAccount_003)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schemaFile/account_policy/objNoDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant
    ret = GmcBeginCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    bool isAbornormal = (ret != GMERR_OK);
    ret = GmcEndCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, isAbornormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004.无delete对象权限，调用GmcEndCheck接口
TEST_F(SEC_004_ObjPrivsAccount, SEC_004_ObjPrivsAccount_004)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schemaFile/account_policy/objNoDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant
    ret = GmcBeginCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE);
    EXPECT_EQ(GMERR_OK, ret);
    bool isAbornormal = (ret != GMERR_OK);
    ret = GmcEndCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, isAbornormal);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.无delete对象权限，调用GmcUpdateCheckVersion接口
TEST_F(SEC_004_ObjPrivsAccount, SEC_004_ObjPrivsAccount_005)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schemaFile/account_policy/objNoDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant
    ret = GmcBeginCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE);
    EXPECT_EQ(GMERR_OK, ret);

    // refresh version
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t pk = 0;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    bool isAbornormal = (ret != GMERR_OK);
    ret = GmcEndCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, isAbornormal);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 006.无delete对象权限，调用GmcGetCheckInfo接口
TEST_F(SEC_004_ObjPrivsAccount, SEC_004_ObjPrivsAccount_006)
{
    int ret = 0;
    int expectAffectRows = 1;
    int count = 100;
    void *vertexLabel = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // grant other
    const char *obj_policy_file1 = "schemaFile/account_policy/objNoDeleteVertex.gmpolicy";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "Import single policy file", "successfully.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    for (int i = 0; i < count; i++) {
        set_VertexProperty_F7(g_stmt, i);
        set_VertexProperty_F9(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, expectAffectRows);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // before grant
    ret = GmcBeginCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcCheckInfoT *checkInfo;
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckInfo(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, obj_policy_file1,
        g_connServer);
    printf("[INFO]%s\n", g_command);
    ret = executeCommand(g_command, "revoke policy. object privilege success: 1, warning: 0.");
    EXPECT_EQ(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));

    bool isAbornormal = (ret != GMERR_OK);
    ret = GmcEndCheck(g_stmt, g_normal_vertexlabel_name, GMC_FULL_TABLE, isAbornormal);
    EXPECT_EQ(GMERR_OK, ret);
    
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
}
