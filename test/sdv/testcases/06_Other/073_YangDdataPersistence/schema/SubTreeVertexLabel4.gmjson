[{"type": "container", "name": "ContainerOne", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string", "size": 8}, {"name": "F7", "type": "char", "default": "A"}, {"name": "F8", "type": "uchar", "nullable": true}, {"name": "F9", "type": "int8", "nullable": true}, {"name": "F10", "type": "uint8", "nullable": true}, {"name": "F11", "type": "int16", "nullable": true}, {"name": "F12", "type": "uint16", "nullable": true}, {"name": "F13", "type": "uint32", "nullable": true}, {"name": "F14", "type": "int64", "nullable": true}, {"name": "F15", "type": "uint64", "nullable": true}, {"name": "F16", "type": "time", "nullable": true}, {"name": "F17", "type": "uint8: 4", "nullable": true}, {"name": "F18", "type": "uint16: 15", "nullable": true}, {"name": "F19", "type": "uint32: 31", "nullable": true}, {"name": "F20", "type": "bytes", "size": 7, "nullable": true}, {"name": "F21", "type": "fixed", "size": 7, "nullable": true}, {"name": "F22", "type": "bitmap", "size": 128, "nullable": true}, {"name": "F23", "type": "uint64: 59", "nullable": true}, {"name": "F30", "type": "uint32"}, {"name": "F31", "type": "int32"}, {"name": "F32", "type": "boolean"}, {"name": "F33", "type": "double"}, {"name": "F34", "type": "boolean"}, {"name": "F35", "type": "float"}, {"name": "F36", "type": "string", "size": 8}, {"name": "F37", "type": "char", "default": "A"}, {"name": "F38", "type": "uchar", "nullable": true}, {"name": "F39", "type": "int8", "nullable": true}, {"name": "F310", "type": "uint8", "nullable": true}, {"name": "F311", "type": "int16", "nullable": true}, {"name": "F312", "type": "uint16", "nullable": true}, {"name": "F313", "type": "uint32", "nullable": true}, {"name": "F314", "type": "int64", "nullable": true}, {"name": "F315", "type": "uint64", "nullable": true}, {"name": "F316", "type": "time", "nullable": true}, {"name": "F317", "type": "uint8: 4", "nullable": true}, {"name": "F318", "type": "uint16: 15", "nullable": true}, {"name": "F319", "type": "uint32: 31", "nullable": true}, {"name": "F320", "type": "bytes", "size": 7, "nullable": true}, {"name": "F321", "type": "fixed", "size": 7, "nullable": true}, {"name": "F322", "type": "bitmap", "size": 128, "nullable": true}, {"name": "F323", "type": "uint64: 59", "nullable": true}, {"name": "F324", "type": "int32", "nullable": true}, {"type": "container", "name": "ContainerTwo", "presence": true, "clause": [{"type": "when", "formula": "/ContainerOne/ListOne[F0=$TARGET_DS]/F1 = 50"}], "fields": [{"name": "F0", "type": "uint32", "default": 3, "clause": [{"type": "leafref", "formula": "../../F0"}]}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "Container<PERSON>hree", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ContainerFour", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "choice", "name": "Choice", "fields": [{"type": "case", "name": "CaseOne", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}], "keys": [{"node": "ContainerOne", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "ListOne", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "int16", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ListContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ListContainerthree", "presence": false, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "choice", "name": "Listchoice", "fields": [{"type": "case", "name": "ListchoiceCase", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "container", "name": "ListContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}], "keys": [{"fields": ["PID", "F0"], "node": "ListOne", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "ListOne", "name": "<PERSON><PERSON><PERSON>", "fields": ["PID", "F1"], "index": {"type": "list_localhash"}, "constraints": {"unique": true, "null_check": true}}]}, {"type": "leaf-list", "name": "LeafList", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false, "default": [2, 3, 4, 5, 6, 7, 8, 9]}], "keys": [{"fields": ["PID", "F0"], "node": "LeafList", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "ListTwo", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}], "keys": [{"fields": ["PID", "F0"], "node": "ListTwo", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "TABLE_YANG_REMOVED_DEFAULT_RECORD", "type": "list", "np_access": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "labelId", "type": "uint32", "nullable": false}, {"name": "nodeId", "type": "uint16", "nullable": false}, {"name": "propeId", "type": "uint32", "nullable": false}], "keys": [{"node": "0", "name": "k0", "fields": [":id", ":pid", "labelId", "nodeId", "propeId"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]