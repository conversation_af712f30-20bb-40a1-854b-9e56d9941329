#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

// 同步连接
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

// 公共变量
int ret = 0;
int TIMEOUT = 10;
int affectRows = 0;
unsigned int len = 0;
// MS的config，确认事务类型
GmcTxConfigT MSTrxConfig;

// label和key的name
// MS Vertex
const char *MS_VLabel_Name = "vertex_ms";
const char *MS_VLabel_Key_Name = "vertex_ms_key";
const char *MS_VLabel_Hash_Name = "vertex_ms_hash";
const char *MS_VLabel_SF_Name = "vertex_ms_superfields";
// MS Tree
const char *MS_VLabel_Tree_Name = "vertex_ms_tree";
const char *MS_VLabel_Tree_Key_Name = "vertex_ms_tree_key";
const char *MS_VLabel_Tree_Hash_Name = "vertex_ms_tree_hash";
const char *MS_VLabel_Tree_SF_Name = "vertex_ms_tree_superfields";
const char *MS_VLabel_Tree_SF_Name_01 = "vertex_ms_tree_superfields_1";

// 显示事务专用表
// MS Vertex
const char *MS_TRANS_VLabel_Name = "vertex_ms_trans";
const char *MS_TRANS_VLabel_Key_Name = "vertex_ms_key_trans";
const char *MS_TRANS_VLabel_Hash_Name = "vertex_ms_hash_trans";
const char *MS_TRANS_VLabel_SF_Name = "vertex_ms_superfields_trans";
// MS Edge
const char *MS_TRANS_VLabel_Src_Name = "vertex_ms_src_trans";
const char *MS_TRANS_VLabel_Src_Key_Name = "vertex_ms_src_key_trans";
const char *MS_TRANS_VLabel_Src_Hash_Name = "vertex_ms_src_hash_trans";
const char *MS_TRANS_VLabel_Src_SF_Name = "vertex_ms_src_superfields_trans";
const char *MS_TRANS_VLabel_Dst_Name = "vertex_ms_dst_trans";
const char *MS_TRANS_VLabel_Dst_Key_Name = "vertex_ms_dst_key_trans";
const char *MS_TRANS_VLabel_Dst_Hash_Name = "vertex_ms_dst_hash_trans";
const char *MS_TRANS_VLabel_Dst_SF_Name = "vertex_ms_dst_superfields";
const char *MS_TRANS_Edge_Name = "edge_ms_trans";
// MS Tree
const char *MS_TRANS_VLabel_Tree_Name = "vertex_ms_tree_trans";
const char *MS_TRANS_VLabel_Tree_Key_Name = "vertex_ms_tree_key_trans";
const char *MS_TRANS_VLabel_Tree_Hash_Name = "vertex_ms_tree_hash_trans";
const char *MS_TRANS_VLabel_Tree_SF_Name = "vertex_ms_tree_superfields_trans";
const char *MS_TRANS_VLabel_Tree_SF_Name_01 = "vertex_ms_tree_superfields_1_trans";

// MS config
const char *MS_config = "{\"max_record_count\" : 30000}";
const char *MS_config_trans = "{\"max_record_count\" : 30000, \"isFastReadUncommitted\":0}";

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

// 创建MS的Vertex和Edge label
void testCreateLabelMS(GmcStmtT *stmt)
{
    char *MS_VLabel_schema = NULL;
    char *MS_VLabel_Tree_schema = NULL;

    readJanssonFile("schema_file/Vertex_ms.gmjson", &MS_VLabel_schema);
    ASSERT_NE((void *)NULL, MS_VLabel_schema);
    readJanssonFile("schema_file/Vertex_ms_tree.gmjson", &MS_VLabel_Tree_schema);
    ASSERT_NE((void *)NULL, MS_VLabel_Tree_schema);

    ret = GmcCreateVertexLabel(stmt, MS_VLabel_schema, MS_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, MS_VLabel_Tree_schema, MS_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(MS_VLabel_schema);
    free(MS_VLabel_Tree_schema);
}
// 删除MS的Vertex和Edge label
void testDropLabelMS(GmcConnT *conn, GmcStmtT *stmt)
{
    ret = GmcDropVertexLabel(stmt, MS_VLabel_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        if (ret == GMERR_UNDEFINED_TABLE) {
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    ret = GmcDropVertexLabel(stmt, MS_VLabel_Tree_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        if (ret == GMERR_UNDEFINED_TABLE) {
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

// 创建MS 显示事务的Vertex和Edge label
void testCreateLabelMSTrans(GmcStmtT *stmt)
{
    char *MS_VLabel_schema = NULL;
    char *MS_VLabel_Src_schema = NULL;
    char *MS_VLabel_Dst_schema = NULL;
    char *MS_Edge_schema = NULL;
    char *MS_VLabel_Tree_schema = NULL;

    readJanssonFile("schema_file/Vertex_ms_trans.gmjson", &MS_VLabel_schema);
    ASSERT_NE((void *)NULL, MS_VLabel_schema);
    readJanssonFile("schema_file/Vertex_ms_src_trans.gmjson", &MS_VLabel_Src_schema);
    ASSERT_NE((void *)NULL, MS_VLabel_Src_schema);
    readJanssonFile("schema_file/Vertex_ms_dst_trans.gmjson", &MS_VLabel_Dst_schema);
    ASSERT_NE((void *)NULL, MS_VLabel_Dst_schema);
    readJanssonFile("schema_file/Edge_ms_trans.gmjson", &MS_Edge_schema);
    ASSERT_NE((void *)NULL, MS_Edge_schema);
    readJanssonFile("schema_file/Vertex_ms_tree_trans.gmjson", &MS_VLabel_Tree_schema);
    ASSERT_NE((void *)NULL, MS_VLabel_Tree_schema);

    ret = GmcCreateVertexLabel(stmt, MS_VLabel_schema, MS_config_trans);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, MS_VLabel_Src_schema, MS_config_trans);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, MS_VLabel_Dst_schema, MS_config_trans);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(stmt, MS_Edge_schema, MS_config_trans);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, MS_VLabel_Tree_schema, MS_config_trans);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(MS_VLabel_schema);
    free(MS_VLabel_Src_schema);
    free(MS_VLabel_Dst_schema);
    free(MS_Edge_schema);
    free(MS_VLabel_Tree_schema);
}
// 删除MS 显示事务的Vertex和Edge label
void testDropLabelMSTrans(GmcConnT *conn, GmcStmtT *stmt)
{
    ret = GmcDropVertexLabel(stmt, MS_TRANS_VLabel_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        if (ret == GMERR_UNDEFINED_TABLE) {
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    ret = GmcDropEdgeLabel(stmt, MS_TRANS_Edge_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        if (ret == GMERR_UNDEFINED_TABLE) {
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    ret = GmcDropVertexLabel(stmt, MS_TRANS_VLabel_Src_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        if (ret == GMERR_UNDEFINED_TABLE) {
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    ret = GmcDropVertexLabel(stmt, MS_TRANS_VLabel_Dst_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        if (ret == GMERR_UNDEFINED_TABLE) {
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    ret = GmcDropVertexLabel(stmt, MS_TRANS_VLabel_Tree_Name);
    if ((GMERR_UNDEFINED_TABLE != ret) and (GMERR_OK != ret)) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        if (ret == GMERR_UNDEFINED_TABLE) {
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

// Insert
void testInsertVertex(GmcStmtT *stmt, const char *SuperFiledName, uint32_t times, uint32_t initValue)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, SuperFiledName);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写主键和hash索引数据
        value = initValue + i;
        ret = GmcSetVertexProperty(stmt, "PK0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "PK1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Hash0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Hash1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // // 写fixed数据
        // // superfileds的size = 256 * 16（属性数量）
        // uint32_t SuperSize = 256 * 16;
        // char *SuperValue = (char *)malloc(SuperSize);
        // memset(SuperValue,'A',SuperSize);

        // ret = GmcSetSuperfieldByName(stmt, SuperFiledName, SuperValue, SuperSize);
        // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // free(SuperValue);
        // 写string数据
        uint32_t SuperSize = 256;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'A', (SuperSize - 1));
        SuperValue[SuperSize - 1] = '\0';

        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        free(SuperValue);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// Insert Tree
void testInsertVertexTree(GmcStmtT *stmt, const char *SuperFiledName, const char *SuperFiledName01, uint32_t times)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, SuperFiledName);
    ASSERT_NE((void *)NULL, SuperFiledName01);

    GmcNodeT *root = NULL, *T1 = NULL;

    // insert vertex
    for (i = 0; i < times; i++) {
        ret = GmcGetRootNode(g_stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写主键和hash索引数据
        value = i;
        ret = GmcNodeSetPropertyByName(root, "PK0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root, "PK1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root, "Hash0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(root, "Hash1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写fixed数据
        // superfileds的size = 128 * 16（属性数量）
        uint32_t SuperSize = 128 * 16;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'A', SuperSize);

        ret = GmcNodeSetSuperfieldByName(root, SuperFiledName, SuperValue, SuperSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetSuperfieldByName(T1, SuperFiledName01, SuperValue, SuperSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        free(SuperValue);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// Insert
void testInsertVertexTimes(GmcStmtT *stmt, const char *SuperFiledName, uint32_t times, uint32_t initValue)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, SuperFiledName);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写主键和hash索引数据
        value = initValue + i;
        ret = GmcSetVertexProperty(stmt, "PK0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "PK1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Hash0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Hash1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// Delete MS
void testDeleteVertex(GmcStmtT *stmt, const char *keyName, uint32_t times, uint32_t initValue)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, keyName);

    // delete vertex
    for (i = 0; i < times; i++) {
        value = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删除
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// Delete MS
void testDeleteVertexTrans(GmcStmtT *stmt,
    const char *labelName, const char *keyName, uint32_t times, uint32_t initValue)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, keyName);

    // delete vertex
    for (i = 0; i < times; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        value = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删除
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// Update
void testUpdateVertex(GmcStmtT *stmt, const char *keyName, const char *SuperFiledName, uint32_t times)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, keyName);
    ASSERT_NE((void *)NULL, SuperFiledName);

    // update vertex
    for (i = 0; i < times; i++) {
        value = i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // // 写fixed数据
        // // superfileds的size = 256 * 16（属性数量）
        // uint32_t SuperSize = 256 * 16;
        // char *SuperValue = (char *)malloc(SuperSize);
        // memset(SuperValue,'B',SuperSize);

        // ret = GmcSetSuperfieldByName(stmt, SuperFiledName, SuperValue, SuperSize);
        // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // free(SuperValue);
        uint32_t SuperSize = 256;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'B', (SuperSize - 1));
        SuperValue[SuperSize - 1] = '\0';

        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        free(SuperValue);

        // update
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// Update Tree
void testUpdateVertexTree(
    GmcStmtT *stmt, const char *keyName, const char *SuperFiledName, const char *SuperFiledName01, uint32_t times)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, keyName);
    ASSERT_NE((void *)NULL, SuperFiledName);
    ASSERT_NE((void *)NULL, SuperFiledName01);

    GmcNodeT *root = NULL, *T1 = NULL;

    // update vertex
    for (i = 0; i < times; i++) {
        ret = GmcGetRootNode(g_stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        value = i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写fixed数据
        // superfileds的size = 128 * 16（属性数量）
        uint32_t SuperSize = 128 * 16;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'B', SuperSize);

        ret = GmcNodeSetSuperfieldByName(root, SuperFiledName, SuperValue, SuperSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetSuperfieldByName(T1, SuperFiledName01, SuperValue, SuperSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        free(SuperValue);

        // update
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void testUpdateVertexTimes(GmcStmtT *stmt, const char *keyName, const char *SuperFiledName, uint32_t times)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, keyName);
    ASSERT_NE((void *)NULL, SuperFiledName);

    // update vertex
    for (i = 0; i < times; i++) {
        value = i + 20000;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // update
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// Replace
void testReplaceVertex(GmcStmtT *stmt, const char *SuperFiledName, uint32_t times)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, SuperFiledName);

    // Replace vertex
    for (i = 0; i < times; i++) {
        // 写主键和hash索引数据
        value = i;
        ret = GmcSetVertexProperty(stmt, "PK0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "PK1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Hash0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Hash1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // // 写fixed数据
        // // superfileds的size = 256 * 16（属性数量）
        // uint32_t SuperSize = 256 * 16;
        // char *SuperValue = (char *)malloc(SuperSize);
        // memset(SuperValue,'C',SuperSize);

        // ret = GmcSetSuperfieldByName(stmt, SuperFiledName, SuperValue, SuperSize);
        // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // free(SuperValue);
        uint32_t SuperSize = 256;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'C', (SuperSize - 1));
        SuperValue[SuperSize - 1] = '\0';

        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        free(SuperValue);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// Replace Tree
void testReplaceVertexTree(GmcStmtT *stmt, const char *SuperFiledName, const char *SuperFiledName01, uint32_t times)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, SuperFiledName);
    ASSERT_NE((void *)NULL, SuperFiledName01);

    GmcNodeT *root = NULL, *T1 = NULL;

    // Replace vertex
    for (i = 0; i < times; i++) {
        ret = GmcGetRootNode(g_stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写主键和hash索引数据
        value = i;
        ret = GmcSetVertexProperty(stmt, "PK0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "PK1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Hash0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Hash1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写fixed数据
        // superfileds的size = 128 * 16（属性数量）
        uint32_t SuperSize = 128 * 16;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'C', SuperSize);

        ret = GmcNodeSetSuperfieldByName(root, SuperFiledName, SuperValue, SuperSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetSuperfieldByName(T1, SuperFiledName01, SuperValue, SuperSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        free(SuperValue);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void testReplaceVertexTimes(GmcStmtT *stmt, const char *SuperFiledName, uint32_t times)
{
    uint32_t i = 0;
    uint32_t value = 0;

    ASSERT_NE((void *)NULL, stmt);
    ASSERT_NE((void *)NULL, SuperFiledName);

    // Replace vertex
    for (i = 0; i < times; i++) {
        // 写主键和hash索引数据
        value = i + 40000;
        ret = GmcSetVertexProperty(stmt, "PK0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "PK1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Hash0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Hash1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void getSTORAGE_MEMDATA_STAT()
{
    char const *view_name = "V\\$STORAGE_MEMDATA_STAT";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, view_name);
    printf("%s\n", g_command);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
}

void getSTORAGE_HASH_INDEX_STAT(const char *Filter = NULL, uint32_t *entry_used = NULL)
{
    if (Filter == NULL) {
        char const *view_name = "V\\$STORAGE_HASH_INDEX_STAT";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer,
             view_name);
        printf("%s\n", g_command);
        system(g_command);
    } else {
        char const *view_name = "V\\$STORAGE_HASH_INDEX_STAT";
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f %s", g_toolPath,
            g_connServer, view_name, Filter);
        printf("%s\n", g_command);
        system(g_command);

        if (entry_used != NULL) {
            snprintf(g_command, MAX_CMD_SIZE,
                "%s/gmsysview -s %s -q %s -f %s |grep -E 'ENTRY_USED' |awk -F '[:,]' '{print $2}'",
                g_toolPath, g_connServer, view_name, Filter);
            // printf("%s\n", g_command);
            system(g_command);

            FILE *pf = popen(g_command, "r");
            if (pf == NULL) {
                printf("popen(%s) error./n", g_command);
            }
            char cmdOutput[64] = {0};
            while (NULL != fgets(cmdOutput, 64, pf))
                ;

            *entry_used = atoi(cmdOutput);
            printf("ENTRY_USED = %d\n", *entry_used);
            pclose(pf);
        }
    }

    memset(g_command, 0, sizeof(g_command));
}
