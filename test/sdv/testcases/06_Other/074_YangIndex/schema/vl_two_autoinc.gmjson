[{"comment": "特殊复杂表", "type": "record", "name": "vl_special_complex", "special_complex": true, "fields": [{"name": "A0", "type": "int32", "nullable": false, "auto_increment": true}, {"name": "A1", "type": "int64", "nullable": false, "auto_increment": true}, {"name": "A2", "type": "uint32", "nullable": false}, {"name": "A3", "type": "uint64", "nullable": false}, {"name": "A4", "type": "int64", "nullable": true}, {"name": "A5", "type": "int64", "nullable": true}, {"name": "A6", "type": "bitmap", "size": 16, "nullable": true}, {"name": "A7", "type": "fixed", "size": 16, "nullable": true}, {"name": "A8", "type": "bytes", "nullable": true}, {"name": "A9", "type": "string", "nullable": true}, {"name": "M0", "type": "record", "array": true, "size": 1024, "fields": [{"name": "B0", "type": "int32", "nullable": true}, {"name": "B1", "type": "uint32", "nullable": true}, {"name": "B2", "type": "bytes", "nullable": true}, {"name": "B3", "type": "bytes", "nullable": true}, {"name": "B4", "type": "string", "nullable": true}, {"name": "B5", "type": "string", "nullable": true}]}], "keys": [{"node": "vl_special_complex", "name": "<PERSON><PERSON><PERSON>", "index": {"type": "primary"}, "fields": ["A0"], "constraints": {"unique": true}, "comment": "主键索引"}, {"node": "vl_special_complex", "name": "LocalKey", "index": {"type": "local"}, "fields": ["A2"], "constraints": {"unique": false}, "comment": "local索引"}, {"node": "vl_special_complex", "name": "LocalHashKey", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["A3"], "constraints": {"unique": false}, "comment": "localhash索引"}]}]