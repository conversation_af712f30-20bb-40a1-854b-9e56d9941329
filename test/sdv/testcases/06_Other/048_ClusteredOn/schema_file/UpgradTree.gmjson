[{"version": "2.0", "type": "record", "name": "UpgradTree", "fields": [{"name": "PK", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "uint32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "T1", "type": "record", "fields": [{"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}, {"name": "A1", "type": "record", "fixed_array": true, "size": 3, "fields": [{"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}], "keys": [{"name": "Tree_pk", "index": {"type": "primary"}, "node": "UpgradTree", "fields": ["PK"], "constraints": {"unique": true}}, {"name": "Tree_localhash", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "UpgradTree", "fields": ["F1"], "constraints": {"unique": false}}, {"name": "Tree_hashcluster", "index": {"type": "hashcluster"}, "node": "UpgradTree", "fields": ["F2"], "constraints": {"unique": false}}, {"name": "Tree_local", "index": {"type": "local"}, "node": "UpgradTree", "fields": ["F3"], "constraints": {"unique": false}}]}]