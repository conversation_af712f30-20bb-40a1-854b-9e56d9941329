{"type": "record", "name": "labelOOM", "fields": [{"name": "F0", "type": "int32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "int32"}, {"name": "F3", "type": "int32"}, {"name": "F4", "type": "int32"}, {"name": "F5", "type": "int32"}, {"name": "F6", "type": "int32"}, {"name": "F7", "type": "int32"}, {"name": "F8", "type": "int32"}, {"name": "F9", "type": "int32"}, {"name": "F10", "type": "int32"}, {"name": "F11", "type": "int32"}, {"name": "F12", "type": "int32"}, {"name": "F13", "type": "int32"}, {"name": "F14", "type": "int32"}, {"name": "F15", "type": "int32"}, {"name": "F16", "type": "int32"}, {"name": "F17", "type": "int32"}, {"name": "F18", "type": "int32"}, {"name": "F19", "type": "int32"}, {"name": "F20", "type": "int32"}, {"name": "F21", "type": "int32"}], "keys": [{"name": "PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}