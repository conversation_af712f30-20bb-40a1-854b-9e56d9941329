[{"type": "record", "name": "T90", "fields": [{"name": "F0", "type": "uint8"}, {"name": "F1", "type": "string", "size": 8000}, {"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "dest_ip_addr", "type": "fixed", "size": 16, "comment": "目的地址"}, {"name": "mask_len", "type": "uint8", "comment": "掩码长度"}], "keys": [{"node": "T90", "name": "T90_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T90", "name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F0"], "constraints": {"unique": false}, "comment": "根据localhash索引"}, {"node": "T90", "name": "local_key", "index": {"type": "local"}, "fields": ["F0"], "constraints": {"unique": false}, "comment": "根据local索引"}, {"node": "T90", "name": "hashcluster_key", "index": {"type": "hashcluster"}, "fields": ["F0"], "constraints": {"unique": false}, "comment": "根据hashcluster索引"}, {"name": "lpm6_key", "node": "T90", "index": {"type": "lpm6_tree_bitmap"}, "fields": ["vr_id", "vrf_index", "dest_ip_addr", "mask_len"], "constraints": {"unique": true}, "comment": "根据lpm6_tree_bitmap索引"}]}]