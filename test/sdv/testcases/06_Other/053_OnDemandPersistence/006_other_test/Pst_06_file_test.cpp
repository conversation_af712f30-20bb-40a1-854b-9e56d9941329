/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: 按需持久化--文件损坏/变动
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Create: 2023-08-16
 * History:
 */

#include "../Persistence_common.h"

char g_dbFilePath[1024] = {0};
char g_dbFilePathOther[1024] = {0};

const char *g_vertexLabelJsonFormat = R"(
    [{
        "version":"2.0",
        "type":"record",
        "name":"%s",
        "fields":
            [
                {"name":"A0", "type":"int32", "nullable":false},
                {"name":"A1", "type":"int64", "nullable":false},
                {"name":"A2", "type":"uint32", "nullable":false},
                {"name":"A3", "type":"uint64", "nullable":false},
                {"name":"A4", "type":"float", "nullable":true},
                {"name":"A5", "type":"double", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"%s",
                    "name":"PrimaryKey",
                    "index":{"type":"primary"},
                    "fields":["A0"],
                    "constraints":{"unique":true},
                    "comment": "主键索引"
                }
          ]
    }]
    )";

class Pst_06_file : public testing::Test {
protected:
    static void SetUpTestCase()
    {   
    }

    static void TearDownTestCase()
    {
    };
public:
    virtual void SetUp();
    virtual void TearDown();
};
void Pst_06_file::SetUp()
{
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"recover", NULL));
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    system("sh $TEST_HOME/tools/stop.sh -f");
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(g_dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(g_dbFilePath, S_IRUSR | S_IWUSR));
    (void)sprintf(g_dbFilePathOther, "%s/gmdb_other", pwdDir);
    (void)Rmdir(g_dbFilePathOther);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(g_dbFilePathOther, S_IRUSR | S_IWUSR));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", g_dbFilePath));
    
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    VlComplexRecordCtxT vertexCfg = {
        .opStart = 0,
        .opCount = 1000,
        .startMkVal = 0,
        .childCount = 10,
        .coefficient = 0,
    };
    int len = snprintf(vertexCfg.labelName, sizeof(vertexCfg.labelName), VL_GENERAL_COMPLEX_NAME);
    ASSERT_GT(len, 0);
    ret = VlComplexInsert(g_stmt, vertexCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_CORRUPTION);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNEXPECTED_NULL_VALUE);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
    AW_CHECK_LOG_BEGIN();
}

void Pst_06_file::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    // 停掉服务，恢复配置，清理持久化文件
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    char *homePath = getenv("HOME");
    (void)snprintf(g_command, sizeof(g_command), "rm %s/../data/gmdb/* -rf", homePath);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    char *pwdDir = getenv("PWD");
    (void)sprintf(g_command, "rm %s/gmdb -rf;rm %s/gmdb_other -rf", pwdDir, pwdDir);
    system(g_command);
}

// 删除dbctrlfile进行恢复，恢复失败,表不存在
TEST_F(Pst_06_file, Other_053_006_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    // 落盘(PATH为NULL，使用默认路径落盘)
    int ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("rm -rf %s/dbctrlfile", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新建表
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NO_DATA);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 删除dbsystemspace进行恢复，恢复失败
TEST_F(Pst_06_file, Other_053_006_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    // 落盘(PATH为NULL，使用默认路径落盘)
    int ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("rm -rf %s/dbsystemspace", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/start.sh -f");
    system(g_command);
    ret = executeCommand(g_command, "Error: start service failure");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_EXCEPTION);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NO_DATA);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 删除dbuserspace进行恢复，恢复失败
TEST_F(Pst_06_file, Other_053_006_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    // 落盘(PATH为NULL，使用默认路径落盘)
    int ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GtExecSystemCmd("rm -rf %s/dbuserspace", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/start.sh -f");
    system(g_command);
    ret = executeCommand(g_command, "Error: start service failure");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_EXCEPTION);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NO_DATA);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 删除dbundospace进行恢复，恢复失败
TEST_F(Pst_06_file, Other_053_006_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    // 落盘(PATH为NULL，使用默认路径落盘)
    int ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GtExecSystemCmd("rm -rf %s/dbundospace", g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/start.sh -f");
    system(g_command);
    ret = executeCommand(g_command, "Error: start service failure");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_EXCEPTION);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_NO_DATA);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 截断dbctrlfile进行恢复，恢复失败,表不存在
TEST_F(Pst_06_file, Other_053_006_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    // 落盘(PATH为NULL，使用默认路径落盘)
    int ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("truncate -s $(($(stat -c '%%s' %s/dbctrlfile) / 2)) %s/dbctrlfile", g_dbFilePath, g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/start.sh -f");
    system(g_command);
    ret = executeCommand(g_command, "Error: start service failure");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 截断dbsystemspace进行恢复，恢复失败
TEST_F(Pst_06_file, Other_053_006_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    // 落盘(PATH为NULL，使用默认路径落盘)
    int ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("truncate -s $(($(stat -c '%%s' %s/dbsystemspace) / 2)) %s/dbsystemspace", g_dbFilePath, g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/start.sh -f");
    system(g_command);
    ret = executeCommand(g_command, "Error: start service failure");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 截断dbuserspace进行恢复，恢复失败
TEST_F(Pst_06_file, Other_053_006_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    // 落盘(PATH为NULL，使用默认路径落盘)
    int ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("truncate -s $(($(stat -c '%%s' %s/dbuserspace) / 2)) %s/dbuserspace", g_dbFilePath, g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/start.sh -f");
    system(g_command);
    ret = executeCommand(g_command, "Error: start service failure");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 截断dbundospace进行恢复，恢复失败
TEST_F(Pst_06_file, Other_053_006_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    // 落盘(PATH为NULL，使用默认路径落盘)
    int ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("truncate -s $(($(stat -c '%%s' %s/dbundospace) / 2)) %s/dbundospace", g_dbFilePath, g_dbFilePath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/start.sh -f");
    system(g_command);
    ret = executeCommand(g_command, "Error: start service failure");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 移动持久化文件到其他目录进行恢复，恢复失败
TEST_F(Pst_06_file, Other_053_006_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    // 落盘(PATH为NULL，使用默认路径落盘)
    int ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("mv %s/dbctrlfile %s/dbsystemspace %s/", g_dbFilePath, g_dbFilePath, g_dbFilePathOther);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("mv %s/dbundospace %s/dbuserspace %s/", g_dbFilePath, g_dbFilePath, g_dbFilePathOther);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新建表
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 移动持久化文件到其他目录下并指定为恢复路径进行恢复，恢复成功
TEST_F(Pst_06_file, Other_053_006_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    // 落盘(PATH为NULL，使用默认路径落盘)
    int ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("mv %s/dbctrlfile %s/dbsystemspace %s/", g_dbFilePath, g_dbFilePath, g_dbFilePathOther);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtExecSystemCmd("mv %s/dbundospace %s/dbuserspace %s/", g_dbFilePath, g_dbFilePath, g_dbFilePathOther);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
    (void)GtExecSystemCmd("sh $TEST_HOME/tools/start.sh -f -r %s/", g_dbFilePathOther);
    ret = testEnvInit(-1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新建表
    ret = GtCreateVertexLabel(g_stmt, VL_GENERAL_COMPLEX_JSON_PATH, VL_GENERAL_COMPLEX_CONFIG_PATH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_GENERAL_COMPLEX_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}
