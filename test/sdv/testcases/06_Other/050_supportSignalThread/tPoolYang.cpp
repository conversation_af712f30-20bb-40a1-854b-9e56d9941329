#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <atomic>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#include "tToolsYang.h"

using namespace std; 

class tPoolYang : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};


void tPoolYang::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"isFastReadUncommitted=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"scheduleMode=2\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxWorkerNum=10\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"permanentWorkerNum=4\"");

    system("sh $TEST_HOME/tools/start.sh");

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void tPoolYang::TearDownTestCase()
{
    int ret;
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void tPoolYang::SetUp()
{
    int ret = 0;
 
    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    system("rm -rf \"../../../log/run/rgmserver/rgmserver.log\"");

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    createNameSpace(g_stmt_async, g_namespace);

    // use namespace
    useNameSpace(g_stmt_async, g_namespace);

    AW_CHECK_LOG_BEGIN();
}

void tPoolYang::TearDown()
{
    int ret = 0;
    // drop namespace
    dropNameSpace(g_stmt_async, g_namespace);

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);
    GmcFreeStmt(g_stmt_list);

    // 释放异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    AW_CHECK_LOG_END();
}

#define MY_TRANS_START_CALLBACK(index) \
void my_trans_start_callback_##index(void *userData, int32_t status, const char *errMsg) \
{ \
    create_vertex_label_callback(userData, status, errMsg); \
}

MY_TRANS_START_CALLBACK(0);
MY_TRANS_START_CALLBACK(1);
MY_TRANS_START_CALLBACK(2);
MY_TRANS_START_CALLBACK(3);
MY_TRANS_START_CALLBACK(4);
MY_TRANS_START_CALLBACK(5);
MY_TRANS_START_CALLBACK(6);
MY_TRANS_START_CALLBACK(7);
MY_TRANS_START_CALLBACK(8);
MY_TRANS_START_CALLBACK(9);
MY_TRANS_START_CALLBACK(10);

#define MY_TRANS_START(index) \
ret = GmcTransStartAsync(g_conn_tht[conn_id], &config, my_trans_start_callback_##index, &tData); 


#define THR_NUM 100
GmcConnT *g_conn_tht[THR_NUM * 2];
GmcStmtT *g_stmt_tht[THR_NUM * 2];

void *thread_vertex_dml_async(void *args)
{
    int conn_id = *((int *)args);
    int ret = 0;
    AW_FUN_Log(LOG_INFO, "-- thread: %d", conn_id);

    ret = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    AsyncUserDataT tData = {0};

    GmcStmtT *tStmt_root = NULL, *tStmt_list = NULL;
    ret = GmcAllocStmt(g_conn_tht[conn_id], &tStmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_tht[conn_id], &tStmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // use namespace
    ret = GmcUseNamespaceAsync(g_stmt_tht[conn_id], g_namespace, use_namespace_callback, &tData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&tData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (tData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, tData.status);
        AW_FUN_Log(LOG_DEBUG, "use namespace error code:%d\n", tData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 乐观事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStartAsync(g_conn_tht[conn_id], &config, trans_start_callback, &tData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&tData);
    if (tData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, tData.status);
        AW_FUN_Log(LOG_DEBUG, "trans start error code:%d\n", tData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&tData, 0, sizeof(AsyncUserDataT));

    // 设置批处理
    GmcBatchT *tBatch;
    ret = testBatchPrepare(g_conn_tht[conn_id], &tBatch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret); 

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(tStmt_root, g_graphConListRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(tBatch, tStmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty(tStmt_root, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(tBatch, tStmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(tStmt_list, g_graphConListChildName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(tBatch, tStmt_root, tStmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的尾部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, &refKey);
        ret = GmcYangSetListLocator(tStmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点属性值
        fieldValue = i;
        testYangSetVertexProperty_PK(tStmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        testYangSetVertexPropertyWithoutF0(tStmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(tBatch, tStmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    memset(&tData, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchExecuteAsync(tBatch, batch_execute_callback, &tData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&tData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, tData.status);
    AW_MACRO_EXPECT_EQ_INT(11, tData.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, tData.succNum);
    GmcBatchDestroy(tBatch);

    // 提交事务
    memset(&tData, 0, sizeof(AsyncUserDataT));
    ret = GmcTransCommitAsync(g_conn_tht[conn_id], trans_commit_callback, &tData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&tData);
    if (tData.status == GMERR_RESTRICT_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, tData.status);
        AW_FUN_Log(LOG_INFO, "-- thread: %d trans fail", conn_id);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, tData.status);
        AW_FUN_Log(LOG_INFO, "-- thread: %d trans success", conn_id);
    }

    GmcFreeStmt(tStmt_root);
    GmcFreeStmt(tStmt_list);

    // 释放异步连接
    ret = testGmcDisconnect(g_conn_tht[conn_id], g_stmt_tht[conn_id]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "-- thread: %d end", conn_id);
    return ((void *)0);
}

// 019.yang表，线程池模式,异步连接，20个多线程并发开启乐观可重复读事务，批量dml操作
TEST_F(tPoolYang, Other_050_002_019)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangVertexLabel(g_stmt_async, g_conListVertexPath, g_conListEdgePath);

    // 开启乐观事务
    TransStart(g_conn_async);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConListRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty(g_stmt_root, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_graphConListChildName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的头部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetVertexProperty_PK(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexPropertyWithoutF0(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret); 

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConListRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_graphConListChildName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的尾部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_LAST, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        int PID = 1;
        testSetKeyNameAndValue(g_stmt_list, fieldValue, PID, true);
        int newvalue = i + 200;
        testYangSetVertexProperty(g_stmt_list, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    // 提交事务
    TransCommit(g_conn_async);

    // 多线程并发dml操作，视图查询
    pthread_t thr_arr[100];
    void *thr_ret[100];
    int index[100] = {0};
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < 10; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_vertex_dml_async, (void *)&index[i]);
    }
 
    for (int i = 0; i < 10; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);

    // 删表
    DropYangVertexLabel(g_stmt_async, g_graphConListEdgeName, g_graphConListRootName, g_graphConListChildName);

    AW_FUN_Log(LOG_STEP, "END");
}


// 025.yang表，线程池模式,异步连接，100个多线程并发开启乐观可重复读事务，批量dml操作
TEST_F(tPoolYang, Other_050_002_025)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangVertexLabel(g_stmt_async, g_conListVertexPath, g_conListEdgePath);

    // 开启乐观事务
    TransStart(g_conn_async);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConListRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty(g_stmt_root, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_graphConListChildName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的头部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetVertexProperty_PK(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexPropertyWithoutF0(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret); 

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConListRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_graphConListChildName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的尾部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_LAST, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        int PID = 1;
        testSetKeyNameAndValue(g_stmt_list, fieldValue, PID, true);
        int newvalue = i + 200;
        testYangSetVertexProperty(g_stmt_list, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    // 提交事务
    TransCommit(g_conn_async);

    // 多线程并发dml操作，视图查询
    pthread_t thr_arr[100];
    void *thr_ret[100];
    int index[100] = {0};
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < 100; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_vertex_dml_async, (void *)&index[i]);
    }
 
    for (int i = 0; i < 100; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);

    // 删表
    DropYangVertexLabel(g_stmt_async, g_graphConListEdgeName, g_graphConListRootName, g_graphConListChildName);

    AW_FUN_Log(LOG_STEP, "END");
}

