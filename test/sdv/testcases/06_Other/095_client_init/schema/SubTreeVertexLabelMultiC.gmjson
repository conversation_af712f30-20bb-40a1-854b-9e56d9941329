[{"type": "container", "name": "ContainerOneC", "alias": "alias_ContainerOneC", "presence": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true, "default": 101}, {"name": "F3", "type": "uint32", "nullable": true, "default": 102}, {"name": "F4", "type": "uint32", "nullable": true, "default": 103}, {"name": "F5", "type": "uint32", "nullable": true, "default": 555}, {"name": "F6", "type": "uint32", "nullable": true, "default": 666}, {"name": "F7", "type": "uint32", "nullable": true, "default": 777}, {"name": "F8", "type": "uint32", "nullable": true, "default": 888}, {"name": "F9", "type": "uint32", "nullable": true, "default": 999}, {"name": "F10", "type": "string", "size": 10, "nullable": true}, {"name": "F12", "type": "uint16", "nullable": true}, {"name": "F13", "type": "uint32", "nullable": true}, {"name": "F14", "type": "int64", "nullable": true}, {"name": "F15", "type": "uint64", "nullable": true}, {"name": "F16", "type": "time", "nullable": true}, {"name": "F17", "type": "uint8: 4", "nullable": true}, {"name": "F18", "type": "uint16: 15", "nullable": true}, {"name": "F19", "type": "uint32: 31", "nullable": true}, {"name": "F20", "type": "bytes", "size": 7, "nullable": true}, {"name": "F21", "type": "fixed", "size": 7, "nullable": true}, {"name": "F22", "type": "bitmap", "size": 128, "nullable": true}, {"name": "F23", "type": "uint64: 59", "nullable": true}, {"name": "F24", "type": "int32", "nullable": true}, {"type": "container", "name": "ContainerTwo", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "choice", "name": "Choice", "fields": [{"type": "case", "name": "CaseOne", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32"}, {"name": "F2", "type": "uint32"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "case", "name": "CaseTwo", "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "CaseContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "container", "name": "CaseContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}]}, {"type": "container", "name": "Container<PERSON>hree", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ContainerFour", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}], "keys": [{"node": "ContainerOneC", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "ListOneC", "alias": "alias_ListOneC", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "default": 123}, {"name": "F2", "type": "uint32", "clause": [{"type": "when", "formula": "/alias_ContainerOneC/F1 = 100"}]}, {"name": "F3", "type": "uint32", "default": 456, "clause": [{"type": "when", "formula": "/alias_ContainerOneC/alias_ListOneC/F1 != 101"}]}, {"name": "F4", "type": "uint32", "default": 456, "clause": [{"type": "when", "formula": "/alias_ContainerOneC/alias_ListOneC/F1 != 101"}]}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ContainerTwo", "presence": true, "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true, "default": 555}, {"name": "F6", "type": "uint32", "nullable": true, "default": 666}, {"name": "F7", "type": "uint32", "nullable": true, "default": 777}, {"name": "F8", "type": "uint32", "nullable": true, "default": 888}, {"name": "F9", "type": "uint32", "nullable": true, "default": 999}, {"name": "F10", "type": "string", "size": 10, "nullable": true}, {"name": "F11", "type": "string", "size": 10, "nullable": true, "default": "default11"}, {"name": "F12", "type": "string", "size": 10, "nullable": true, "default": "default12"}, {"name": "F13", "type": "string", "size": 10, "nullable": true, "default": "default13"}]}, {"type": "container", "name": "Container<PERSON>hree", "presence": true, "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"name": "F5", "type": "uint32", "nullable": true, "default": 555}, {"name": "F6", "type": "uint32", "nullable": true, "default": 666}, {"name": "F7", "type": "uint32", "nullable": true, "default": 777}, {"name": "F8", "type": "uint32", "nullable": true, "default": 888}, {"name": "F9", "type": "uint32", "nullable": true, "default": 999}, {"name": "F10", "type": "string", "size": 10, "nullable": true}, {"name": "F11", "type": "string", "size": 10, "nullable": true, "default": "default11"}, {"name": "F12", "type": "string", "size": 10, "nullable": true, "default": "default12"}, {"name": "F13", "type": "string", "size": 10, "nullable": true, "default": "default13"}, {"type": "container", "name": "ContainerFour", "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "container", "name": "ListContainerOne", "presence": true, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}, {"type": "container", "name": "ListContainerthree", "presence": false, "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "choice", "name": "Listchoice", "fields": [{"type": "case", "name": "ListchoiceCase", "fields": [{"name": "F0", "type": "uint32", "default": 123}, {"name": "F1", "type": "uint32", "default": 456}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean", "default": false}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}, {"type": "case", "name": "ListchoiceCaseTwo", "fields": [{"name": "F0", "type": "uint32", "default": 123}, {"name": "F1", "type": "uint32", "default": 456}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean", "default": false}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}]}, {"type": "container", "name": "ListContainerTwo", "presence": false, "fields": [{"name": "F0", "type": "uint32"}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}]}], "keys": [{"fields": ["PID", "F0"], "node": "ListOneC", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "LeafListC", "alias": "alias_LeafListC", "min-elements": 0, "max-elements": 10000, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "F0"], "node": "LeafListC", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "ListTwoC", "alias": "alias_ListTwoC", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "int32"}, {"name": "F2", "type": "boolean"}, {"name": "F3", "type": "double"}, {"name": "F4", "type": "boolean"}, {"name": "F5", "type": "float"}, {"name": "F6", "type": "string"}, {"name": "F7", "type": "int32"}], "keys": [{"fields": ["PID", "F0"], "node": "ListTwoC", "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}]