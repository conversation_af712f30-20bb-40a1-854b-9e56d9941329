alias_ContainerOne:update[(pri<PERSON><PERSON>(ID:1)),(pri<PERSON><PERSON>(ID:1))]
alias_ContainerOne.F0:create(100)
alias_ContainerOne.F1:create(100)
alias_ContainerOne.F2:update(102,101)
alias_ContainerOne.F3:remove(102)
alias_ContainerOne.F22:create(NIL:23)
alias_ContainerOne.Choice:update
Choice.CaseOne:update
CaseOne.CaseContainerOne:remove
CaseContainerOne.F0:remove(111)
alias_ContainerOne.alias_LeafList:remove[(NULL),(pri<PERSON>ey(PID:1,F0:2))]
alias_ContainerOne.alias_LeafList:remove[(NULL),(pri<PERSON>ey(PID:1,F0:3), preKey(PID:1,F0:2))]
