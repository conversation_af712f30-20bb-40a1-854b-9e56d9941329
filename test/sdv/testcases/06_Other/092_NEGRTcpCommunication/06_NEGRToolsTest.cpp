/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 * Description: 按需持久化 简单表DDL
 */

#include "Vertex_common.h"
#include "ddl_dml.h"

class NEGRToolsTest : public testing::Test {
protected:
    static void SetUpTestCase()
    {}

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/stop.sh -f");
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};
void NEGRToolsTest::SetUp()
{
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"recover", NULL));
    char dbFilePath[1024] = {0};
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        printf("get env PWD fail.\n");
    }
    (void)sprintf(dbFilePath, "%s/gmdb", pwdDir);
    (void)Rmdir(dbFilePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"dataFileDirPath", dbFilePath));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, mkdir(dbFilePath, S_IRUSR | S_IWUSR));

    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void NEGRToolsTest::TearDown()
{
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DATA_EXCEPTION);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNDEFINED_OBJECT);
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"recover", NULL));
    system("sh $TEST_HOME/tools/stop.sh -f");
}

// 预置数据，使用gmsysview工具查询表、内存、客户端信息
TEST_F(NEGRToolsTest, Other_092_061)
{
    int32_t ret;
    int32_t start = 0, count = 1000, coefficient = 0;
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFlushData(g_stmt, NULL, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -s %s record %s", g_toolPath, g_connServer, VL_SIMPLE_NAME);
    printf("%s\n", g_command);
    system(g_command);
    // 校验视图返回字段
    ret = executeCommand(g_command, "index = 998, check_version = 0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ASSERT_EQ(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 查询配置信息
TEST_F(NEGRToolsTest, Other_092_062)
{
    int32_t ret;
    int32_t start = 0, count = 1000, coefficient = 0;
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 1000, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFlushData(g_stmt, NULL, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -s %s -cfgName %s", g_toolPath, g_connServer, "localLocatorListened");
    printf("%s\n", g_command);
    system(g_command);
    // 校验视图返回字段
    ret = executeCommand(g_command, "tcp:host=");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ASSERT_EQ(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmcmd 连接、查询、插入、更新、删除，清空db数据
TEST_F(NEGRToolsTest, Other_092_063)
{
    int32_t ret;
    int32_t start = 0, count = 1000, coefficient = 0;
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 10, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFlushData(g_stmt, NULL, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -s %s -cfgName %s", g_toolPath, g_connServer, "localLocatorListened");
    printf("%s\n", g_command);
    system(g_command);
    // 校验视图返回字段
    ret = executeCommand(g_command, "tcp:host=");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ASSERT_EQ(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmddl 升级、降级表，删除表
TEST_F(NEGRToolsTest, Other_092_064)
{
    int32_t ret;
    int32_t start = 0, count = 1000, coefficient = 0;
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 10, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFlushData(g_stmt, NULL, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmddl -s %s -c drop -t %s", g_toolPath, g_connServer, VL_SIMPLE_NAME);
    printf("%s\n", g_command);
    system(g_command);
    // 校验视图返回字段
    ret = executeCommand(g_command, "successfully dropped table num is 1");
    EXPECT_EQ(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmexport 导出表数据
TEST_F(NEGRToolsTest, Other_092_065)
{
    int32_t ret;
    int32_t start = 0, count = 1000, coefficient = 0;
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 10, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFlushData(g_stmt, NULL, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 查视图
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -s %s -c vdata -t %s", g_toolPath, g_connServer, VL_SIMPLE_NAME);
    printf("%s\n", g_command);
    system(g_command);
    // 校验视图返回字段
    ret = executeCommand(g_command, "export_vdata, export file successfully");
    EXPECT_EQ(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// gmimport 导入表数据
TEST_F(NEGRToolsTest, Other_092_066)
{
    int32_t ret;
    int32_t start = 0, count = 1000, coefficient = 0;
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    // 建表并插入1000条数据
    GmcDropVertexLabel(g_stmt, VL_SIMPLE_NAME);
    ret = GtCreateVertexLabel(g_stmt, VL_SIMPLE_JSON_PATH, CONFIG_JSON_PATH);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = VlSimpleInsert(g_stmt, 0, 10, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFlushData(g_stmt, NULL, false);
    ASSERT_EQ(GMERR_OK, ret);

    // 重启
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = GtGmserverRestart(SIGKILL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit(-1, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 导出数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -s %s -c vdata -t %s", g_toolPath, g_connServer, VL_SIMPLE_NAME);
    printf("%s\n", g_command);
    system(g_command);
    // 校验视图返回字段
    ret = executeCommand(g_command, "export_vdata, export file successfully");
    EXPECT_EQ(GMERR_OK, ret);

    // 导入数据
    snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmimport -s %s -c vdata -f %s.gmdata", g_toolPath, g_connServer, VL_SIMPLE_NAME);
    printf("%s\n", g_command);
    system(g_command);
    // 校验视图返回字段
    ret = executeCommand(g_command, "import_vdata, Import file from");
    EXPECT_EQ(GMERR_OK, ret);

    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}
