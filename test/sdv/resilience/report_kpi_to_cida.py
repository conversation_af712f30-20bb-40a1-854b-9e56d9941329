# coding:utf-8
import requests
import json
import sys
import re
import time
import copy
import argparse

num_per_batch = 128
second_to_msecond_to_usecond = 1000
kibi = 1024
infinity = 99999999
batch_throughput_normal_sla = 158 #kops
single_throughput_normal_sla = 4800 #ops
percent = 100

TASK_TEMPLATE1 = {"sceneName": "园区/数据中心设备", "tdt": "GMDB 503", "taskId": "20220621163740", "taskRound": "1",
                 "testCaseId": "resilienceTest_0009", "testCaseUri": "032e0tqe9gh7f", "expectedResilienceLevel": "AL2",
                 "taskStartTime": "1655289848973", "taskEndTime": "1655290053376",
                 "attackMode": "TMODE_DFRs_IMPACT_OS_CPU_003", "attackDetectedTime": "1655289874970",
                 "attackHappenedTime": "1655289869088",
                 "attackEndTime": "1655289932943",
                 "metricList": [{"missionName": "数据读写低时延，高吞吐", "kpiObject": "数据请求处理吞吐", "kpiName": "数据请求处理吞吐"}]
                 }

KPI_TEMPLATE1 = {"tdt": "GMDB 503", "taskId": "20220621163740", "taskRound": "1", "testCaseId": "resilienceTest_0009",
                "kpiDataList": [
                    {"missionName": "数据读写低时延，高吞吐", "kpiObject": "数据请求处理吞吐", "kpiName": "数据请求处理吞吐", "kpiValue": "1",
                     "timeStamp": 1655289849448}
                ]
                }

TASK_TEMPLATE2 = {"sceneName": "园区/数据中心设备", "tdt": "GMDB 503", "taskId": "20220621163740", "taskRound": "1",
                 "testCaseId": "resilienceTest_0009", "testCaseUri": "032e0tqe9gh7f", "expectedResilienceLevel": "AL2",
                 "taskStartTime": "1655289848973", "taskEndTime": "1655290053376",
                 "attackMode": "TMODE_DFRs_IMPACT_OS_CPU_003", "attackDetectedTime": "1655289874970",
                 "attackHappenedTime": "1655289869088",
                 "attackEndTime": "1655289932943",
                 "metricList": [{"missionName": "数据读写低时延，高吞吐", "kpiObject": "数据请求处理时延", "kpiName": "数据请求处理时延"}]
                 }

KPI_TEMPLATE2 = {"tdt": "GMDB 503", "taskId": "20220621163740", "taskRound": "1", "testCaseId": "resilienceTest_0009",
                "kpiDataList": [
                    {"missionName": "数据读写低时延，高吞吐", "kpiObject": "数据请求处理时延", "kpiName": "数据请求处理时延", "kpiValue": "1",
                     "timeStamp": 1655289849448}
                ]
                }

CALC_TEMPLATE = {"sceneName": "园区/数据中心设备", "tdt": "GMDB 503", "taskId": "20220621163740", "taskRound": "1",
                 "testCaseId": "resilienceTest_0009"}


class TestCase(object):
    def __init__(self, expLevel, testCaseId, testCaseUri, attackMode):
        self.expLevel = expLevel
        self.testCaseId = testCaseId
        self.testCaseUri = testCaseUri
        self.attackMode = attackMode
        self.taskId = None


class Durations(object):
    def __init__(self, taskStartTime, taskEndTime, attackHappenedTime, attackDetectedTime, attackEndTime):
        self.taskStartTime = taskStartTime
        self.taskEndTime = taskEndTime
        self.attackHappenedTime = attackHappenedTime
        self.attackDetectedTime = attackDetectedTime
        self.attackEndTime = attackEndTime


class KpiInfo(object):
    def __init__(self, kpiValue, timeStamp):
        self.kpiValue = kpiValue
        self.timeStamp = timeStamp


def report_task(case, durations, kpi):
    # 上传评估项
    if kpi == "throughput":
        task = copy.deepcopy(TASK_TEMPLATE1)
    elif kpi == "latency":
        task = copy.deepcopy(TASK_TEMPLATE2)
    else:
        print("[report_task] Error: invalid kpi")
        return
    task_id = time.strftime("%Y%m%d%H%M%S", time.localtime())
    task["taskId"] = task_id
    case.taskId = task_id
    # 任务开始时间
    task["taskStartTime"] = str(durations.taskStartTime)
    # 任务结束时间
    task["taskEndTime"] = str(durations.taskEndTime)
    # 攻击注入时间
    task["attackHappenedTime"] = str(durations.attackHappenedTime)
    # 检测到攻击时间
    task["attackDetectedTime"] = str(durations.attackDetectedTime)
    # 攻击结束时间
    task["attackEndTime"] = str(durations.attackEndTime)
    # 用例编号
    task["testCaseId"] = case.testCaseId
    # 用例Uri
    task["testCaseUri"] = case.testCaseUri
    # 用例攻击模式
    task["attackMode"] = case.attackMode
    # 预期韧性等级
    task["expectedResilienceLevel"] = case.expLevel

    # 指标上报接口
    url = "http://apigw-01.huawei.com/api/testDataTrace/storage/api/v1/kpi/resilience/init"

    data_json = json.dumps(task)
    # print(data_json)
    token = "Bear eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJpYXQiOjE2MzY2ODA2MTEsInNlcnZpY2UiOiJHTURC5Y-v6" \
            "Z2g5oCnJumfp-aAp0FQSeadg-mZkOeUs-ivtyIsInR5cGUiOiJTRVJWSUNFIiwiZGVwYXJ0bWVudCI6IumrmOaWr-S6p-W" \
            "TgemDqCBbMjAxMuWunumqjOWupF0ifQ.ojFzZ_qh7pqxsjsz_wvklqcyPByT0jFKutHfj39W4zyju8eaqs4RhuhudbZj" \
            "DMAtfpiFkHVppV--ahdCH8sk95OiwBazz5BNaj4FAdGkAeH0b4rpW7NftCt2_KJXBGzq-ARP7c3eG432NNv1GyjSJh02dJ_b5TGX6i9Trl6Tzds"
    headers = {"content-type": "application/json", "Authorization": token, "X-HW-ID": "com.huawei.ipd.noproduct.tenant_w00495442.20200321", "X-HW-APPKEY": "A31ickh5y7LXYc1AoKW4cw=="}
    if data_json:
        response = requests.post(url, data=data_json, headers=headers)
        print("-------------------- report_task --------------------")
        print(response)
        msg = json.loads(response.content)
        print(msg)
        status = msg.get(u"state")
        print(u"status: {0}".format(status))
        if status == "failed":
            print(u"{0}: failed, detail: {1}".format(task.get(u"testCaseId"), msg.get(u"detail")))


def report_kpi(kpi_infos, case, kpi):
    # 指标值上报
    if kpi == "throughput":
        kpi = copy.deepcopy(KPI_TEMPLATE1)
    elif kpi == "latency":
        kpi = copy.deepcopy(KPI_TEMPLATE2)
    else:
        print("[report_kpi] Error: invalid kpi")
        return
    kpi["taskId"] = case.taskId
    kpi["testCaseId"] = case.testCaseId

    for i, kpi_info in enumerate(kpi_infos):
        if i == 0:
            kpi["kpiDataList"][i]["kpiValue"] = kpi_info.kpiValue
            kpi["kpiDataList"][i]["timeStamp"] = kpi_info.timeStamp
        else:
            kpiData = copy.deepcopy(kpi["kpiDataList"][0])
            kpiData["kpiValue"] = kpi_info.kpiValue
            kpiData["timeStamp"] = kpi_info.timeStamp
            kpi["kpiDataList"].append(kpiData)

    url = "http://apigw-01.huawei.com/api/testDataTrace/storage/api/v1/kpi/resilience/insert"

    data_json = json.dumps(kpi)
    #print(data_json)
    token = "Bear eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJpYXQiOjE2MzY2ODA2MTEsInNlcnZpY2UiOiJHTURC5Y-v6" \
            "Z2g5oCnJumfp-aAp0FQSeadg-mZkOeUs-ivtyIsInR5cGUiOiJTRVJWSUNFIiwiZGVwYXJ0bWVudCI6IumrmOaWr-S6p-W" \
            "TgemDqCBbMjAxMuWunumqjOWupF0ifQ.ojFzZ_qh7pqxsjsz_wvklqcyPByT0jFKutHfj39W4zyju8eaqs4RhuhudbZj" \
            "DMAtfpiFkHVppV--ahdCH8sk95OiwBazz5BNaj4FAdGkAeH0b4rpW7NftCt2_KJXBGzq-ARP7c3eG432NNv1GyjSJh02dJ_b5TGX6i9Trl6Tzds"
    headers = {"content-type": "application/json", "Authorization": token, "X-HW-ID": "com.huawei.ipd.noproduct.tenant_w00495442.20200321", "X-HW-APPKEY": "A31ickh5y7LXYc1AoKW4cw=="}
    if data_json:
        response = requests.post(url, data=data_json, headers=headers)
        print("-------------------- report_kpi --------------------")
        print(response)
        msg = json.loads(response.content)
        print(msg)
        status = msg.get(u"state")
        print(u"status: {0}".format(status))
        if status == "failed":
            print(u"{0}: failed, detail: {1}".format(kpi.get(u"testCaseId"), msg.get(u"detail")))


def report_calc(case):
    # 指标值上报
    calc = copy.deepcopy(CALC_TEMPLATE)
    calc["taskId"] = case.taskId
    calc["testCaseId"] = case.testCaseId

    url = "http://apigw-01.huawei.com/api/testDataTrace/storage/api/v1/kpi/resilience/startup"

    data_json = json.dumps(calc)
    # print(data_json)
    token = "Bear eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJpYXQiOjE2MzY2ODA2MTEsInNlcnZpY2UiOiJHTURC5Y-v6" \
            "Z2g5oCnJumfp-aAp0FQSeadg-mZkOeUs-ivtyIsInR5cGUiOiJTRVJWSUNFIiwiZGVwYXJ0bWVudCI6IumrmOaWr-S6p-W" \
            "TgemDqCBbMjAxMuWunumqjOWupF0ifQ.ojFzZ_qh7pqxsjsz_wvklqcyPByT0jFKutHfj39W4zyju8eaqs4RhuhudbZj" \
            "DMAtfpiFkHVppV--ahdCH8sk95OiwBazz5BNaj4FAdGkAeH0b4rpW7NftCt2_KJXBGzq-ARP7c3eG432NNv1GyjSJh02dJ_b5TGX6i9Trl6Tzds"
    headers = {"content-type": "application/json", "Authorization": token, "X-HW-ID": "com.huawei.ipd.noproduct.tenant_w00495442.20200321", "X-HW-APPKEY": "A31ickh5y7LXYc1AoKW4cw=="}
    if data_json:
        response = requests.post(url, data=data_json, headers=headers)
        print("-------------------- report_calc --------------------")
        print(response)
        msg = json.loads(response.content)
        print(msg)
        status = msg.get(u"state")
        print(u"status: {0}".format(status))
        if status == "failed":
            print(u"{0}: failed, detail: {1}".format(calc.get(u"testCaseId"), msg.get(u"detail")))


def get_infos(file_name, kpi, op):
    with open(file_name, "r") as fp:
        lines = fp.readlines()
    start_testcase = ""
    start_attack = ""
    stop_attack = ""
    finish_testcase = ""
    detect_time = ""
    kpi_list = []
    is_start = False
    is_detect = False
    for line in lines:
        if kpi == "throughput":
            is_match = re.match("(\d+) (.+)delta : (\d+)", line)
            if is_match:
                if is_start:
                    time_stamp = is_match.group(1)
                    time_stamp = int(int(time_stamp) / second_to_msecond_to_usecond)
                    delta = is_match.group(3)
                    if op == "batch":
                        delta = int(delta) * int(num_per_batch) / int(kibi) / batch_throughput_normal_sla * percent
                    elif op == "single":
                        delta = int(delta) / single_throughput_normal_sla * percent
                    else:
                        print("[get_infos] Error: invalid op")
                        return
                    kpi_list.append([int(time_stamp), int(delta)])
                continue
        elif kpi == "latency":
            is_match = re.match("(\d+) (.+)durationLatency : (\d+) (.+)", line)
            if is_match:
                if is_start:
                    time_stamp = is_match.group(1)
                    time_stamp = int(int(time_stamp) / second_to_msecond_to_usecond)
                    latency = is_match.group(3)
                    # GMDB 502的异步时延由ms级别变为us级别, 此处不再需要换算
                    # latency = float(latency) / float(second_to_msecond_to_usecond)
                    kpi_list.append([int(time_stamp), float(latency)])
                continue
        else:
            print("[get_infos] Error: invalid kpi")
            return
        if "start testcase" in line:
            start_testcase = int(line.split()[0])
            is_start = True
            continue
        if "finish testcase" in line:
            finish_testcase = int(line.split()[0])
            break
        if "start crash" in line:
            start_attack = int(line.split()[0])
            continue
        if "stop crash" in line:
            stop_attack = int(line.split()[0])
            continue
        if "detect" in line:
            if not is_detect:
                detect_time = int(line.split()[0])
                is_detect = True
            continue
        if "alarmData.alarmStatus : 1" in line:
            if not is_detect:
                detect_time = int(line.split()[0])
                is_detect = True
            continue

    kpi_list.sort(key=lambda x:x[0])

    start_testcase = int(int(start_testcase) / second_to_msecond_to_usecond)
    finish_testcase = int(int(finish_testcase) / second_to_msecond_to_usecond)
    start_attack = int(int(start_attack) / second_to_msecond_to_usecond)
    detect_time = int(int(detect_time) / second_to_msecond_to_usecond)
    stop_attack = int(int(stop_attack) / second_to_msecond_to_usecond)
    return [start_testcase, finish_testcase, start_attack, detect_time, stop_attack], kpi_list


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        add_help="example: python report_kpi_to_cida.py -p batch -k throughput -f info_file.txt -l AL2 -t resilienceTest_0009 032e0tqe9gh7f TMODE_DFRs_IMPACT_OS_CPU_003")
    parser.add_argument('-k', dest='kpi', default="throughput", type=str, help='KPI') # throughput / latency
    parser.add_argument('-p', dest='op', default="batch", type=str, help='OPERATE') # batch / single
    parser.add_argument('-f', dest='file_path', default="info_file.txt", type=str, help='input SLA file, for throughput and latency')
    parser.add_argument('-l', dest='exp_level', default="AL3", type=str, help='expect level')
    parser.add_argument("-t", "--test_case", type=str, help="test_case", nargs="+")
    args = parser.parse_args()

    duration, kpi_infos = get_infos(args.file_path, args.kpi, args.op)
    # print(duration, kpi_infos)
    test_case = args.test_case
    kpi_infos = [(x[1], x[0]) for x in kpi_infos] #转化成元组, 转成kpi在前, 时间戳在后
    # 上传任务
    case = TestCase(args.exp_level, *test_case)
    durations = Durations(*duration)
    report_task(case, durations, args.kpi)
    # 刷新kpi值,一组值是一个点
    kpi_infos = [tuple(i) for i in kpi_infos]
    time.sleep(4)
    kpi_infos = [KpiInfo(kpi_info[0], kpi_info[1]) for kpi_info in kpi_infos]
    report_kpi(kpi_infos, case, args.kpi)
    time.sleep(5)
    # 计算，显示走势图
    report_calc(case)
