#!/bin/bash

function usage()
{
    echo "usage: resetdb.sh <drop/truncate> [cycle]"
    exit 3
}

if [ $# -eq 0 -o $# -gt 2 ]; then
    usage
fi
act=drop
cycle=1
if [ $# -ge 1 ]; then
    if [ "$1" = "-h" -o "$1" = "-H" ]; then
        usage
    fi
    if [ "$1" != "drop" -a "$1" = "truncate" ]; then
        usage
    fi
    act="$1"
fi
if [ $# -ge 2 ]; then
    cycle="$2"
fi

#327681 public
#327682 system
#327683 sysview
function dumpNamespace
{
    nsID=(`${exeTool} -q 'V$CATA_NAMESPACE_INFO' | grep "NAMESPACE_ID: " | awk '{print $2}'`)
    nsName=(`${exeTool} -q 'V$CATA_NAMESPACE_INFO' | grep "NAMESPACE_NAME: " | awk '{print $2}'`)
    nsNum=${#nsName[*]}
}
function getNamespace
{
    j=0
    spaceName="public"
    while [ ${j} -lt ${nsNum} ]
    do
        if [ $1 -eq ${nsID[$j]} ]; then
            spaceName=${nsName[$j]}
            break
        fi
        j=`expr ${j} + 1`
    done
}
function cleanNamespace
{
    i=0
    while [ ${i} -lt ${nsNum} ]
    do
        if [ "${nsName[$i]}" != "public" -a "${nsName[$i]}" != "system" -a "${nsName[$i]}" != "sysview" ]; then
            resetdb namespace ${nsName[$i]} public
        fi
        i=`expr ${i} + 1`
    done
}

function dumpAllTable
{
    vertexID=(`${exeTool} -q 'V$CATA_VERTEX_LABEL_INFO' | grep "NAMESPACE_ID: " | awk '{print $2}'`)
    vertexName=(`${exeTool} -q 'V$CATA_VERTEX_LABEL_INFO' | grep "VERTEX_LABEL_NAME: " | awk '{print $2}'`)
    vertexNum=${#vertexName[*]}
    kvID=(`${exeTool} -q 'V$CATA_KV_TABLE_INFO' | grep "NAMESPACE_ID: " | awk '{print $2}'`)
    kvName=(`${exeTool} -q 'V$CATA_KV_TABLE_INFO' | grep "LABEL_NAME: " | grep -v SOURCE_LABEL_NAME | awk '{print $2}'`)
    kvNum=${#kvName[*]}
}

function cleanVertex
{
    i=0
    while [ ${i} -lt ${vertexNum} ]
    do
        namePre=`echo "${vertexName[$i]}" | cut -c1-2`
        if [ "xV$" != "x${namePre}" ]; then
            getNamespace ${vertexID[$i]}
	    echo "resetdb vertex ${vertexName[$i]} ${spaceName}"
            resetdb vertex ${vertexName[$i]} ${spaceName}
        fi
        i=`expr ${i} + 1`
    done
}

function cleanKV
{
    i=0
    while [ ${i} -lt ${kvNum} ]
    do
        if [ "xT_GMDB" != "x${kvName[$i]}" ]; then
            echo "clean table[kv]: ${kvName[$i]}"
            getNamespace ${kvID[$i]}
            resetdb kv ${kvName[$i]} ${spaceName}
        fi
        i=`expr ${i} + 1`
    done
}

function cleanSubinfo
{
    spaceId=(`${exeTool} -q 'V$CATA_LABEL_SUBS_INFO' | grep "NAMESPACE_ID: " | awk '{print $2}'`)
    subinfoName=(`${exeTool} -q 'V$CATA_LABEL_SUBS_INFO' | grep "SUBS_NAME: " | awk '{print $2}'`)
    num=${#subinfoName[*]}
    i=0
    while [ ${i} -lt ${num} ]
    do
        echo "clean subinfo: ${subinfoName[$i]}"
        getNamespace ${spaceId[$i]}
        resetdb subinfo ${subinfoName[$i]} ${spaceName}
        i=`expr ${i} + 1`
    done
}

function cleanResPool
{
    spaceId=(`${exeTool} -q 'V$CATA_RESOURCE_INFO' | grep "NAMESPACE_ID: " | awk '{print $2}'`)
    resName=(`${exeTool} -q 'V$CATA_RESOURCE_INFO' | grep "RESOURCE_NAME: " | awk '{print $2}'`)
    num=${#resName[*]}
    i=0
    while [ ${i} -lt ${num} ]
    do
        echo "clean resSource: ${resName[$i]}"
        getNamespace ${spaceId[$i]}
        resetdb resource ${resName[$i]} ${spaceName}
        i=`expr ${i} + 1`
    done
}

function cleanDeltaStore
{
    dsName=(`${exeTool} -q 'V$DSTORE_CONFIG_STAT' | grep "DSTORE_NAME: " | awk '{print $2}'`)
    num=${#dsName[*]}
    i=0
    while [ ${i} -lt ${num} ]
    do
        resetdb resource ${resName[$i]} public
        i=`expr ${i} + 1`
    done
}

exeTool="gmsysview -u XXuser -p XXpwd -e DOPRA -s channel:"
if [ -f /etc/euleros-release ]; then
    exeTool="gmsysview -u XXuser -p XXpwd -e RTOS -s usocket:/run/verona/unix_emserver"
fi

dumpNamespace
dumpAllTable

id=0
while [ ${id} -lt ${cycle} ]
do
    id=`expr ${id} + 1`
    cleanSubinfo
    cleanVertex
    cleanKV
    cleanResPool
    cleanDeltaStore
    cleanNamespace
done

