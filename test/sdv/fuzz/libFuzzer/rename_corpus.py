import os
import sys
import hashlib

def calc_file_hash(file_path):
    if not os.path.isfile(file_path):
        return None
    h = hashlib.md5()
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda:f.read(4096), b''):
            h.update(chunk)
    return h.hexdigest()


def rename_file_by_md5(file):
    file_dir = os.path.dirname(file)
    file_hash = calc_file_hash(file)
    if file_hash:
        new_name = os.path.join(file_dir, file_hash)
        new_name = new_name +'.txt'
        os.rename(file, new_name)
        print(f'rename {os.path.basename(file)} to {file_hash}.txt')

def rename_files(target_dir):
    src = os.path.realpath(target_dir)
    for r, _, fs in os.walk(src):
        for f in fs:
            file_name = os.path.join(r,f)
            if '.txt' in file_name:
                continue
            rename_file_by_md5(file_name)


if __name__ == '__main__':
    if len(sys.argv) == 2:
        rename_files(sys.argv[1])


