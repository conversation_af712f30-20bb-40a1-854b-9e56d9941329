<?xml version="1.0" encoding="utf-8"?>
<PitDefines>
    <All>
        <Ipv4 key="TargetIPv4"
              value="*************"
              name="Target IPv4 Address"
              description="IPv4 address of the target machine."/>

        <Range key="TargetPort"
               value="13341"
               min="0"
               max="65535"
               name="Target Port"
               description="Port on the target machine to receive packets."/>

        <String key="ServerUsername"
              value="root"
              name="Username"
              description="Username of server which need to test"/>

        <String key="ServerPassword"
              value="huawei.123"
              name="Password"
              description="Password of server which need to test"/>

        <String key="LoggerPath"
                value="../logs/"
                name="Logger Path"
                description="The directory where <PERSON><PERSON> will save the log produced when fuzzing."/>

        <String key="PitLibraryPath"
                value="."
                name="Pit Library Path"
                description="Path to the directory where <PERSON><PERSON> loads the Pits. This value is read-only."/>

        <Range key="TimeOut"
               value="4000"
               min="0" max="10000000"
               name="Timeout"
               description="Duration, in milliseconds, to wait for incoming data."/>

        <Strategy key="Strategy"
                  value="Random"
                  name="Mutation Strategy"
                  description="The mutation strategy to use when fuzzing."/>
				  
	  <String key="FuzzedFile"
                  value="Files/fuzzedvertexschema.gmjson"
                  name="Fuzzed File output path"
                  description="The path that is saving fuzzing files. This file will be consumed by the target application." />
				
    </All>
</PitDefines>

