<?xml version="1.0" encoding="utf-8"?>
<Secray
        description="Network Time Protocol PIT DataModels"
        version="1.0">
<!--
2022-10-13 10:00:00 CST [281473463442864] [0-AuditLogEnhance] [namespaceId: 1048577 type: KV_TABLE name: "KvTable" ] [DML] [success] sessionId: "0" stmtId: "0" type: "DELETE KV" action: "prepare" 
-->
	<!--fuzz upgrade 数据建模 -->
  <DataModel name="reqGmids">
       <Block name="reqGmids">
        <String  name="CreateUserName01"   value="2022-10-13"/>
        <String  name="CreateUserName02"   value="10:00:00"/>
        <String  name="FixPart01"   value="CST ["/>
        <String  name="CreateUserName03"   value="281473463442864"/>
        <String  name="FixPart02"   value="] ["/>
        <String  name="CreateUserName04"   value="0-AuditLogEnhance"/>
        <String  name="FixPart03"   value="] [namespaceId: "/>
        <String  name="CreateUserName05"   value="1048577"/>
        <String  name="FixPart04"   value="type: "/>
        <String  name="CreateUserName06"   value="KV_TABLE"/>
        <String  name="FixPart05"   value="name: &quot;"/>
        <String  name="CreateUserName07"   value="KvTable"/>
        <String  name="FixPart06"   value="&quot; ] ["/>
        <String  name="CreateUserName08"   value="DML"/>
        <String  name="FixPart07"   value="] ["/>
        <String  name="CreateUserName09"   value="success"/>
        <String  name="FixPart08"   value="] sessionId: &quot;"/>
        <String  name="CreateUserName10"   value="0"/>
        <String  name="FixPart09"   value="&quot; stmtId: &quot;"/>
        <String  name="CreateUserName11"   value="0"/>
        <String  name="FixPart10"   value="&quot; type: &quot;"/>
        <String  name="CreateUserName12"   value="DELETE KV"/>
        <String  name="FixPart11"   value="&quot; action: &quot;"/>
        <String  name="CreateUserName13"   value="prepare"/>
        <String  name="FixPart12"   value="&quot;"/>
	    </Block>
   </DataModel>

</Secray>

