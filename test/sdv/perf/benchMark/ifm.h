/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 */
#ifndef DATA_GMDBV5_TEST_SDV_PERF_BENCHMARK_IFM_H
#define DATA_GMDBV5_TEST_SDV_PERF_BENCHMARK_IFM_H

#include "../testperf/perf_tool.h"
#include "../testperf/perf_fib.h"
#include "perf_scene_oper.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
char *g_if_table_name = (char *)"if";
char *g_if_fwd_table_name = (char *)"if_fwd";
char *g_if_fwd_pk_name = (char *)"if_fwd_pk";
char *g_trunkmem_id_table_name = (char *)"trunkmem_id";
char *g_port_base_info_table_name = (char *)"port_base_info";
char *g_if_statistics_table_name = (char *)"if_statistics";
char *g_if_pk_name = (char *)"if_pk";
char *g_if_statistics_pk_name = (char *)"if_statistics_pk";
char *g_port_base_info_pk_name = (char *)"port_base_info_pk";
char *g_if_vlan_table_name = (char *)"if_vlan";
char *g_if_vlan_pk_name = (char *)"if_vlan_pk";

int32_t if_update_spec_filed()
{
    GmcStmtT *if_update_stmt;
    GmcAllocStmt(g_conn, &if_update_stmt);
    int32_t ret = GmcPrepareStmtByLabelName(if_update_stmt, g_if_table_name, GMC_OPERATION_UPDATE);
    if (ret != GMERR_OK) {
        GmcFreeStmt(if_update_stmt);
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName: %s failed.", "ifm");
        return ret;
    }

    uint32_t link_protocol = 0;
    uint32_t vrid = 0;
    uint32_t vrf_index = 0;
    uint32_t port_group_id = 0xffff0001;
    uint32_t if_group_id = 0x80000015;
    uint32_t id_df = 0;
    uint32_t encap_type = 0xff;
    uint32_t is_subif = 0;

    ret = GmcSetVertexProperty(
        if_update_stmt, "link_protocol", GMC_DATATYPE_UINT32, &link_protocol, sizeof(link_protocol));
    CHECK_OK_RET(ret, "GmcGetVertexPropertyByName if_index.");

    ret = GmcSetVertexProperty(if_update_stmt, "vrid", GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
    CHECK_OK_RET(ret, "GmcGetVertexPropertyByName if_index.");

    ret = GmcSetVertexProperty(if_update_stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
    CHECK_OK_RET(ret, "GmcGetVertexPropertyByName if_index.");

    ret = GmcSetVertexProperty(
        if_update_stmt, "port_group_id", GMC_DATATYPE_UINT32, &port_group_id, sizeof(port_group_id));
    CHECK_OK_RET(ret, "GmcGetVertexPropertyByName if_index.");

    ret = GmcSetVertexProperty(if_update_stmt, "if_group_id", GMC_DATATYPE_UINT32, &if_group_id, sizeof(if_group_id));
    CHECK_OK_RET(ret, "GmcGetVertexPropertyByName if_index.");

    ret = GmcSetVertexProperty(if_update_stmt, "if_df", GMC_DATATYPE_UINT32, &id_df, sizeof(id_df));
    CHECK_OK_RET(ret, "GmcGetVertexPropertyByName if_index.");

    ret = GmcSetVertexProperty(if_update_stmt, "encap_type", GMC_DATATYPE_UINT32, &encap_type, sizeof(encap_type));
    CHECK_OK_RET(ret, "GmcGetVertexPropertyByName if_index.");

    ret = GmcSetVertexProperty(if_update_stmt, "is_subif", GMC_DATATYPE_UINT32, &is_subif, sizeof(is_subif));
    CHECK_OK_RET(ret, "GmcGetVertexPropertyByName if_index.");
    DB_START_TEST_CPU_CYCLES(if_update_spec_filed);
    ret = GmcExecute(if_update_stmt);
    DB_START_TEST_CPU_CYCLES(if_update_spec_filed);

    if (ret != GMERR_OK) {
        GmcFreeStmt(if_update_stmt);
        DB_LOG_ERROR(ret, "GmcExecute: %s failed.", "ifm");
        return ret;
    }
    GmcFreeStmt(if_update_stmt);
    return ret;
}

int32_t if_update_filed(char *filed, uint32_t fileddata)
{
    GmcStmtT *if_update_filed_stmt;
    GmcAllocStmt(g_conn, &if_update_filed_stmt);
    int32_t ret = GmcPrepareStmtByLabelName(if_update_filed_stmt, g_if_table_name, GMC_OPERATION_UPDATE);
    if (ret != GMERR_OK) {
        GmcFreeStmt(if_update_filed_stmt);
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName: %s failed.", "ifm");
        return ret;
    }
    ret = GmcSetVertexProperty(if_update_filed_stmt, filed, GMC_DATATYPE_UINT32, &fileddata, sizeof(uint32_t));
    DB_START_TEST_CPU_CYCLES(if_update_filed);
    ret = GmcExecute(if_update_filed_stmt);
    DB_START_TEST_CPU_CYCLES(if_update_filed);
    if (ret != GMERR_OK) {
        GmcFreeStmt(if_update_filed_stmt);

        DB_LOG_ERROR(ret, "GmcExecute: %s failed.", "ifm");
        return ret;
    }
    GmcFreeStmt(if_update_filed_stmt);
    return ret;
}

int32_t if_read_filed_by_key(uint32_t uInputIndex, char *filed, uint32_t *fileddata)
{
    GmcStmtT *if_read_stmt;
    GmcAllocStmt(g_conn, &if_read_stmt);

    int32_t ret = GmcPrepareStmtByLabelName(if_read_stmt, "if", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName: %s failed.", "ifm");
        return ret;
    }

    for (uint32_t i = 0; i < uInputIndex; i++) {
        ret = GmcSetIndexKeyName(if_read_stmt, "if_pk");
        CHECK_OK_RET(ret, "GmcSetIndexKeyName.");

        ret = GmcSetIndexKeyValue(if_read_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue ifindex.");
        DB_START_TEST_CPU_CYCLES(if_read_filed_by_key);
        ret = GmcExecute(if_read_stmt);
        DB_START_TEST_CPU_CYCLES(if_read_filed_by_key);

        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcExecute failed");
            return ret;
        }

        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(if_read_stmt, &isFinish);
            if (ret != GMERR_OK) {
                TEST_LOG_ERROR(ret, "GmcFetch failed");
                return ret;
            }
            if (isFinish == true) {
                break;
            }

            bool isNull;
            ret = GmcGetVertexPropertyByName(if_read_stmt, filed, fileddata, sizeof(&fileddata), &isNull);
            if (ret != GMERR_OK) {
                DB_LOG_INFO("if_read_filed_by_key: %s failed.", "ifm");
            }
        }
    }
    GmcFreeStmt(if_read_stmt);
    return ret;
}

int32_t if_read_l2_portindex(uint32_t uInputIndex)
{
    GmcStmtT *if_read_l2_stmt;
    int32_t ret = GmcAllocStmt(g_conn, &if_read_l2_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    ret = GmcPrepareStmtByLabelName(if_read_l2_stmt, "if", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "if");
        return ret;
    }
    CHECK_OK_RET(ret, "db_create_if_obj");

    ret = GmcSetIndexKeyValue(if_read_l2_stmt, 0, GMC_DATATYPE_UINT32, &uInputIndex, sizeof(uInputIndex));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue ifindex.");

    ret = GmcSetIndexKeyName(if_read_l2_stmt, "if_pk");
    CHECK_OK_RET(ret, "GmcSetIndexKeyName.");
    DB_START_TEST_CPU_CYCLES(if_read_l2_portindex);
    ret = GmcExecute(if_read_l2_stmt);
    DB_START_TEST_CPU_CYCLES(if_read_l2_portindex);

    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute failed");
        return ret;
    }

    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(if_read_l2_stmt, &isFinish);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcFetch failed");
            return ret;
        }
        if (isFinish == true) {
            break;
        }
        GmcNodeT *root;
        ret = GmcGetRootNode(if_read_l2_stmt, &root);
        CHECK_OK_RET(ret, "GmcGetRootNode.");

        bool isNull;
        uint32_t value_u32;
        // 插入record节点 l2
        GmcNodeT *node_l2;
        ret = GmcNodeGetChild(root, "l2", &node_l2);
        CHECK_OK_RET(ret, "GmcNodeGetChild l2.");

        ret = GmcNodeGetPropertyByName(node_l2, (char *)"l2_portindex", &value_u32, sizeof(uint32_t), &isNull);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName l2_portindex.");
        return ret;
    }
    GmcFreeStmt(if_read_l2_stmt);
    return ret;
}

int32_t if_read_tb_tp_by_key(uint32_t uInputIndex)
{
    int32_t ret = GMERR_OK;
    GmcStmtT *if_read_tbtp_stmt;
    ret = GmcAllocStmt(g_conn, &if_read_tbtp_stmt);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
        return ret;
    }
    ret = GmcPrepareStmtByLabelName(if_read_tbtp_stmt, "if", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "if");
        return ret;
    }
    for (uint32_t i = 0; i < uInputIndex; i++) {
        ret = GmcSetIndexKeyName(if_read_tbtp_stmt, "if_pk");
        CHECK_OK_RET(ret, "GmcSetIndexKeyName: if_pk");
        ret = GmcSetIndexKeyValue(if_read_tbtp_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue: uInputIndex");
        DB_START_TEST_CPU_CYCLES(if_read_tb_tp_by_key);
        ret = GmcExecute(if_read_tbtp_stmt);
        DB_START_TEST_CPU_CYCLES(if_read_tb_tp_by_key);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "db_read_obj if failed");
            GmcFreeStmt(if_read_tbtp_stmt);
            return ret;
        }

        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(if_read_tbtp_stmt, &isFinish);
            if (ret != GMERR_OK) {
                TEST_LOG_ERROR(ret, "GmcFetch failed");
                return ret;
            }
            if (isFinish == true) {
                break;
            }
            GmcNodeT *root;
            ret = GmcGetRootNode(if_read_tbtp_stmt, &root);
            CHECK_OK_RET(ret, "GmcGetRootNode.");

            bool isNull;
            uint32_t tb;
            uint32_t tp;
            ret = GmcNodeGetPropertyByName(root, (char *)"tb", &tb, sizeof(tb), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tb.");
            ret = GmcNodeGetPropertyByName(root, (char *)"tp", &tp, sizeof(tp), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tp.");
        }
    }
    GmcFreeStmt(if_read_tbtp_stmt);
    return ret;
}

int32_t if_fwd_read_by_key(uint32_t uInputIndex)
{
    GmcStmtT *if_fwd_stmt;
    int32_t ret = GmcAllocStmt(g_conn, &if_fwd_stmt);
    if (ret != GMERR_OK) {
        printf("ERROR! gmserverConnect failed.\n");
        return GMERR_CONNECTION_FAILURE;
    }
    DB_START_TEST_CPU_CYCLES(if_fwd_read_by_key);
    ret = GmcPrepareStmtByLabelName(if_fwd_stmt, "if_fwd", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "if_fwd");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(if_fwd_read_by_key);

    if_fwd_key_t key_data = {0};
    key_data.ifindex = uInputIndex;

    struct structTestCtx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.stmt = if_fwd_stmt;

    GmcSeriT s;
    s.seriFunc = SeriPrimaryKey;
    s.version = GMC_SERI_VERSION_DEFAULT;
    s.userData = &ctx;
    s.obj = (uint8_t *)&key_data;
    s.seriFunc = SeriPrimaryKey_fix<if_fwd_key_t>;
    getSerialKeyLength(&s);

    GmcDeseriT d;
    d.deseriFunc = deSeriStructVertex;
    d.version = GMC_SERI_VERSION_DEFAULT;
    if_fwd_struct_t read_data;
    d.obj = (uint8_t *)&read_data;
    d.userData = &ctx;

    uint32_t fixedLen = sizeof(if_fwd_struct_t);
    uint32_t newVal = 0;
    uint32_t newSize = 0;
    DmConvertUint32ToVarint(fixedLen + 1, &newVal, &newSize);
    GmcStructBufferT inputBufInfo = {(uint8_t *)&read_data, fixedLen, 1 + newSize};

    DB_START_TEST_CPU_CYCLES(if_fwd_read_by_key);
    ret = GmcGetVertexBuf(if_fwd_stmt, 0, &s, &inputBufInfo);
    if (ret == GMERR_NO_DATA || ret == GMERR_OK) {
        goto EXIT;
    }
    GmcFreeStmt(if_fwd_stmt);
    DB_STOP_TEST_CPU_CYCLES(if_fwd_read_by_key);
    return ret;
EXIT:
    ret = GMERR_OK;
    GmcFreeStmt(if_fwd_stmt);
    return ret;
}

int32_t if_read_by_key(uint32_t uInputIndex)
{
    int32_t ret = GMERR_OK;
    GmcStmtT *t_stmt;
    ret = BenchMarkAllocStmt(&(g_conn), &t_stmt);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "ERROR! gmserverConnect failed.");
        return ret;
    }
    DB_START_TEST_CPU_CYCLES(if_read_by_key_db_create_if_obj);
    ret = GmcPrepareStmtByLabelName(t_stmt, "if", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "if");
        return ret;
    }
    DB_STOP_TEST_CPU_CYCLES(if_read_by_key_db_create_if_obj);
    for (uint32_t i = 0; i < uInputIndex; i++) {
        ret = GmcSetIndexKeyName(t_stmt, "if_pk");
        CHECK_OK_RET(ret, "set index key name");
        ret = GmcSetIndexKeyValue(t_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        CHECK_OK_RET(ret, "set index key value");

        DB_START_TEST_CPU_CYCLES(if_read_by_key_db_read_obj);
        ret = GmcExecute(t_stmt);
        DB_STOP_TEST_CPU_CYCLES(if_read_by_key_db_read_obj);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "GmcExecute");
            GmcFreeStmt(t_stmt);
            return ret;
        }

        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(t_stmt, &isFinish);
            if (ret != GMERR_OK) {
                TEST_LOG_ERROR(ret, "GmcFetch failed");
                return ret;
            }
            if (isFinish == true) {
                break;
            }
            GmcNodeT *root;
            ret = GmcGetRootNode(t_stmt, &root);
            CHECK_OK_RET(ret, "GmcGetRootNode.");

            bool isNull;
            uint32_t ifindex;
            char if_name[64] = {0};
            uint32_t vrid;
            uint32_t if_type;
            uint32_t shutdown;
            uint32_t linkup;
            uint32_t tbtp;
            uint32_t tb;
            uint32_t tp;
            uint32_t port_switch;
            uint32_t forwardType;
            char macAddress[6] = {0};
            uint16_t ipv4_mtu;
            uint16_t ipv4_enable;
            uint16_t ipv6_mtu;
            uint16_t ipv6_enable;
            uint32_t on_board;
            uint32_t lagid;
            uint32_t hppsvcflg;
            uint32_t error_down;
            uint64_t speed;
            uint32_t link_protocol;
            uint32_t vrf_index;
            uint32_t port_group_id;
            uint32_t if_group_id;
            uint32_t if_df;
            uint32_t encap_type;
            uint32_t is_subif;
            uint32_t mainifindex;
            uint32_t logicTB;
            uint32_t logicTP;
            uint32_t vlandomain;
            uint32_t coreId;
            uint32_t ipv4mss;
            uint32_t ipv6mss;

            uint32_t get_field_uint32;

            uint32_t if_macaddress_len = 0;
            uint32_t if_name_len = 0;
            uint32_t trunk_id;
            uint32_t vlan_id;
            uint32_t l2_portindex;
            uint32_t tunnel_id;
            uint32_t vsi;
            uint32_t id;
            ret = GmcNodeGetPropertyByName(root, (char *)"ifindex", &ifindex, sizeof(ifindex), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ifindex.");
            ret = GmcNodeGetPropertyByName(root, (char *)"name", &if_name, sizeof(if_name), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName name.");
            ret = GmcNodeGetPropertyByName(root, (char *)"vrid", &vrid, sizeof(vrid), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vrid.");
            ret = GmcNodeGetPropertyByName(root, (char *)"if_type", &if_type, sizeof(if_type), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName if_type.");
            ret = GmcNodeGetPropertyByName(root, (char *)"shutdown", &shutdown, sizeof(shutdown), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName shutdown.");
            ret = GmcNodeGetPropertyByName(root, (char *)"linkup", &linkup, sizeof(linkup), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName linkup.");
            ret = GmcNodeGetPropertyByName(root, (char *)"tbtp", &tbtp, sizeof(tbtp), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tbtp.");
            ret = GmcNodeGetPropertyByName(root, (char *)"tb", &tb, sizeof(tb), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tb.");
            ret = GmcNodeGetPropertyByName(root, (char *)"tp", &tp, sizeof(tp), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tp.");
            ret = GmcNodeGetPropertyByName(root, (char *)"port_switch", &port_switch, sizeof(port_switch), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_switch.");
            ret = GmcNodeGetPropertyByName(root, (char *)"forwardType", &forwardType, sizeof(forwardType), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName forwardType.");
            ret = GmcNodeGetPropertyByName(root, (char *)"macAddress", &macAddress, sizeof(macAddress), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName macAddress.");
            ret = GmcNodeGetPropertyByName(root, (char *)"ipv4_mtu", &ipv4_mtu, sizeof(ipv4_mtu), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv4_mtu.");
            ret = GmcNodeGetPropertyByName(root, (char *)"ipv4_enable", &ipv4_enable, sizeof(ipv4_enable), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv4_enable.");
            ret = GmcNodeGetPropertyByName(root, (char *)"ipv6_mtu", &ipv6_mtu, sizeof(ipv6_mtu), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv6_mtu.");
            ret = GmcNodeGetPropertyByName(root, (char *)"ipv6_enable", &ipv6_enable, sizeof(ipv6_enable), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv6_enable.");
            ret = GmcNodeGetPropertyByName(root, (char *)"on_board", &on_board, sizeof(on_board), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName on_board.");
            ret = GmcNodeGetPropertyByName(root, (char *)"lagid", &lagid, sizeof(lagid), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName lagid.");
            ret = GmcNodeGetPropertyByName(root, (char *)"hppsvcflg", &hppsvcflg, sizeof(hppsvcflg), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName hppsvcflg.");
            ret = GmcNodeGetPropertyByName(root, (char *)"error_down", &error_down, sizeof(error_down), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName error_down.");
            ret = GmcNodeGetPropertyByName(root, (char *)"speed", &speed, sizeof(speed), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName speed.");
            ret =
                GmcNodeGetPropertyByName(root, (char *)"link_protocol", &link_protocol, sizeof(link_protocol), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName link_protocol.");
            ret = GmcNodeGetPropertyByName(root, (char *)"vrf_index", &vrf_index, sizeof(vrf_index), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vrf_index.");
            ret =
                GmcNodeGetPropertyByName(root, (char *)"port_group_id", &port_group_id, sizeof(port_group_id), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_group_id.");
            ret = GmcNodeGetPropertyByName(root, (char *)"if_group_id", &if_group_id, sizeof(if_group_id), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName if_group_id.");
            ret = GmcNodeGetPropertyByName(root, (char *)"if_df", &if_df, sizeof(if_df), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName if_df.");
            ret = GmcNodeGetPropertyByName(root, (char *)"encap_type", &encap_type, sizeof(encap_type), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName encap_type.");
            ret = GmcNodeGetPropertyByName(root, (char *)"is_subif", &is_subif, sizeof(is_subif), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName is_subif.");
            ret = GmcNodeGetPropertyByName(root, (char *)"mainifindex", &mainifindex, sizeof(mainifindex), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName mainifindex.");
            ret = GmcNodeGetPropertyByName(root, (char *)"logicTB", &logicTB, sizeof(logicTB), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName logicTB.");
            ret = GmcNodeGetPropertyByName(root, (char *)"logicTP", &logicTP, sizeof(logicTP), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName logicTP.");
            ret = GmcNodeGetPropertyByName(root, (char *)"vlandomain", &vlandomain, sizeof(vlandomain), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vlandomain.");
            ret = GmcNodeGetPropertyByName(root, (char *)"coreId", &coreId, sizeof(coreId), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName coreId.");
            ret = GmcNodeGetPropertyByName(root, (char *)"ipv4mss", &ipv4mss, sizeof(ipv4mss), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv4mss.");
            ret = GmcNodeGetPropertyByName(root, (char *)"ipv6mss", &ipv6mss, sizeof(ipv6mss), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv6mss.");

            char str_pro[64] = {0};
            unsigned int proSize = sizeof(str_pro);

            uint16_t value_u16;
            uint32_t value_u32;
            uint64_t value_u64;

            GmcNodeT *node_dev;
            ret = GmcNodeGetChild(root, "dev", &node_dev);
            CHECK_OK_RET(ret, "GmcNodeGetChild dev.");

            ret = GmcNodeGetPropertyByName(node_dev, (char *)"dev_id", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName dev_id.");

            ret = GmcNodeGetPropertyByName(node_dev, (char *)"chassis_id", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName chassis_id.");

            ret = GmcNodeGetPropertyByName(node_dev, (char *)"slot_id", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName slot_id.");

            ret = GmcNodeGetPropertyByName(node_dev, (char *)"card_id", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName card_id.");

            ret = GmcNodeGetPropertyByName(node_dev, (char *)"unit_id", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName unit_id.");

            ret = GmcNodeGetPropertyByName(node_dev, (char *)"port_id", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_id.");

            ret = GmcNodeGetPropertyByName(node_dev, (char *)"port_num", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_num.");

            GmcNodeT *node_l2;
            ret = GmcNodeGetChild(root, "l2", &node_l2);
            CHECK_OK_RET(ret, "GmcNodeGetChild l2.");

            ret = GmcNodeGetPropertyByName(node_l2, (char *)"trunk_id", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName trunk_id.");

            ret = GmcNodeGetPropertyByName(node_l2, (char *)"vlan_id", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vlan_id.");

            ret = GmcNodeGetPropertyByName(node_l2, (char *)"l2_portindex", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName l2_portindex.");

            ret = GmcNodeGetPropertyByName(node_l2, (char *)"vsi", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vsi.");

            ret = GmcNodeGetPropertyByName(node_l2, (char *)"tunnel_id", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tunnel_id.");

            ret = GmcNodeGetPropertyByName(node_l2, (char *)"bd_id", &value_u32, sizeof(uint32_t), &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName bd_id.");
        }
    }
    DB_START_TEST_CPU_CYCLES(if_read_by_key_db_release_if_object);
    GmcFreeStmt(t_stmt);
    DB_STOP_TEST_CPU_CYCLES(if_read_by_key_db_release_if_object);
    return 0;
}

int32_t trunkmem_id_read_by_key(uint32_t uInputIndex)
{
    GmcStmtT *trunkmem_id_stmt;
    GmcAllocStmt(g_conn, &trunkmem_id_stmt);
    int32_t ret = GmcPrepareStmtByLabelName(trunkmem_id_stmt, g_trunkmem_id_table_name, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName: %s failed.", "ifm");
        return ret;
    }
    ret = GmcSetIndexKeyName(trunkmem_id_stmt, "member_ifindex_pk");
    CHECK_OK_RET(ret, "set index key name");

    ret = GmcSetIndexKeyValue(trunkmem_id_stmt, 0, GMC_DATATYPE_UINT32, &uInputIndex, sizeof(uInputIndex));
    CHECK_OK_RET(ret, "set index key value");

    DB_START_TEST_CPU_CYCLES(trunkmem_id_read_by_key);
    ret = GmcExecute(trunkmem_id_stmt);
    DB_STOP_TEST_CPU_CYCLES(trunkmem_id_read_by_key);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcExecute failed");
        GmcFreeStmt(trunkmem_id_stmt);
        return ret;
    }

    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(trunkmem_id_stmt, &isFinish);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcFetch: %s failed.", "ifm");
            return ret;
        }
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }

        bool isNull;
        uint32_t value_u32;
        ret = GmcGetVertexPropertyByName(trunkmem_id_stmt, (char *)"trunkid", &value_u32, sizeof(value_u32), &isNull);
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName trunkid.");
    }
    GmcFreeStmt(trunkmem_id_stmt);
    return ret;
}

int32_t init_insert_if_table()
{
    int32_t ret = 0;
    GmcStmtT *if_insert_stmt;
    GmcAllocStmt(g_conn, &if_insert_stmt);
    // 插入顶点
    ret = GmcPrepareStmtByLabelName(if_insert_stmt, "if", GMC_OPERATION_UPDATE);
    CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ifindex.");
    GmcNodeT *root, *t1, *t2, *t3, *t4;
    ret = GmcGetRootNode(if_insert_stmt, &root);
    ret = GmcNodeGetChild(root, "ifm", &t1);
    ret = GmcNodeGetChild(root, "dev", &t2);
    ret = GmcNodeGetChild(root, "l2", &t3);
    ret = GmcNodeGetChild(root, "port", &t4);

    int8_t value_8 = 1;
    uint8_t value_u8 = 1;
    int16_t value_16 = 1;
    uint16_t value_u16 = 1;
    uint16_t f5_value = 1;
    uint32_t f3_value = 1;
    char f14_value[64];
    for (uint32_t i = 0; i < 62; i++) {
        ret = sprintf(f14_value, "test_%d", i);
        // insert root

        ret = GmcNodeSetPropertyByName(root, (char *)"name", GMC_DATATYPE_FIXED, f14_value, 64);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName name.");

        ret = GmcNodeSetPropertyByName(root, (char *)"vrid", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vrid.");

        ret = GmcNodeSetPropertyByName(root, (char *)"if_type", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName if_type.");

        ret = GmcNodeSetPropertyByName(root, (char *)"shutdown", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName shutdown.");

        ret = GmcNodeSetPropertyByName(root, (char *)"linkup", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName linkup.");

        ret = GmcNodeSetPropertyByName(root, (char *)"tbtp", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tbtp.");

        ret = GmcNodeSetPropertyByName(root, (char *)"tb", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tb.");

        ret = GmcNodeSetPropertyByName(root, (char *)"tp", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tp.");

        ret = GmcNodeSetPropertyByName(root, (char *)"port_switch", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_switch.");

        ret = GmcNodeSetPropertyByName(root, (char *)"forwardType", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName forwardType.");

        ret = GmcNodeSetPropertyByName(root, (char *)"macAddress", GMC_DATATYPE_FIXED, f14_value, 6);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName macAddress.");

        ret = GmcNodeSetPropertyByName(root, (char *)"ipv4_mtu", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv4_mtu.");

        ret = GmcNodeSetPropertyByName(root, (char *)"ipv4_enable", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv4_enable.");

        ret = GmcNodeSetPropertyByName(root, (char *)"ipv6_mtu", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv6_mtu.");

        ret = GmcNodeSetPropertyByName(root, (char *)"ipv6_enable", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName ipv6_enable.");

        ret = GmcNodeSetPropertyByName(root, (char *)"on_board", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName on_board.");

        ret = GmcNodeSetPropertyByName(root, (char *)"lagid", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName lagid.");

        ret = GmcNodeSetPropertyByName(root, (char *)"hppsvcflg", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName hppsvcflg.");

        ret = GmcNodeSetPropertyByName(root, (char *)"error_down", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName error_down.");

        uint64_t f1_value = i;
        ret = GmcNodeSetPropertyByName(root, (char *)"speed", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName speed.");

        // insert ifm

        ret = GmcNodeSetPropertyByName(t1, (char *)"simple_name", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName simple_name.");

        ret = GmcNodeSetPropertyByName(t1, (char *)"description", GMC_DATATYPE_FIXED, f14_value, 64);
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName description.");

        ret = GmcNodeSetPropertyByName(t1, (char *)"is_configure", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName is_configure.");

        ret = GmcNodeSetPropertyByName(t1, (char *)"main_ifindex", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName main_ifindex.");

        ret = GmcNodeSetPropertyByName(t1, (char *)"sub_max_num", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName sub_max_num.");

        ret = GmcNodeSetPropertyByName(t1, (char *)"sub_curr_num", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName sub_curr_num.");

        ret = GmcNodeSetPropertyByName(t1, (char *)"error_down", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName error_down.");

        ret = GmcNodeSetPropertyByName(t1, (char *)"statistic", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName statistic.");

        ret = GmcNodeSetPropertyByName(t1, (char *)"vsys_id", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vsys_id.");

        ret = GmcNodeSetPropertyByName(t1, (char *)"zone_id", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName zone_id.");

        ret = GmcNodeSetPropertyByName(t1, (char *)"last_up_time", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName last_up_time.");

        ret = GmcNodeSetPropertyByName(t1, (char *)"last_down_time", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName last_down_time.");

        // insert dev
        ret = GmcNodeSetPropertyByName(t2, (char *)"dev_id", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName dev_id.");

        ret = GmcNodeSetPropertyByName(t2, (char *)"chassis_id", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName chassis_id.");

        ret = GmcNodeSetPropertyByName(t2, (char *)"slot_id", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName slot_id.");

        ret = GmcNodeSetPropertyByName(t2, (char *)"card_id", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName card_id.");

        ret = GmcNodeSetPropertyByName(t2, (char *)"unit_id", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName unit_id.");

        ret = GmcNodeSetPropertyByName(t2, (char *)"port_id", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_id.");

        ret = GmcNodeSetPropertyByName(t2, (char *)"port_num", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName port_num.");

        // insert l2
        ret = GmcNodeSetPropertyByName(t3, (char *)"trunk_id", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName trunk_id.");

        ret = GmcNodeSetPropertyByName(t3, (char *)"vlan_id", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vlan_id.");

        ret = GmcNodeSetPropertyByName(t3, (char *)"l2_portindex", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName l2_portindex.");

        ret = GmcNodeSetPropertyByName(t3, (char *)"vsi", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName vsi.");

        ret = GmcNodeSetPropertyByName(t3, (char *)"tunnel_id", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName tunnel_id.");

        ret = GmcNodeSetPropertyByName(t3, (char *)"bd_id", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName bd_id.");

        // insert port
        ret = GmcNodeSetPropertyByName(t4, (char *)"speed", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName speed.");

        ret = GmcNodeSetPropertyByName(t4, (char *)"duplex", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName duplex.");

        ret = GmcNodeSetPropertyByName(t4, (char *)"flow_control", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName flow_control.");

        ret = GmcNodeSetPropertyByName(t4, (char *)"phy_type", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName phy_type.");

        ret = GmcNodeSetPropertyByName(t4, (char *)"jumbo", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName jumbo.");

        ret = GmcNodeSetPropertyByName(t4, (char *)"baud", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName baud.");

        ret = GmcNodeSetPropertyByName(t4, (char *)"rmon", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName rmon.");

        ret = GmcNodeSetPropertyByName(t4, (char *)"phy_link", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName phy_link.");

        ret = GmcNodeSetPropertyByName(t4, (char *)"if_mib", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName if_mib.");

        ret = GmcNodeSetPropertyByName(t4, (char *)"on_board", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
        CHECK_OK_RET(ret, "GmcNodeGetPropertyByName on_board.");
        DB_START_TEST_CPU_CYCLES(init_insert_if_table);
        ret = GmcExecute(if_insert_stmt);
        DB_START_TEST_CPU_CYCLES(init_insert_if_table);

        if (ret != 0) {
            return ret;
            printf("i=%d\n", i);
        }
    }
    GmcFreeStmt(if_insert_stmt);
    return ret;
}

int32_t if_fwd_batch_insert(uint32_t uDataCnt)
{
    int32_t ret = GMERR_OK;

    uint32_t j = 0;
    uint32_t total_num, success_num;
    uint32_t sum_succ = 0;

    GmcStmtT *stmt;
    ret = GmcAllocStmt(g_conn, &stmt);
    CHECK_OK_RET(ret, "gmc alloc stmt");
    ret = GmcPrepareStmtByLabelName(stmt, "if_fwd", GMC_OPERATION_UPDATE);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    GmcBatchT *batch = NULL;
    ret = StructureTestBatchInit(&batch, stmt, g_conn);
    CHECK_OK_RET(ret, "gmc prepare stmt");

    uint32_t wr_loop = 60;
    uint32_t end = uDataCnt - 1;

    for (uint32_t i = 0; i < uDataCnt; i++) {
        uint32_t ifkey_type = 2;
        uint32_t vrid = 0;
        uint32_t resource_vsi = 0;
        uint32_t ready_flags = 16647175;
        uint32_t attr_flags = 0;
        uint64_t update_time = 0;
        uint32_t globallif = 0;
        uint64_t globallif_ver = 0;

        ret = GmcSetIndexKeyName(stmt, "if_fwd_pk");
        CHECK_OK_RET(ret, "set index key name");

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        CHECK_OK_RET(ret, "set index key value");

        ret = GmcSetVertexProperty(stmt, (char *)"ifkey_type", GMC_DATATYPE_UINT32, &ifkey_type, sizeof(ifkey_type));
        CHECK_OK_RET(ret, "GmcSetVertexProperty ifkey_type");

        ret = GmcSetVertexProperty(stmt, (char *)"vrid", GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetVertexProperty vrid");

        ret = GmcSetVertexProperty(
            stmt, (char *)"resource_vsi", GMC_DATATYPE_UINT32, &resource_vsi, sizeof(resource_vsi));
        CHECK_OK_RET(ret, "GmcSetVertexProperty resource_vsi");

        ret = GmcSetVertexProperty(stmt, (char *)"ready_flags", GMC_DATATYPE_UINT32, &ready_flags, sizeof(ready_flags));
        CHECK_OK_RET(ret, "GmcSetVertexProperty ready_flags");

        ret = GmcSetVertexProperty(stmt, (char *)"attr_flags", GMC_DATATYPE_UINT32, &attr_flags, sizeof(attr_flags));
        CHECK_OK_RET(ret, "GmcSetVertexProperty attr_flags");

        ret = GmcSetVertexProperty(stmt, (char *)"update_time", GMC_DATATYPE_TIME, &update_time, sizeof(update_time));
        CHECK_OK_RET(ret, "GmcSetVertexProperty update_time");

        ret = GmcSetVertexProperty(stmt, (char *)"globallif", GMC_DATATYPE_UINT32, &globallif, sizeof(globallif));
        CHECK_OK_RET(ret, "GmcSetVertexProperty globallif");

        ret = GmcSetVertexProperty(
            stmt, (char *)"globallif_ver", GMC_DATATYPE_UINT64, &globallif_ver, sizeof(globallif_ver));
        CHECK_OK_RET(ret, "GmcSetVertexProperty globallif_ver");

        j++;
        GmcResetStmt(stmt);

        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK && ret != GMERR_PROGRAM_LIMIT_EXCEEDED) {
            CHECK_OK_RET(ret, "failed to batch add dml.");
            goto CLEAR2;
        }

        if (j >= wr_loop || GMERR_PROGRAM_LIMIT_EXCEEDED == ret || i >= end) {
            uint64_t count = 0;
            GmcBatchRetT batchRet = {};
            ret = GmcBatchExecute(batch, &batchRet);
            if (ret != GMERR_OK) {
                CHECK_OK_RET(ret, "failed to batch execute.");
                goto CLEAR2;
            }
            ret = GmcBatchDeparseRet(&batchRet, &total_num, &success_num);
            CHECK_OK_RET(ret, "failed to batch execute.");
            sum_succ += success_num;
            j = 0;
        }
        if (ret || i >= end) {
            break;
        }
    }
CLEAR2:
    GmcBatchUnbindStmt(batch, stmt);
CLEAR1:
    if (batch != NULL) {
        return GmcBatchDestroy(batch);
    }
    GmcFreeStmt(stmt);
    return ret;
}

int32_t if_statistics_read_by_pk(int32_t uInputIndex)
{
    GmcStmtT *if_statistics_stmt;
    GmcAllocStmt(g_conn, &if_statistics_stmt);

    int32_t ret = GmcPrepareStmtByLabelName(if_statistics_stmt, g_if_statistics_table_name, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName: %s failed.", "ifm");
        return ret;
    }
    for (uint32_t i = 0; i < uInputIndex; i++) {
        ret = GmcSetIndexKeyName(if_statistics_stmt, "if_statistics_pk");
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcSetIndexKeyName: %s failed.", "ifm");
            return ret;
        }
        ret = GmcSetIndexKeyValue(if_statistics_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcSetIndexKeyValue: %s failed.", "ifm");
            return ret;
        }
        DB_START_TEST_CPU_CYCLES(if_statistics_read_by_pk);
        ret = GmcExecute(if_statistics_stmt);
        DB_START_TEST_CPU_CYCLES(if_statistics_read_by_pk);
        if (ret != GMERR_OK) {
            GmcFreeStmt(if_statistics_stmt);
            DB_LOG_ERROR(ret, "GmcExecute: %s failed.", "ifm");
            return ret;
        }
        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(if_statistics_stmt, &isFinish);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "GmcFetch: %s failed.", "ifm");
                return ret;
            }
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }

            bool isNull;
            uint8_t value_u8;
            ret = GmcGetVertexPropertyByName(
                if_statistics_stmt, (char *)"statistics_enable", &value_u8, sizeof(value_u8), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName ifindex.");
        }
    }
    GmcFreeStmt(if_statistics_stmt);
    return ret;
}

int32_t if_read_macAddress_by_key(uint64_t uInputIndex)
{
    GmcStmtT *if_read_macAddress_stmt;
    GmcAllocStmt(g_conn, &if_read_macAddress_stmt);
    uint32_t value_u32 = uInputIndex & 0xffffffff;

    int32_t ret = GmcPrepareStmtByLabelName(if_read_macAddress_stmt, "if", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName: %s failed.", "ifm");
        return ret;
    }
    for (uint32_t i = 0; i < uInputIndex; i++) {
        ret = GmcSetIndexKeyName(if_read_macAddress_stmt, "if_pk");
        CHECK_OK_RET(ret, "GmcSetIndexKeyName.");
        ret = GmcSetIndexKeyValue(if_read_macAddress_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue.");
        DB_START_TEST_CPU_CYCLES(if_read_macAddress_by_key);
        ret = GmcExecute(if_read_macAddress_stmt);
        DB_START_TEST_CPU_CYCLES(if_read_macAddress_by_key);
        if (ret) {
            TEST_LOG_ERROR(ret, "GmcExecute index:%lu .", uInputIndex);
            return -1;
        }

        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(if_read_macAddress_stmt, &isFinish);
            if (ret) {
                TEST_LOG_ERROR(ret, "GmcFetch index:%lu .", uInputIndex);
                return -1;
            }
            if (isFinish == true) {
                break;
            }

            GmcNodeT *root;
            ret = GmcGetRootNode(if_read_macAddress_stmt, &root);
            CHECK_OK_RET(ret, "GmcGetRootNode.");

            bool isNull;
            char str_pro[64] = {0};
            unsigned int proSize = sizeof(str_pro);

            ret = GmcNodeGetPropertyByName(root, (char *)"macAddress", str_pro, proSize, &isNull);
            CHECK_OK_RET(ret, "GmcNodeGetPropertyByName macAddress.");
        }
    }
    GmcFreeStmt(if_read_macAddress_stmt);
    return ret;
}

int32_t if_read_hppsvcflg_by_key(uint64_t uInputIndex)
{
    GmcStmtT *if_read_hppsvcflg_stmt;
    GmcAllocStmt(g_conn, &if_read_hppsvcflg_stmt);
    uint32_t value_u32 = uInputIndex & 0xffffffff;
    int32_t ret = GmcPrepareStmtByLabelName(if_read_hppsvcflg_stmt, "if", GMC_OPERATION_SCAN);
    CHECK_OK_RET(ret, "GmcPrepareStmtByLabelName.");

    ret = GmcSetIndexKeyValue(if_read_hppsvcflg_stmt, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
    CHECK_OK_RET(ret, "GmcSetIndexKeyValue.");
    for (uint32_t i = 0; i < uInputIndex; i++) {
        ret = GmcSetIndexKeyName(if_read_hppsvcflg_stmt, "if_pk");
        CHECK_OK_RET(ret, "GmcSetIndexKeyName.");
        ret = GmcSetIndexKeyValue(if_read_hppsvcflg_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue.");
        DB_START_TEST_CPU_CYCLES(if_read_hppsvcflg_by_key);
        ret = GmcExecute(if_read_hppsvcflg_stmt);
        DB_START_TEST_CPU_CYCLES(if_read_hppsvcflg_by_key);
        if (ret) {
            TEST_LOG_ERROR(ret, "GmcExecute index:%lu .", uInputIndex);
            return -1;
        }

        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(if_read_hppsvcflg_stmt, &isFinish);
            if (ret) {
                TEST_LOG_ERROR(ret, "GmcFetch index:%lu .", uInputIndex);
                return -1;
            }
            if (isFinish == true) {
                break;
            }

            GmcNodeT *root;
            ret = GmcGetRootNode(if_read_hppsvcflg_stmt, &root);
            CHECK_OK_RET(ret, "GmcGetRootNode.");

            bool isNull;
            uint32_t hppsvcflg;
            ret = GmcNodeGetPropertyByName(root, (char *)"hppsvcflg", &hppsvcflg, sizeof(hppsvcflg), &isNull);
            if (ret != GMERR_OK) {
                DB_LOG_INFO("if_read_filed_by_key: %s failed.", "ifm");
            }
        }
    }
    GmcFreeStmt(if_read_hppsvcflg_stmt);
    return ret;
}

int32_t if_vlan_read_by_key(uint32_t uInputIndex)
{
    GmcStmtT *if_vlan_read_stmt;
    GmcAllocStmt(g_conn, &if_vlan_read_stmt);
    status_t ret = GmcPrepareStmtByLabelName(if_vlan_read_stmt, "if_vlan", GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TEST_LOG_ERROR(ret, "GmcPrepareStmtByLabelName:%s failed.", "if_vlan");
        return ret;
    }
    for (uint32_t i = 0; i < uInputIndex; i++) {
        ret = GmcSetIndexKeyName(if_vlan_read_stmt, "if_vlan_pk");
        CHECK_OK_RET(ret, "set index key name");
        ret = GmcSetIndexKeyValue(if_vlan_read_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        CHECK_OK_RET(ret, "set index key value");
        DB_START_TEST_CPU_CYCLES(if_vlan_read_by_key);
        ret = GmcExecute(if_vlan_read_stmt);
        DB_STOP_TEST_CPU_CYCLES(if_vlan_read_by_key);
        if (ret != GMERR_OK) {
            TEST_LOG_ERROR(ret, "db_read_obj if failed");
            GmcFreeStmt(if_vlan_read_stmt);
            return ret;
        }

        bool isFinish = false;
        while (!isFinish) {
            ret = GmcFetch(if_vlan_read_stmt, &isFinish);
            if (ret != GMERR_OK) {
                TEST_LOG_ERROR(ret, "GmcFetch failed");
                return ret;
            }
            if (isFinish == true) {
                break;
            }

            uint32_t if_index;
            uint16_t vlan_id;
            uint32_t vr_id;
            uint32_t app_source_id;
            uint32_t app_serial_id;
            uint64_t app_obj_id;

            bool isNull;

            ret =
                GmcGetVertexPropertyByName(if_vlan_read_stmt, (char *)"if_index", &if_index, sizeof(if_index), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName if_index.");
            ret = GmcGetVertexPropertyByName(if_vlan_read_stmt, (char *)"vlan_id", &vlan_id, sizeof(vlan_id), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName vlan_id.");
            ret = GmcGetVertexPropertyByName(if_vlan_read_stmt, (char *)"vr_id", &vr_id, sizeof(vr_id), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName vr_id.");
            ret = GmcGetVertexPropertyByName(
                if_vlan_read_stmt, (char *)"app_source_id", &app_source_id, sizeof(app_source_id), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName app_source_id.");
            ret = GmcGetVertexPropertyByName(
                if_vlan_read_stmt, (char *)"app_serial_id", &app_serial_id, sizeof(app_serial_id), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName app_serial_id.");
            ret = GmcGetVertexPropertyByName(
                if_vlan_read_stmt, (char *)"app_obj_id", &app_obj_id, sizeof(app_obj_id), &isNull);
            CHECK_OK_RET(ret, "GmcGetVertexPropertyByName app_obj_id.");
        }
    }

    GmcFreeStmt(if_vlan_read_stmt);
    return ret;
}

int32_t if_update_some_filed(uint32_t end_num)
{
    uint32_t start_num = 0;

    GmcStmtT *if_update_some_stmt;
    GmcAllocStmt(g_conn, &if_update_some_stmt);
    int32_t ret = GmcPrepareStmtByLabelName(if_update_some_stmt, g_if_table_name, GMC_OPERATION_UPDATE);
    CHECK_OK_RET(ret, "GmcPrepareStmtByLabelName.");

    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName: %s failed.", "ifm");
        return ret;
    }

    uint32_t forwardType = 17;
    uint32_t vrid = 0;
    uint32_t vlan_id = 100;
    GmcNodeT *root, *l2;
    ret = GmcGetRootNode(if_update_some_stmt, &root);
    ret = GmcNodeGetChild(root, "l2", &l2);
    bool isNull;

    while (start_num < end_num) {
        ret = GmcSetVertexProperty(
            if_update_some_stmt, "forwardType", GMC_DATATYPE_UINT32, &forwardType, sizeof(forwardType));
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName forwardType.");
        ret = GmcSetVertexProperty(if_update_some_stmt, "vrid", GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName vrid.");
        ret = GmcNodeSetPropertyByName(l2, "vlan_id", GMC_DATATYPE_UINT32, &vlan_id, sizeof(vlan_id));
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName vlan_id.");
        DB_START_TEST_CPU_CYCLES(if_update_some_filed);
        ret = GmcExecute(if_update_some_stmt);
        DB_START_TEST_CPU_CYCLES(if_update_some_filed);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcSetVertexProperty: %s failed.", "ifm");
            return ret;
        }
        start_num += 1;
    }
    GmcFreeStmt(if_update_some_stmt);
    return ret;
}

int32_t if_update_macAddress()
{
    uint32_t start_num = 0;
    uint32_t end_num = 61;

    GmcStmtT *if_update_macAddress;
    GmcAllocStmt(g_conn, &if_update_macAddress);
    int32_t ret = GmcPrepareStmtByLabelName(if_update_macAddress, g_if_table_name, GMC_OPERATION_UPDATE);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "GmcPrepareStmtByLabelName: %s failed.", "ifm");
        return ret;
    }

    char macAddress[6];
    bool isNull;
    while (start_num < end_num) {
        ret = sprintf(macAddress, "test_%d", start_num);
        ret = GmcSetVertexProperty(
            if_update_macAddress, "macAddress", GMC_DATATYPE_FIXED, &macAddress, sizeof(macAddress));
        CHECK_OK_RET(ret, "GmcGetVertexPropertyByName forwardType.");
        DB_START_TEST_CPU_CYCLES(if_update_macAddress);
        ret = GmcExecute(if_update_macAddress);
        DB_START_TEST_CPU_CYCLES(if_update_macAddress);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "GmcSetVertexProperty: %s failed.", "ifm");
            return ret;
        }
        start_num += 1;
    }
    GmcFreeStmt(if_update_macAddress);
    return ret;
}
#endif
