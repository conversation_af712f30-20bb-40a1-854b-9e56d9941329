{"comment": "组播IP转发表", "version": "2.0", "type": "record", "name": "l3mc_mfib", "config": {"check_validity": false, "generate_cache_interface": true}, "max_record_count": 65536, "fields": [{"name": "vrId", "type": "uint32", "comment": "VR索引"}, {"name": "vrfId", "type": "uint32", "comment": "VRF索引"}, {"name": "grpAddr", "type": "uint32", "comment": "组播组地址"}, {"name": "srcAddr", "type": "uint32", "comment": "源地址"}, {"name": "srcMaskLen", "type": "uint8", "comment": "源掩码长度"}, {"name": "fvrfId", "type": "uint32", "comment": "FVRF字段"}, {"name": "grpMaskLen", "type": "uint8", "comment": "组掩码长度"}, {"name": "reserve", "type": "uint16", "comment": "预留字段"}, {"name": "inIfIndex", "type": "uint32", "comment": "入接口索引"}, {"name": "entryType", "type": "uint32", "comment": "标识表项类型, 定义在fes_pub.h中,标志注册入接口"}, {"name": "pathFlags", "type": "uint32", "comment": "表项完备性标识"}, {"name": "isWaiting", "type": "uint8", "comment": "等待下发标记"}, {"name": "isOldExist", "type": "uint8", "comment": "老入接口是否存在标志"}, {"name": "vlanId", "type": "uint16", "comment": "vlanId"}, {"name": "oldInIfIndex", "type": "uint32", "comment": "老的入接口信息"}, {"name": "oldEntryType", "type": "uint32", "comment": "注册入出接口标志，定义在fesi_pub.h中"}, {"name": "mcId", "type": "uint32", "comment": "组播组Id"}, {"name": "mcidVersion", "type": "uint64", "comment": "组播组Id的版本号"}, {"name": "mfibVersion", "type": "uint32", "comment": "mfib和elb一致性判断的版本号，mfib删除场景仅删除<=version的elb"}, {"name": "rootAddr", "type": "uint32", "comment": "VXLAN隧道场景，外层源地址"}, {"name": "originInIfIndex", "type": "uint32", "comment": "vxlan mlag场景，原始入接口"}, {"name": "mcStatSvcCtx", "type": "fixed", "size": 40, "comment": "基于芯片的统计id(uint32)和统计方式(uint8)"}, {"name": "svcCtxNormalPrio", "type": "fixed", "size": 8, "comment": "保存低优先级SVC返回的资源，ORCH层只保存，不作解释"}, {"name": "oldSvcCtxHighPrio", "type": "fixed", "size": 8, "comment": "接口信息"}, {"name": "svcCtxHighPrio", "type": "fixed", "size": 8, "comment": "保存低高先级SVC返回的资源，ORCH层只保存，不作解释；高4字节保存vid，低四字节保存fwdiftype"}, {"name": "oldServiceStatus", "type": "fixed", "size": 2, "comment": "老的入接口下发SVC状态，包括svc enp和svc hpp"}, {"name": "appSrcPid", "type": "uint32", "comment": "vrp生产者appid"}, {"name": "appVersion", "type": "uint32", "comment": "对账版本号，与APP对账使用"}, {"name": "smoothVersion", "type": "uint32", "comment": "平滑版本号，主备倒换各板表项平滑使用"}, {"name": "serviceStatus", "type": "fixed", "size": 2, "comment": "下发SVC状态，包括svc enp和svc hpp"}, {"name": "hppErrCode", "type": "uint32", "comment": "下发SVC错误码"}, {"name": "enpErrCode", "type": "uint32", "comment": "下发SVC错误码"}, {"name": "timeStampCreate", "type": "time", "comment": "创建时间"}, {"name": "timeStampSmooth", "type": "time", "comment": "平滑时间"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "l3mc_mfib", "fields": ["vrId", "vrfId", "grpAddr", "srcAddr"], "constraints": {"unique": true}, "comment": "主键索引"}, {"name": "dictionary_key", "index": {"type": "hashcluster"}, "node": "l3mc_mfib", "fields": ["vrId", "vrfId", "srcAddr", "grpAddr"], "constraints": {"unique": true}, "comment": "字典序排序索引"}, {"name": "inIfIndex_key", "index": {"type": "hashcluster"}, "node": "l3mc_mfib", "fields": ["inIfIndex"], "constraints": {"unique": false}, "comment": "入接口索引"}, {"name": "vrVrfIndex_key", "index": {"type": "hashcluster"}, "node": "l3mc_mfib", "fields": ["vrId", "vrfId"], "constraints": {"unique": false}, "comment": "命令行查询索引（vrId, vrfId）"}, {"name": "source_key", "index": {"type": "hashcluster"}, "node": "l3mc_mfib", "fields": ["vrId", "vrfId", "srcAddr"], "constraints": {"unique": false}, "comment": "命令行查询索引（vrId, vrfId, srcAddr）"}, {"name": "group_key", "index": {"type": "hashcluster"}, "node": "l3mc_mfib", "fields": ["vrId", "vrfId", "grpAddr"], "constraints": {"unique": false}, "comment": "命令行查询索引 （vrId, vrfId, grpAddr）"}, {"name": "sourceIntf_key", "index": {"type": "hashcluster"}, "node": "l3mc_mfib", "fields": ["vrId", "vrfId", "srcAddr", "inIfIndex"], "constraints": {"unique": false}, "comment": "命令行查询索引（vrId, vrfId, srcAddr, inIfIndex）"}, {"name": "groupIntf_key", "index": {"type": "hashcluster"}, "node": "l3mc_mfib", "fields": ["vrId", "vrfId", "grpAddr", "inIfIndex"], "constraints": {"unique": false}, "comment": "命令行查询索引（vrId, vrfId, grpAddr, inIfIndex）"}, {"name": "vrVrfIntf_key", "index": {"type": "hashcluster"}, "node": "l3mc_mfib", "fields": ["vrId", "vrfId", "inIfIndex"], "constraints": {"unique": false}, "comment": "命令行查询索引（vrId, vrfId, inIfIndex）"}, {"name": "srcpid_key", "index": {"type": "hashcluster"}, "node": "l3mc_mfib", "fields": ["vrId", "vrfId", "appSrcPid"], "constraints": {"unique": false}, "comment": "老化查询索引（vrId, vrfId, appSrcPid）"}, {"name": "errCode_key", "index": {"type": "hashcluster"}, "node": "l3mc_mfib", "fields": ["vrId", "enpErrCode"], "constraints": {"unique": false}, "comment": "命令行查询索引(errCode)"}, {"name": "sg_intf_key", "index": {"type": "hashcluster"}, "node": "l3mc_mfib", "fields": ["vrId", "grpAddr", "srcAddr", "inIfIndex"], "constraints": {"unique": false}, "comment": "S,G,Intf索引"}]}