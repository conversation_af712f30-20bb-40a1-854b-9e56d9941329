{"comment": "IF AFT", "version": "2.0", "type": "record", "name": "if", "config": {"check_validity": false}, "max_record_count": 500000, "fields": [{"name": "ifindex", "type": "uint32"}, {"name": "name", "type": "fixed", "size": 64}, {"name": "vrid", "type": "uint32"}, {"name": "if_type", "type": "uint32"}, {"name": "shutdown", "type": "uint32"}, {"name": "linkup", "type": "uint32"}, {"name": "tbtp", "type": "uint32"}, {"name": "tb", "type": "uint32"}, {"name": "tp", "type": "uint32"}, {"name": "port_switch", "type": "uint32"}, {"name": "forwardType", "type": "uint32"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "fixed", "size": 6}, {"name": "ipv4_mtu", "type": "uint16"}, {"name": "ipv4_enable", "type": "uint16"}, {"name": "ipv6_mtu", "type": "uint16"}, {"name": "ipv6_enable", "type": "uint16"}, {"name": "on_board", "type": "uint32"}, {"name": "lagid", "type": "uint32", "comment": "id of the interface trunk"}, {"name": "hppsvcflg", "type": "uint32"}, {"name": "error_down", "type": "uint32"}, {"name": "speed", "type": "uint64"}, {"name": "link_protocol", "type": "uint32"}, {"name": "vrf_index", "type": "uint32"}, {"name": "port_group_id", "type": "uint32"}, {"name": "if_group_id", "type": "uint32"}, {"name": "if_df", "type": "uint32"}, {"name": "encap_type", "type": "uint32"}, {"name": "is_subif", "type": "uint32"}, {"name": "mainifindex", "type": "uint32"}, {"name": "logicTB", "type": "uint32"}, {"name": "logicTP", "type": "uint32"}, {"name": "vlandomain", "type": "uint32"}, {"name": "coreId", "type": "uint32"}, {"name": "ifm", "type": "record", "fields": [{"name": "simple_name", "type": "uint32"}, {"name": "description", "type": "fixed", "size": 64}, {"name": "is_configure", "type": "uint32"}, {"name": "main_ifindex", "type": "uint32"}, {"name": "sub_max_num", "type": "uint32"}, {"name": "sub_curr_num", "type": "uint32"}, {"name": "error_down", "type": "uint32"}, {"name": "statistic", "type": "uint32"}, {"name": "vsys_id", "type": "uint32"}, {"name": "zone_id", "type": "uint32"}, {"name": "last_up_time", "type": "uint32"}, {"name": "last_down_time", "type": "uint32"}]}, {"name": "dev", "type": "record", "fields": [{"name": "dev_id", "type": "uint32"}, {"name": "chassis_id", "type": "uint32"}, {"name": "slot_id", "type": "uint32"}, {"name": "card_id", "type": "uint32"}, {"name": "unit_id", "type": "uint32"}, {"name": "port_id", "type": "uint32"}, {"name": "port_num", "type": "uint32"}]}, {"name": "l2", "type": "record", "fields": [{"name": "trunk_id", "type": "uint32", "comment": "phy member belong to a trunk id"}, {"name": "vlan_id", "type": "uint32"}, {"name": "l2_portindex", "type": "uint32"}, {"name": "vsi", "type": "uint32"}, {"name": "tunnel_id", "type": "uint32"}, {"name": "bd_id", "type": "uint32", "comment": "bd id"}]}, {"name": "port", "type": "record", "fields": [{"name": "speed", "type": "uint64"}, {"name": "duplex", "type": "uint32"}, {"name": "flow_control", "type": "uint32"}, {"name": "phy_type", "type": "uint32"}, {"name": "jumbo", "type": "uint32"}, {"name": "baud", "type": "uint32"}, {"name": "rmon", "type": "uint32"}, {"name": "phy_link", "type": "uint32"}, {"name": "if_mib", "type": "uint32"}, {"name": "on_board", "type": "uint32"}]}], "keys": [{"name": "if_pk", "index": {"type": "primary"}, "node": "if", "fields": ["ifindex"], "constraints": {"unique": true}}, {"name": "ifname_pk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "if", "fields": ["name"], "constraints": {"unique": false}}]}