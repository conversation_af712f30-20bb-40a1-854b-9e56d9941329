
#include "if_r21_1_data.h"
#include "static_schema_model.h"
#include "tools.h"
#include "securec.h"
#include <unistd.h>
#include "db_if_r21_1.h"
#include "db_trunk.h"
#include "cpu_cycles.h"
#include <pthread.h>

DB_DEF_CPU_CYCLES(if_r21_1_CreateObj);
DB_DEF_CPU_CYCLES(if_r21_1_Release);
DB_DEF_CPU_CYCLES(if_r21_1_Read);
DB_DEF_CPU_CYCLES(if_r21_1_ResetObj);
DB_DEF_CPU_CYCLES(if_r21_1_CreateChild);
DB_DEF_CPU_CYCLES(if_r21_1_GetFirst);
DB_DEF_CPU_CYCLES(if_r21_1_GetSecond);

p_static_schema_model_t g_if_r21_1_model = NULL;

status_t static_if_r21_1_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type, db_object* object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;
    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateObj);
    ret = db_create_if_r21_1_obj(obj_type, conn_type, &obj);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateObj);
    *object = obj;
    return ret;
}

status_t static_if_r21_1_create_db_obj_spec_conn_func(db_object_type obj_type, db_connect_t conn, db_object* object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    ret = db_create_if_r21_1_obj_specific_conn(obj_type, conn, &obj);
    CHECK_OK_RET(ret, "Create if spec conn object.");

    *object = obj;
    return ret;
}

void static_if_r21_1_release_db_obj_func(db_object object)
{
    DB_START_TEST_CPU_CYCLES(if_r21_1_Release);
    db_release_if_r21_1_object(object);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_Release);
}

status_t static_if_r21_1_reset_db_obj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    DB_START_TEST_CPU_CYCLES(if_r21_1_ResetObj);
    ret = db_reset_if_r21_1_object(object, obj_type);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_ResetObj);

    return ret;
}

status_t static_if_r21_1_create_db_batch_obj_func(db_conn_type type, db_object* object)
{
    status_t ret = STATUS_OK;
    db_object obj = NULL;

    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateObj);
    ret = db_create_if_r21_1_obj(BATCH_OPERATION, type, &obj);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateObj);
    *object = obj;
    return ret;
}

status_t static_if_r21_1_reset_db_bobj_func(db_object object, db_object_type obj_type)
{
    status_t ret = STATUS_OK;

    DB_START_TEST_CPU_CYCLES(if_r21_1_ResetObj);
    ret = db_reset_if_r21_1_batch_object(object, obj_type);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_ResetObj);

    return ret;
}

status_t static_if_r21_1_primary_key_malloc_func(void** key)
{
    *key = malloc(sizeof(db_if_r21_1_key_t));
    return 0;
}

void static_if_r21_1_primary_key_free_func(void* key)
{
    free(key);
}

status_t static_if_r21_1_struct_data_malloc_func(void** data, uint32_t* length)
{
#if _SPECIAL_COMPLEX
    *length = sizeof(if_r21_1_struct_t);
    *data = malloc(*length);
    return 0;
#else
    return 0;
#endif
}

void static_if_r21_1_struct_data_free_func(void* data)
{
#if _SPECIAL_COMPLEX
    free(data);
#else
    return;
#endif
}

status_t static_if_r21_1_get_field_func(db_object object, uint32_t read_field_cnt)
{
    status_t ret = STATUS_OK;
    unsigned int ifindex;
    char if_name[64];
    unsigned int vrid;
    unsigned int if_type;
    unsigned int shutdown;
    unsigned int linkup;
    unsigned int tbtp;
    unsigned int tb;
    unsigned int tp;
    unsigned int port_switch;
    unsigned int fwdIfType;
    unsigned char mac[6];
    unsigned short ipv4_mtu;
    unsigned short ipv6_mtu;
    unsigned int on_board;
    unsigned int lagid;
    uint64_t speed;
    uint32_t get_field_uint32;

    uint32_t if_macaddress_len = 0;
    uint32_t if_name_len = 0;
    int8_t* macAddress = NULL;
    int8_t* name = NULL;
    db_child_iterator child_iterator = NULL;
    unsigned int trunk_id;
    unsigned int vlan_id;
    unsigned int l2_portindex;
    unsigned int tunnel_id;
    unsigned int vsi;
    unsigned int id;
    if (read_field_cnt <= 1) {
        ret = db_get_if_r21_1_linkup(object, &linkup);
        CHECK_OK_RET(ret, "db_get_if_r21_1_linkup");
        return ret;
    }
    if (read_field_cnt == 8) {  // for V5 perf test, set args -R 8
        (void)db_get_if_r21_1_name(object, (int8_t**)&name, &if_name_len);
        (void)db_get_if_r21_1_linkup(object, &linkup);
        (void)db_get_if_r21_1_port_switch(object, &port_switch);
        (void)db_get_if_r21_1_macAddress(object, (int8_t**)&macAddress, &if_macaddress_len);

        (void)db_create_child_node(object, NULL, "dev", NULL, NULL, &child_iterator);
        (void)db_get_if_r21_1_dev_card_id(child_iterator, &id);
        (void)db_get_if_r21_1_dev_port_id(child_iterator, &id);
        (void)db_get_if_r21_1_dev_slot_id(child_iterator, &id);

        (void)db_create_child_node(object, NULL, "l2", NULL, NULL, &child_iterator);
        (void)db_get_if_r21_1_l2_trunk_id(child_iterator, &trunk_id);
        return ret;
    }

    // only read dev_node records
    if (read_field_cnt == 9) {  // for V5 perf test, set args -R 9
        DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
        (void)db_create_child_node(object, NULL, "dev", NULL, NULL, &child_iterator);
        DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);
        (void)db_get_if_r21_1_dev_dev_id(child_iterator, &id);
        (void)db_get_if_r21_1_dev_chassis_id(child_iterator, &id);
        (void)db_get_if_r21_1_dev_card_id(child_iterator, &id);
        (void)db_get_if_r21_1_dev_port_id(child_iterator, &id);
        (void)db_get_if_r21_1_dev_slot_id(child_iterator, &id);
        (void)db_get_if_r21_1_dev_unit_id(child_iterator, &id);
        (void)db_get_if_r21_1_dev_port_num(child_iterator, &id);
        return ret;
    }

    (void)db_get_if_r21_1_ifindex(object, &ifindex);
    DB_START_TEST_CPU_CYCLES(if_r21_1_GetFirst);
    (void)db_get_if_r21_1_name(object, (int8_t**)&name, &if_name_len);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_GetFirst);
    (void)memcpy_s(if_name, if_name_len, name, if_name_len);
    (void)db_get_if_r21_1_vrid(object, &vrid);
    (void)db_get_if_r21_1_if_type(object, (uint32_t*)&if_type);
    (void)db_get_if_r21_1_shutdown(object, &shutdown);
    (void)db_get_if_r21_1_linkup(object, &linkup);
    (void)db_get_if_r21_1_tbtp(object, &tbtp);
    (void)db_get_if_r21_1_tb(object, &tb);
    (void)db_get_if_r21_1_tp(object, &tp);
    (void)db_get_if_r21_1_port_switch(object, &port_switch);
    (void)db_get_if_r21_1_ipv4_mtu(object, &ipv4_mtu);
    (void)db_get_if_r21_1_ipv4_enable(object, &ipv4_mtu);
    (void)db_get_if_r21_1_ipv6_mtu(object, &ipv6_mtu);
    (void)db_get_if_r21_1_ipv6_enable(object, &ipv4_mtu);
    (void)db_get_if_r21_1_on_board(object, &on_board);
    (void)db_get_if_r21_1_forwardType(object, &fwdIfType);
    (void)db_get_if_r21_1_macAddress(object, (int8_t**)&macAddress, &if_macaddress_len);
    (void)memcpy_s(mac, if_macaddress_len, macAddress, if_macaddress_len);
    (void)db_get_if_r21_1_lagid(object, &lagid);
    (void)db_get_if_r21_1_hppsvcflg(object, &lagid);
    (void)db_get_if_r21_1_error_down(object, &lagid);
    (void)db_get_if_r21_1_speed(object, &speed);

    (void)db_get_if_r21_1_link_protocol(object, &get_field_uint32);
    (void)db_get_if_r21_1_vrf_index(object, &get_field_uint32);
    (void)db_get_if_r21_1_port_group_id(object, &get_field_uint32);
    (void)db_get_if_r21_1_if_group_id(object, &get_field_uint32);
    (void)db_get_if_r21_1_if_df(object, &get_field_uint32);
    (void)db_get_if_r21_1_encap_type(object, &get_field_uint32);
    (void)db_get_if_r21_1_is_subif(object, &get_field_uint32);
    (void)db_get_if_r21_1_mainifindex(object, &get_field_uint32);
    (void)db_get_if_r21_1_logicTB(object, &get_field_uint32);
    (void)db_get_if_r21_1_logicTP(object, &get_field_uint32);
    (void)db_get_if_r21_1_vlandomain(object, &get_field_uint32);
    (void)db_get_if_r21_1_coreId(object, &get_field_uint32);
    (void)db_get_if_r21_1_ipv4mss(object, &get_field_uint32);
    (void)db_get_if_r21_1_ipv6mss(object, &get_field_uint32);

    // only read root_node fileds
    if (read_field_cnt == 10) {  // for V5 perf test, set args -R 9
        return ret;
    }

    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    (void)db_create_child_node(object, NULL, "l2", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    DB_START_TEST_CPU_CYCLES(if_r21_1_GetSecond);
    (void)db_get_if_r21_1_l2_trunk_id(child_iterator, &trunk_id);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_GetSecond);
    (void)db_get_if_r21_1_l2_vlan_id(child_iterator, &vlan_id);
    (void)db_get_if_r21_1_l2_vsi(child_iterator, &vsi);
    (void)db_get_if_r21_1_l2_l2_portindex(child_iterator, &l2_portindex);
    (void)db_get_if_r21_1_l2_tunnel_id(child_iterator, &tunnel_id);
    (void)db_get_if_r21_1_l2_bd_id(child_iterator, &tunnel_id);

    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    (void)db_create_child_node(object, NULL, "dev", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    (void)db_get_if_r21_1_dev_dev_id(child_iterator, &id);
    (void)db_get_if_r21_1_dev_chassis_id(child_iterator, &id);
    (void)db_get_if_r21_1_dev_card_id(child_iterator, &id);
    (void)db_get_if_r21_1_dev_port_id(child_iterator, &id);
    (void)db_get_if_r21_1_dev_slot_id(child_iterator, &id);
    (void)db_get_if_r21_1_dev_unit_id(child_iterator, &id);
    (void)db_get_if_r21_1_dev_port_num(child_iterator, &id);

    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    (void)db_create_child_node(object, NULL, "port", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    (void)db_get_if_r21_1_port_speed(child_iterator, &speed);
    (void)db_get_if_r21_1_port_duplex(child_iterator, &id);
    (void)db_get_if_r21_1_port_flow_control(child_iterator, &id);
    (void)db_get_if_r21_1_port_phy_type(child_iterator, &id);
    (void)db_get_if_r21_1_port_jumbo(child_iterator, &id);
    (void)db_get_if_r21_1_port_baud(child_iterator, &id);
    (void)db_get_if_r21_1_port_rmon(child_iterator, &id);
    (void)db_get_if_r21_1_port_phy_link(child_iterator, &id);
    (void)db_get_if_r21_1_port_if_mib(child_iterator, &id);
    (void)db_get_if_r21_1_port_on_board(child_iterator, &id);

    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    (void)db_create_child_node(object, NULL, "ifm", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    int8_t* szBuf = NULL;
    unsigned int ulLen = 0;
    int8_t pszBuf[64] = {0};
    uint32_t simple_name;
    uint32_t is_configure;
    (void)db_get_if_r21_1_ifm_simple_name(child_iterator, &simple_name);
    (void)db_get_if_r21_1_ifm_is_configure(child_iterator, &is_configure);
    (void)db_get_if_r21_1_ifm_description(child_iterator, &szBuf, &ulLen);
    memcpy_s(pszBuf, ulLen, szBuf, ulLen);
    return ret;
}

status_t static_if_r21_1_convert_obj_to_struct(db_object object, void* data)
{
    status_t ret = STATUS_OK;
#if _SPECIAL_COMPLEX
    if_struct_t* obj_struct = (if_struct_t*)data;
    int8_t* buf_name = NULL;
    int8_t* buf_mac = NULL;
    int8_t* buf_desc = NULL;
    uint32_t buf_len = 0;
    db_child_iterator child_iterator = NULL;
    (void)db_get_if_r21_1_ifindex(object, &(obj_struct->ifindex));
    DB_START_TEST_CPU_CYCLES(if_r21_1_GetFirst);
    (void)db_get_if_r21_1_name(object, (int8_t**)&buf_name, &buf_len);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_GetFirst);
    (void)memcpy_s(obj_struct->name, buf_len, buf_name, buf_len);
    (void)db_get_if_r21_1_vrid(object, &(obj_struct->vrid));
    (void)db_get_if_r21_1_if_type(object, (uint32_t*)&(obj_struct->if_type));
    (void)db_get_if_r21_1_shutdown(object, &(obj_struct->shutdown));
    (void)db_get_if_r21_1_linkup(object, &(obj_struct->linkup));
    (void)db_get_if_r21_1_tbtp(object, &(obj_struct->tbtp));
    (void)db_get_if_r21_1_tb(object, &(obj_struct->tb));
    (void)db_get_if_r21_1_tp(object, &(obj_struct->tp));
    (void)db_get_if_r21_1_port_switch(object, &(obj_struct->port_switch));
    (void)db_get_if_r21_1_forwardType(object, &(obj_struct->forwardType));
    (void)db_get_if_r21_1_macAddress(object, (int8_t**)&buf_mac, &buf_len);
    (void)memcpy_s(obj_struct->macAddress, buf_len, buf_mac, buf_len);
    (void)db_get_if_r21_1_ipv4_mtu(object, &(obj_struct->ipv4_mtu));
    (void)db_get_if_r21_1_ipv6_mtu(object, &(obj_struct->ipv6_mtu));
    (void)db_get_if_r21_1_on_board(object, &(obj_struct->on_board));
    (void)db_get_if_r21_1_lagid(object, &(obj_struct->lagid));

    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    ret = db_create_child_node(object, NULL, "l2", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    if (ret) {
        return ret;
    }
    obj_struct->l2_flag = 1;
    DB_START_TEST_CPU_CYCLES(if_r21_1_GetSecond);
    (void)db_get_if_r21_1_l2_trunk_id(child_iterator, &(obj_struct->l2->trunk_id));
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_GetSecond);
    (void)db_get_if_r21_1_l2_vlan_id(child_iterator, &(obj_struct->l2->vlan_id));
    (void)db_get_if_r21_1_l2_vsi(child_iterator, &(obj_struct->l2->vsi));
    (void)db_get_if_r21_1_l2_l2_portindex(child_iterator, &(obj_struct->l2->l2_portindex));
    (void)db_get_if_r21_1_l2_tunnel_id(child_iterator, &(obj_struct->l2->tunnel_id));

    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    ret = db_create_child_node(object, NULL, "dev", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    if (ret) {
        return ret;
    }
    obj_struct->dev_flag = 1;
    (void)db_get_if_r21_1_dev_dev_id(child_iterator, &(obj_struct->dev->dev_id));
    (void)db_get_if_r21_1_dev_chassis_id(child_iterator, &(obj_struct->dev->chassis_id));
    (void)db_get_if_r21_1_dev_card_id(child_iterator, &(obj_struct->dev->card_id));
    (void)db_get_if_r21_1_dev_port_id(child_iterator, &(obj_struct->dev->port_id));
    (void)db_get_if_r21_1_dev_slot_id(child_iterator, &(obj_struct->dev->slot_id));
    (void)db_get_if_r21_1_dev_unit_id(child_iterator, &(obj_struct->dev->unit_id));

    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    ret = db_create_child_node(object, NULL, "ifm", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    if (ret) {
        return ret;
    }
    obj_struct->ifm_flag = 1;
    (void)db_get_if_r21_1_ifm_simple_name(child_iterator, &(obj_struct->ifm->simple_name));
    (void)db_get_if_r21_1_ifm_is_configure(child_iterator, &(obj_struct->ifm->is_configure));
    (void)db_get_if_r21_1_ifm_description(child_iterator, &buf_desc, &buf_len);
    if (sizeof(obj_struct->ifm->description) != buf_len) {
        ret = -1;
        printf("get len : %u, expect %lu", buf_len, sizeof(obj_struct->ifm->description));
        return ret;
    }
    memcpy_s(obj_struct->ifm->description, buf_len, buf_desc, buf_len);

#endif
    return ret;
}

status_t setIfDevNode_r21_1(db_object object, void* obj_struct, uint64_t uiVrIndex)
{
    status_t ret = STATUS_OK;
#if _SPECIAL_COMPLEX
    if_dev_t* dev = (if_dev_t*)obj_struct;
    dev->dev_id = uiVrIndex;
    dev->chassis_id = uiVrIndex;
    dev->slot_id = uiVrIndex;
    dev->card_id = uiVrIndex;
    dev->unit_id = uiVrIndex;
    dev->port_id = uiVrIndex;
    return 0;

#else
    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    ret = db_create_child_node(object, NULL, "dev", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);

    ret = db_set_if_r21_1_dev_dev_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_dev_id");
    ret = db_set_if_r21_1_dev_chassis_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_chassis_id");
    ret = db_set_if_r21_1_dev_card_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_card_id");
    ret = db_set_if_r21_1_dev_port_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_port_id");
    ret = db_set_if_r21_1_dev_slot_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_slot_id");
    ret = db_set_if_r21_1_dev_unit_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_unit_id");
    ret = db_set_if_r21_1_dev_port_num(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_dev_port_num");
    return ret;
#endif
    return ret;
}

status_t setIfL2Node_r21_1(db_object object, void* obj_struct, uint64_t uiVrIndex)
{
    status_t ret = STATUS_OK;
#if _SPECIAL_COMPLEX
    if_l2_t* l2 = (if_l2_t*)obj_struct;
    l2->trunk_id = uiVrIndex;
    l2->vlan_id = uiVrIndex;
    l2->l2_portindex = uiVrIndex;
    l2->vsi = uiVrIndex;
    l2->tunnel_id = uiVrIndex;
    return 0;

#else
    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    ret = db_create_child_node(object, NULL, "l2", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);

    ret = db_set_if_r21_1_l2_trunk_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_trunk_id");
    ret = db_set_if_r21_1_l2_vlan_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_vlan_id");
    ret = db_set_if_r21_1_l2_l2_portindex(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_l2_portindex");
    ret = db_set_if_r21_1_l2_vsi(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_vsi");
    ret = db_set_if_r21_1_l2_tunnel_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_tunnel_id");
    ret = db_set_if_r21_1_l2_bd_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_l2_bd_id");
    return ret;
#endif
    return ret;
}

status_t setIfIfmNode_r21_1(db_object object, void* obj_struct, uint64_t uiVrIndex)
{
    status_t ret = STATUS_OK;
#if _SPECIAL_COMPLEX
    if_ifm_t* ifm = (if_ifm_t*)obj_struct;
    ifm->simple_name = uiVrIndex;
    // int8_t arr[64] = {0};
    // memset(arr, 0, 64);
    // memcpy(ifm->description, (int8_t *)(arr), 64);
    memcpy(ifm->description, (int8_t*)(ZERO_64_BYTES), 64);
    ifm->is_configure = uiVrIndex;
    ifm->main_ifindex = uiVrIndex;
    ifm->sub_max_num = uiVrIndex;
    ifm->sub_curr_num = uiVrIndex;
    ifm->error_down = uiVrIndex;
    ifm->statistic = uiVrIndex;
    ifm->vsys_id = uiVrIndex;
    ifm->zone_id = uiVrIndex;
    ifm->last_up_time = uiVrIndex;
    ifm->last_down_time = uiVrIndex;
    return 0;

#else
    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    ret = db_create_child_node(object, NULL, "ifm", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);

    ret = db_set_if_r21_1_ifm_simple_name(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_simple_name");
    ret = db_set_if_r21_1_ifm_description(child_iterator, (int8_t*)(ZERO_64_BYTES), 64);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_description");
    ret = db_set_if_r21_1_ifm_is_configure(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_is_configure");
    ret = db_set_if_r21_1_ifm_main_ifindex(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_main_ifindex");
    ret = db_set_if_r21_1_ifm_sub_max_num(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_sub_max_num");
    ret = db_set_if_r21_1_ifm_sub_curr_num(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_sub_curr_num");
    ret = db_set_if_r21_1_ifm_error_down(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_error_down");
    ret = db_set_if_r21_1_ifm_statistic(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_statistic");
    ret = db_set_if_r21_1_ifm_vsys_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_vsys_id");
    ret = db_set_if_r21_1_ifm_zone_id(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_zone_id");
    ret = db_set_if_r21_1_ifm_last_up_time(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_last_up_time");
    ret = db_set_if_r21_1_ifm_last_down_time(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_last_down_time");
    return ret;
#endif

    return ret;
}

status_t setIfPortNode_r21_1(db_object object, void* obj_struct, uint64_t uiVrIndex)
{
    status_t ret = STATUS_OK;

#if _SPECIAL_COMPLEX
    if_port_t* port = (if_port_t*)obj_struct;
    port->speed = uiVrIndex;
    port->duplex = uiVrIndex;
    port->flow_control = uiVrIndex;
    port->phy_type = uiVrIndex;
    port->jumbo = uiVrIndex;
    port->baud = uiVrIndex;
    port->rmon = uiVrIndex;
    port->phy_link = uiVrIndex;
    port->if_mib = uiVrIndex;
    port->on_board = uiVrIndex;
    return 0;

#else

    db_child_iterator child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    ret = db_create_child_node(object, NULL, "port", NULL, NULL, &child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);

    ret = db_set_if_r21_1_port_speed(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_port_speed");
    ret = db_set_if_r21_1_port_duplex(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_port_duplex");
    ret = db_set_if_r21_1_port_flow_control(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_port_flow_control");
    ret = db_set_if_r21_1_port_phy_type(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_port_phy_type");
    ret = db_set_if_r21_1_port_jumbo(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_port_jumbo");
    ret = db_set_if_r21_1_port_baud(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_port_baud");
    ret = db_set_if_r21_1_port_rmon(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_port_rmon");
    ret = db_set_if_r21_1_port_phy_link(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_port_phy_link");
    ret = db_set_if_r21_1_port_if_mib(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_port_if_mib");
    ret = db_set_if_r21_1_port_on_board(child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_port_on_board");

    return ret;
#endif

    return ret;
}

status_t static_if_r21_1_set_field_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
    int8_t arr[64] = {0};
    //memcpy(arr, (int8_t*)&uiVrIndex, 8);
    snprintf((char *)arr, 32, "if_name_%.23llu", uiVrIndex);
    // ret = db_set_if_r21_1_ifindex(object, (uint32_t)(uiVrIndex & ((uint32_t)~0)));
    // CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_ifindex");
    ret = db_set_if_r21_1_name(object, (int8_t*)arr, 64);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_name");
    ret = db_set_if_r21_1_vrid(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_vrid");
    ret = db_set_if_r21_1_if_type(object, 2);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_if_type");
#if RANDOM   // 覆盖写，更新2个非索引字段
    ret = db_set_if_r21_1_shutdown(object, ((uiVrIndex + 7) & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_shutdown");
    ret = db_set_if_r21_1_linkup(object, ((uiVrIndex + 8) & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_linkup");
#else
    ret = db_set_if_r21_1_shutdown(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_shutdown");
    ret = db_set_if_r21_1_linkup(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_linkup");
#endif
    ret = db_set_if_r21_1_tbtp(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_tbtp");
    ret = db_set_if_r21_1_tb(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_tb");
    ret = db_set_if_r21_1_tp(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_tp");
    ret = db_set_if_r21_1_port_switch(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_tp");
    ret = db_set_if_r21_1_forwardType(object, 2);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_tp");
    ret = db_set_if_r21_1_macAddress(object, (int8_t*)arr, 6);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_l2_l3_flag");
    ret = db_set_if_r21_1_ipv4_mtu(object, (uiVrIndex & (uint16_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_ipv4_mtu");
    ret = db_set_if_r21_1_ipv4_enable(object, (uiVrIndex & (uint16_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_ipv4_enable");
    ret = db_set_if_r21_1_ipv6_mtu(object, (uiVrIndex & (uint16_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_ipv6_mtu");
    ret = db_set_if_r21_1_ipv6_enable(object, (uiVrIndex & (uint16_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_ipv6_mtu");
    ret = db_set_if_r21_1_on_board(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_on_board");
    ret = db_set_if_r21_1_lagid(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_lagid");
    ret = db_set_if_r21_1_hppsvcflg(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_lagid");
    ret = db_set_if_r21_1_error_down(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_lagid");
    ret = db_set_if_r21_1_speed(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_lagid");

    ret = db_set_if_r21_1_link_protocol(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_link_protocol");
    ret = db_set_if_r21_1_vrf_index(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_vrf_index");
    ret = db_set_if_r21_1_port_group_id(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_port_group_id");
    ret = db_set_if_r21_1_if_group_id(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_if_group_id");
    ret = db_set_if_r21_1_if_df(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_if_df");
    ret = db_set_if_r21_1_encap_type(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_encap_type");
    ret = db_set_if_r21_1_is_subif(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_is_subif");
    ret = db_set_if_r21_1_mainifindex(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_mainifindex");
    ret = db_set_if_r21_1_logicTB(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_logicTB");
    ret = db_set_if_r21_1_logicTP(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_logicTP");
    ret = db_set_if_r21_1_vlandomain(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_vlandomain");
    ret = db_set_if_r21_1_coreId(object, (uiVrIndex & (uint32_t)(~0)));
    CHECK_OK_RET_DEBUG(ret, "set_if_coreId");

    ret = setIfDevNode_r21_1(object, NULL, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set  dev node");

    ret = setIfL2Node_r21_1(object, NULL, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set  l2 node");

    ret = setIfIfmNode_r21_1(object, NULL, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set  ifm node");

    ret = setIfPortNode_r21_1(object, NULL, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set  port node");
    // set time in fields for sub delay test, ./bin -v1
    if (g_if_r21_1_model->data_value_type == 1) {
        struct timeval start;
        db_static_gettimeofday(&start, DB_NULL);
        ret = db_set_if_r21_1_ipv4mss(object, (start.tv_sec & (uint32_t)(~0)));
        CHECK_OK_RET_DEBUG(ret, "set_if_coreId");
        ret = db_set_if_r21_1_ipv6mss(object, (start.tv_usec & (uint32_t)(~0)));
        CHECK_OK_RET_DEBUG(ret, "set_if_coreId");
    } else {
        ret = db_set_if_r21_1_ipv4mss(object, (uiVrIndex & (uint32_t)(~0)));
        CHECK_OK_RET_DEBUG(ret, "set_if_coreId");
        ret = db_set_if_r21_1_ipv6mss(object, (uiVrIndex & (uint32_t)(~0)));
        CHECK_OK_RET_DEBUG(ret, "set_if_coreId");
    }
    return ret;
}

int static_if_r21_1_set_obj_struct_data(void* data, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num)
{
#if _SPECIAL_COMPLEX
    if_struct_t* obj_struct = (if_struct_t*)data;

    int8_t arr[64] = {0};
    memcpy(arr, (int8_t*)&uiVrIndex, 8);
    obj_struct->ifindex = (uint32_t)(uiVrIndex & ((uint32_t)~0));
    memcpy(obj_struct->name, (int8_t*)arr, 64);
    obj_struct->vrid = 0;
    obj_struct->if_type = 0;
    obj_struct->shutdown = uiVrIndex;
    obj_struct->linkup = 0;
    obj_struct->tbtp = 0xffffffff;
    obj_struct->tb = 0xffffffff;
    obj_struct->tp = 0xffffffff;
    obj_struct->port_switch = 0;
    obj_struct->forwardType = 0;
    memcpy(obj_struct->macAddress, (int8_t*)arr, 6);
    obj_struct->ipv4_mtu = 1500;
    obj_struct->ipv6_mtu = 0xffff;
    obj_struct->on_board = uiVrIndex;
    obj_struct->lagid = uiVrIndex;

    if (array > 0) {
        obj_struct->dev_flag = 1;
        (void)setIfDevNode_r21_1(NULL, obj_struct->dev, uiVrIndex);
        obj_struct->l2_flag = 1;
        (void)setIfL2Node_r21_1(NULL, obj_struct->l2, uiVrIndex);
        obj_struct->ifm_flag = 1;
        (void)setIfIfmNode_r21_1(NULL, obj_struct->ifm, uiVrIndex);
    }

    obj_struct->port_flag = 0;
#endif

    return 0;
}

int static_if_r21_1_check_field_value(void* data, uint64_t check_value, uint32_t array, uint32_t thread_local_num)
{
#ifdef TEST_DEBUG
#if _SPECIAL_COMPLEX
    if_struct_t expect_data;
    memset(&expect_data, 0, sizeof(expect_data));
    if_dev_t dev = {0};
    if_l2_t l2 = {0};
    if_ifm_t ifm = {0};
    if_port_t port = {0};
    expect_data.dev = &dev;
    expect_data.l2 = &l2;
    expect_data.ifm = &ifm;
    expect_data.port = &port;
    (void)static_if_r21_1_set_obj_struct_data(&expect_data, check_value, array, thread_local_num);

    if_struct_t* obj_struct = (if_struct_t*)data;

    CHECK_INT_EQUAL_DEBUG(obj_struct->ifindex, expect_data.ifindex);
    CHECK_ARRAY_EQUAL_DEBUG(obj_struct->name, expect_data.name, sizeof(obj_struct->name));
    // CHECK_INT_EQUAL_DEBUG(obj_struct->vrid, expect_data.vrid);
    CHECK_INT_EQUAL_DEBUG(obj_struct->if_type, expect_data.if_type);
    CHECK_INT_EQUAL_DEBUG(obj_struct->shutdown, expect_data.shutdown);

    CHECK_INT_EQUAL_DEBUG(obj_struct->linkup, expect_data.linkup);
    CHECK_INT_EQUAL_DEBUG(obj_struct->tbtp, expect_data.tbtp);
    CHECK_INT_EQUAL_DEBUG(obj_struct->tb, expect_data.tb);
    CHECK_INT_EQUAL_DEBUG(obj_struct->tp, expect_data.tp);

    CHECK_INT_EQUAL_DEBUG(obj_struct->port_switch, expect_data.port_switch);
    CHECK_INT_EQUAL_DEBUG(obj_struct->forwardType, expect_data.forwardType);
    CHECK_ARRAY_EQUAL_DEBUG(obj_struct->macAddress, expect_data.macAddress, sizeof(obj_struct->macAddress));
    CHECK_INT_EQUAL_DEBUG(obj_struct->ipv4_mtu, expect_data.ipv4_mtu);
    CHECK_INT_EQUAL_DEBUG(obj_struct->ipv6_mtu, expect_data.ipv6_mtu);
    CHECK_INT_EQUAL_DEBUG(obj_struct->on_board, expect_data.on_board);
    // CHECK_INT_EQUAL_DEBUG(obj_struct->lagid, expect_data.lagid);
    CHECK_INT_EQUAL_DEBUG(obj_struct->dev_flag, expect_data.dev_flag);
    CHECK_INT_EQUAL_DEBUG(obj_struct->l2_flag, expect_data.l2_flag);
    CHECK_INT_EQUAL_DEBUG(obj_struct->ifm_flag, expect_data.ifm_flag);
    CHECK_INT_EQUAL_DEBUG(obj_struct->port_flag, expect_data.port_flag);

    if (obj_struct->dev_flag) {
        CHECK_INT_EQUAL_DEBUG(obj_struct->dev->dev_id, expect_data.dev->dev_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->dev->chassis_id, expect_data.dev->chassis_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->dev->slot_id, expect_data.dev->slot_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->dev->card_id, expect_data.dev->card_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->dev->unit_id, expect_data.dev->unit_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->dev->port_id, expect_data.dev->port_id);
    }

    if (obj_struct->l2_flag) {
        // CHECK_INT_EQUAL_DEBUG(obj_struct->l2->trunk_id, expect_data.l2->trunk_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->l2->vlan_id, expect_data.l2->vlan_id);
        CHECK_INT_EQUAL_DEBUG(obj_struct->l2->l2_portindex, expect_data.l2->l2_portindex);
        CHECK_INT_EQUAL_DEBUG(obj_struct->l2->vsi, expect_data.l2->vsi);
        CHECK_INT_EQUAL_DEBUG(obj_struct->l2->tunnel_id, expect_data.l2->tunnel_id);
    }

    if (obj_struct->ifm_flag) {
        CHECK_INT_EQUAL_DEBUG(obj_struct->ifm->simple_name, expect_data.ifm->simple_name);
        CHECK_ARRAY_EQUAL_DEBUG(
            obj_struct->ifm->description, expect_data.ifm->description, DB_static_if_r21_1_DESCRIPTION_LEN);
        CHECK_INT_EQUAL_DEBUG(obj_struct->ifm->is_configure, expect_data.ifm->is_configure);
    }

    if (obj_struct->port_flag) {
        return -1;
    }

#endif
#endif

    return STATUS_OK;
}

status_t static_if_r21_1_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num)
{
    status_t ret = STATUS_OK;
#if _SPECIAL_COMPLEX
    if_struct_t obj_struct;
    memset(&obj_struct, 0, sizeof(obj_struct));
    if_dev_t dev = {0};
    if_l2_t l2 = {0};
    if_ifm_t ifm = {0};
    if_port_t port = {0};
    obj_struct.dev = &dev;
    obj_struct.l2 = &l2;
    obj_struct.ifm = &ifm;
    obj_struct.port = &port;
    
    (void)static_if_r21_1_set_obj_struct_data(&obj_struct, uiVrIndex, array, thread_local_num);

set_again:
    ret = db_set_if_r21_1_all_fields(object, &obj_struct, sizeof(obj_struct));  // root object struct size
    if (ret == STATUS_QUEUE_EMPTY) {
        usleep(50);
        goto set_again;
    }
    return ret;

#else
    ret = db_set_if_r21_1_ifindex(object, (uint32_t)(uiVrIndex & ((uint32_t)~0)));
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_ifindex");
    ret = static_if_r21_1_set_field_func(object, uiVrIndex, array, thread_local_num);
    return ret;

#endif

    return ret;
}

status_t static_if_r21_1_get_field_by_key_func(
    db_object object, uint64_t uiVrIndex, uint32_t thread_local_num, uint32_t read_field_cnt)
{
    status_t ret = STATUS_OK;
    db_key_info_t key_info = {0};
    db_if_r21_1_key_t key_data = {0};
    key_info.index_id = DB_IF_R21_1_IF_PK_ID;
    key_info.key_len = sizeof(key_data);
    key_data.ifindex = (uint32_t)(uiVrIndex & ((uint32_t)~0));
    STATIC_TIME_DELAY_START(g_time_delay_data);
    DB_START_TEST_CPU_CYCLES(if_r21_1_Read);
    ret = db_read_obj(object, &key_info, &key_data);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_Read);
    STATIC_TIME_DELAY_STOP(g_time_delay_data);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "db_read_obj");
        return ret;
    }
    ret = static_if_r21_1_get_field_func(object, read_field_cnt);
    if (ret != STATUS_OK) {
        LOG_ERROR(ret, "static_if_r21_1_get_field_func");
        return ret;
    }
    return ret;
}

status_t static_if_r21_1_key_set_func(
    db_object object, uint64_t uiVrIndex, uint32_t thread_local_num, void* key_info, void* key_data)
{
    db_if_r21_1_key_t* pri_key_data = (db_if_r21_1_key_t*)key_data;
    db_key_info_t* pri_key_info = (db_key_info_t*)key_info;
    memset(pri_key_data, 0, sizeof(db_if_r21_1_key_t));
    memset(pri_key_info, 0, sizeof(db_key_info_t));
    pri_key_info->index_id = DB_IF_R21_1_IF_PK_ID;
    pri_key_info->key_len = sizeof(db_if_r21_1_key_t);
    pri_key_data->ifindex = (uint32_t)(uiVrIndex & ((uint32_t)~0));
    return 0;
}

status_t static_if_r21_1_set_update_field_func(db_object object, uint64_t uiVrIndex)
{
    status_t ret = STATUS_OK;
    int8_t arr[64] = {0};
    memcpy(arr, (int8_t*)&uiVrIndex, 8);

    ret = db_set_if_r21_1_name(object, (int8_t*)arr, 64);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_name");
    ret = db_set_if_r21_1_vrid(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_vrid");
    ret = db_set_if_r21_1_if_type(object, 2);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_if_type");
    ret = db_set_if_r21_1_shutdown(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_shutdown");
    ret = db_set_if_r21_1_linkup(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_linkup");
    ret = db_set_if_r21_1_tbtp(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_tbtp");
    ret = db_set_if_r21_1_tb(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_tb");
    ret = db_set_if_r21_1_tp(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_tp");
    ret = db_set_if_r21_1_port_switch(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_tp");
    ret = db_set_if_r21_1_forwardType(object, 2);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_tp");
    ret = db_set_if_r21_1_macAddress(object, (int8_t*)arr, 6);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_l2_l3_flag");
    ret = db_set_if_r21_1_ipv4_mtu(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_linkup");
    ret = db_set_if_r21_1_ipv6_mtu(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "db_set_if_r21_1_ipv6_mtu");
    ret = db_set_if_r21_1_on_board(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_on_board");
    ret = db_set_if_r21_1_lagid(object, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_lagid");

    db_child_iterator dev_child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    // ret = db_create_child_node(object, NULL, "dev", &key_info, &key_data, &dev_child_iterator);
    ret = db_create_child_node(object, NULL, "dev", NULL, NULL, &dev_child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);

    ret = db_set_if_r21_1_dev_dev_id(dev_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_dev_id");
    ret = db_set_if_r21_1_dev_chassis_id(dev_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_chassis_id");
    ret = db_set_if_r21_1_dev_card_id(dev_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_card_id");
    ret = db_set_if_r21_1_dev_port_id(dev_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_port_id");
    ret = db_set_if_r21_1_dev_slot_id(dev_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_slot_id");
    ret = db_set_if_r21_1_dev_unit_id(dev_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_dev_unit_id");

    db_child_iterator l2_child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    // ret = db_create_child_node(object, NULL, "l2", &key_info, &key_data, &l2_child_iterator);
    ret = db_create_child_node(object, NULL, "l2", NULL, NULL, &l2_child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);

    ret = db_set_if_r21_1_l2_trunk_id(l2_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_trunk_id");
    ret = db_set_if_r21_1_l2_vlan_id(l2_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_vlan_id");
    ret = db_set_if_r21_1_l2_l2_portindex(l2_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_l2_portindex");
    ret = db_set_if_r21_1_l2_vsi(l2_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_vsi");
    ret = db_set_if_r21_1_l2_tunnel_id(l2_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_l2_tunnel_id");

    db_child_iterator ifm_child_iterator = NULL;
    DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    // ret = db_create_child_node(object, NULL, "ifm", &key_info, &key_data, &ifm_child_iterator);
    ret = db_create_child_node(object, NULL, "ifm", NULL, NULL, &ifm_child_iterator);
    DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);

    ret = db_set_if_r21_1_ifm_simple_name(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_simple_name");
    ret = db_set_if_r21_1_ifm_description(ifm_child_iterator, (int8_t*)(ZERO_64_BYTES), 64);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_description");
    ret = db_set_if_r21_1_ifm_is_configure(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_is_configure");
    ret = db_set_if_r21_1_ifm_main_ifindex(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_main_ifindex");
    ret = db_set_if_r21_1_ifm_sub_max_num(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_sub_max_num");
    ret = db_set_if_r21_1_ifm_sub_curr_num(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_sub_curr_num");
    ret = db_set_if_r21_1_ifm_error_down(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_error_down");
    ret = db_set_if_r21_1_ifm_statistic(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_statistic");
    ret = db_set_if_r21_1_ifm_vsys_id(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_vsys_id");
    ret = db_set_if_r21_1_ifm_zone_id(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_zone_id");
    ret = db_set_if_r21_1_ifm_last_up_time(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_last_up_time");
    ret = db_set_if_r21_1_ifm_last_down_time(ifm_child_iterator, uiVrIndex);
    CHECK_OK_RET_DEBUG(ret, "set_if_ifm_last_down_time");

    // set time in fields for sub delay test, ./bin -v1
    if (g_if_r21_1_model->data_value_type == 1) {
        struct timeval start;
        db_static_gettimeofday(&start, DB_NULL);
        ret = db_set_if_r21_1_ipv4mss(object, (start.tv_sec & (uint32_t)(~0)));
        CHECK_OK_RET_DEBUG(ret, "set_if_coreId");
        ret = db_set_if_r21_1_ipv6mss(object, (start.tv_usec & (uint32_t)(~0)));
        CHECK_OK_RET_DEBUG(ret, "set_if_coreId");
    } else {
        ret = db_set_if_r21_1_ipv4mss(object, (uiVrIndex & (uint32_t)(~0)));
        CHECK_OK_RET_DEBUG(ret, "set_if_coreId");
        ret = db_set_if_r21_1_ipv6mss(object, (uiVrIndex & (uint32_t)(~0)));
        CHECK_OK_RET_DEBUG(ret, "set_if_coreId");
    }
    // db_child_iterator child_iterator = NULL;
    // DB_START_TEST_CPU_CYCLES(if_r21_1_CreateChild);
    // ret = db_create_child_node(object, NULL, "l2", &key_info, &key_data, &child_iterator);
    // DB_STOP_TEST_CPU_CYCLES(if_r21_1_CreateChild);

    // ret = db_set_if_r21_1_l2_trunk_id(child_iterator, uiVrIndex + 1);
    // CHECK_OK_RET_DEBUG(ret, "set_if_l2_trunk_id");
    return ret;
}

status_t static_if_r21_1_update_func(db_object object, uint64_t uiVrIndex)
{
    status_t ret = STATUS_OK;
    // ret = static_if_r21_1_set_update_field_func(object, uiVrIndex + 1);
    ret = static_if_r21_1_set_field_func(object, uiVrIndex + 1, 0, 0);
    return ret;
}

status_t static_if_r21_1_update_bykey_func(
    db_object object, uint64_t uiVrIndex, uint32_t thread_local_num, uint32_t delta_value, uint32_t field_cnt)
{
    status_t ret = STATUS_OK;
    db_key_info_t key_info = {0};
    db_if_r21_1_key_t key_data = {0};
    // key_info.index_id = DB_IF_R21_1_IF_PK_ID;
    // key_info.key_len = sizeof(key_data);
    // key_data.ifindex = (uint32_t)(uiVrIndex & ((uint32_t)~0));
    (void)static_if_r21_1_key_set_func(object, uiVrIndex, thread_local_num, &key_info, &key_data);
    uiVrIndex += delta_value;
    if (field_cnt <= 1) {
        ret = db_set_if_r21_1_linkup(object, uiVrIndex);
        CHECK_OK_RET(ret, "db_set_if_r21_1_linkup");
        return ret;
    } else if (field_cnt <= 2) {
        ret = db_set_if_r21_1_shutdown(object, uiVrIndex);
        CHECK_OK_RET(ret, "db_set_if_r21_1_shutdown");
        ret = db_set_if_r21_1_linkup(object, uiVrIndex);
        CHECK_OK_RET(ret, "db_set_if_r21_1_linkup");
        return ret;
    } else {
    }

    // ret = static_if_r21_1_set_update_field_func(object, uiVrIndex);
    ret = static_if_r21_1_set_field_func(object, uiVrIndex, 0, thread_local_num);
    // ret = db_update_obj(object, &key_info, &key_data);
    // CHECK_OK_RET_DEBUG(ret, "db_update_obj failed.");

    return ret;
}

status_t static_if_r21_1_fullscan_func(db_object object, uint64_t *op)
{
    status_t ret = STATUS_OK;
    db_root_iterator root_iterator;
    uint64_t scan_idx = 0;

    ret = db_create_root_iter(object, NULL, NULL, &root_iterator);
    CHECK_OK_RET(ret, "Create root iterator failed.");

    // for (ret = db_root_iterator_next(root_iterator);; ret = db_root_iterator_next(root_iterator)) {
    for (;;) {
        STATIC_TIME_DELAY_START(g_time_delay_data);
        ret = db_root_iterator_next(root_iterator);
        STATIC_TIME_DELAY_STOP(g_time_delay_data);
        if (ret == STATUS_SCAN_TO_END) {
            ret = STATUS_OK;
            break;
        }
        CHECK_OK_RET(ret, "Scan table failed");
        ret = static_if_r21_1_get_field_func(object, FIELD_CNT_ALL);
        if (ret != STATUS_OK) {
            LOG_ERROR(ret, "Get field failed");
            break;
        }
        scan_idx++;
    }
    *op = scan_idx;
    return ret;
}

status_t static_if_r21_1_register_schema_model(void* schema_model)
{
    g_if_r21_1_model = (p_static_schema_model_t)(schema_model);
    if (g_if_r21_1_model->schema_json_file_name == NULL) {
        g_if_r21_1_model->schema_json_file_name = "if_r21_1";
        g_if_r21_1_model->obj_create_func = static_if_r21_1_create_db_obj_func;
        g_if_r21_1_model->obj_create_spec_conn_func = static_if_r21_1_create_db_obj_spec_conn_func;
        g_if_r21_1_model->obj_release_func = static_if_r21_1_release_db_obj_func;
        g_if_r21_1_model->obj_reset_func = static_if_r21_1_reset_db_obj_func;
        g_if_r21_1_model->bobj_reset_func = static_if_r21_1_reset_db_bobj_func;
        // g_if_r21_1_model->bobj_create_func = static_if_r21_1_create_db_batch_obj_func;
        g_if_r21_1_model->obj_set_func = static_if_r21_1_set_obj_func;
        g_if_r21_1_model->malloc_primary_key_func = static_if_r21_1_primary_key_malloc_func;
        g_if_r21_1_model->free_primary_key_func = static_if_r21_1_primary_key_free_func;
        g_if_r21_1_model->malloc_struct_data_func = static_if_r21_1_struct_data_malloc_func;
        g_if_r21_1_model->free_struct_data_func = static_if_r21_1_struct_data_free_func;
        g_if_r21_1_model->getfield_bykey_func = static_if_r21_1_get_field_by_key_func;
        g_if_r21_1_model->pri_key_set_func = static_if_r21_1_key_set_func;
        g_if_r21_1_model->getfield_func = static_if_r21_1_get_field_func;
        g_if_r21_1_model->update_func = static_if_r21_1_update_func;
        g_if_r21_1_model->update_bykey_func = static_if_r21_1_update_bykey_func;
        g_if_r21_1_model->fullscan_func = static_if_r21_1_fullscan_func;
    }
    return 0;
}

