#ifndef __LOCAL_DB_PORT_VLAN_CFG_H__
#define __LOCAL_DB_PORT_VLAN_CFG_H__

#include "gm_errno.h"
#include "type.h"
#include "db_wrapper.h"
#include "db_wrapper_private.h"

status_t local_db_port_vlan_cfg_create_db_obj_func(db_object_type obj_type, db_conn_type conn_type,
                                              db_object *object);
void local_db_port_vlan_cfg_release_db_obj_func(db_object object);
status_t local_db_port_vlan_cfg_reset_db_obj_func(db_object object, db_object_type obj_type);
status_t local_db_port_vlan_cfg_set_obj_func(db_object object, uint64_t uiVrIndex, uint32_t array, uint32_t thread_local_num);
status_t local_db_port_vlan_cfg_create_db_batch_obj_func(db_conn_type, db_object *);
status_t local_db_port_vlan_cfg_key_set_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num, void *key_info, void *key_data);
status_t local_db_port_vlan_cfg_get_field_by_key_func(db_object object, uint64_t uiVrIndex, uint32_t thread_local_num);
status_t local_db_port_vlan_cfg_reset_db_bobj_func(db_object object, db_object_type obj_type);
status_t local_db_port_vlan_cfg_register_shcema_model(void *schema_model);
status_t local_db_port_vlan_cfg_get_field_func(db_object object, uint32_t get_field_cnt);

#endif
