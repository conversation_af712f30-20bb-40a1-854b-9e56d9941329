#include <unistd.h>
#include <stdio.h>
#include <string.h>

#include "securec.h"
#include "db_wrapper.h"
#include "db_wrapper_private.h"
#include "type.h"
#include "localize_common.h"
#include "localize_multi_thread.h"
#include "localize_func_async.h"
#include "localize_func.h"
#include "localize_config.h"
#include "tools.h"
#include "local_schema_model.h"
#include "local_db_nd_nhp6.h"
#include "local_db_nd_re6.h"
#include "local_db_nhp_res_info.h"
#include "local_db_nhp6_group.h"
#include "local_db_l3if.h"
#include "local_db_if.h"
#include "local_db_nd_fake.h"
#include "local_db_nd.h"
#include "local_db_aib.h"
#include "nd_miss.h"
#include "db_nhp_res_info.h"
#include "local_db_nd_nhp6.h"
#include "local_db_nd_re6.h"
#include "local_db_nd.h"
#include "local_db_nd_fake.h"
#include "db_nd_re6.h"
#include "db_nd_nhp6.h"
#include "db_nd_fake.h"

#define STATUS_OK  0
#define STATUS_RES_ID_UPDATE_FORBIDDEN 110048

typedef enum nd_miss_thread_idx {
    ND_MISS_THREAD_INDEX_COMBINE_OPERATION_0,       // 0
    ND_MISS_THREAD_INDEX_COMBINE_OPERATION_1,  // 1
    ND_MISS_THREAD_INDEX_INVALID
} nd_miss_thread_idx_e;

static char *g_nd_miss_thread_string[] = {
    [ND_MISS_THREAD_INDEX_COMBINE_OPERATION_0] = "nhp_res_info，nd_nhp6，nd_re6 (write prim_read prim_update prim_delete)",
    [ND_MISS_THREAD_INDEX_COMBINE_OPERATION_1] = "nhp6_group,l3if,if(read)  aib(write)  nd(read write update)  nd_fake(write update delete)",
    };

static void *nd_miss_combine_operation_0(void *thr_user_args);
static void *nd_miss_combine_operation_1(void *thr_user_args);

static void nd_miss_usage(char *basename)
{
    printf("usage:%s [-h] [-c flag] [-p num] [-t runtime] [-0|1 sleep_ms] [-f schema_idx]"
           " [-s conf_file]\n",
           basename);

    printf("-p num       : num threads to create\n");
    printf("-c flag      : 0|1 create table data.\n");
    printf("-t runtime   : run time\n");
    printf("-n           : max data\n");
    printf("-f schema_idx: schema_idx\n");
    printf("-%d sleep_ms : %s\n", ND_MISS_THREAD_INDEX_COMBINE_OPERATION_0,
           g_nd_miss_thread_string[ND_MISS_THREAD_INDEX_COMBINE_OPERATION_0]);
    printf("-%d sleep_ms : %s\n", ND_MISS_THREAD_INDEX_COMBINE_OPERATION_1,
           g_nd_miss_thread_string[ND_MISS_THREAD_INDEX_COMBINE_OPERATION_1]);
    printf("-h           : show usage\n");
}

int nd_miss_get_config(int argc, char **argv, local_config_t *conf)
{
    int ret = STATUS_OK;
    int opt;
    local_p_thr_arg_t p_thr_args;
    unsigned char temp;
    while ((opt = getopt(argc, argv, "hp:c:f:n:t:s:0:1:")) != -1) {
        switch (opt) {
            case 'h':
                nd_miss_usage(argv[0]);
                ret = -1;
                break;
            case 'p':
                conf->parallel_count = atoi(optarg);
                break;

            case 'c':
                conf->create_talbe_data = atoi(optarg);  // 1: should create
                break;

            case 'f':
                conf->table_index = atoi(optarg);
                break;

            case 'n':
                conf->data_max = atoi(optarg);  // 100w
                break;

            case 't':
                conf->run_time = atoi(optarg);
                break;

            case 's':
                ret = local_config_set_table_low_high(optarg);
                break;

            case '0':
            case '1':
                temp = (unsigned char)(opt - '0');
                p_thr_args = multi_thread_get_thread_args(temp);
                if (p_thr_args == NULL) {
                    ret = -1;
                    break;
                }
                p_thr_args->is_use = 1;
                p_thr_args->sleep_ms_when_run = atoi(optarg);
                conf->parallel_count += 1;
                break;

            default:
                break;
        }
    }

    if (conf->parallel_count == 0) {
        printf("Parallel_count is 0\n");
        ret = -1;
    }
    return ret;
}

int nd_miss_test_task(local_config_t *conf)
{
    int ret = STATUS_OK;
    local_thr_arg_t thr_args;

    // thread 0 : nhp_res_info，nd_nhp6，nd_re6 (write prim_read prim_update prim_delete)
    memset(&thr_args, 0, sizeof(local_thr_arg_t));
    thr_args.thr_num = ND_MISS_THREAD_INDEX_COMBINE_OPERATION_0;
    thr_args.thr_name = "ND_MISS_00";
    thr_args.thr_description = g_nd_miss_thread_string[ND_MISS_THREAD_INDEX_COMBINE_OPERATION_0];
    thr_args.thr_conf = conf;
    thr_args.restrict_attr = NULL;
    thr_args.thread_func = nd_miss_combine_operation_0;
    thr_args.thr_run = DB_TRUE;
    (void)multi_thread_set_thread_args(&thr_args);

    // thread 1 : nhp6_group,l3if,if(read)  aib(write)  nd(read write update)  nd_fake(write update delete)
    memset(&thr_args, 0, sizeof(local_thr_arg_t));
    thr_args.thr_num = ND_MISS_THREAD_INDEX_COMBINE_OPERATION_1;
    thr_args.thr_name = "ND_MISS_01";
    thr_args.thr_description = g_nd_miss_thread_string[ND_MISS_THREAD_INDEX_COMBINE_OPERATION_1];
    thr_args.thr_conf = conf;
    thr_args.restrict_attr = NULL;
    thr_args.thread_func = nd_miss_combine_operation_1;
    thr_args.thr_run = DB_TRUE;
    (void)multi_thread_set_thread_args(&thr_args);

    ret = multi_thread_start_all_threads();
    if (ret != 0) {
        TEST_ERROR("Threads start error, ret:%d", ret);
    }

    sleep(conf->run_time);
    TEST_INFO("Time over, now we will stop all threads");

    // send signal to stop threads
    local_p_thr_arg_t p_thr_args;
    unsigned int i = 0;
    for (i = 0; i < ND_MISS_THREAD_INDEX_INVALID; i++) {
        p_thr_args = multi_thread_get_thread_args((unsigned char)i);
        if (p_thr_args == NULL || p_thr_args->is_use == 0) {
            continue;
        }
        p_thr_args->thr_run = 0;
    }
    for (i = 0; i < ND_MISS_THREAD_INDEX_INVALID; i++) {
        p_thr_args = multi_thread_get_thread_args((unsigned char)i);
        if (p_thr_args == NULL || p_thr_args->is_use == 0) {
            continue;
        }
        TEST_INFO("waiting thread %u to end...", i);
        ret = pthread_join(p_thr_args->thread_id, DB_NULL);
        if (ret != STATUS_OK) {
            TEST_ERROR("Josin benchmark thread failed, id=%u, err_msg=%s.", i, strerror(errno));
        }
        TEST_INFO("thread %u has exited", i);
        thread_print_result_data(i);
    }

    // print result info.
    // ...
    return ret;
}

void *nd_miss_combine_operation_0(void *thr_user_args)
{
    status_t ret = STATUS_OK;
    local_p_thr_arg_t p_thr_arg = (local_p_thr_arg_t)thr_user_args;
    unsigned int thr_num = p_thr_arg->thr_num;
    unsigned int sleep_us = 1000 * (p_thr_arg->sleep_ms_when_run);
    (p_thr_arg->thr_result)->is_finished = 0;

    p_local_db_schema_model_t nhp_res_info_model_obj = NULL;
    nhp_res_info_model_obj = local_db_get_schema_model_by_index(LOCAL_DB_IDX_NHP_RES_INFO);
    p_local_db_schema_model_t nd_nhp6_model_obj = NULL;
    nd_nhp6_model_obj = local_db_get_schema_model_by_index(LOCAL_DB_IDX_ND_NHP6);
    p_local_db_schema_model_t nd_re6_model_obj = NULL;
    nd_re6_model_obj = local_db_get_schema_model_by_index(LOCAL_DB_IDX_ND_RE6);

    uint64_t nhp_res_info_i = nhp_res_info_model_obj->low;
    uint64_t nhp_res_info_high = nhp_res_info_model_obj->high;
    uint64_t nd_nhp6_i = nd_nhp6_model_obj->low;
    uint64_t nd_nhp6_high = nd_nhp6_model_obj->high;
    uint64_t nd_re6_i = nd_re6_model_obj->low;
    uint64_t nd_re6_high = nd_re6_model_obj->high;

    ret = thread_start_set_result_data(thr_num);
    if (ret) {
        LOG_ERROR(ret, "Error when reset result i=%u", thr_num);
        return NULL;
    }

    while (p_thr_arg->thr_run) {
        //nhp_res_info teble operate
        //nhp_res_info write
        ret = nhp_res_info_single_write(nhp_res_info_i);
        if (ret) {
            break;
        }

        // nhp_res_info pri read
        ret = nhp_res_info_pri_read(nhp_res_info_i);
        if (ret) {
            break;
        }

        // nhp_res_info pri update
        ret = nhp_res_info_pri_update(nhp_res_info_i);
        if (ret) {
            break;
        }

        // nhp_res_info pri remove
        ret = nhp_res_info_pri_remove(nhp_res_info_i);
        if (ret) {
            break;
        }
        
        nhp_res_info_i++;
        if (nhp_res_info_i > nhp_res_info_high) {
            nhp_res_info_i = nhp_res_info_model_obj->low;
        }

         //nd_nhp6 teble operate
        //nd_nhp6 write
        ret = nd_nhp6_single_write(nd_nhp6_i);
        if (ret) {
            break;
        }

        // nd_nhp6 pri read
        ret = nd_nhp6_pri_read(nd_nhp6_i);
        if (ret) {
            break;
        }

        // nd_nhp6 pri update
        ret = nd_nhp6_pri_update(nd_nhp6_i);
        if (ret) {
            break;
        }

        // nd_nhp6 pri remove
        ret = nd_nhp6_pri_remove(nd_nhp6_i);
        if (ret) {
            break;
        }
        
        nd_nhp6_i++;
        if (nd_nhp6_i > nd_nhp6_high) {
            nd_nhp6_i = nd_nhp6_model_obj->low;
        }

         //nd_re6 teble operate
        //nd_re6 write
        ret = nd_re6_single_write(nd_re6_i);
        if (ret) {
            break;
        }

        // nd_re6 pri read
        ret = nd_re6_pri_read(nd_re6_i);
        if (ret) {
            break;
        }

        // nd_re6 pri update
        ret = nd_re6_pri_update(nd_re6_i);
        if (ret) {
            break;
        }

        // nd_re6 pri remove
        ret = nd_re6_pri_remove(nd_re6_i);
        if (ret) {
            break;
        }
        
        nd_re6_i++;
        if (nd_re6_i > nd_re6_high) {
            nd_re6_i = nd_re6_model_obj->low;
        }

        ((p_thr_arg->thr_result)->op_total)++;

        if (sleep_us > 0) {
            usleep(sleep_us);
        }
    }

    if (ret == 0) {
        (p_thr_arg->thr_result)->is_finished = 1;
    }

    ret = thread_end_set_result_data(thr_num);
    if (ret) {
        LOG_ERROR(ret, "Error when set result i=%u", thr_num);
        return NULL;
    }

    return NULL;
}

void *nd_miss_combine_operation_1(void *thr_user_args)
{
    status_t ret = STATUS_OK;
    //db_object object;
    local_p_thr_arg_t p_thr_arg = (local_p_thr_arg_t)thr_user_args;
    unsigned int thr_num = p_thr_arg->thr_num;
    unsigned int sleep_us = 1000 * (p_thr_arg->sleep_ms_when_run);
    (p_thr_arg->thr_result)->is_finished = 0;

    p_local_db_schema_model_t nhp6_group_model_obj = NULL;
    nhp6_group_model_obj = local_db_get_schema_model_by_index(LOCAL_DB_IDX_NHP6_GROUP);
    p_local_db_schema_model_t l3if_model_obj = NULL;
    l3if_model_obj = local_db_get_schema_model_by_index(LOCAL_DB_IDX_L3IF);
    p_local_db_schema_model_t if_model_obj = NULL;
    if_model_obj = local_db_get_schema_model_by_index(LOCAL_DB_IDX_IF);
    p_local_db_schema_model_t aib_model_obj = NULL;
    aib_model_obj = local_db_get_schema_model_by_index(LOCAL_DB_IDX_AIB);
    p_local_db_schema_model_t nd_model_obj = NULL;
    nd_model_obj = local_db_get_schema_model_by_index(LOCAL_DB_IDX_ND);
    p_local_db_schema_model_t nd_fake_model_obj = NULL;
    nd_fake_model_obj = local_db_get_schema_model_by_index(LOCAL_DB_IDX_ND_FAKE);

    uint64_t nhp6_group_i = nhp6_group_model_obj->low;
    uint64_t nhp6_group_high = nhp6_group_model_obj->high;
    uint64_t l3if_i = l3if_model_obj->low;
    uint64_t l3if_high = l3if_model_obj->high;
    uint64_t if_i = if_model_obj->low;
    uint64_t if_high = if_model_obj->high;
    uint64_t aib_i = aib_model_obj->low;
    uint64_t aib_high = aib_model_obj->high;
    uint64_t nd_i = nd_model_obj->low;
    uint64_t nd_high = nd_model_obj->high;
    uint64_t nd_fake_i = nd_fake_model_obj->low;
    uint64_t nd_fake_high = nd_fake_model_obj->high;

    ret = thread_start_set_result_data(thr_num);
    if (ret) {
        LOG_ERROR(ret, "Error when reset result i=%u", thr_num);
        return NULL;
    }

    while (p_thr_arg->thr_run) {
        // nhp_res_info read  
        ret = nhp6_group_local_read(nhp6_group_i);
        if (ret){
            break;
        }

        nhp6_group_i++;
        if (nhp6_group_i > nhp6_group_high) {
            nhp6_group_i = nhp6_group_model_obj->low;
        }

        //l3if table key read
        ret = l3if_pri_read(l3if_i);
        if (ret){
            break;
        }

        l3if_i++;
        if (l3if_i > l3if_high) {
            l3if_i = l3if_model_obj->low;
        }

        //if table pri read
        ret = if_local_read(if_i);
        if (ret){
            break;
        }

        if_i++;
        if (if_i > if_high) {
            if_i = if_model_obj->low;
        }

        //aib table write
        ret = aib_single_write(aib_i);
        if(ret){
            break;
        }

        aib_i++;
        if (aib_i > aib_high) {
            aib_i = aib_model_obj->low;
        }

        //nd table read,write and update
        ret = nd_pri_read(nd_i);
        if(ret){
            break;
        }

        ret = nd_single_write(nd_i);
        if(ret){
            break;
        }

        ret = nd_pri_update(nd_i);
        if(ret){
            break;
        }

        nd_i++;
        if (nd_i > nd_high) {
            nd_i = nd_model_obj->low;
        }

        //nd_fake table write,update and remove
        ret = nd_fake_single_write(nd_fake_i);
        if(ret){
            break;
        }

        ret = nd_fake_pri_update(nd_fake_i);
        if(ret){
            break;
        }

        ret = nd_fake_pri_remove(nd_fake_i);
        if(ret){
            break;
        }

        nd_fake_i++;
        if (nd_fake_i > nd_fake_high) {
            nd_fake_i = nd_fake_model_obj->low;
        }

        ((p_thr_arg->thr_result)->op_total)++;

        if (sleep_us > 0) {
            usleep(sleep_us);
        }
    }

    if (ret == 0) {
        (p_thr_arg->thr_result)->is_finished = 1;
    }

    ret = thread_end_set_result_data(thr_num);
    if (ret) {
        LOG_ERROR(ret, "Error when set result i=%u", thr_num);
        return NULL;
    }

    return NULL;
}

//set fixed ipv6_addr 
void set_this_fixed(int8_t fixed[DB_ND_FAKE_IPV6_ADDR_LEN], uint64_t i)
{
    int8_t base_fixed[DB_ND_FAKE_IPV6_ADDR_LEN] = {'1', '3', '8', '0', '0', '1', '3', '8', '0', '0', '0','2','4','5','2','6'};
    memcpy_s(fixed, DB_ND_FAKE_IPV6_ADDR_LEN, base_fixed, DB_ND_FAKE_IPV6_ADDR_LEN);
    memcpy_s(fixed + DB_ND_FAKE_IPV6_ADDR_LEN - sizeof(uint64_t), sizeof(uint64_t), &i, sizeof(uint64_t));
}

//nd_miss_combine_operation_0 tables operate
// table nhp_res_info operate
status_t nhp_res_info_pri_read(uint64_t nhp_res_info_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL;

    if (nhp_res_info_i > g_model_high[LOCAL_DB_IDX_NHP_RES_INFO]) {
        nhp_res_info_i = g_model_low[LOCAL_DB_IDX_NHP_RES_INFO];
    }

    ret = local_db_nhp_res_info_create_db_obj_func(READ_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_nhp_res_info_get_field_by_key_func(object, nhp_res_info_i, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "getfield_bykey_func i=%lu", nhp_res_info_i);
        local_db_nhp_res_info_release_db_obj_func(object);
        return ret;
    }

    local_db_nhp_res_info_release_db_obj_func(object);
    return ret;
}

status_t nhp_res_info_single_write(uint64_t nhp_res_info_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL;

    if (nhp_res_info_i > g_model_high[LOCAL_DB_IDX_NHP_RES_INFO]) {
        nhp_res_info_i = g_model_low[LOCAL_DB_IDX_NHP_RES_INFO];
    }

    ret = local_db_nhp_res_info_create_db_obj_func(WRITE_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_nhp_res_info_set_obj_func(object, nhp_res_info_i, 1, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "set_obj_func i=%lu", nhp_res_info_i);
        local_db_nhp_res_info_release_db_obj_func(object);
        return ret;
    }

    ret = db_write_obj(object);  // thr_num
    if ( ret == STATUS_OK || ret == STATUS_RES_ID_UPDATE_FORBIDDEN ) {
        ret = STATUS_OK;
    }
    else {
        LOG_ERROR(ret, "db_write_object i=%lu", nhp_res_info_i);
        local_db_nhp_res_info_release_db_obj_func(object);
        return ret;
    }

    local_db_nhp_res_info_release_db_obj_func(object);
    return ret;
}

status_t nhp_res_info_pri_update(uint64_t nhp_res_info_i)
{
    status_t ret = STATUS_OK;
    db_key_info_t key_info = {0};
    db_nhp_res_info_key_t key_data = {0};
    db_object object = NULL;

    if (nhp_res_info_i > g_model_high[LOCAL_DB_IDX_NHP_RES_INFO]) {
        nhp_res_info_i = g_model_low[LOCAL_DB_IDX_NHP_RES_INFO];
    }

    ret = local_db_nhp_res_info_create_db_obj_func(UPDATE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_nhp_res_info_create_db_obj_func");

    ret = local_db_nhp_res_info_key_set_func(object, nhp_res_info_i, 0, &key_info, &key_data);
    CHECK_OK_RET(ret, "local_db_arp_key_set_func");

    /*key_info.index_id = DB_NHP_RES_INFO_PRIMARY_KEY_ID;
    key_info.key_len = sizeof(key_data);
    key_data.user_type = nhp_res_info_i;
    key_data.nhp_flag = nhp_res_info_i;
    key_data.vs_id = nhp_res_info_i;
    key_data.vrf_index = nhp_res_info_i;
    key_data.next_hop = nhp_res_info_i;
    key_data.outif_index = nhp_res_info_i;
    //key_data.next_hop6 = nhp_res_info_i;
    int8_t arr[16] = { 0 };
    memcpy(arr, (int8_t *)&nhp_res_info_i, DB_NHP_RES_INFO_NEXT_HOP6_LEN);
    (void)memcpy_s(key_data.next_hop6, DB_NHP_RES_INFO_NEXT_HOP6_LEN, (int8_t *)arr,sizeof(arr));
    */

    db_set_nhp_res_info_nhp_index(object, nhp_res_info_i);

    ret = db_update_obj(object, &key_info, &key_data);
    if ( ret == STATUS_OK || ret == STATUS_RES_ID_UPDATE_FORBIDDEN ) {
        ret = STATUS_OK;
    }
    else {
        local_db_nd_release_db_obj_func(object);
        TEST_INFO("update nhp_res_info object failed. ret = %d", ret);
    }

    local_db_nhp_res_info_release_db_obj_func(object);
    return ret;
}

status_t nhp_res_info_pri_remove(uint64_t nhp_res_info_i)
{
    status_t ret;
    db_object object = NULL;

    if (nhp_res_info_i > g_model_high[LOCAL_DB_IDX_NHP_RES_INFO]) {
        nhp_res_info_i = g_model_low[LOCAL_DB_IDX_NHP_RES_INFO];
    }

    ret = local_db_nhp_res_info_create_db_obj_func(REMOVE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_nhp_res_info_create_db_obj_func");

    db_key_info_t key_info;
    db_nhp_res_info_key_t key_data;
    ret = local_db_nhp_res_info_key_set_func(object, nhp_res_info_i, 0, &key_info, &key_data);
    CHECK_OK_RET(ret, "local_db_nhp_res_info_key_set_func");

    ret = (uint32_t)db_remove_obj(object, &key_info, &key_data);
    if (ret != 0) {
        local_db_nhp_res_info_release_db_obj_func(object);
        TEST_INFO("remove nhp_res_info object failed. ret = %d", ret);
        return ret;
    }

    local_db_nhp_res_info_release_db_obj_func(object);

    return ret;
}

//table nd_nhp6 operation
status_t nd_nhp6_pri_read(uint64_t nd_nhp6_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL;

    if (nd_nhp6_i > g_model_high[LOCAL_DB_IDX_ND_NHP6]) {
        nd_nhp6_i = g_model_low[LOCAL_DB_IDX_ND_NHP6];
    }
    // nd_nhp6 read
    ret = local_db_nd_nhp6_create_db_obj_func(READ_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_nd_nhp6_get_field_by_key_func(object, nd_nhp6_i, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "getfield_bykey_func i=%lu", nd_nhp6_i);
        local_db_nd_nhp6_release_db_obj_func(object);
        return ret;
    }

    local_db_nd_nhp6_release_db_obj_func(object);
    return ret;
}

status_t nd_nhp6_single_write(uint64_t nd_nhp6_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL;

    if (nd_nhp6_i > g_model_high[LOCAL_DB_IDX_ND_NHP6]) {
        nd_nhp6_i = g_model_low[LOCAL_DB_IDX_ND_NHP6];
    }
    // nd_nhp6 write
    ret = local_db_nd_nhp6_create_db_obj_func(WRITE_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_nd_nhp6_set_obj_func(object, nd_nhp6_i, 1, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "set_obj_func i=%lu", nd_nhp6_i);
        local_db_nd_nhp6_release_db_obj_func(object);
        return ret;
    }

    ret = db_write_obj(object);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "db_write_object i=%lu", nd_nhp6_i);
        local_db_nd_nhp6_release_db_obj_func(object);
        return ret;
    }

    local_db_nd_nhp6_release_db_obj_func(object);
    return ret;
}

status_t nd_nhp6_pri_update(uint64_t nd_nhp6_i)
{
    status_t ret = STATUS_OK;
    //db_key_info_t key_info = {0};
    //db_nd_nhp6_key_t key_data = {0};
    db_object object = NULL;

    if (nd_nhp6_i > g_model_high[LOCAL_DB_IDX_ND_NHP6]) {
        nd_nhp6_i = g_model_low[LOCAL_DB_IDX_ND_NHP6];
    }

    ret = local_db_nd_nhp6_create_db_obj_func(UPDATE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_nd_nhp6_pri_create_db_obj_func");

    ret = local_db_nd_nhp6_update_func(object, nd_nhp6_i, 0);
    if( ret ){
        local_db_nd_nhp6_release_db_obj_func(object);
        TEST_INFO("update nd_nhp6 object failed. ret = %d", ret);
    }

    local_db_nd_nhp6_release_db_obj_func(object);
    return ret;
}

status_t nd_nhp6_pri_remove(uint64_t nd_nhp6_i)
{
    status_t ret;
    db_object object = NULL;

    if (nd_nhp6_i > g_model_high[LOCAL_DB_IDX_ND_NHP6]) {
        nd_nhp6_i = g_model_low[LOCAL_DB_IDX_ND_NHP6];
    }

    ret = local_db_nd_nhp6_create_db_obj_func(REMOVE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_nd_nhp6_create_db_obj_func");

    db_key_info_t key_info;
    db_nd_nhp6_key_t key_data;
    ret = local_db_nd_nhp6_key_set_func(object, nd_nhp6_i, 0, &key_info, &key_data);
    CHECK_OK_RET(ret, "local_db_nd_nhp6_key_set_func");

    ret = (uint32_t)db_remove_obj(object, &key_info, &key_data);
    if (ret != 0) {
        local_db_nd_nhp6_release_db_obj_func(object);
        TEST_INFO("remove nd_nhp6 object failed. ret = %d", ret);
        return ret;
    }

    local_db_nd_nhp6_release_db_obj_func(object);
    return ret;
}

//table nd_re6 operation
status_t nd_re6_pri_read(uint64_t nd_re6_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL;

    if (nd_re6_i > g_model_high[LOCAL_DB_IDX_ND_RE6]) {
        nd_re6_i = g_model_low[LOCAL_DB_IDX_ND_RE6];
    }

    // nd_re6 read
    ret = local_db_nd_re6_create_db_obj_func(READ_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_nd_re6_get_field_by_key_func(object, nd_re6_i, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "getfield_bykey_func i=%lu", nd_re6_i);
        local_db_nd_re6_release_db_obj_func(object);
        return ret;
    }

    local_db_nd_re6_release_db_obj_func(object);
    return ret;
}

status_t nd_re6_single_write(uint64_t nd_re6_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL ;

    if (nd_re6_i > g_model_high[LOCAL_DB_IDX_ND_RE6]) {
        nd_re6_i = g_model_low[LOCAL_DB_IDX_ND_RE6];
    }
    // nd_re6 write
    ret = local_db_nd_re6_create_db_obj_func(WRITE_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_nd_re6_set_obj_func(object, nd_re6_i, 1, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "set_obj_func i=%lu", nd_re6_i);
        local_db_nd_re6_release_db_obj_func(object);
        return ret;
    }

    ret = db_write_obj(object);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "db_write_object i=%lu", nd_re6_i);
        local_db_nd_re6_release_db_obj_func(object);
        return ret;
    }

    local_db_nd_re6_release_db_obj_func(object);
    return ret;
}

status_t nd_re6_pri_update(uint64_t nd_re6_i)
{
    status_t ret = STATUS_OK;
    //db_key_info_t key_info = {0};
    //db_nd_re6_key_t key_data = {0};
    db_object object = NULL;

    if (nd_re6_i > g_model_high[LOCAL_DB_IDX_ND_RE6]) {
        nd_re6_i = g_model_low[LOCAL_DB_IDX_ND_RE6];
    }

    
    ret = local_db_nd_re6_create_db_obj_func(UPDATE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_nd_re6_pri_create_db_obj_func");

    /*
    ret = local_db_nd_re6_key_set_func(object, nd_re6_i, 0, &key_info, &key_data);
    CHECK_OK_RET(ret, "local_db_nd_re6_key_set_func");

    key_info.index_id = DB_ND_RE6_ND_RE6_KEY_ID;
    key_info.key_len = sizeof(key_data);

    int8_t arr[16] = { 0 };
    memcpy(arr, (int8_t *)&nd_re6_i, 16);
    (void)memcpy_s(key_data.ip_addr, 16, (int8_t *)arr,sizeof(arr));
    key_data.vr_id = nd_re6_i;
    key_data.vrf_index = nd_re6_i;   
    
    db_set_nd_re6_status_high_prio(object, nd_re6_i);
    db_set_nd_re6_err_code_normal_prio(object, nd_re6_i); 

    ret = db_update_obj(object, &key_info, &key_data);
    if (ret != STATUS_OK) {
        local_db_nd_re6_release_db_obj_func(object);
        TEST_INFO("update nd_re6 object failed. ret = %d", ret);
    }
    */
   ret = local_db_nd_re6_update_func(object, nd_re6_i, 0);
   if (ret){
        LOG_ERROR(ret, "local_db_nd_re6_update i=%lu", nd_re6_i);
        local_db_nd_re6_release_db_obj_func(object);
        return ret;      
   }

    local_db_nd_re6_release_db_obj_func(object);
    return ret;
}

status_t nd_re6_pri_remove(uint64_t nd_re6_i)
{
    status_t ret;
    db_object object = NULL;

    if (nd_re6_i > g_model_high[LOCAL_DB_IDX_ND_RE6]) {
        nd_re6_i = g_model_low[LOCAL_DB_IDX_ND_RE6];
    }

    ret = local_db_nd_re6_create_db_obj_func(REMOVE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_nd_re6_create_db_obj_func");

    db_key_info_t key_info = { 0 };
    db_nd_re6_key_t key_data = { 0 };
    //ret = local_db_nd_re6_key_set_func(object, nd_re6_i, 0, &key_info, &key_data);
    //CHECK_OK_RET(ret, "local_db_nd_re6_key_set_func");
    key_info.index_id = DB_ND_RE6_ND_RE6_KEY_ID;
    key_info.key_len = sizeof(key_data);
    int8_t arr[DB_ND_RE6_IP_ADDR_LEN] = { 0 };
        set_this_fixed(arr, nd_re6_i);
            //memcpy(key_data.ip_addr, (int8_t *)arr, DB_ND_RE6_IP_ADDR_LEN);  
         memcpy(key_data.ip_addr, arr, DB_ND_RE6_IP_ADDR_LEN);
         key_data.vr_id = nd_re6_i;
         key_data.vrf_index = nd_re6_i;

    ret = (uint32_t)db_remove_obj(object, &key_info, &key_data);
    if (ret != 0) {
        local_db_nd_re6_release_db_obj_func(object);
        TEST_INFO("remove nd_re6 object failed. ret = %d", ret);
        return ret;
    }

    local_db_nd_re6_release_db_obj_func(object);
    return ret;
}

//nd_miss_combine_operation_1 tables operate
//nhp6_group table operate
status_t nhp6_group_local_read(uint64_t nhp6_group_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL;

    if (nhp6_group_i > g_model_high[LOCAL_DB_IDX_NHP6_GROUP]) {
        nhp6_group_i = g_model_low[LOCAL_DB_IDX_NHP6_GROUP];
    }

    // nhp6_group read
    ret = local_db_nhp6_group_create_db_obj_func(READ_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_nhp6_group_get_field_by_key_func(object, nhp6_group_i, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "getfield_bykey_func i=%lu", nhp6_group_i);
        local_db_nhp6_group_release_db_obj_func(object);
        return ret;
    }

    local_db_nhp6_group_release_db_obj_func(object);
    return ret;
}

//l3if table operate
status_t l3if_pri_read(uint64_t l3if_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL;

    if (l3if_i > g_model_high[LOCAL_DB_IDX_L3IF]) {
        l3if_i = g_model_low[LOCAL_DB_IDX_L3IF];
    }

    // l3if read
    ret = local_db_l3if_create_db_obj_func(READ_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_l3if_get_field_by_key_func(object, l3if_i, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "getfield_bykey_func i=%lu", l3if_i);
        local_db_l3if_release_db_obj_func(object);
        return ret;
    }

    local_db_l3if_release_db_obj_func(object);
    return ret;
}

status_t if_local_read(uint64_t if_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL;

    if (if_i > g_model_high[LOCAL_DB_IDX_IF]) {
        if_i = g_model_low[LOCAL_DB_IDX_IF];
    }
    // if read
    ret = local_db_if_create_db_obj_func(READ_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_if_get_field_by_key_func(object, if_i, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "getfield_bykey_func i=%lu", if_i);
        local_db_if_release_db_obj_func(object);
        return ret;
    }

    local_db_if_release_db_obj_func(object);
    return ret;
}

//aib table operate
status_t aib_single_write(uint64_t aib_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL;

    if (aib_i > g_model_high[LOCAL_DB_IDX_AIB]) {
        aib_i = g_model_low[LOCAL_DB_IDX_AIB];
    }
    // aib write
    ret = local_db_aib_create_db_obj_func(WRITE_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_aib_set_obj_func(object, aib_i, 1, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "set_obj_func i=%lu", aib_i);
        local_db_aib_release_db_obj_func(object);
        return ret;
    }

    ret = db_write_obj(object);  // thr_num
    if ( ret == STATUS_OK || ret == STATUS_RES_ID_UPDATE_FORBIDDEN ) {
        ret = STATUS_OK;
    }
    else {
        LOG_ERROR(ret, "db_write_object i=%lu", aib_i);
        local_db_aib_release_db_obj_func(object);
        return ret;
    }

    local_db_aib_release_db_obj_func(object);
    return ret;
}

//nd table operate
status_t nd_pri_read(uint64_t nd_i)
{
     status_t ret;
     db_object object = NULL;

    //printf("***", nd_i);
    //if (nd_i > g_model_high[LOCAL_DB_IDX_ND]) {
     //   nd_i = g_model_low[LOCAL_DB_IDX_ND];
    //}

    ret = local_db_nd_create_db_obj_func(READ_OBJ, LOCAL_SYNC_FLAG, &object);  
    CHECK_OK_RET(ret, "local_db_nd_create_db_obj_func");

    ret = local_db_nd_get_field_by_key_func(object, nd_i, 0);  // thr_num
    if (ret) {
        local_db_nd_release_db_obj_func(object);
        TEST_INFO("read nd object failed. ret = %d", ret);
        return ret;
    }
        
    local_db_nd_release_db_obj_func(object);
    return ret;
}

status_t nd_single_write(uint64_t nd_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL ;

    if (nd_i > g_model_high[LOCAL_DB_IDX_ND]) {
        nd_i = g_model_low[LOCAL_DB_IDX_ND];
    }

    // nd write
    ret = local_db_nd_create_db_obj_func(WRITE_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_nd_set_obj_func(object, nd_i, 1, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "set_obj_func i=%lu", nd_i);
        local_db_nd_release_db_obj_func(object);
        return ret;
    }

    ret = db_write_obj(object);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "db_write_object i=%lu", nd_i);
        local_db_nd_release_db_obj_func(object);
        return ret;
    }

    local_db_nd_release_db_obj_func(object);
    return ret;
}

status_t nd_pri_update(uint64_t nd_re6_i)
{
    status_t ret;
    db_object object = NULL;

    if (nd_re6_i > g_model_high[LOCAL_DB_IDX_ND]) {
        nd_re6_i = g_model_low[LOCAL_DB_IDX_ND];
    }
    ret = local_db_nd_create_db_obj_func(UPDATE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_nd_create_db_obj_func");

    ret = local_db_nd_update_func(object, nd_re6_i, 0);
    if (ret) {
        local_db_nd_release_db_obj_func(object);
        TEST_INFO("update nd object failed. ret = %d", ret);
        return ret;
    }

    local_db_nd_release_db_obj_func(object);

    return ret;
}

//nd_fade table operate
status_t nd_fake_single_write(uint64_t nd_fake_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL;

    if (nd_fake_i > g_model_high[LOCAL_DB_IDX_ND_FAKE]) {
        nd_fake_i = g_model_low[LOCAL_DB_IDX_ND_FAKE];
    }
    // nd_fake write
    ret = local_db_nd_fake_create_db_obj_func(WRITE_OBJ, LOCAL_SYNC_FLAG, &object);
    if (ret) {
        LOG_ERROR(ret, "create_object");
        return ret;
    }

    ret = local_db_nd_fake_set_obj_func(object, nd_fake_i, 1, 0);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "set_obj_func i=%lu", nd_fake_i);
        local_db_nd_fake_release_db_obj_func(object);
        return ret;
    }

    ret = db_write_obj(object);  // thr_num
    if (ret) {
        LOG_ERROR(ret, "db_write_object i=%lu", nd_fake_i);
        local_db_nd_fake_release_db_obj_func(object);
        return ret;
    }

    local_db_nd_fake_release_db_obj_func(object);
    return ret;
}

status_t nd_fake_pri_update1(uint64_t nd_fake_i)
{
    status_t ret = STATUS_OK;
    db_key_info_t key_info = { 0 };
    db_nd_fake_key_t key_data = { 0 };
    db_object object = NULL;

    if (nd_fake_i > g_model_high[LOCAL_DB_IDX_ND_FAKE]) {
        nd_fake_i = g_model_low[LOCAL_DB_IDX_ND_FAKE];
    }

    ret = local_db_nd_fake_create_db_obj_func(UPDATE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_nd_fake_pri_create_db_obj_func");

    key_info.index_id = DB_ND_FAKE_ND_FAKE_KEY_ID;
    key_info.key_len = sizeof(db_nd_fake_key_t);
    int8_t arr[DB_ND_FAKE_IPV6_ADDR_LEN] = { 0 };
    set_this_fixed(arr, nd_fake_i);

    memcpy(key_data.ipv6_addr, arr, DB_ND_FAKE_IPV6_ADDR_LEN);
    
    //int8_t arr[16] = { 0 };
    //memcpy(arr, (int8_t *)&nd_fake_i, 16);
    //(void)memcpy_s(key_data.ipv6_addr, 16, (int8_t *)arr,sizeof(arr));
    key_data.if_index = nd_fake_i;   
    db_set_nd_fake_vr_id(object, nd_fake_i+1);
    db_set_nd_fake_vrf_index(object, nd_fake_i+1);

    ret = db_update_obj(object, &key_info, &key_data);
    if (ret != STATUS_OK) {
        local_db_nd_fake_release_db_obj_func(object);
        TEST_INFO("update nd_fake object failed. ret = %d", ret);
    }

    local_db_nd_fake_release_db_obj_func(object);
    return ret;
}

status_t nd_fake_pri_update(uint64_t nd_fake_i)
{
    status_t ret = STATUS_OK;
    db_object object = NULL;

    if (nd_fake_i > g_model_high[LOCAL_DB_IDX_ND_FAKE]) {
        nd_fake_i = g_model_low[LOCAL_DB_IDX_ND_FAKE];
    }

    ret = local_db_nd_fake_create_db_obj_func(UPDATE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_nd_re6_pri_create_db_obj_func");

   //printf("nd_fake_i is %d,set failed!\n",nd_fake_i);
   ret = local_db_nd_fake_update_func(object, nd_fake_i, 0);
   if (ret){
        LOG_ERROR(ret, "local_db_nd_fake_update i=%lu", nd_fake_i);
        local_db_nd_fake_release_db_obj_func(object);
        return ret;      
   }

    local_db_nd_fake_release_db_obj_func(object);
    return ret;

}

status_t nd_fake_pri_remove(uint64_t nd_fake_i)
{
    status_t ret;
    db_object object = NULL;

    if (nd_fake_i > g_model_high[LOCAL_DB_IDX_ND_FAKE]) {
        nd_fake_i = g_model_low[LOCAL_DB_IDX_ND_FAKE];
    }

    ret = local_db_nd_fake_create_db_obj_func(REMOVE_OBJ, LOCAL_SYNC_FLAG, &object);
    CHECK_OK_RET(ret, "local_db_nd_fake_create_db_obj_func");

    db_key_info_t key_info = { 0 };
    db_nd_fake_key_t key_data = { 0 };
    key_info.index_id = DB_ND_FAKE_ND_FAKE_KEY_ID;
    key_info.key_len = sizeof(db_nd_fake_key_t);
    //ret = local_db_nd_fake_key_set_func(object, nd_fake_i, 0, &key_info, &key_data);
    //CHECK_OK_RET(ret, "local_db_nd_fake_key_set_func");
    int8_t arr[DB_ND_FAKE_IPV6_ADDR_LEN] = { 0 };
    set_this_fixed(arr, nd_fake_i);
    memcpy(key_data.ipv6_addr, arr, DB_ND_FAKE_IPV6_ADDR_LEN);
    key_data.if_index = nd_fake_i; 

    ret = (uint32_t)db_remove_obj(object, &key_info, &key_data);
    if (ret != 0) {
        local_db_nd_fake_release_db_obj_func(object);
        TEST_INFO("remove nd_fake object failed. ret = %d", ret);
        return ret;
    }

    local_db_nd_fake_release_db_obj_func(object);
    return ret;
}
