ARCH=$1
if [ "x" = "x${ARCH}" ] || [ "xh" = "x${ARCH}" ] || [ "X${ARCH}" = "X-h" ] || [ $# -lt 3 ];then
    echo ">> benchMark data length "
    echo ">> usage:arg1 [120B/250B/500B/1kB/2kB] support: data length "
    echo ">>       arg2 [num]"
    echo ">>       arg3 [batch_num] support: actul_num"
    exit 1
fi

DATA_LENGTH=$1
NUM=$2
BATCH_NUM=$3

PERF=0
GET_CPU=1

if [ "x${PERF}" = "x1" ];then
    GET_CPU=0
fi

TABLE_NAME="ip4forward_benchMark_${DATA_LENGTH}"
SCHEMA_FILE_NAME="ip4forward_benchMark_${DATA_LENGTH}.gmjson"

if [ "x${DATA_LENGTH}" = "x120B" ];then
  TABLE_NAME="ip4forward_benchMark"
  SCHEMA_FILE_NAME="ip4forward_benchMark.gmjson"
fi
cur=`pwd`

env_9700_rtos=0

cur=`pwd`
env_type=$(cat /etc/*release|grep -w PRETTY_NAME)
env_rtos=$(echo ${env_type}|grep EulerOS)
env_iot=$(echo ${env_type}|grep "RTOS V2X")
env_9700=$(echo ${env_type}|grep "RTOS"|grep "arm64le-preempt-pro")

if [ "x${env_9700_rtos}" != "x0" ];then
    env_rtos=1
    env_9700=""
    echo ">>> test on 9700_RTOS."
fi

SysType=0
if [ "x${env_rtos}" != "x" ];then
    echo ">>> test on RTOS."
    SysType=0
    cd ../../tools/
    ./deploy.sh  euler
    source ../autotest_env.sh    
elif [ "x${env_iot}" != "x" ];then
    echo ">>> test on iot_hm."
    SysType=1
    cd ../../tools/
    ./deploy.sh  rtosv2x
    source ../autotest_env.sh    
elif [ "x${env_9700}" != "x" ];then
    echo ">>> test on 9700 ."
    SysType=2
    cd ../../tools/
    ./deploy.sh  rtosv2
    source ../autotest_env.sh    
fi

# 修改配置
if [ "x${env_rtos}" != "x" ];then
echo ">>> modify config:"
sh $TEST_HOME/tools/modifyCfg.sh "enableTableLock=1"
sh $TEST_HOME/tools/modifyCfg.sh "isFastReadUncommitted=1"
sh $TEST_HOME/tools/modifyCfg.sh "isUseHugePage=1"
sh $TEST_HOME/tools/modifyCfg.sh "messageSecurityCheck=0"
sysctl -w vm.nr_hugepages=1024
cat /proc/meminfo |grep HugePages_
echo ""
fi

cd $cur
if [ "x${BATCH_NUM}" = "x" ];then
    BATCH_NUM=0
fi

TEST_SYN_BIN=tf
TEST_ASYN_BIN=tf_async

killall_process_of_name()
{
    local perf_tgt_process=$1
    local first_kill_pid=`pidof ${perf_tgt_process} |  awk -F" " '{print $1}'`

    while true
    do
        if [ "$first_kill_pid"X != ""X ];then
            kill -9 ${first_kill_pid}
            first_kill_pid=`pidof ${perf_tgt_process} |  awk -F" " '{print $1}'`
            continue
        fi
        return
    done
    sleep 2
}

echo ""
echo ">>> Start to test table: ${TABLE_NAME}  data length: ${DATA_LENGTH}  num: ${NUM}  batch_num: ${BATCH_NUM} json: ${SCHEMA_FILE_NAME}"

killall_process_of_name ${TEST_SYN_BIN}
killall_process_of_name ${TEST_ASYN_BIN}

if [ "x${env_9700}" = "x" ];then
echo ">>> restart server"
stop.sh -f
start.sh -f

sleep 3
fi

log_file=log_${TABLE_NAME}_data_lenth.txt	

echo ">>> Create table: ${TABLE_NAME}  gmjson: benchMark_schema/${SCHEMA_FILE_NAME}"
echo ">>> ./tf -v -C -n ${TABLE_NAME} -f  benchMark_schema/${SCHEMA_FILE_NAME}"
./tf -v -C -n ${TABLE_NAME} -f  benchMark_schema/${SCHEMA_FILE_NAME}

echo ">>> Start to test:"

if [ "x${BATCH_NUM}" != "x0" ];then
    batch_oper=" -N ${BATCH_NUM} "
fi

echo ">>>>>> start sync single insert"
echo "./${TEST_SYN_BIN} -v -m insert -n ${TABLE_NAME} -e ${NUM}"
./${TEST_SYN_BIN} -v -m insert -n ${TABLE_NAME} -e ${NUM} |tee temp.txt
cat temp.txt > ${log_file}

./${TEST_SYN_BIN} -v -m count -n ${TABLE_NAME} | tee tmpCnt.txt
cat tmpCnt.txt >> ${log_file}

echo ">>>>>> start sync single replace"
echo "./${TEST_SYN_BIN} -v -m replace -n ${TABLE_NAME} -e ${NUM}"
./${TEST_SYN_BIN} -v -m replace -n ${TABLE_NAME} -e ${NUM} |tee temp.txt
cat temp.txt >> ${log_file}

echo ">>>>>> start select"
echo "./${TEST_SYN_BIN} -v -m select -n ${TABLE_NAME} -e ${NUM}"
./${TEST_SYN_BIN} -v -m select -n ${TABLE_NAME} -e ${NUM} |tee temp.txt
cat temp.txt >> ${log_file}

echo ">>>>>> start sync single delete"
echo "./${TEST_SYN_BIN} -v -m delete -n ${TABLE_NAME} -e ${NUM}"
./${TEST_SYN_BIN} -v -m delete -n ${TABLE_NAME} -e ${NUM} |tee temp.txt
cat temp.txt >> ${log_file}

echo ">>>>>> start async batch insert"
echo "./${TEST_ASYN_BIN} -v -m insert -n ${TABLE_NAME} -e ${NUM} ${batch_oper}"
./${TEST_ASYN_BIN} -v -m insert -n ${TABLE_NAME} -e ${NUM} ${batch_oper} |tee temp.txt
cat temp.txt >> ${log_file}

echo ">>>>>> start async batch replace"
echo "./${TEST_ASYN_BIN} -v -m replace -n ${TABLE_NAME} -e ${NUM} ${batch_oper}"
./${TEST_ASYN_BIN} -v -m replace -n ${TABLE_NAME} -e ${NUM} ${batch_oper} |tee temp.txt
cat temp.txt >> ${log_file}

echo ">>>>>> start sync batch delete"
echo "./${TEST_SYN_BIN} -v -m delete -n ${TABLE_NAME} -e ${NUM} ${batch_oper}"
./${TEST_SYN_BIN} -v -m delete -n ${TABLE_NAME} -e ${NUM} ${batch_oper} |tee temp.txt
cat temp.txt >> ${log_file}

# 9700 不支持重启服务，删表
if [ "x${env_9700}" != "x" ];then
    echo "./tf -v -m drop -n ${TABLE_NAME}"
    ./tf -v -m drop -n ${TABLE_NAME} 
fi

echo "---------Logfile is: ${log_file}"
echo ""

exit 0
