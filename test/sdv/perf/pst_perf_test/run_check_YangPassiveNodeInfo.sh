#校验光启V\$YANG_PASSIVE_NODE_INFO视图文件
#同级目录下yangjson/PASSIVE_NODE_B506_no.txt文件为基线文件

cd "$(dirname "${BASH_SOURCE[0]}")"
NAME=GMDBV5
GMDB_DIR=$(cd "$(dirname $0)"/../../../..; pwd)
YangPasPath=${GMDB_DIR}/test/sdv/testcases/06_Other/082_YangBrowser/03_YangPassiveNodeInfo/YANG_PASSIVE_NODE_INFO.txt
YangB506=${GMDB_DIR}/test/sdv/perf/pst_perf_test/yangjson/PASSIVE_NODE_B506_no.txt

if [ -f "$YangPasPath" ]; then
    echo "$YangPasPath 目录下 YANG_PASSIVE_NODE_INFO.txt 存在"
else
    echo "$YangPasPath 目录下 YANG_PASSIVE_NODE_INFO.txt 不存在"
    exit 1
fi

# $1 入参是 V\$YANG_PASSIVE_NODE_INFO新版本视图文件
input_file2=$YangPasPath
# 将文件copy
new_file="guangqi_PASSIVE_NODE_test.txt"
cp "$input_file2" "$new_file"

# 统计index =个数
count=$(grep -o "$index =" "$new_file" | wc -l)
echo "'index =' 在文件中出现 $count 次."

# 设置要查找的字段
search_char="index = "
search_interface="PLAN_NAME: \[if:interface.1"

# 清空文件
echo "" > PASSIVEfail.txt
echo "" > PASSIVEdata.txt
echo "" > PASSIVE_NODE_diff.txt

# 逐行读取文件内容
while IFS= read -r line
do
    echo "" > PASSIVEdata.txt
    # 判断当前行是否包含index =字符
    if [[ "$line" == *"$search_char"* ]]; then
        echo "$line" >> PASSIVEdata.txt
        # 将包含指定字符及后9行写入到PASSIVEdata.txt文件中
        for ((i=1; i<=9; i++)); do
            read -r next_line
            regex="\(([^)]+)\)"
            if [[ $next_line =~ $regex ]]; then
                # 删除（）里的id内容：PLAN_NAME: [if:interface.1(1049486), onu-catv(81)]
                new_line=$(echo $next_line| sed -r "s/\\([^()]+\\)//g")
                echo "$new_line" >> PASSIVEdata.txt
            else
                # 将没有括号的行直接写入输出文件
                echo "$next_line" >> PASSIVEdata.txt
            fi
        done
        sed -i '/DB_ID/d' PASSIVEdata.txt
        sed -i '/NAMESPACE_ID/d' PASSIVEdata.txt
        sed -i '/NAMESPACE_NAME/d' PASSIVEdata.txt
        # 查找是否包含"PLAN_NAME: [if:interface.1"的内容
        if grep -q "$search_interface" PASSIVEdata.txt; then
            Common=$(cat PASSIVEdata.txt |awk -F ',' '{print $1}' |grep "Common parent level:" |awk -F ':' '{print $3}')
            plan=$(cat PASSIVEdata.txt |awk -F ',' '{print $2}' |grep "plan key level:" |awk -F ':' '{print $2}')
            has=$(cat PASSIVEdata.txt |awk -F ',' '{print $3}' |grep "has passive plan:" |awk -F ':' '{print $2}' |awk -F '.' '{print $1}')
            if [[ $Common != $plan && $has = 0 ]]; then
                pasive=$(cat PASSIVEdata.txt |grep "PASSIVE_RELATION_INFO:" )
                echo "Common!=plan&&has=0有: $pasive"
                cat PASSIVEdata.txt >> PASSIVEfail.txt
            fi
        fi
        sed -i "/$line/,+9d" "$new_file"
    fi
done < "$new_file"

sleep 2
countall=$(grep -o "$search_interface" $YangPasPath | wc -l)
echo "$1 文件中包含字符 $search_interface 个数为 $countall \n"

countMatch=$(grep -o "$search_interface" PASSIVEfail.txt | wc -l)
echo "PASSIVEfail.txt 文件中过滤后包含字符 $search_interface 个数为 $countMatch \n"

sleep 3
diff_output2=$(diff PASSIVEfail.txt $YangB506)
if [ -z "$diff_output2" ]; then
    echo "两个文件相同"
else
    echo "$diff_output2" >> PASSIVE_NODE_diff.txt
    echo -e "\n两个文件不同的地方如下: "
    cat PASSIVE_NODE_diff.txt
    echo -e "\n>>> the log is YANG_PASSIVE_NODE_INFO视图diff文件 PASSIVE_NODE_diff.txt \n"
    exit 1
fi

rm -rf PASSIVEdata.txt guangqi_PASSIVE_NODE_test.txt

exit 0
