{"comment": "接口的VLAN配置，FES360#，637#, 1659#, 2776#", "version": "2.0", "type": "record", "name": "port_vlan_cfg", "config": {"check_validity": false}, "max_record_count": 4608, "fields": [{"name": "ifindex", "type": "uint32", "comment": "接口索引"}, {"name": "vrid", "type": "uint32", "comment": "vrid"}, {"name": "tpid", "type": "uint32", "comment": "FES637#"}, {"name": "link_type", "type": "uint8", "comment": "1:access, 2:trunk, 3:hybrid, 4:unknown"}, {"name": "pvid", "type": "uint16", "comment": "defaultvlanforuntaggedpacket"}, {"name": "vlan_port_index", "type": "uint32", "comment": "需要分配给V5使用l2port-ifindex"}, {"name": "discard_untag", "type": "uint8", "comment": "untag报文丢弃"}, {"name": "discard_tag", "type": "uint8", "comment": "tag报文丢弃"}, {"name": "priority", "type": "uint8", "comment": "优先级"}, {"name": "port_bridge_enable", "type": "uint8", "comment": "自发自收使能"}, {"name": "port_group_id", "type": "uint32", "comment": "port group id"}, {"name": "onoff_state", "type": "uint8", "comment": "FES1659#"}, {"name": "gvrp_enable", "type": "uint8", "comment": "FES2776#"}, {"name": "if_phy_type", "type": "uint8", "comment": "接口类型"}, {"name": "contain_untag_subif", "type": "uint8", "comment": "是否包含untag子接口"}, {"name": "hpp_svc_status", "type": "uint8", "comment": "0:ok, 9:fail"}, {"name": "enp_svc_status", "type": "uint8", "comment": "0:ok, 9:fail"}, {"name": "app_source_id", "type": "uint32", "comment": "生产者实例id"}, {"name": "app_serial_id", "type": "uint32", "comment": "生产者实例id"}, {"name": "app_obj_id", "type": "uint64", "comment": "生产者实例id"}, {"name": "app_version", "type": "uint32", "comment": "db表版本信息"}], "keys": [{"name": "port_vlan_cfg_pk", "index": {"type": "primary"}, "node": "port_vlan_cfg", "fields": ["ifindex"], "constraints": {"unique": true}, "comment": "表索引"}], "super_fields": [{"name": "port_vlan_cfg_sf", "fields": ["ifindex", "vrid", "tpid", "link_type", "pvid", "vlan_port_index", "discard_untag", "discard_tag", "priority", "port_bridge_enable", "port_group_id", "onoff_state", "gvrp_enable", "if_phy_type", "contain_untag_subif", "hpp_svc_status", "enp_svc_status", "app_source_id", "app_serial_id", "app_obj_id", "app_version"]}]}