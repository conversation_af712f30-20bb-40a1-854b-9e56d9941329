{"comment": "mac路由下一跳隧道信息", "version": "2.0", "type": "record", "name": "iidg_node_nhp_entry", "config": {"check_validity": false}, "max_record_count": 800000, "fields": [{"name": "vr_id", "type": "uint32", "comment": "vs索引"}, {"name": "bd_id", "type": "uint32", "comment": "广播域Id"}, {"name": "bd_type", "type": "uint32", "comment": "广播域类型"}, {"name": "iidg_index", "type": "uint32", "comment": "全局下一跳索引"}, {"name": "iid_index", "type": "uint32", "comment": "下一跳索引"}, {"name": "tunnel_id", "type": "uint32", "comment": "隧道ID"}, {"name": "tunnel_type", "type": "uint32", "comment": "隧道类型"}, {"name": "vrf_id", "type": "uint32", "comment": "vpn域ID"}, {"name": "vp_id", "type": "uint32", "comment": "vp资源索引"}, {"name": "vni_id", "type": "uint32", "comment": "vni索引"}, {"name": "vsi_id", "type": "uint32", "comment": "vsi资源索引"}, {"name": "complete_flags", "type": "uint32", "comment": "资源完备标识"}], "keys": [{"name": "keys", "index": {"type": "primary"}, "node": "iidg_node_nhp_entry", "fields": ["vr_id", "bd_id", "bd_type", "iidg_index", "iid_index"], "constraints": {"unique": true}, "comment": "主键值"}, {"name": "vr_iidindex", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "iidg_node_nhp_entry", "fields": ["vr_id", "iid_index"], "constraints": {"unique": false}, "comment": "iid索引"}, {"name": "tunnel_info", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "iidg_node_nhp_entry", "fields": ["vr_id", "tunnel_id", "tunnel_type", "vrf_id"], "constraints": {"unique": false}, "comment": "隧道信息索引"}]}