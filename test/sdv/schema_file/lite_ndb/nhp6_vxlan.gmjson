{"comment": "扩展下一跳VXLAN表，对应3174#表", "version": "2.0", "type": "record", "name": "nhp6_vxlan", "config": {"check_validity": true}, "max_record_count": 4096000, "fields": [{"name": "nhp_index", "type": "uint32", "comment": "下一跳索引"}, {"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "tunnel_vrf_index", "type": "uint32", "comment": "Tunnel所在的VRF"}, {"name": "tunnel_id", "type": "uint32", "comment": "Tunnel ID"}, {"name": "tunnel_type", "type": "uint8", "comment": "Tunnel类型"}, {"name": "af_type", "type": "uint8", "comment": "afType"}, {"name": "gwip", "type": "fixed", "size": 16, "comment": "gwip"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "flags", "type": "uint32"}, {"name": "vni_id", "type": "uint32", "comment": "Vxlan隧道所在vni"}, {"name": "aib_index", "type": "uint32", "comment": "申请的aibIndex索引"}, {"name": "overlay_type", "type": "uint16", "comment": "隧道承载类型"}, {"name": "mac_addr", "type": "fixed", "size": 6, "comment": "下一跳Mac地址"}, {"name": "status_high_prio", "type": "uint8", "default": 0, "comment": "SERVICE高优先级下发状态,1对应成功,如下发错误,错误状态见errcode_high_prio"}, {"name": "status_normal_prio", "type": "uint8", "default": 0, "comment": "SERVICE普通优先级下发状态,1对应成功,如下发错误,错误状态见errcode_normal_prio"}, {"name": "errcode_high_prio", "type": "uint8", "default": 0, "comment": "SERVICE高优先级下发状态错误码"}, {"name": "errcode_normal_prio", "type": "uint8", "default": 0, "comment": "SERVICE普通优先级下发状态错误码"}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的NHP上下文"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的NHP上下文"}, {"name": "app_source_id", "type": "uint32"}, {"name": "group_smooth_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}, {"name": "vsi_index", "type": "uint32", "comment": "vsi索引"}, {"name": "dvp_index", "type": "uint32", "comment": "vp索引"}, {"name": "time_stamp_create", "type": "time"}, {"name": "time_stamp_smooth", "type": "time"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nhp6_vxlan", "fields": ["nhp_index", "vr_id", "tunnel_vrf_index", "tunnel_id", "tunnel_type", "af_type", "gwip"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "nhpindex_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp6_vxlan", "fields": ["nhp_index"], "constraints": {"unique": false}, "comment": "根据nhp_index索引"}, {"name": "tunnel_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp6_vxlan", "fields": ["vr_id", "tunnel_vrf_index", "tunnel_id", "tunnel_type"], "constraints": {"unique": false}, "comment": "根据隧道key索引"}, {"name": "vni_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp6_vxlan", "fields": ["vr_id", "vni_id"], "constraints": {"unique": false}, "comment": "根据vni索引"}]}