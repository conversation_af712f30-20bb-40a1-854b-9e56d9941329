{"comment": "标准下一跳表，对应42#表", "version": "2.0", "type": "record", "name": "nhp_std", "config": {"check_validity": true}, "max_record_count": 4096000, "fields": [{"name": "nhp_index", "type": "uint32", "comment": "下一跳索引"}, {"name": "next_hop", "type": "uint32", "comment": "下一跳"}, {"name": "out_if_index", "type": "uint32", "comment": "出接口"}, {"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "flags", "type": "uint32"}, {"name": "if_type", "type": "uint32", "comment": "接口类型"}, {"name": "iid_flags", "type": "uint32", "comment": "直连路由/非直连路由切换过"}, {"name": "work_if_index", "type": "uint32"}, {"name": "status_high_prio", "type": "uint8", "default": 0, "comment": "SERVICE高优先级下发状态,1对应成功,如下发错误,错误状态见errcode_high_prio"}, {"name": "status_normal_prio", "type": "uint8", "default": 0, "comment": "SERVICE普通优先级下发状态,1对应成功,如下发错误,错误状态见errcode_normal_prio"}, {"name": "errcode_high_prio", "type": "uint8", "default": 0, "comment": "SERVICE高优先级下发状态错误码"}, {"name": "errcode_normal_prio", "type": "uint8", "default": 0, "comment": "SERVICE普通优先级下发状态错误码"}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的NHP上下文"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的NHP上下文"}, {"name": "app_source_id", "type": "uint32"}, {"name": "group_smooth_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}, {"name": "attr_flag", "type": "uint32", "comment": "公用字段"}, {"name": "fwd_if_type", "type": "uint16"}, {"name": "reserved", "type": "uint16", "nullable": true}, {"name": "time_stamp_create", "type": "time"}, {"name": "time_stamp_smooth", "type": "time"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nhp_std", "fields": ["nhp_index", "next_hop", "out_if_index"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "nhpindex_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["nhp_index"], "constraints": {"unique": false}, "comment": "根据nhp_index索引"}, {"name": "ip_ifindex_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["next_hop", "out_if_index"], "constraints": {"unique": false}, "comment": "根据出接口和下一跳查询"}, {"name": "ifindex_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["out_if_index"], "constraints": {"unique": false}, "comment": "根据出接口查询"}, {"name": "iftype_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["if_type"], "constraints": {"unique": false}, "comment": "根据出接口类型查询"}, {"name": "work_if_index_key", "index": {"type": "hashcluster"}, "node": "nhp_std", "fields": ["work_if_index"], "constraints": {"unique": false}, "comment": "根据work_if_index查询"}]}