{"comment": "ACL UCL表", "version": "2.0", "type": "record", "name": "acl_ucl", "config": {"check_validity": true}, "max_record_count": 128000, "fields": [{"name": "vrid", "type": "uint32", "comment": "VRRP备份组的索引"}, {"name": "acl_index", "type": "uint32", "comment": "ACL的索引"}, {"name": "acl_group_id", "type": "uint32", "comment": "ACLGroup的索引"}, {"name": "vrvrf_index", "type": "uint32", "nullable": true, "comment": "平台VrVrf的索引"}, {"name": "acl_priority", "type": "uint32", "comment": "ACL的优先级"}, {"name": "cond_mask", "type": "uint32", "comment": "条件掩码"}, {"name": "time_range_id", "type": "uint32", "nullable": true, "comment": "Time Range的索引"}, {"name": "time_range_status", "type": "uint8", "nullable": true, "comment": "Time Range的激活状态"}, {"name": "acl_action_type", "type": "uint8", "comment": "ACL动作类型"}, {"name": "src_ip", "type": "uint32", "nullable": true, "comment": "源IP地址"}, {"name": "src_ip_mask", "type": "uint32", "nullable": true, "comment": "源IP地址掩码"}, {"name": "dest_ip", "type": "uint32", "nullable": true, "comment": "目的IP地址"}, {"name": "dest_ip_mask", "type": "uint32", "nullable": true, "comment": "目的IP地址掩码"}, {"name": "s_ippool_index", "type": "uint32", "nullable": true, "comment": "源IP池的索引"}, {"name": "d_ippool_index", "type": "uint32", "nullable": true, "comment": "目的IP池的索引"}, {"name": "b_src_port", "type": "uint16", "nullable": true, "comment": "源起始端口号"}, {"name": "e_src_port", "type": "uint16", "nullable": true, "comment": "源终止端口号"}, {"name": "b_dst_port", "type": "uint16", "nullable": true, "comment": "目的起始端口"}, {"name": "e_dst_port", "type": "uint16", "nullable": true, "comment": "目的终止端口"}, {"name": "s_port_pool_id", "type": "uint32", "nullable": true, "comment": "源端口池的索引"}, {"name": "d_port_pool_id", "type": "uint32", "nullable": true, "comment": "目的端口池索引"}, {"name": "any_flag", "type": "uint8", "nullable": true, "comment": "匹配任意规则的标志"}, {"name": "protocol", "type": "uint8", "nullable": true, "comment": "协议"}, {"name": "frag_type", "type": "uint8", "nullable": true, "comment": "分片类型"}, {"name": "tos", "type": "uint8", "nullable": true, "comment": "TOS"}, {"name": "tcp_flag", "type": "uint8", "nullable": true, "comment": "TCP标志"}, {"name": "established", "type": "uint8", "nullable": true, "comment": "创建标记"}, {"name": "icmp_type", "type": "uint8", "nullable": true, "comment": "ICMP类型"}, {"name": "icmp_code", "type": "uint8", "nullable": true, "comment": "ICMP码"}, {"name": "dscp", "type": "uint8", "nullable": true, "comment": "DSCP(Diffserv Code Point)"}, {"name": "ip_prec", "type": "uint8", "nullable": true, "comment": "IP优先级"}, {"name": "igmp_type", "type": "uint8", "nullable": true, "comment": "IGMP类型"}, {"name": "src_group_id", "type": "uint16", "nullable": true, "comment": "源GroupID"}, {"name": "dst_group_id", "type": "uint16", "nullable": true, "comment": "目的GroupID"}, {"name": "fqdn", "type": "string", "nullable": true, "size": 65, "comment": "FQDN名字认证"}, {"name": "app_source_id", "type": "uint32", "comment": "生产者源标识，对应VRP8 hSrcPid字段"}, {"name": "app_serial_id", "type": "uint32", "comment": "生产者源标识"}, {"name": "app_obj_id", "type": "uint64", "comment": "用于记录的生命周期管理，即使KEY和DATA相同，但删除后再添加时这个ID也会不同，具体使用场景不明确，暂时保留不用"}, {"name": "app_version", "type": "uint32", "comment": "记录版本号，用于跟踪同一条记录的变化情形，具体使用场景不明确，暂时保留不用"}], "keys": [{"name": "acl_ucl_pk", "index": {"type": "primary"}, "node": "acl_ucl", "fields": ["vrid", "acl_index", "acl_group_id"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "acl_ucl_pri_pk", "index": {"type": "local"}, "node": "acl_ucl", "fields": ["vrid", "acl_group_id", "acl_priority"], "comment": "根据vrid、acl_group_id和acl_priority索引"}]}