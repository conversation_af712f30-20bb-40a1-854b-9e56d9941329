{"comment": "arp订阅关系表", "version": "2.0", "type": "record", "name": "sub_arp", "config": {"check_validity": false}, "max_record_count": 1000000, "fields": [{"name": "subscriber_id", "type": "uint32", "comment": "订阅者id"}, {"name": "ip_address", "type": "uint32", "comment": "下一跳Ip地址"}, {"name": "ifindex", "type": "uint32", "comment": "下一跳出接口"}, {"name": "update_notify_flag", "type": "uint32", "comment": "更新通知标志：单个Key可能对应多份数据，在数据变化时，推送时应当根据该标志可选，是推送变化后的全量数据还是只推送变化的数据"}, {"name": "app_version", "type": "uint32"}], "keys": [{"name": "arp_sub_key", "index": {"type": "primary"}, "node": "sub_arp", "fields": ["subscriber_id", "ip_address", "ifindex"], "constraints": {"unique": true}}]}