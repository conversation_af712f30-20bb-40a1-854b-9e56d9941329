{"comment": "存储eid", "version": "2.0", "type": "record", "name": "hsec_ppe_eid", "config": {"check_validity": false}, "max_record_count": 2048, "fields": [{"name": "unit", "type": "uint32", "comment": "芯片号"}, {"name": "port", "type": "uint32", "comment": "端口号"}, {"name": "l3Flag", "type": "uint32", "comment": "标记是否是三层口"}, {"name": "protocol", "type": "uint32", "comment": "协议值"}, {"name": "eid", "type": "uint32", "comment": "acl规则返回值"}], "keys": [{"name": "hsec_ppe_eid_pk", "index": {"type": "primary"}, "node": "hsec_ppe_eid", "fields": ["unit", "port", "l3Flag", "protocol"], "constraints": {"unique": true}}]}