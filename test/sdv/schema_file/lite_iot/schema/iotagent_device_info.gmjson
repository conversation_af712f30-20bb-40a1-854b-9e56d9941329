{"version": "2.0", "type": "record", "name": "iotagent_device_info", "max_record_count": 4096, "fields": [{"name": "serial_number", "type": "string", "size": 36}, {"name": "state", "type": "uint8", "default": 0}, {"name": "device_id", "type": "string", "size": 50}, {"name": "register_time", "type": "string", "size": 36}, {"name": "device_type", "type": "string", "size": 36, "nullable": true}, {"name": "model", "type": "string", "size": 36, "nullable": true}, {"name": "protocol_type", "type": "string", "size": 36, "nullable": true}, {"name": "manufacturer_id", "type": "string", "size": 36, "nullable": true}, {"name": "manufacturer_name", "type": "string", "size": 36, "nullable": true}], "keys": [{"name": "PK", "index": {"type": "primary"}, "node": "iotagent_device_info", "fields": ["serial_number"], "constraints": {"unique": true}}]}