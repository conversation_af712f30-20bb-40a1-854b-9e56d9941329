#!/bin/bash
# Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
# version V1.0
#
# used for build and pack db in rtosv2
#

set -e

CUR_DIR=$PWD
GMDB_HOME=$(dirname $(dirname $(readlink -f ${BASH_SOURCE[0]})))

function build_clean() {
    pushd ${GMDB_HOME};
    ./build.sh clean
    popd;
}

function build_rtosv2() {
    pushd ${GMDB_HOME};
    ./build.sh -R rtosv2 -a aarch64 -P yunshan -s YUNSHAN ${@};
    popd;
}

function build_gmcmd() {
    pushd ${GMDB_HOME};
    sh test/tools/tool_build.sh -R rtosv2 -a aarch64 -l src || true
    popd;
}

function package_rtosv2() {
    rm -rf ${GMDB_HOME}/ac_client/usr/local/;
    mkdir -p ${GMDB_HOME}/ac_client/usr/local/lib;
    mkdir -p ${GMDB_HOME}/ac_client/usr/local/bin;

    cp -arpf ${GMDB_HOME}/output/rtosv2/aarch64/bin/*gm* ${GMDB_HOME}/ac_client/usr/local/bin/;
    cp -arpf ${GMDB_HOME}/output/rtosv2/aarch64/lib/*gm* ${GMDB_HOME}/ac_client/usr/local/lib/;

    [ -f "${GMDB_HOME}"/test/tools/build/bin/gmcmd ] && cp -arpf ${GMDB_HOME}/test/tools/build/bin/gm* ${GMDB_HOME}/ac_client/usr/local/bin/;

    pushd ${GMDB_HOME}/ac_client
    tar zcvf ac_client.tar.gz usr/local/
    mv ac_client.tar.gz $CUR_DIR/

    mkdir -p strip/usr/local/;
    cp -arpf usr/local/* strip/usr/local/
    pushd strip/
    llvm-strip usr/local/*/*
    tar zcvf ac_client_strip.tar.gz usr/local/
    mv ac_client_strip.tar.gz $CUR_DIR/
    popd

    popd
}

function build_hpe() {
    pushd ${GMDB_HOME};
    ./build.sh -R hpe -a aarch64 -P yunshan -s YUNSHAN $@
    popd;
}

function package_hpe() {
    rm -rf ${GMDB_HOME}/ac_server/usr/local/hpe*;
    mkdir -p ${GMDB_HOME}/ac_server/usr/local/hpe/;

    pushd ${GMDB_HOME}/ac_server

    cp -arpf ${GMDB_HOME}/output/hpe/aarch64/lib/*gm* usr/local/hpe/
    cp -arpf ${GMDB_HOME}/output/hpe/aarch64/bin/*gm* usr/local/hpe/
    tar zcvf ac_server.tar.gz usr/local/hpe/
    mv ac_server.tar.gz $CUR_DIR/

    mkdir -p strip/usr/local/hpe/;
    cp -arpf usr/local/hpe/* strip/usr/local/hpe/
    pushd strip/
    llvm-strip usr/local/hpe/*
    tar zcvf ac_server_strip.tar.gz usr/local/hpe/
    mv ac_server_strip.tar.gz $CUR_DIR/
    popd

    popd
}

function main() {
    if [ "$1" = "all" ]; then
        build_clean
        build_hpe ${@:2}
        package_hpe

        build_clean
        build_rtosv2 ${@:2}
        #build_gmcmd
        package_rtosv2
    elif [ "$1" = "client" ]; then
        build_rtosv2 ${@:2}
        #build_gmcmd
        package_rtosv2
    elif [ "$1" = "server" ]; then
        build_hpe ${@:2}
        package_hpe
    elif [ "$1" = "clean" ]; then
        build_clean
    else
        echo "only support usage: build_package_ac.sh [all/client/server/clean] [ -f debug/release ] ..."
    fi
    echo "Built successfully, please check packages ac_*.tar.gz here: $CUR_DIR/"
    ls $CUR_DIR/ac_*.tar.gz
}

main $@
